import { ColorPicker as AntdColorPicker } from "antd";
import { useState } from "react";
import PropTypes from "prop-types";
import classNames from "classnames";
import styles from "./index.module.scss";

function ColorPicker(props) {
  const { defaultValue, value: propValue, onChange, disabled, className } = props;
  const [innerValue, setInnerValue] = useState(defaultValue);
  const isControlled = Object.hasOwnProperty.call(props, "value");
  const value = isControlled ? propValue : innerValue;

  return (
    <AntdColorPicker
      {...props}
      value={value}
      onChange={onChange}
      onClear={() => {
        setInnerValue(null);
        onChange?.(null, "");
      }}
      rootClassName={classNames({ [styles.disabled]: disabled })}
    ></AntdColorPicker>
  );
}

ColorPicker.propTypes = {
  defaultValue: PropTypes.any,
  value: PropTypes.any,
  onChange: PropTypes.func,
  onChangeComplete: PropTypes.func,
  disabled: PropTypes.bool,
};

export default ColorPicker;
