import styles from "./index.module.scss";
import FabricCanvas from "./components/fabric-canvas";
import { useEffect, useState } from "react";
import Fetchers from "@/fetchers";
import Utils from "@/utils";

function EditTemplate(props) {
  const {} = props;
  const [template, setTemplate] = useState({});

  useEffect(() => {
    (async () => {
      const queryParams = Utils.getQueryParams(window.location.href);
      const template = await Fetchers.getPodTemplate({ id: queryParams.id || 1 }).then((res) => res.data.data.item);
      setTemplate(template);
    })();

    return function () {};
  }, []);

  return (
    <div className={styles.page}>
      <div className={styles.left}>
        <FabricCanvas width={template.initWidth} height={template.initHeight} jsonData={template.canvas}></FabricCanvas>
      </div>
      <div className={styles.right}></div>
    </div>
  );
}

export default EditTemplate;
