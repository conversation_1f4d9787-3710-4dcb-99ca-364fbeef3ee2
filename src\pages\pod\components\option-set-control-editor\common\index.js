import { CustomFunctionType, OptionType } from "@/pages/pod/common";
import CustomFunctions from "../components/custom-functions";
import Conditions from "../components/conditions";
import SwatchValues from "../components/swatch-values";
import DropdownValues from "../components/dropdown-values";
import SwatchPrivateControls from "../components/swatch-private-controls";
import DropdownPrivateControls from "../components/dropdown-private-controls";
import TextInputPrivateControls from "../components/text-input-private-controls";
import { ControlWrapper, FormControl } from "@/components/react-form-x";
import { Input } from "antd";
import ImageUploadPrivateControls from "@/pages/pod/components/option-set-control-editor/components/image-upload-private-controls";

function CustomCssClassControl(props) {
  const { keyPath } = props;
  return (
    <div>
      <FormControl
        keyPath={keyPath}
        name="css_class"
        render={(props) => (
          <div>
            <div>Custom CSS classes:</div>
            <div>
              <ControlWrapper {...props} render={(props) => <Input {...props}></Input>}></ControlWrapper>
            </div>
          </div>
        )}
      ></FormControl>
    </div>
  );
}

export function renderPrivateControls({ optionType, keyPath, control }) {
  if ([OptionType.Swatch].includes(optionType)) {
    return (
      <>
        <SwatchPrivateControls keyPath={keyPath}></SwatchPrivateControls>
        <CustomCssClassControl keyPath={keyPath}></CustomCssClassControl>
      </>
    );
  } else if ([OptionType.Dropdown].includes(optionType)) {
    return (
      <>
        <DropdownPrivateControls keyPath={keyPath}></DropdownPrivateControls>
        <CustomCssClassControl keyPath={keyPath}></CustomCssClassControl>
      </>
    );
  } else if ([OptionType.TextInput].includes(optionType)) {
    return (
      <>
        <TextInputPrivateControls keyPath={keyPath} control={control}></TextInputPrivateControls>
        <CustomCssClassControl keyPath={keyPath}></CustomCssClassControl>
      </>
    );
  } else if ([OptionType.ImageUpload].includes(optionType)) {
    return (
      <>
        <ImageUploadPrivateControls keyPath={keyPath}></ImageUploadPrivateControls>
      </>
    );
  }
}

export const customFunctionOptions = [
  { label: "Text", value: CustomFunctionType.Text },
  { label: "Text Color", value: CustomFunctionType.TextColor },
  { label: "Font Type", value: CustomFunctionType.FontType },
  { label: "Dynamic Image", value: CustomFunctionType.DynamicImage },
  { label: "Image Color", value: CustomFunctionType.ImageColor },
  { label: "Dynamic Vector", value: CustomFunctionType.DynamicVector },
  { label: "Dynamic Vector Color", value: CustomFunctionType.DynamicVectorColor },
  { label: "Change Template", value: CustomFunctionType.ChangeTemplate },
  { label: "Add Product", value: CustomFunctionType.AddProduct },
  { label: "Layout Visibility", value: CustomFunctionType.LayoutVisibility },
  { label: "Upload Image", value: CustomFunctionType.UploadImage },
];

export function createIncrementId({ items }) {
  return (items?.length > 0 ? Math.max(...items.map((item) => item.id)) : 0) + 1;
}

export function renderSubCollapseItems({ control, keyPath, forceUpdate, optionSet, optionType }) {
  const collapseItems = [
    {
      key: "functions",
      label: "Custom Functions",
      children: <CustomFunctions control={control} keyPath={keyPath} forceUpdate={forceUpdate}></CustomFunctions>,
    },
    {
      key: "conditions",
      label: "Conditions",
      children: (
        <Conditions control={control} keyPath={keyPath} forceUpdate={forceUpdate} optionSet={optionSet}></Conditions>
      ),
    },
  ];
  if ([OptionType.Swatch].includes(optionType) && control.options?.length > 0) {
    collapseItems.push({
      key: `swatchValues`,
      label: `Swatch Values`,
      children: (
        <SwatchValues
          optionSet={optionSet}
          control={control}
          keyPath={keyPath}
          forceUpdate={forceUpdate}
        ></SwatchValues>
      ),
    });
  } else if ([OptionType.Dropdown].includes(optionType) && control.options?.length > 0) {
    collapseItems.push({
      key: `dropdownValues`,
      label: `Dropdown Values`,
      children: (
        <DropdownValues
          optionSet={optionSet}
          control={control}
          keyPath={keyPath}
          forceUpdate={forceUpdate}
        ></DropdownValues>
      ),
    });
  }
  return collapseItems;
}
