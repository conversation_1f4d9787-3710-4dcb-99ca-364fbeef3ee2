import { useEffect } from "react";
import EnvHelper from "helpers/env-helper";
import * as ww from "@wecom/jssdk";
import Enums from "enums";

const AgentIds = {
  test: "1000046",
  production: "1000042",
};

function WwLogin({ onLogin }) {
  useEffect(() => {
    let wwLogin = null;
    wwLogin = ww.createWWLoginPanel({
      el: "#wwLogin",
      params: {
        login_type: "CorpApp",
        appid: "wwb1fc67f17091ca05",
        agentid: AgentIds[EnvHelper.RuntimeEnv] || AgentIds.production,
        redirect_uri: EnvHelper.RedirectOrigin, // 登录成功重定向 url
        // state: "loginState",
        redirect_type: "callback", // 通过 onLoginSuccess 回调code 自行处理跳转
        panel_size: "small",
        lang: "zh",
      },
      onCheckWeComLogin({ isWeComLogin }) {},
      onLoginSuccess({ code }) {
        onLogin({ code }, Enums.LoginType.Code);
      },
      onLoginFail(err) {
        console.log(err);
      },
    });

    return function unmount() {
      wwLogin?.unmount();
    };
  }, [onLogin]);

  return <div id="wwLogin"></div>;
}

export default WwLogin;
