import { useState, useRef } from "react";

function useToggle(defaultValue = false, reverseValue) {
  const [state, setState] = useState(defaultValue);
  const reverseValueOrigin = reverseValue === undefined ? !defaultValue : reverseValue;

  const actionsRef = useRef();
  if (!actionsRef.current) {
    actionsRef.current = {
      toggle: () => setState((prevValue) => (prevValue === defaultValue ? reverseValueOrigin : defaultValue)),
      set: (value) => setState(value),
      setLeft: () => setState(defaultValue),
      setRight: () => setState(reverseValueOrigin),
    };
  }

  return [state, actionsRef.current];
}

export default useToggle;
