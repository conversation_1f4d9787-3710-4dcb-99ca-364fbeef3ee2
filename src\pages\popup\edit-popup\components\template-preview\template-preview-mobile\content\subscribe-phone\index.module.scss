@media (max-width: 767px) {
  .dialog {
    position: fixed;
    z-index: 1300;
    inset: 0px;

    .backdrop {
      position: fixed;
      display: flex;
      align-items: center;
      justify-content: center;
      inset: 0px;
      z-index: -1;
      background-color: rgba(0, 0, 0, 0.22);
      overscroll-behavior: contain;
    }
  }
}

.smsPopup {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  text-align: center;
  border-radius: 6px;
  background: #fce9da;
  user-select: none;
  cursor: pointer;
  position: fixed;
  right: 6px;
  bottom: 80px;
  z-index: 100;
}

.container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: 0px;
  overflow: hidden auto;
  text-align: center;
  overscroll-behavior: contain;

  &::after {
    content: "";
    display: inline-block;
    vertical-align: middle;
    height: 100%;
    width: 0px;
  }

  .content {
    width: 80%;
    min-height: 50px;
    background-color: rgb(255, 255, 255);
    // margin: 0 37px;
    overflow-y: auto;
    display: inline-block;
    vertical-align: middle;
    text-align: left;
    background: #fffaf7;
    position: relative;

    .close {
      position: absolute !important;
      top: 0;
      right: 0;
      z-index: 9000;
      padding: 8px;
      cursor: pointer;
    }

    .contentWrapper {
      padding: 36px 33px;

      .title {
        font-size: 12px;
        text-align: left;
        color: #231816;
        line-height: 12px;
      }

      .form {
        .label {
          font-size: 12px;
          font-weight: 400;
          text-align: left;
          color: #231816;
          line-height: 12px;
          letter-spacing: 0.12px;
          margin-top: 17px;
        }

        .subscribeWrapper {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: 100%;
          height: 34px;
          background: #f1eae5;
          margin-top: 10px;
          padding: 0 10px;
          overflow: hidden;

          .inputWrapper {
            flex: 1;
            overflow: hidden;
          }

          select,
          input {
            font-size: 12px;
            border: none;
            background-color: transparent;
            outline: none;
          }

          select {
            cursor: pointer;
          }

          input {
            font-weight: 400;
            text-align: left;
            color: #231816;
            line-height: 24px;
            letter-spacing: 0.14px;
          }
        }

        &[class~="has-error"] {
          .errorMessage {
            display: block;
            transform: scaleY(1);
            pointer-events: none;
          }
        }

        .errorMessage {
          display: none;
          font-size: 12px;
          font-weight: 400;
          text-align: left;
          color: #ff0000;
          line-height: 11px;
          transform: scaleY(0);
          transform-origin: top;
          transition: transform ease 0.3s;
          margin-top: 5px;
        }

        .tipsWrapper {
          margin-top: 10px;

          .tipsLabel {
            user-select: none;

            .privacyPolicyCheckbox {
              display: none;
              vertical-align: middle;
            }

            .show {
              display: inline-block;
            }
          }

          .asterisk,
          .tips {
            font-size: 12px;
            font-weight: 400;
            text-align: left;
            line-height: 18px;
          }

          .asterisk {
            color: #cd0b0b;
          }

          .tips {
            color: #000;
          }

          .privacyPolicy {
            color: #ff9600;
            text-decoration: underline;
          }
        }

        .submitWrapper {
          margin-top: 20px;

          button {
            width: 100%;
            height: 33px;
            background: #e1d5ce;
            border: none;
            cursor: pointer;
            font-size: 15px;
            font-weight: 400;
            color: #231816;
            line-height: 24px;
          }
        }

        .doNotShowAgain {
          font-size: 16px;
          font-weight: 400;
          text-align: center;
          color: #999999;
          line-height: 24px;
          cursor: pointer;
          margin-top: 10px;
        }
      }
    }

    .successWrapper {
      padding: 33px;

      .header {
        display: flex;
        align-items: center;
        flex-direction: column;
        gap: 10px;

        .title {
          font-size: 13px;
          font-weight: 400;
          text-align: center;
          color: #231816;
          line-height: 15px;
        }
      }

      .discount {
        display: flex;
        align-items: center;
        flex-direction: column;
        gap: 8px;
        padding: 13px 10px;
        background: #f7f1ed;
        border: 1px dashed #d4d4d4;
        margin-top: 20px;

        .tips {
          font-size: 12px;
          font-weight: 400;
          text-align: center;
          color: #666666;
          line-height: 16px;
        }

        .code {
          font-size: 15px;
          font-weight: normal;
          text-align: center;
          color: #ff9600;
          line-height: 12px;
        }
      }

      .message {
        font-size: 12px;
        font-weight: 400;
        text-align: center;
        color: #888888;
        line-height: 18px;
        margin-top: 20px;
      }
    }
  }
}
