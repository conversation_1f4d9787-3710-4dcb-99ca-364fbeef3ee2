import PropTypes from "prop-types";
import classNames from "classnames";
import styles from "./index.module.scss";

function FormControlLabel(props) {
  const { label, children, required, layout = "vertical" } = props;

  return (
    <div className={classNames({ [styles.horizontal]: layout === "horizontal" })}>
      <div style={{ display: "flex", gap: 2 }}>
        {required ? <span style={{ color: "var(--ant-error-color)" }}>*</span> : null}
        <label>{label}</label>
      </div>
      <div className={styles.control}>{children}</div>
    </div>
  );
}

FormControlLabel.propTypes = {
  label: PropTypes.string,
  required: PropTypes.bool,
  layout: PropTypes.oneOf(["vertical", "horizontal"]),
};

export default FormControlLabel;
