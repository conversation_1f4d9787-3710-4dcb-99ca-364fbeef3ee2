import styles from "./index.module.scss";
import { Col, Form, Row, Input, Flex, Button, Image, Modal, Carousel, Checkbox } from "antd";
import { DoubleLeftOutlined, LeftOutlined, RightOutlined, DoubleRightOutlined } from "@ant-design/icons";
import JSONComponents from "components/common/JSONComponent";
import ProductDetailModal from "./components/product-detail-modal";
import { useState, useRef, useEffect } from "react";
import HTMLBlock from "components/common/HtmlBlock";
import Fetchers from "fetchers";
import classNames from "classnames";
import Helper from "helpers";
import Tags from "components/business/Tags";

const defaultFilterStatus = { order_no: { readOnly: false }, product_no: { readOnly: true } };

function ShipOut(props) {
  const formRef = useRef();
  const orderNoRef = useRef();
  const goodsNoRef = useRef();
  const [filterStatus, setFilterStatus] = useState(defaultFilterStatus);
  const [data, setData] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [productDetail, setProductDetail] = useState({});
  const [activeProduct, setActiveProduct] = useState({});
  const [repairModalVisible, setRepairModalVisible] = useState(false);
  const [viewLabelOpen, setViewLabelOpen] = useState(false);
  const [isPrintOrderNo, setIsPrintOrderNo] = useState(false);
  const [buttonsLoading, setButtonsLoading] = useState({
    submitLoading: false,
    printLabelLoading: false,
    printCardLoading: false,
    printGpsrLabelLoading: false,
    returnToWarehouseLoading: false,
  });
  const unverifiedItems = data?.items?.filter((item) => !item?.passed) || [];
  const verifiedItems = data?.items?.filter((item) => item?.passed) || [];
  const unverifiedCount = unverifiedItems.length;
  const verifiedCount = verifiedItems.length;

  const { Search } = Input;

  const onSubmit = async ({ values, fetcher }) => {
    try {
      const items = data?.items?.filter((item) => !item?.passed) || [];
      if (items?.length > 0) {
        return Helper.openMessage({ type: "warning", content: "请检验所有商品" });
      }
      setButtonsLoading((buttonsLoading) => ({ ...buttonsLoading, submitLoading: true }));
      const result = await (fetcher || Fetchers.shipOrder)({
        data: { order_id: data?.order_id, is_print_order_no: isPrintOrderNo },
      }).then((res) => res?.data);
      if (!result?.success) {
        return;
      }
      handleReset();
    } finally {
      setButtonsLoading((buttonsLoading) => ({ ...buttonsLoading, submitLoading: false }));
    }
  };

  const onFinish = async (values) => {
    onSubmit({ values });
  };

  const onHandleOfflineShip = () => {
    onSubmit({ fetcher: Fetchers.offlineShip });
  };

  const onOrderSearch = async (value, e, info) => {
    e.preventDefault();
    if (!value) {
      return Helper.openMessage({ type: "warning", content: "请输入运单号或订单编号" });
    }
    try {
      Helper.pageLoading(true);
      const result = await Fetchers.getShipOutOrderInfo({ params: { order_no: value } }).then((res) => res?.data);
      if (!result?.success) {
        return;
      }
      setData(result?.data);
      setFilterStatus({ order_no: { readOnly: true }, product_no: { readOnly: false } });
      goodsNoRef.current.focus();
    } finally {
      Helper.pageLoading(false);
    }
  };

  const onGoodsSearch = (value, e, info) => {
    e.preventDefault();
    if (!value) {
      return Helper.openMessage({ type: "warning", content: "请输入商品编码" });
    }
    handlePassGoods({ code: value });
    formRef.current.setFieldsValue({ product_no: "" });
  };

  const handleViewLabel = () => {
    setViewLabelOpen(true);
  };

  const handleReset = () => {
    formRef.current.resetFields();
    setData({});
    setFilterStatus(defaultFilterStatus);
    handleFocusOrderNo();
  };

  const handleFocusOrderNo = async () => {
    await Promise.resolve().then(() => {
      orderNoRef.current?.focus();
    });
  };

  // 取消弹窗逻辑，直接放入已验
  const handlePassGoods = (params) => {
    const numericCode = params?.code?.replace(/[^\d]/g, "");
    const numberCode = numericCode ? Number(numericCode) : null;
    const matchedItem = data.items.find((item) => item?.item_code === numberCode);

    if (!matchedItem) {
      return Helper.openMessage({ type: "warning", content: "订单商品未找到" });
    }

    setData((data) => {
      return {
        ...data,
        items: data.items.map((item) => {
          if (item?.item_code === numberCode) {
            item.passed = true;
          }
          return item;
        }),
      };
    });
  };

  const handleExamineGoods = async (params) => {
    const { data: product } = (await getProductDetail(params)) || {};

    if (!product) return;
    setModalVisible(true);
    setData((data) => {
      return {
        ...data,
        items: data.items.map((item) => {
          if (item.item_id === product?.item_id) {
            item.isActive = true;
            setActiveProduct(item);
          } else {
            item.isActive = false;
          }
          return item;
        }),
      };
    });
    setProductDetail(product);
    setActiveProduct(product);
  };

  const handleExamine = (product) => {
    setData((data) => {
      return {
        ...data,
        items: data.items.map((item) => {
          if (product?.item_id === item?.item_id) {
            item.passed = true;
          }
          return item;
        }),
      };
    });
  };

  const handleReexamine = (product) => {
    setData((data) => {
      return {
        ...data,
        items: data.items.map((item) => {
          if (product?.item_id === item?.item_id) {
            item.passed = false;
          }
          return item;
        }),
      };
    });
  };

  const handleModalOk = () => {
    const product = data.items.find((product) => product?.item_id === activeProduct?.item_id);
    setData((data) => {
      return {
        ...data,
        items: data.items.map((item) => {
          if (product?.item_id === item?.item_id) {
            // if (!item.qty) return item;
            // item.qty && (item.qty -= 1);
            // if (!item?.passQty) {
            //   item.passQty = 1;
            // } else {
            //   item.passQty += 1;
            // }
            item.passed = true;
          }
          return item;
        }),
      };
    });
    handleModalCancel();
  };

  const handleModalCancel = () => {
    setModalVisible(false);
    setRepairModalVisible(false);
  };

  const onRepairOk = () => {
    setRepairModalVisible(false);
    setModalVisible(false);
    handleReset();
  };

  const handleCommonAction = async (actionType, fetcherFn, params = {}) => {
    try {
      setButtonsLoading((prevState) => ({ ...prevState, [actionType]: true }));
      return await fetcherFn({
        params: { order_id: data?.order_id, ...params },
      }).then((res) => res?.data);
    } finally {
      setButtonsLoading((prevState) => ({ ...prevState, [actionType]: false }));
    }
  };

  const handlePrintLabel = () => handleCommonAction("printLabelLoading", Fetchers.orderPrintLabel);

  const handlePrintCard = () => handleCommonAction("printCardLoading", Fetchers.orderPrintCard);

  const handlePrintGpsrLabel = () => handleCommonAction("printGpsrLabelLoading", Fetchers.orderPrintGpsrLabel);

  const handleReturnToWarehouse = () => handleCommonAction("returnToWarehouseLoading", Fetchers.orderRestore);

  const getProductDetail = async (incomingParams) => {
    try {
      setModalLoading(true);
      const result = await Fetchers.getShipOutProductDetail({
        params: {
          order_id: data?.order_id,
          ...incomingParams,
        },
      }).then((res) => res?.data);
      if (!result?.success) {
        return null;
      }
      return result;
    } finally {
      setModalLoading(false);
    }
  };

  const onHandleChange = (e) => {
    setIsPrintOrderNo(e.target.checked);
  };

  const renderProductItem = ({ item, index, canClick = true }) => {
    return (
      <div className={classNames(styles.productItem, { [styles.productItemActive]: item?.isActive })} key={index}>
        <div className={styles.productItemInfo}>
          <div className={classNames({ [styles.productImage]: canClick })}>
            <div
              className={styles.imageWrapper}
              onClick={() => (canClick ? handleExamineGoods({ item_id: item?.item_id }) : false)}
            >
              <Image src={item?.image?.src} width={110} height={110} preview={false} />
            </div>
          </div>
          <div className={styles.customProps}>
            {item?.custom_props?.map((custom, i) => (
              <div className={styles.customPropsItem} key={i}>
                <span>{custom?.label}：</span>
                <span>
                  <HTMLBlock html={custom?.value} />
                </span>
              </div>
            ))}
          </div>
          <div className={styles.productItemActions}>
            {item?.passed && (
              <div className={styles.reexamine}>
                <Button
                  type="link"
                  size="large"
                  onClick={() => {
                    handleReexamine(item);
                  }}
                >
                  <DoubleLeftOutlined />
                  重验
                </Button>
              </div>
            )}
            {!item?.passed && (
              <div className={styles.reexamine}>
                <Button
                  type="link"
                  size="large"
                  onClick={() => {
                    handleExamine(item);
                  }}
                >
                  合格
                  <DoubleRightOutlined />
                </Button>
              </div>
            )}
          </div>
        </div>
        {item?.card_title ? (
          <div className={styles.cardTitle}>
            <span>贺卡标题：</span>
            <span>
              <HTMLBlock html={item?.card_title} tag="span" />
            </span>
          </div>
        ) : null}

        {item?.card_content ? (
          <div className={styles.cardContent}>
            <span>贺卡内容：</span>
            <span>
              <HTMLBlock html={item?.card_content} tag="span" />
            </span>
          </div>
        ) : null}
      </div>
    );
  };

  useEffect(() => {
    handleFocusOrderNo();
  }, []);

  return (
    <div className={styles.container}>
      <Row gutter={16}>
        <Col span={8}>
          <div className={styles.panel}>
            <Form ref={formRef} onFinish={onFinish} layout="vertical" autoComplete="off">
              <Form.Item label="运单号/订单编号/唯一码" name="order_no" style={{ marginTop: 10 }}>
                <Search
                  ref={orderNoRef}
                  placeholder="请扫运单号/订单编号/唯一码"
                  readOnly={filterStatus.order_no.readOnly}
                  onSearch={onOrderSearch}
                />
              </Form.Item>
              <Form.Item label="商品编码" name="product_no">
                <Search
                  ref={goodsNoRef}
                  placeholder="请输入商品编码"
                  readOnly={filterStatus.product_no.readOnly}
                  onSearch={onGoodsSearch}
                />
              </Form.Item>
              <Row gutter={5} style={{ marginBottom: 24 }}>
                <Col span={24}>
                  <div>基础信息：</div>
                  <JSONComponents data={data?.basic_info} />
                </Col>
                {/* <Col span={12}>
                  <div className={styles.totalWrapper}>
                    <span className={styles.totalLabel}>商品数：</span>
                    <span className={styles.total}>{data?.qty}</span>
                  </div>
                </Col> */}
              </Row>
              <div style={{ marginBottom: 24 }}>
                <Tags tags={data?.tags} />
              </div>
              <Checkbox checked={isPrintOrderNo} onChange={onHandleChange} style={{ marginBottom: 24 }}>
                打印订单编号
              </Checkbox>
              <Flex gap="small" justify="space-between">
                <Flex wrap="wrap" gap="small" className={styles.panelItem}>
                  {/* <Button disabled={!data?.order_id}>加入包裹</Button> */}
                  <Button onClick={handleReset}>重新验货</Button>
                  <Button
                    onClick={handlePrintLabel}
                    loading={buttonsLoading.printLabelLoading}
                    disabled={!data?.order_id}
                  >
                    打印面单
                  </Button>
                  <Button
                    htmlType="submit"
                    type="primary"
                    loading={buttonsLoading.submitLoading}
                    disabled={!data?.order_id}
                  >
                    发货
                  </Button>
                </Flex>
                <Button
                  onClick={onHandleOfflineShip}
                  type="primary"
                  loading={buttonsLoading.submitLoading}
                  disabled={!data?.order_id}
                >
                  线下发货
                </Button>
              </Flex>
              <Flex wrap="wrap" gap="small" className={styles.panelItem}>
                <Button onClick={handlePrintCard} disabled={!data?.order_id} loading={buttonsLoading.printCardLoading}>
                  打印贺卡
                </Button>
                <Button
                  onClick={handlePrintGpsrLabel}
                  disabled={!data?.order_id}
                  loading={buttonsLoading.printGpsrLabelLoading}
                >
                  打印GPSR标签
                </Button>
                <Button
                  onClick={handleReturnToWarehouse}
                  disabled={!data?.order_id}
                  loading={buttonsLoading.returnToWarehouseLoading}
                >
                  归还仓库
                </Button>
              </Flex>
            </Form>

            <div className={styles.panelItem}>
              <div className={styles.address}>
                <div>地址：</div>
                <Button onClick={handleViewLabel} disabled={!data?.way_bills?.length}>
                  查看面单
                </Button>
              </div>
              <div>
                <JSONComponents data={data?.address} />
              </div>
            </div>

            <div className={styles.panelItem}>
              <div className={styles.remarkLabel}>备注：</div>
              <div>
                <JSONComponents data={data?.remark} />
              </div>
            </div>
          </div>
        </Col>
        <Col span={8}>
          <div className={classNames(styles.panel, styles.unverified)}>
            <h2>待验（{unverifiedCount}）</h2>
            <div className={styles.productItemWrapper}>
              {unverifiedItems?.map((item, index) => renderProductItem({ item, index }))}
            </div>
          </div>
        </Col>
        <Col span={8}>
          <div className={styles.panel}>
            <h2>已验（{verifiedCount}）</h2>
            <div className={styles.productItemWrapper}>
              {verifiedItems?.map((item, index) => renderProductItem({ item, index, canClick: false }))}
            </div>
          </div>
        </Col>
      </Row>
      <ProductDetailModal
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        onRepair={() => setRepairModalVisible(true)}
        loading={modalLoading}
        data={productDetail}
        repairOpen={repairModalVisible}
        onRepairOk={onRepairOk}
        onRepairCancel={() => setRepairModalVisible(false)}
      />
      <Modal
        className={styles.viewLabeModal}
        open={viewLabelOpen}
        onCancel={() => setViewLabelOpen(false)}
        width="1500px"
        destroyOnClose
        centered
        title="查看面单"
        closeIcon={true}
        footer={null}
      >
        <div className={styles.viewLabeContainer}>
          <Carousel arrows infinite={false} prevArrow={<LeftOutlined />} nextArrow={<RightOutlined />}>
            {data?.way_bills?.map((image, index) => (
              <div className={styles.imageItem} key={index}>
                <Image {...image} height="100%" preview={false} />
              </div>
            ))}
          </Carousel>
        </div>
      </Modal>
    </div>
  );
}

export default ShipOut;
