import styles from "./index.module.scss";
import classNames from "classnames";
import { useMemo, useState, useEffect } from "react";

import { useDroppable } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

import SortableItem from "../SortableItem";
import { Drag<PERSON><PERSON>le, SettingHandle, DeleteHandle } from "../Handles";

import {
  getComponentTitle,
  getComponentConfigOptions,
} from "@/pages/pms/product/edit/components/FormDesigner/common/utils";

function DroppableGroup({ children, widget, index }) {
  const { attributes, listeners, setNodeRef, transform, transition } = useDroppable({
    id: widget.key,
    data: {
      id: widget.key,
      index,
      component: widget,
      isGroupContainer: true,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div ref={setNodeRef} {...listeners} {...attributes} style={style}>
      {children}
    </div>
  );
}

function GroupContainer({
  widget,
  index,
  isSelected,
  isDragging,
  handleProps,
  isOverlay,
  openConfigDrawer,
  onWidgetDelete,
  onSelect,
  selectedKey,
  ...restProps
}) {
  const [options, setOptions] = useState([]);
  const childrenName = widget.component_config?.childrenName ?? "children";
  const items = useMemo(() => widget[childrenName] ?? [], [widget, childrenName]);

  const isGhost = widget?.type === "ghost";

  const sortedKeys = useMemo(() => items.map((widget) => widget.key), [items]);

  useEffect(() => {
    (async () => {
      const options = await getComponentConfigOptions({
        key: "title",
        identifier: widget?.component_config?.identifiers?.title,
      });
      setOptions(options);
    })();
  }, [widget]);

  return (
    <div className={styles.groupContainer}>
      <SortableContext items={sortedKeys} strategy={verticalListSortingStrategy}>
        {isGhost ? (
          <div className={styles.groupGhostLine}></div>
        ) : (
          <div
            className={classNames(
              !isGhost && styles.groupWrapper,
              (isSelected || isDragging) && styles.selected,
              isOverlay && styles.isOverlay,
              isGhost && styles.ghost
            )}
          >
            <div className={styles.groupHeader}>{getComponentTitle(widget, "title", options)}</div>

            {/* <DroppableGroup widget={widget} index={index}> */}
            <div className={styles.groupContent} data-droppable-group={true} data-key={widget.key}>
              {items.map((item, index) => (
                <SortableItem
                  key={item.key}
                  parentKey={widget.key}
                  widget={item}
                  index={index}
                  selectedKey={selectedKey}
                  onSelect={onSelect}
                  openConfigDrawer={openConfigDrawer}
                  onWidgetDelete={onWidgetDelete}
                  {...restProps}
                />
              ))}
            </div>
            {/* </DroppableGroup> */}

            {isSelected && !isGhost && (
              <>
                <DragHandle className={styles.dragHandle} {...handleProps} />

                <SettingHandle
                  className={styles.configButton}
                  onClick={(e) => {
                    e.stopPropagation();
                    openConfigDrawer();
                  }}
                />

                <DeleteHandle
                  className={styles.deleteButton}
                  onClick={(e) => {
                    e.stopPropagation();
                    onWidgetDelete(widget.key);
                  }}
                />
              </>
            )}
          </div>
        )}
      </SortableContext>
    </div>
  );
}

export default GroupContainer;
