import styles from "./index.module.scss";

import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

import { <PERSON><PERSON>, Tooltip } from "antd";
import { DeleteOutlined, DragOutlined } from "@ant-design/icons";
import classNames from "classnames";
import FormFieldItem from "../FormFieldItem";
import ComponentContainer from "../ComponentContainer";
import { DroppableIds } from "../../../../enums";

function ComponentItem({
  component,
  isSelected,
  onSelect,
  onDelete,
  selectedComponentId,
  parentKey = DroppableIds.Canvas,
  index,
}) {
  const isGroup = component.isGroup ?? component.component_config?.isGroup ?? false;

  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: component.id,
    data: {
      id: component.id,
      parentKey,
      index,
      component,
      isGroup,
      isGhost: component?.type === "ghost",
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  function handleSelectComponent(e) {
    e.stopPropagation();
    onSelect(component);
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={classNames(styles.componentItem, {
        [styles.selected]: isSelected,
        [styles.dragging]: isDragging,
      })}
      onClick={handleSelectComponent}
      {...attributes}
    >
      <div className={styles.componentContent}>
        {isGroup ? (
          <ComponentContainer
            component={component}
            parentKey={component.id}
            index={index}
            isSelected={component.id === selectedComponentId}
            selectedComponentId={selectedComponentId}
            onSelect={onSelect}
            onDelete={onDelete}
          />
        ) : (
          <FormFieldItem
            component={component}
            isSelected={isSelected}
            onSelect={onSelect}
            onDelete={onDelete}
            selectedComponentId={selectedComponentId}
          />
        )}
      </div>

      <div className={styles.componentActions}>
        <Tooltip title="拖动排序">
          <Button type="text" size="small" icon={<DragOutlined />} className={styles.dragHandle} {...listeners} />
        </Tooltip>
        <Tooltip title="删除">
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              onDelete(component.id);
            }}
          />
        </Tooltip>
      </div>
    </div>
  );
}

export default ComponentItem;
