import { lazy, Suspense } from "react";
import { Spin } from "antd";
import Enums from "enums";

const SubscriptPhonePc = lazy(() => import("./template-preview-pc"));
const SubscriptPhoneMobile = lazy(() => import("./template-preview-mobile"));

const Components = {
  [Enums.Device.Pc]: SubscriptPhonePc,
  [Enums.Device.Mobile]: SubscriptPhoneMobile,
};

function TemplatePreview(props) {
  const { device } = props;
  const Component = Components[device];
  return (
    <Suspense
      fallback={
        <div>
          <Spin />
        </div>
      }
    >
      <Component {...props} />
    </Suspense>
  );
}

export default TemplatePreview;
