.richEditor {
  &.disabled {
    [class~="ck-editor"] {
      color: rgba(0, 0, 0, 0.25);
      background-color: #f5f5f5;
      border-color: #d9d9d9;
      box-shadow: none;
      cursor: not-allowed;
      opacity: 1;

      * {
        color: rgba(0, 0, 0, 0.25);
        background-color: #f5f5f5 !important;
        pointer-events: none !important;
      }
    }
  }

  [class~="ck-editor"] {
    [class~="ck-sticky-panel"] {
      [class~="ck-sticky-panel__content"] {
        position: static;
      }
    }

    [class~="ck-editor__main"] {
      > [class~="ck-editor__editable"] {
        min-height: var(--ckeditor-min-height);
        resize: vertical;

        &:not(.ck-focused) {
          position: relative;
          box-sizing: border-box;
          width: 100%;
          min-width: 0;
          margin: 0;
          padding: 4px 11px;
          color: rgba(0, 0, 0, 0.85);
          font-size: 14px;
          font-variant: tabular-nums;
          line-height: 1.5715;
          list-style: none;
          //background-color: #fff;
          background-image: none;
          border: 1px solid #d9d9d9;
          border-radius: 0 0 2px 2px;
          transition: border 0.3s, box-shadow 0.3s;
          font-feature-settings: "tnum", "tnum";
        }

        &:hover {
          border-color: var(--ant-primary-color-hover);
          //border-radius: 2px;
        }

        &[class~="ck-focused"] {
          &:not(.ck-editor__nested-editable) {
            border-color: var(--ant-primary-color-hover);
            border-right-width: 1px;
            //border-radius: 2px;
            outline: 0;
            box-shadow: 0 0 0 2px var(--ant-primary-color-outline);
          }
        }

        [class~="ck-placeholder"] {
          opacity: 0.5;
        }

        p {
          margin: 0;
        }
      }

      [class~="ck-source-editing-area"] {
        min-height: var(--ckeditor-min-height);
        overflow: visible;

        textarea {
          padding: 4px 11px;
          font-size: 14px;
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
            sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
          font-variant: tabular-nums;
          border: 1px solid #d9d9d9;
          transition: border 0.3s, box-shadow 0.3s;
          resize: vertical;

          &::placeholder {
            line-height: 1.5715;
            opacity: 0.5;
          }

          &:hover {
            border-color: var(--ant-primary-color-hover);
            //border-radius: 2px;
          }

          &:not([readonly]):focus {
            border-color: var(--ant-primary-color-hover);
            border-right-width: 1px;
            //border-radius: 2px;
            outline: 0;
            box-shadow: 0 0 0 2px var(--ant-primary-color-outline);
          }
        }
      }
    }
  }
}

body {
  [class~="ant-form-item-has-error"] {
    [class~="ck-editor"] {
      [class~="ck-editor__main"] {
        > [class~="ck-editor__editable"] {
          &:not(.ck-focused) {
            border-color: var(--ant-error-color);
          }

          &:hover {
            border-color: var(--ant-error-color-hover);
          }

          &[class~="ck-focused"] {
            &:not(.ck-editor__nested-editable) {
              border-color: var(--ant-error-color);
              box-shadow: 0 0 0 2px var(--ant-error-color-outline);
            }
          }
        }

        [class~="ck-source-editing-area"] {
          textarea {
            border-color: var(--ant-error-color);

            &:hover {
              border-color: var(--ant-error-color-hover);
            }

            &:not([readonly]):focus {
              border-color: var(--ant-error-color);
              box-shadow: 0 0 0 2px var(--ant-error-color-outline);
            }
          }
        }
      }
    }
  }
}
