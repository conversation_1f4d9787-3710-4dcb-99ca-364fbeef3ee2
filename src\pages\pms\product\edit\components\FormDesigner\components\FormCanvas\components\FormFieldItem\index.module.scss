.widgetFormItemWrapper {
  margin-bottom: 20px;

  .widgetFormItem {
    cursor: pointer;
    position: relative;
    background: #f9f9f9;
    border: 1px solid #d9d9d9;
    padding: 25px;

    &:not(:last-child) {
      margin-bottom: 20px;
    }

    &:hover {
      border: 1px dashed #1890ff;
    }

    &.selected {
      border: 2px solid #1890ff;
      background: #e6f7ff;
    }

    &.isOverlay {
      border: 2px solid rgb(24, 144, 255);
      border-bottom: none;
      background: linear-gradient(to bottom, rgba(230, 247, 255, 1), rgba(230, 247, 255, 0));
      opacity: 0.6;
    }

    &.ghost {
      padding: 0;
    }

    .dragHandle,
    .configButton,
    .deleteButton {
      position: absolute;
      z-index: 1;
      width: 28px;
      height: 28px;
      color: #fff;
      line-height: 28px;
      text-align: center;
      background: #409eff;
    }

    .dragHandle {
      top: 0;
      left: 0;
      cursor: move;
    }

    .configButton {
      top: 0;
      right: 0;
      cursor: pointer;
    }

    .deleteButton {
      bottom: 0;
      right: 0;
      cursor: pointer;
    }
  }
}
