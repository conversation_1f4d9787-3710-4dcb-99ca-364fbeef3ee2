import styles from "@/pages/pod/components/fabric-props-editor/index.module.scss";
import { Form, FormControl } from "@/components/react-form-x";
import classNames from "classnames";
import UpdateButton from "@/pages/pod/components/update-image-button";
import podStore from "@/pages/pod/stores";
import { FabricEvent, getFileNameFromUrl, LayerType } from "@/pages/pod/common";
import Helper from "@/helpers";
import { Button, ColorPicker, Input, InputNumber, Modal, Select, Tooltip } from "antd";
import ControlWrapper from "@/components/react-form-x/control-wrapper";
import { buttons, DefaultPlaceholderImage } from "@/pages/pod/components/fabric-props-editor/common";
import { CloseOutlined } from "@ant-design/icons";
import { FabricObjectType } from "@/pages/pod/common/init-fabric-types";
import SvgIcon from "@/components/common/SvgIcon";
import Utils from "@/utils";
import { useState } from "react";
import { toJS } from "mobx";
import LibrarySelect from "@/pages/pod/components/library-select";
import { observer } from "mobx-react-lite";
import PropTypes from "prop-types";

function FabricObjectTransform(props) {
  const { transformFormRef } = props;
  const canvas = toJS(podStore.canvas);
  const [librarySelectModalOpen, setLibrarySelectModalOpen] = useState(false);
  const layerType = podStore.activeObject.extraData?.layerType;
  const isImageLayer = [LayerType.ImagePlaceholder, LayerType.DynamicImage].includes(layerType);
  const isTextLayer = [LayerType.TextBox].includes(layerType);
  const hasOptionId = [LayerType.DynamicImage, LayerType.TextBox].includes(layerType);
  const fontFamilyOptions = podStore.fontFamilyOptions;
  const setFontFamilyOptions = (options) => {
    if (typeof options === "function") {
      const nextOptions = options(toJS(podStore.fontFamilyOptions));
      podStore.setFontFamilyOptions(nextOptions);
    } else {
      podStore.setFontFamilyOptions(options);
    }
  };

  function renderFormControl({ control: Control, name, label, controlProps, handleChange, addonBefore, addonAfter }) {
    return (
      <FormControl
        name={name}
        render={({ ref, ...renderProps }) => {
          return (
            <ControlWrapper
              {...renderProps}
              ref={ref}
              render={({ value, onChange }) => {
                return (
                  <div className={styles.controlWrapper}>
                    {label ? (
                      <div className={styles.label}>
                        <label>{label}</label>
                        <span>: </span>
                      </div>
                    ) : null}
                    {addonBefore}
                    <Control
                      {...controlProps}
                      {...renderProps}
                      style={{ flex: 1, ...controlProps.style }}
                      value={value}
                      onChange={(value) => {
                        handleChange({ value, onChange });
                      }}
                    ></Control>
                    {addonAfter}
                  </div>
                );
              }}
            ></ControlWrapper>
          );
        }}
      ></FormControl>
    );
  }

  function renderFormInput({ name, label, control = InputNumber, unit, precision = 2, min, max }) {
    return renderFormControl({
      control,
      name,
      label,
      controlProps: {
        addonAfter: unit,
        precision,
        min,
        max,
        controls: false,
        onKeyDown(event) {
          if (event.key.toLowerCase() === "enter") {
            handleObjectResize({ name, value: +event.target.value });
          }
        },
        onBlur(event) {
          handleObjectResize({ name, value: +event.target.value });
        },
      },
      handleChange({ value, onChange }) {
        // onChange(new Event("change"), value);
        const activeObject = canvas.getActiveObject();
        const prevValue = Utils.toNumber(activeObject[name]?.toFixed(2), 0);
        if (activeObject && value !== prevValue) {
          activeObject.set({ [name]: value });
          if (name === "zIndex") {
            activeObject.extraData.zIndex = value;
            activeObject.moveTo(value);
          } else if (["elementId", "optionId"].includes(name)) {
            activeObject.extraData[name] = value;
          }
          // podStore.setActiveObject({ ...activeObject });
          canvas.fire(FabricEvent.ObjectModified, { object: activeObject });
          canvas.renderAll();
        }
      },
    });
  }

  function renderFormColorPicker({ name, label, controlProps, addonBefore, addonAfter }) {
    return renderFormControl({
      control: ColorPicker,
      name,
      label,
      controlProps,
      handleChange({ value, onChange }) {
        // onChange(new Event("change"), value.toHexString());
        const activeObject = canvas.getActiveObject();
        if (activeObject) {
          activeObject.set({ [name]: value.toHexString() });
          canvas.renderAll();
          podStore.setActiveObject({ ...activeObject });
        }
      },
      addonBefore,
      addonAfter,
    });
  }

  function renderFormSelect({ name, label, controlProps, onChange }) {
    return renderFormControl({
      control: Select,
      name,
      label,
      controlProps,
      handleChange: ({ value }) => {
        // onChange(new Event("change"), value);
        const activeObject = canvas.getActiveObject();
        if (activeObject) {
          onChange?.({ value });
          activeObject.set({ [name]: value });
          canvas.renderAll();
          podStore.setActiveObject({ ...activeObject });
        }
      },
    });
  }

  function handleObjectResize({ name, value }) {
    const activeObject = canvas.getActiveObject();
    const zoom = canvas.getZoom();
    if (name === "scaledWidth") {
      activeObject.scaleToWidth(+value * zoom);
      activeObject.scaledWidth = +Number(activeObject.width * activeObject.scaleX).toFixed(2);
    } else if (name === "scaledHeight") {
      activeObject.scaleToHeight(+value * zoom);
      activeObject.scaledHeight = +Number(activeObject.height * activeObject.scaleY).toFixed(2);
    }
    canvas.fire(FabricEvent.ObjectModified, { object: activeObject });
    canvas.renderAll();
  }

  function setImageSrc({ object, src }) {
    return new Promise((resolve) => {
      object.setSrc(src, (updatedObject) => {
        resolve(updatedObject);
      });
    });
  }

  return (
    <>
      <div className={styles.transform}>
        <Form
          ref={transformFormRef}
          onSubmit={(event, values) => {
            canvas.discardActiveObject();
          }}
          updateMode="mutation"
        >
          <div className={classNames(styles.formRow, styles.privateProps)}>
            <div>
              {renderFormInput({
                name: "elementId",
                label: "Element ID",
                precision: 0,
                min: 0,
                max: 99,
              })}
            </div>
            {hasOptionId ? <div>{renderFormInput({ name: "optionId", label: "Option ID", precision: 0 })}</div> : null}
            {isImageLayer ? (
              <div>
                <UpdateButton
                  buttonProps={{ block: true, disabled: !!podStore.activeObject?.extraData?.library }}
                  onConfirm={async (value) => {
                    const activeObject = canvas.getActiveObject();
                    delete activeObject?.extraData?.library;
                    activeObject.extraData.upload = value;
                    await setImageSrc({ object: activeObject, src: value.prod_image });
                    canvas.fire(FabricEvent.ObjectModified, { target: activeObject });
                  }}
                  isMultiImageInput
                >
                  Update Image
                </UpdateButton>
              </div>
            ) : null}
            {isTextLayer ? (
              <>
                <div>
                  {renderFormSelect({
                    name: "fontFamily",
                    label: "Font Family",
                    controlProps: {
                      value: podStore.activeObject?.fontFamily,
                      options: fontFamilyOptions,
                    },
                    onChange({ value }) {
                      const option = fontFamilyOptions.find((item) => item.value === value);
                      if (option.url) {
                        Helper.loadFont(option.value, option.url);
                      }
                      const activeObject = canvas.getActiveObject();
                      if (activeObject) {
                        activeObject.extraData.font = { family: option.value, url: option.url };
                      }
                    },
                  })}
                </div>
                <div className={styles.uploadFont}>
                  <UpdateButton
                    onConfirm={async (url) => {
                      const fontFamily = getFileNameFromUrl(url);
                      await Helper.loadFont(fontFamily, url);
                      const activeObject = canvas.getActiveObject();
                      activeObject.set({ fontFamily });
                      activeObject.extraData.font = { family: fontFamily, url };
                      if (!fontFamilyOptions.some((item) => item.url === url)) {
                        setFontFamilyOptions((options) => [...options, { label: fontFamily, value: fontFamily, url }]);
                      }
                      canvas.fire(FabricEvent.ObjectModified, { object: activeObject });
                    }}
                  >
                    Update
                  </UpdateButton>
                  <Button>Pick Library</Button>
                </div>
                <div>
                  {renderFormInput({
                    name: "minFontSize",
                    label: "Min Font Size",
                    control: InputNumber,
                  })}
                </div>
                <div>
                  {renderFormInput({
                    name: "maxFontSize",
                    label: "Max Font Size",
                    control: InputNumber,
                  })}
                </div>
                <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                  {renderFormColorPicker({
                    name: "textColor",
                    label: "Color",
                    controlProps: { style: { flex: 0 } },
                    addonAfter: <Button style={{ flex: 1, minWidth: 0, padding: 0 }}>Pick Library</Button>,
                  })}
                </div>
                <div>
                  {renderFormColorPicker({
                    name: "textStroke",
                    label: "Stroke",
                    controlProps: {
                      showText: (color) => color.toRgbString(),
                      style: { justifyContent: "flex-start" },
                    },
                  })}
                </div>
                <div>
                  {renderFormInput({
                    name: "textStrokeWidth",
                    label: "Stroke Width",
                    control: InputNumber,
                  })}
                </div>
                <div className={styles.textBoxContent}>
                  <FormControl
                    name="text"
                    render={({ ref, ...controlProps }) => {
                      const defaultValue = podStore.activeObject?.text;
                      return (
                        <div className={styles.controlWrapper}>
                          <ControlWrapper
                            {...controlProps}
                            ref={ref}
                            defaultValue={defaultValue}
                            render={({ value, onChange }) => {
                              return (
                                <Input.TextArea
                                  {...controlProps}
                                  value={value}
                                  onChange={(event) => {
                                    const value = { text: event.target.value };
                                    onChange(event, value);
                                    const activeObject = canvas.getActiveObject();
                                    if (activeObject) {
                                      activeObject.set(value);
                                      canvas.renderAll();
                                      canvas.fire(FabricEvent.ObjectModified, {
                                        target: activeObject,
                                      });
                                    }
                                  }}
                                ></Input.TextArea>
                              );
                            }}
                          ></ControlWrapper>
                        </div>
                      );
                    }}
                  ></FormControl>
                </div>
              </>
            ) : null}
            {layerType === LayerType.DynamicImage ? (
              <div>
                {podStore.activeObject?.extraData?.library ? (
                  <Button
                    type="default"
                    block
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      gap: 6,
                    }}
                    onClick={() => {
                      const activeObject = canvas.getActiveObject();
                      delete activeObject?.extraData?.library;
                      activeObject.setSrc(DefaultPlaceholderImage[LayerType.DynamicImage], () => {
                        canvas.renderAll();
                      });
                      podStore.setActiveObject({ ...activeObject });
                    }}
                  >
                    <span>{podStore.activeObject?.extraData?.library?.name}</span>
                    <span>
                      <CloseOutlined></CloseOutlined>
                    </span>
                  </Button>
                ) : (
                  <Button
                    type="default"
                    block
                    data-for="dynamicImage"
                    onClick={() => {
                      setLibrarySelectModalOpen(true);
                    }}
                  >
                    Pick Library
                  </Button>
                )}
              </div>
            ) : null}
          </div>
          <div className={styles.transformProps}>
            <div>{renderFormInput({ name: "left", label: "X", unit: "px" })}</div>
            <div>{renderFormInput({ name: "top", label: "Y", unit: "px" })}</div>
            <div
              style={{
                visibility: podStore.activeObject?.type === FabricObjectType.ActiveSelection ? "hidden" : "visible",
              }}
            >
              {renderFormInput({ name: "zIndex", label: "Z", precision: 0 })}
            </div>
            <div>{renderFormInput({ name: "scaledWidth", label: "W", unit: "px" })}</div>
            <div>{renderFormInput({ name: "scaledHeight", label: "H", unit: "px" })}</div>
            <div>{renderFormInput({ name: "angle", label: "R", unit: "deg" })}</div>
          </div>
          <div className={styles.formRow}>
            <div>
              {renderFormInput({
                name: "skewX",
                label: "Skew X",
                unit: "deg",
                min: -80,
                max: 80,
              })}
            </div>
            <div>
              {renderFormInput({
                name: "skewY",
                label: "Skew Y",
                unit: "deg",
                min: -80,
                max: 80,
              })}
            </div>
          </div>
        </Form>
        <div className={styles.buttons}>
          {buttons.map((button, index) => {
            if (button.hide?.({ canvas })) return null;
            return (
              <div key={button.id}>
                <Tooltip title={button.title}>
                  <Button
                    className={styles.iconButton}
                    onClick={() => {
                      button.onClick({ canvas });
                    }}
                  >
                    <SvgIcon src={button.icon}></SvgIcon>
                  </Button>
                </Tooltip>
              </div>
            );
          })}
        </div>
      </div>
      <Modal
        title="图片素材库"
        open={librarySelectModalOpen}
        onCancel={() => {
          setLibrarySelectModalOpen(false);
        }}
        footer={null}
      >
        <LibrarySelect
          type="image"
          onSelect={({ item, index }) => {
            const activeObject = canvas.getActiveObject();
            if (activeObject?.extraData?.layerType === LayerType.DynamicImage) {
              delete activeObject?.extraData?.upload;
              activeObject.extraData.library = item;
              if (item.cover?.src) {
                activeObject.setSrc(item.cover.src, () => {
                  canvas.renderAll();
                });
              }
            }
            setLibrarySelectModalOpen(false);
            podStore.setActiveObject({ ...activeObject });
          }}
        ></LibrarySelect>
      </Modal>
    </>
  );
}

FabricObjectTransform = observer(FabricObjectTransform);

FabricObjectTransform.propTypes = {
  canvas: PropTypes.object,
  transformFormRef: PropTypes.object,
};

export default FabricObjectTransform;
