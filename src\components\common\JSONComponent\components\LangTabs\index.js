import styles from "./index.module.scss";
import { Tabs, Dropdown, Button } from "antd";
import { useCallback, useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { operations, items } from "./common";
import { DownOutlined } from "@ant-design/icons";

function LangTabs(props) {
  const { data } = props;
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const defaultActiveKey = searchParams.get("lang") || operations[0].key;
  const [activeKey, setActiveKey] = useState(defaultActiveKey);
  const [operationsSelection, setOperationsSelection] = useState([operations[0]]);

  const updateSearchParams = useCallback(
    ({ value }) => {
      const searchParams = new URLSearchParams(location.search);
      searchParams.set("lang", value);
      navigate({ pathname: location.pathname, search: decodeURIComponent(searchParams.toString()) });
    },
    [location, navigate]
  );

  const onTabsChange = useCallback(
    (value) => {
      setActiveKey(value);
      updateSearchParams({ value });
    },
    [updateSearchParams]
  );

  const handleMenuClick = useCallback(
    ({ key }) => {
      const selectedOperation = operations.find((item) => item.key === key);
      setOperationsSelection([selectedOperation]);
      setActiveKey(key);
      updateSearchParams({ value: key });
    },
    [updateSearchParams]
  );

  const OperationsSlot = {
    left: (
      <Dropdown
        menu={{
          items: operations,
          selectable: true,
          defaultSelectedKeys: [activeKey],
          onClick: handleMenuClick,
        }}
      >
        <Button style={{ height: 40 }}>
          <DownOutlined />
        </Button>
      </Dropdown>
    ),
  };

  useEffect(() => {
    const activeOption = operations.filter((item) => item.key === defaultActiveKey);
    if (activeOption.length > 0) {
      setOperationsSelection(activeOption);
    }
  }, [defaultActiveKey]);

  return (
    <Tabs
      size="small"
      type="card"
      {...data?.props}
      className={styles.langTabs}
      activeKey={activeKey}
      onChange={onTabsChange}
      tabBarExtraContent={OperationsSlot}
      items={[...operationsSelection, ...items]}
    />
  );
}

export default LangTabs;
