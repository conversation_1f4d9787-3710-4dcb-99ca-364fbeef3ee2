import styles from "./index.module.scss";
import { Tabs, Dropdown, Button } from "antd";
import { useCallback, useEffect, useState, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { DownOutlined } from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import { reaction } from "mobx";
import { extraItems as commonExtraItems, items as commonItems } from "./common";
import store from "stores";

const DEFAULT_LANG = "en";

function LangTabs(props) {
  const { data } = props;
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const [extraItems, setExtraItems] = useState(tabItemsTransform(commonExtraItems));
  const [items, setItems] = useState(tabItemsTransform(commonItems));
  const defaultActiveKey = searchParams.get("lang") || DEFAULT_LANG;
  const [activeKey, setActiveKey] = useState(defaultActiveKey);
  const [operationsSelection, setOperationsSelection] = useState([DEFAULT_LANG]);
  const paramsRef = useRef({});

  paramsRef.current = {
    tabItemsTransform,
  };

  const updateSearchParams = useCallback(
    ({ value }) => {
      const searchParams = new URLSearchParams(location.search);
      searchParams.set("lang", value);
      navigate({ pathname: location.pathname, search: decodeURIComponent(searchParams.toString()) });
    },
    [location, navigate]
  );

  const onTabsChange = useCallback(
    (value) => {
      setActiveKey(value);
      updateSearchParams({ value });
    },
    [updateSearchParams]
  );

  const handleMenuClick = useCallback(
    ({ key }) => {
      const selectedOperation = extraItems.find((item) => item.key === key);
      setOperationsSelection([selectedOperation]);
      setActiveKey(key);
      updateSearchParams({ value: key });
    },
    [updateSearchParams, extraItems]
  );

  function renderTab({ label, icon }) {
    return (
      <div className="lang-tabs-wrapper">
        <span
          className="lang-tabs-flag-icon"
          style={{
            backgroundImage: `url(${icon})`,
          }}
        ></span>
        <span>{label}</span>
      </div>
    );
  }

  const OperationsSlot = {
    left: (
      <Dropdown
        menu={{
          items: extraItems,
          selectable: true,
          defaultSelectedKeys: [activeKey],
          onClick: handleMenuClick,
        }}
      >
        <Button style={{ height: 40 }}>
          <DownOutlined />
        </Button>
      </Dropdown>
    ),
  };

  function tabItemsTransform(items) {
    return items.map((item) => ({
      ...item,
      icon: null,
      label: renderTab({ label: item?.label, icon: item?.icon }),
    }));
  }

  useEffect(() => {
    const disposer = reaction(
      () => store.systemInfo,
      (systemInfo) => {
        if (systemInfo?.lang_tabs) {
          const { tabItemsTransform } = paramsRef.current;
          const extraItems = store.systemInfo.lang_tabs?.extraItems || [];
          const items = store.systemInfo.lang_tabs?.items || [];
          const processedOperations = tabItemsTransform(extraItems);
          const processedItems = tabItemsTransform(items);

          // 激活默认选项
          const activeOption = processedOperations.filter((item) => item.key === defaultActiveKey);

          if (activeOption.length > 0) {
            setOperationsSelection(activeOption);
          }

          // 处理选项
          setExtraItems(processedOperations);
          setItems(processedItems);
        }
      },
      { fireImmediately: true }
    );

    return () => disposer();
  }, [defaultActiveKey]);

  return (
    <Tabs
      size="small"
      type="card"
      {...data?.props}
      className={styles.langTabs}
      activeKey={activeKey}
      onChange={onTabsChange}
      tabBarExtraContent={OperationsSlot}
      items={[...operationsSelection, ...items]}
    />
  );
}

LangTabs = observer(LangTabs);

export default LangTabs;
