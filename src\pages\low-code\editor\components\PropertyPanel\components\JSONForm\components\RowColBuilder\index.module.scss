.rowColBuilder {
  width: 100%;

  .previewCard {
    margin-bottom: 16px;
  }

  .previewRow {
    min-height: 60px;
  }

  .previewCol {
    position: relative;

    .colContent {
      background: #f0f0f0;
      padding: 8px;
      text-align: center;
      border: 1px dashed #d9d9d9;
      border-radius: 2px;
      height: 100%;
      min-height: 60px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;

      .childrenCount {
        margin-top: 4px;
        font-size: 12px;
        color: #666;
      }

      .actionButtons {
        position: absolute;
        top: 4px;
        right: 4px;
        display: flex;

        .editBtn {
          padding: 0 4px;
          margin-right: 4px;
        }

        .removeBtn {
          padding: 0 4px;
        }
      }
    }
  }

  .columnsConfig {
    margin-bottom: 16px;

    .columnCard {
      margin-bottom: 8px;
    }
  }
}
