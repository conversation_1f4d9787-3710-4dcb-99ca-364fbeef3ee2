const { runCommand, getCommitHash, createZipFile } = require("./common");
const fs = require("node:fs");

async function main() {
  const version = await getCommitHash();
  await fs.promises.writeFile(`./public/app.json`, JSON.stringify({ version }), { encoding: "utf8" });
  await runCommand(`npm i`);
  await runCommand(`cross-env REACT_APP_RUNTIME_ENV=test REACT_APP_VERSION=${version} npm run build`);
  await createZipFile({ dir: "./", items: ["build", "public/app.json"], filename: `${version}.zip`, level: 9 });
}

main().catch(console.error);
