import styles from "./index.module.scss";
import { Carousel, Image } from "antd";

function Gallery(props) {
  const { data } = props;
  const { items, otherProps } = data;

  return (
    <div className={styles.container}>
      <Carousel arrows infinite={false} {...otherProps}>
        {items?.map((item, index) => (
          <div className={styles.imageWrapper} style={item?.style} key={index}>
            <Image height="100%" {...item?.image} key={index} preview={false} />
          </div>
        ))}
      </Carousel>
    </div>
  );
}

export default Gallery;
