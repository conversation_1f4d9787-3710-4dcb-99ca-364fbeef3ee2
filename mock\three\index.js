const common = require("../common");

module.exports = {
  "GET /rest/v1/texture/draft/data": async (req, res) => {
    res.status(200).json({
      success: true,
      // data: {
      //   width: 800,
      //   height: 800,
      //   background_color: `#ffffff`,
      //   model_url: `https://static.bizseas.com/3d/model/baseball_jersey_1.glb`,
      //   map_url: `https://static.bizseas.com/3d/model/compressed/baseball_jersey_color.png`,
      //   normal_map_url: `https://static.bizseas.com/3d/model/compressed/baseball_jersey_normal.png`,
      //   ao_map_url: `https://static.bizseas.com/3d/model/compressed/baseball_jersey_ambient_occlusion_1.png`,
      //   mesh_name: `mesh_out`,
      //   photo_params: [{ rotation: { y: 0 } }, { rotation: { y: -45 } }, { rotation: { y: 180 } }],
      // },
      data: {
        model_file: `https://static.bizseas.com/3d/model/baseball_jersey_1.glb`,
        cut_parts: [
          {
            name: "mesh_out",
            _table_key: 1,
            schematic_plot: "https://static.bizseas.com/test/pms/c/2/684f83710762c.png",
            texture_map: `https://static.bizseas.com/3d/model/compressed/baseball_jersey_color.png`,
            normal_map: `https://static.bizseas.com/3d/model/compressed/baseball_jersey_normal.png`,
            ao_map: `https://static.bizseas.com/3d/model/compressed/baseball_jersey_ambient_occlusion_1.png`,
          },
        ],
        coordinate_data: [],
      },
    });
  },

  "GET /rest/v1/texture/template/:id": async (req, res) => {
    res.status(200).json({
      success: true,
      data: {
        id: 1,
        name: "棒球衫-模板-1",
        data: {},
      },
    });
  },

  "POST /rest/v1/texture/template": async (req, res) => {
    await common.sleep(500);
    res.status(200).json({ success: true, data: {} });
  },

  "GET /rest/v1/texture/type/:id": async (req, res) => {
    res.status(200).json({
      success: true,
      data: {
        id: 1,
        model_file: `https://static.bizseas.com/3d/model/baseball_jersey_1.glb`,
        cut_parts: [
          {
            name: "mesh_out",
            _table_key: 1,
            schematic_plot: "https://static.bizseas.com/test/pms/c/2/684f83710762c.png",
            texture_map: `https://static.bizseas.com/3d/model/compressed/baseball_jersey_color.png`,
            normal_map: `https://static.bizseas.com/3d/model/compressed/baseball_jersey_normal.png`,
            ao_map: `https://static.bizseas.com/3d/model/compressed/baseball_jersey_ambient_occlusion_1.png`,
          },
        ],
        coordinate_data: {
          groups: [
            {
              name: "正面",
              scene: {
                background: "#ffffff",
              },
              camera: {
                fov: 60,
                aspect: 1,
                near: 0.1,
                far: 1000,
                position: {
                  x: 1.5920408388915593e-16,
                  y: 7.960204194457797e-17,
                  z: -1.3,
                },
              },
              model: {
                uuid: "606be3a8-06d7-4385-842f-84efe336f51b",
                name: "model",
                position: {
                  x: 0,
                  y: 0,
                  z: 0,
                },
                rotation: {
                  x: 0,
                  y: 3.141592653589793,
                  z: 0,
                },
                scale: 0.012527115885840236,
                userData: {
                  type: "model",
                  cloned: false,
                },
              },
              clonedModels: [],
              ambientLight: {
                color: "#ffffff",
                intensity: 1,
              },
              directionalLight: {
                color: "#ffffff",
                intensity: 2.6,
                position: {
                  x: 0,
                  y: 0,
                  z: -1.3,
                },
              },
            },
            {
              name: "背面",
              scene: {
                background: "#ffffff",
              },
              camera: {
                fov: 60,
                aspect: 1,
                near: 0.1,
                far: 1000,
                position: {
                  x: 1.5920408388915593e-16,
                  y: 7.960204194457797e-17,
                  z: -1.3,
                },
              },
              model: {
                uuid: "606be3a8-06d7-4385-842f-84efe336f51b",
                name: "model",
                position: {
                  x: 0,
                  y: 0,
                  z: 0,
                },
                rotation: {
                  x: 0,
                  y: -0.015658503988659,
                  z: 0,
                },
                scale: 0.012527115885840236,
                userData: {
                  type: "model",
                  cloned: false,
                },
              },
              clonedModels: [],
              ambientLight: {
                color: "#ffffff",
                intensity: 1,
              },
              directionalLight: {
                color: "#ffffff",
                intensity: 2.6,
                position: {
                  x: 0,
                  y: 0,
                  z: -1.3,
                },
              },
            },
            {
              name: "侧面",
              scene: {
                background: "#ffffff",
              },
              camera: {
                fov: 60,
                aspect: 1,
                near: 0.1,
                far: 1000,
                position: {
                  x: 1.5920408388915593e-16,
                  y: 7.960204194457797e-17,
                  z: -1.3,
                },
              },
              model: {
                uuid: "606be3a8-06d7-4385-842f-84efe336f51b",
                name: "model",
                position: {
                  x: 0,
                  y: 0,
                  z: 0,
                },
                rotation: {
                  x: 0,
                  y: 2.67634149601134,
                  z: 0,
                },
                scale: 0.012527115885840236,
                userData: {
                  type: "model",
                  cloned: false,
                },
              },
              clonedModels: [],
              ambientLight: {
                color: "#ffffff",
                intensity: 1,
              },
              directionalLight: {
                color: "#ffffff",
                intensity: 2.6,
                position: {
                  x: 0,
                  y: 0,
                  z: -1.3,
                },
              },
            },
            {
              name: "正反",
              scene: {
                background: "#ffffff",
              },
              camera: {
                fov: 60,
                aspect: 1,
                near: 0.1,
                far: 1000,
                position: {
                  x: -0.15715355855409155,
                  y: -0.01211872788196804,
                  z: -1.290409196909383,
                },
              },
              model: {
                uuid: "606be3a8-06d7-4385-842f-84efe336f51b",
                name: "model",
                position: {
                  x: -0.30620462233289425,
                  y: -0.16351764562674667,
                  z: -0.0033861243592887558,
                },
                rotation: {
                  x: 3.1202454689678114,
                  y: -0.11867348302613309,
                  z: 3.131946477843402,
                },
                scale: 0.012527115885840236,
                userData: {
                  type: "model",
                  cloned: false,
                },
              },
              clonedModels: [
                {
                  uuid: "5701e927-19f9-41e5-94a3-1f68b936b6d0",
                  name: "clonedModel1",
                  position: {
                    x: 0.4204428521533907,
                    y: 0.2912656843704252,
                    z: 0.31798801903133284,
                  },
                  rotation: {
                    x: 0.05814231688991492,
                    y: 0.19241106040248218,
                    z: -0.014996706067872964,
                  },
                  scale: 0.0148,
                  userData: {
                    type: "model",
                    cloned: true,
                  },
                },
              ],
              ambientLight: {
                color: "#ffffff",
                intensity: 1,
              },
              directionalLight: {
                color: "#ffffff",
                intensity: 2.6,
                position: {
                  x: 0,
                  y: 0,
                  z: -1.3,
                },
              },
            },
          ],
        },
      },
    });
  },

  "PATCH /rest/v1/texture/type/:id": async (req, res) => {
    res.status(200).json({ success: true, data: req.body });
  },

  "GET /rest/v1/texture/draft/:data_id": async (req, res) => {
    res.status(200).json({
      success: true,
      data: {
        id: 1,
        list_url: `/list/order-list`,
        last_step_url: `/3d/editor?id=1`,
        next_step_url: `/3d/editor?id=1`,
        model_file: `https://static.bizseas.com/3d/model/baseball_jersey_1.glb`,
        cut_parts: [
          {
            name: "mesh_out",
            _table_key: 1,
            schematic_plot: "https://static.bizseas.com/test/pms/c/2/684f83710762c.png",
            texture_map: `https://static.bizseas.com/3d/model/compressed/baseball_jersey_color.png`,
            normal_map: `https://static.bizseas.com/3d/model/compressed/baseball_jersey_normal.png`,
            ao_map: `https://static.bizseas.com/3d/model/compressed/baseball_jersey_ambient_occlusion_1.png`,
          },
        ],
        coordinate_data: {
          groups: [
            {
              id: 1,
              name: "正面",
              scene: {
                background: "#ffffff",
              },
              camera: {
                fov: 60,
                aspect: 1,
                near: 0.1,
                far: 1000,
                position: {
                  x: 1.5920408388915593e-16,
                  y: 7.960204194457797e-17,
                  z: -1.3,
                },
              },
              model: {
                uuid: "606be3a8-06d7-4385-842f-84efe336f51b",
                name: "model",
                position: {
                  x: 0,
                  y: 0,
                  z: 0,
                },
                rotation: {
                  x: 0,
                  y: 3.141592653589793,
                  z: 0,
                },
                scale: 0.012527115885840236,
                userData: {
                  type: "model",
                  cloned: false,
                },
              },
              clonedModels: [],
              ambientLight: {
                color: "#ffffff",
                intensity: 1,
              },
              directionalLight: {
                color: "#ffffff",
                intensity: 2.6,
                position: {
                  x: 0,
                  y: 0,
                  z: -1.3,
                },
              },
            },
            {
              id: 2,
              name: "背面",
              scene: {
                background: "#ffffff",
              },
              camera: {
                fov: 60,
                aspect: 1,
                near: 0.1,
                far: 1000,
                position: {
                  x: 1.5920408388915593e-16,
                  y: 7.960204194457797e-17,
                  z: -1.3,
                },
              },
              model: {
                uuid: "606be3a8-06d7-4385-842f-84efe336f51b",
                name: "model",
                position: {
                  x: 0,
                  y: 0,
                  z: 0,
                },
                rotation: {
                  x: 0,
                  y: -0.015658503988659,
                  z: 0,
                },
                scale: 0.012527115885840236,
                userData: {
                  type: "model",
                  cloned: false,
                },
              },
              clonedModels: [],
              ambientLight: {
                color: "#ffffff",
                intensity: 1,
              },
              directionalLight: {
                color: "#ffffff",
                intensity: 2.6,
                position: {
                  x: 0,
                  y: 0,
                  z: -1.3,
                },
              },
            },
            {
              id: 3,
              name: "侧面",
              scene: {
                background: "#ffffff",
              },
              camera: {
                fov: 60,
                aspect: 1,
                near: 0.1,
                far: 1000,
                position: {
                  x: 1.5920408388915593e-16,
                  y: 7.960204194457797e-17,
                  z: -1.3,
                },
              },
              model: {
                uuid: "606be3a8-06d7-4385-842f-84efe336f51b",
                name: "model",
                position: {
                  x: 0,
                  y: 0,
                  z: 0,
                },
                rotation: {
                  x: 0,
                  y: 2.67634149601134,
                  z: 0,
                },
                scale: 0.012527115885840236,
                userData: {
                  type: "model",
                  cloned: false,
                },
              },
              clonedModels: [],
              ambientLight: {
                color: "#ffffff",
                intensity: 1,
              },
              directionalLight: {
                color: "#ffffff",
                intensity: 2.6,
                position: {
                  x: 0,
                  y: 0,
                  z: -1.3,
                },
              },
            },
            {
              id: 4,
              name: "正反",
              scene: {
                background: "#ffffff",
              },
              camera: {
                fov: 60,
                aspect: 1,
                near: 0.1,
                far: 1000,
                position: {
                  x: -0.15715355855409155,
                  y: -0.01211872788196804,
                  z: -1.290409196909383,
                },
              },
              model: {
                uuid: "606be3a8-06d7-4385-842f-84efe336f51b",
                name: "model",
                position: {
                  x: -0.30620462233289425,
                  y: -0.16351764562674667,
                  z: -0.0033861243592887558,
                },
                rotation: {
                  x: 3.1202454689678114,
                  y: -0.11867348302613309,
                  z: 3.131946477843402,
                },
                scale: 0.012527115885840236,
                userData: {
                  type: "model",
                  cloned: false,
                },
              },
              clonedModels: [
                {
                  uuid: "5701e927-19f9-41e5-94a3-1f68b936b6d0",
                  name: "clonedModel1",
                  position: {
                    x: 0.4204428521533907,
                    y: 0.2912656843704252,
                    z: 0.31798801903133284,
                  },
                  rotation: {
                    x: 0.05814231688991492,
                    y: 0.19241106040248218,
                    z: -0.014996706067872964,
                  },
                  scale: 0.0148,
                  userData: {
                    type: "model",
                    cloned: true,
                  },
                },
              ],
              ambientLight: {
                color: "#ffffff",
                intensity: 1,
              },
              directionalLight: {
                color: "#ffffff",
                intensity: 2.6,
                position: {
                  x: 0,
                  y: 0,
                  z: -1.3,
                },
              },
            },
          ],
        },
      },
    });
  },

  "POST /rest/v1/texture/texture": async (req, res) => {
    await common.sleep(500);
    res.status(200).json({ success: true, data: {} });
  },
};
