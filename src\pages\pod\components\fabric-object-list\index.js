import { FabricObjectType } from "@/pages/pod/common/init-fabric-types";
import classNames from "classnames";
import styles from "./index.module.scss";
import { setActiveObjects } from "@/pages/pod/components/fabric-props-editor";
import { View, Views } from "@/components/views";
import { EyeInvisibleOutlined, EyeOutlined, LockOutlined, UnlockOutlined } from "@ant-design/icons";
import PropTypes from "prop-types";
import I18nEditable from "@/pages/pod/components/i18n-editable";
import { observer } from "mobx-react-lite";
import podStore from "@/pages/pod/stores";
import { FabricEvent } from "@/pages/pod/common";

const setFieldWithI18n = ({ object, key, option }) => {
  const paramsKey = `${key}_i18n_params`;
  if (!object[paramsKey]) {
    const matchedIndex = object[key].match(/\d+/)?.[0];
    if (matchedIndex) {
      object[paramsKey] = { index: +matchedIndex };
    }
  }
  object[key] = option.label.replace("{{index}}", object[paramsKey]?.index);
  object[`${key}_i18n_key`] = option.value;
};

function FabricObjectList(props) {
  const { canvas, layouts, className } = props;

  return (
    <div className={classNames(styles.layers, "fabric-object-list", className)}>
      {layouts.map((item, index) => {
        if (item.type === "layout") {
          const selected = item.children.every((x) => canvas.getActiveObjects().some((y) => x.id === y.extraData.id));
          return (
            <div key={item.id} className={classNames(styles.groupItems, { [styles.selected]: selected })}>
              <div
                className={styles.groupName}
                onClick={() => {
                  setActiveObjects({
                    canvas,
                    objects: item.children.map((x) => canvas.getObjects().find((y) => x.id === y.extraData.id)),
                  });
                  canvas.fire(FabricEvent.SelectChange, { target: canvas.getActiveObject() });
                }}
              >
                <I18nEditable
                  onSave={({ option, childrenRef }) => {
                    podStore.objects.forEach((object) => {
                      if (object.extraData.layout?.id === item.id) {
                        setFieldWithI18n({ object: object.extraData.layout, key: "name", option });
                        childrenRef.current.textContent = object.extraData.layout.name;
                      }
                    });
                  }}
                >
                  {item.name}
                </I18nEditable>
              </div>
              <FabricObjectList canvas={canvas} layouts={item.children}></FabricObjectList>
            </div>
          );
        }
        const object = podStore.objects.find((x) => x.extraData.id === item.id);
        if (!object) return null;
        const activeObject = canvas.getActiveObject();
        const currentId = object.extraData?.id;
        const selected =
          !object.extraData?.lock && activeObject?.type === FabricObjectType.ActiveSelection
            ? activeObject?.getObjects().some((a) => a.extraData.id === currentId)
            : activeObject?.extraData?.id === currentId;
        return (
          <div
            key={currentId}
            className={classNames(styles.item, { [styles.selected]: selected, [styles.lock]: !object.selectable })}
            onClick={(event) => {
              if (object.selectable) {
                if (event.ctrlKey) {
                  let activeObjects = canvas.getActiveObjects() || [];
                  const selected = activeObjects.some((a) => a.extraData.id === currentId);
                  if (selected) {
                    activeObjects = activeObjects.filter((a) => a.extraData.id !== currentId);
                  } else {
                    const object = canvas.getObjects().find((a) => a.extraData.id === currentId);
                    activeObjects.push(object);
                  }
                  if (activeObjects.length > 1) {
                    setActiveObjects({ canvas, objects: activeObjects });
                  } else if (activeObjects.length > 0) {
                    canvas.setActiveObject(activeObjects[0]);
                  } else {
                    canvas.discardActiveObject();
                  }
                  canvas.fire(FabricEvent.SelectChange, { target: canvas.getActiveObject() });
                } else {
                  const activeObject = canvas.getActiveObject();
                  if (activeObject?.extraData?.id !== currentId) {
                    const nextActiveObject = canvas.getObjects().find((a) => a.extraData.id === currentId);
                    canvas.setActiveObject(nextActiveObject);
                    canvas.fire(FabricEvent.SelectChange, { target: canvas.getActiveObject() });
                  }
                }
              }
            }}
          >
            <div
              className={styles.layerActions}
              onClick={(event) => {
                event.stopPropagation();
              }}
            >
              <div
                onClick={() => {
                  object.set({ visible: !object.visible });
                  canvas.renderAll();
                  canvas.fire(FabricEvent.ObjectModified, { target: object });
                }}
              >
                <Views active={object?.visible ? "visible" : "invisible"}>
                  <View name="visible">
                    <EyeOutlined className={styles.icon} style={{ color: "#aaa" }} />
                  </View>
                  <View name="invisible">
                    <EyeInvisibleOutlined className={styles.icon} style={{ color: "#333" }} />
                  </View>
                </Views>
              </div>
              <div
                onClick={() => {
                  object.set({ selectable: !object.selectable });
                  canvas.renderAll();
                  canvas.fire(FabricEvent.ObjectModified, { target: object });
                }}
              >
                <Views active={!object.selectable ? "lock" : "unlock"}>
                  <View name="lock">
                    <LockOutlined className={styles.icon} style={{ color: "#333" }} />
                  </View>
                  <View name="unlock">
                    <UnlockOutlined className={styles.icon} style={{ color: "#aaa" }} />
                  </View>
                </Views>
              </div>
            </div>
            <div>
              <I18nEditable
                onSave={({ option, childrenRef }) => {
                  setFieldWithI18n({ object: object.extraData, key: "name", option });
                  childrenRef.current.textContent = object.extraData.name;
                }}
              >
                {object.extraData.name}
              </I18nEditable>
            </div>
            <div>ID {object.extraData.elementId}</div>
          </div>
        );
      })}
    </div>
  );
}

FabricObjectList = observer(FabricObjectList);

FabricObjectList.propTypes = {
  canvas: PropTypes.object,
  layouts: PropTypes.array,
  className: PropTypes.string,
};

export default FabricObjectList;
