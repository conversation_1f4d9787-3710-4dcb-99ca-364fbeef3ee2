import { Button as AntdButton } from "antd";
import { useEffect, useState } from "react";
import Enums from "enums";
import Utils from "utils";

function Button(props) {
  const { children, onClick, command } = props;
  const [loading, setLoading] = useState(false);

  async function handleClick(e) {
    setLoading(true);
    if (command?.type === Enums.CommandType.Submit) {
      onClick(e);
    } else {
      await onClick(e).finally(() => {
        setLoading(false);
      });
    }
  }

  useEffect(() => {
    function handleSetLoading({ loading }) {
      setLoading(loading);
    }
    if (command && command.type) {
      Utils.addEventListener(Enums.EventName.SetSubmitButtonLoading, handleSetLoading);
    }
    return function () {
      Utils.removeEventListener(Enums.EventName.SetSubmitButtonLoading, handleSetLoading);
    };
  }, [command]);

  return (
    <AntdButton {...props} loading={loading} onClick={handleClick}>
      {children}
    </AntdButton>
  );
}

export default Button;
