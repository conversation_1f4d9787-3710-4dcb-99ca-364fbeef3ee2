import { useState } from "react";
import { Button, Dropdown, Upload } from "antd";
import { CaretDownOutlined } from "@ant-design/icons";
import Helper from "helpers";
import styles from "./index.module.scss";
import Icon from "@/components/common/Icon";
import classNames from "classnames";

function HeaderAction({ actions, selectedRowKeys }) {
  const [buttonsLoading, setButtonsLoading] = useState({});

  async function handleCommand({ command, ...others }) {
    Helper.commandHandler({ command, ...others });
  }

  function handleBatchActionClick({ command, buttonKey }) {
    const hasLoading = ["request", "download"].includes(command?.type);
    hasLoading && setButtonsLoading((data) => ({ ...data, [buttonKey]: true }));
    handleCommand({ command, ids: selectedRowKeys }).finally(() => {
      hasLoading && setButtonsLoading((data) => ({ ...data, [buttonKey]: false }));
    });
  }

  return (
    <div className={classNames("page-header-actions", styles.container)}>
      {actions?.map((item, index) => {
        const buttonKey = `batchAction${index}`;
        const isDropdown = item.component === "Dropdown";
        const isUpload = item.component === "Upload";
        const isDisabled = item.enableByRowSelection && selectedRowKeys?.length === 0;

        const button = (
          <Button
            type="primary"
            {...item.props}
            key={buttonKey}
            disabled={isDisabled}
            loading={buttonsLoading[buttonKey]}
            onClick={() => {
              if (!isDropdown && !isUpload) handleBatchActionClick({ command: item.command, buttonKey });
            }}
            style={{ display: "flex", alignItems: "center", gap: 2 }}
          >
            <Icon src={item.icon} style={{ width: 16, height: 16 }} />
            {item.title}
            {isDropdown ? <CaretDownOutlined className={styles.caretDown} /> : null}
          </Button>
        );
        if (isDropdown) {
          return (
            <Dropdown
              key={index}
              trigger={["click"]}
              disabled={isDisabled}
              {...item.dropdownProps}
              menu={{
                ...item.dropdownProps.menu,
                onClick: ({ key }) => {
                  const menuItem = item.dropdownProps.menu.items.find((a) => a.key === key);
                  handleBatchActionClick({ command: menuItem.command, buttonKey });
                },
              }}
            >
              {button}
            </Dropdown>
          );
        } else if (isUpload) {
          const headers = Helper.getCommonHeaders();

          return (
            <Upload
              key={index}
              disabled={isDisabled}
              {...item.uploadProps}
              headers={headers}
              showUploadList={false}
              onChange={({ file }) => {
                if (file.status === "uploading") {
                  setButtonsLoading((data) => ({ ...data, [buttonKey]: true }));
                }
                if (["done", "error"].includes(file.status) || file.response) {
                  setButtonsLoading((data) => ({ ...data, [buttonKey]: false }));
                  if (file.status === "done") {
                    if (file.response.message) {
                      Helper.openMessage({ type: "success", content: file.response.message });
                    }
                  } else {
                    if (!file.response?.command) {
                      Helper.openMessage({ type: "error", content: "上传失败！" });
                    }
                  }
                  return handleCommand({ command: file.response?.command });
                }
              }}
            >
              {button}
            </Upload>
          );
        } else {
          return button;
        }
      })}
    </div>
  );
}

export default HeaderAction;
