import { Image, Upload } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import { closestCenter, DndContext, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { arrayMove, SortableContext } from "@dnd-kit/sortable";
import classNames from "classnames";
import axios from "fetchers/request";
import Helper from "helpers";
import Utils from "utils";
import Enums from "enums";
import styles from "./index.module.scss";
import SortableItem from "components/common/SortableItem";
import UploadItem from "./components/UploadItem";
import { useI18n } from "context/I18nContext";

function ImageUpload(props) {
  const {
    fileList = [],
    onChange,
    maxCount = 100,
    setFieldValue,
    isShowFileInfo,
    renderUploadItem,
    className,
    beforeUpload,
  } = props;
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);
  const [items, setItems] = useState([]);
  const fileListStr = Utils.JSON.stringify(fileList, "[]");
  const sensors = useSensors(useSensor(PointerSensor));
  const headers = Helper.getCommonHeaders();
  const { t } = useI18n();

  async function handlePreview(file) {
    if (!file.url && !file.preview) {
      file.preview = await Utils.readAsDataUrl(file.originFileObj);
    }
    setPreviewOpen(true);
    setPreviewImage(file.url || file.preview);
  }

  function handleRemove(file) {
    const { fid, url } = file;
    return new Promise((resolve) => {
      Helper.modal.confirm({
        title: t({ key: "AreYouSureYouWantToDelete", defaultKey: "确定删除吗？" }),
        onOk: async () => {
          const result = await axios({
            url: props.action,
            method: "DELETE",
            data: { ...props.data, fid, url },
          }).then((res) => res.data);
          if (result.success) {
            resolve(true);
          }
        },
      });
    });
  }

  function handleDragEnd(event) {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = arrayMove(items, oldIndex, newIndex);
        const newFileList = arrayMove(fileList, oldIndex, newIndex);
        setItems(newItems);
        setFieldValue(newFileList);
        return newItems;
      });
    }
  }

  useEffect(() => {
    const fileList = JSON.parse(fileListStr);
    setItems(fileList.map((file) => file.uid));
  }, [fileListStr]);

  return (
    <div className={classNames(styles.imageUpload, className)}>
      <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <SortableContext items={items}>
          <Upload
            {...props}
            headers={headers}
            beforeUpload={beforeUpload}
            onChange={({ file, fileList }) => {
              if (file.status === Enums.UploadFileStatus.Done && file.response.success) {
                const src = file.response?.data?.host + file.response?.data?.image?.src;
                const newFile = { fid: file.fid, url: src, status: file.status, ...file.response?.data };
                // 找到当前文件的位置并替换
                const index = fileList.findIndex((f) => f.uid === file.uid);
                if (index !== -1) {
                  fileList[index] = newFile; // 替换原有文件
                }
              }

              if (file.status === Enums.UploadFileStatus.Error) {
                Helper.openMessage({
                  type: "error",
                  content:
                    typeof file.response === "string" ? file.response : file.response?.message || file?.error?.message,
                });
                fileList = fileList.filter((file) => file.status !== Enums.UploadFileStatus.Error);
              }

              onChange?.(fileList);
            }}
            onPreview={handlePreview}
            onRemove={handleRemove}
            itemRender={(item, file, fileList, actions) => {
              return (
                <SortableItem id={file.uid}>
                  {renderUploadItem ? (
                    renderUploadItem({ item, file, fileList, actions, isShowFileInfo })
                  ) : (
                    <UploadItem item={item} file={file} isShowFileInfo={isShowFileInfo} />
                  )}
                </SortableItem>
              );
            }}
          >
            {fileList?.length < maxCount ? (
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>Upload</div>
              </div>
            ) : null}
          </Upload>
        </SortableContext>
      </DndContext>
      <Image
        style={{ display: "none" }}
        preview={{
          visible: previewOpen,
          onVisibleChange: () => {
            setPreviewOpen(false);
          },
          src: previewImage,
          scaleStep: 0.15,
        }}
      />
    </div>
  );
}

export default ImageUpload;
