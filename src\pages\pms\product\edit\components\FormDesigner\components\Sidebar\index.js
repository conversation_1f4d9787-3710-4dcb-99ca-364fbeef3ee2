import styles from "./index.module.scss";
import { Card } from "antd";

import { useDraggable } from "@dnd-kit/core";
import { widgets } from "../../common/widgets";
import ComponentCard from "./component/ComponentCard";
import AttrSelector from "./component/AttrSelector";

function DraggableComponent({ component, ...restProps }) {
  const { attributes, listeners, setNodeRef } = useDraggable({
    id: component.type,
    disabled: component.isDisabled,
    data: {
      fromSidebar: true,
      component,
    },
  });

  return (
    <div className={styles.componentItem} ref={setNodeRef} {...listeners} {...attributes}>
      <ComponentCard className={styles.originalComponent} component={component} />
    </div>
  );
}

function Sidebar(props) {
  return (
    <div className={styles.container}>
      <AttrSelector {...props} />

      <Card title="定制属性" size="small" bordered={false} className={styles.sidebar}>
        <div className={styles.componentList}>
          {widgets.map((component) => (
            <DraggableComponent key={component.type} component={component} />
          ))}
        </div>
      </Card>
    </div>
  );
}

export default Sidebar;
