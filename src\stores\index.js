import { makeAutoObservable } from "mobx";
import Enums from "@/enums";
import Utils from "@/utils";

const store = makeAutoObservable({
  currentUser: {},
  asideFold: Utils.JSON.parse(localStorage.getItem(Enums.LocalStorageKey.AsideFold), false),
  brand: sessionStorage.getItem(Enums.SessionStorageKey.Brand) || localStorage.getItem(Enums.SessionStorageKey.Brand),
  pathSiteValues: Utils.JSON.parse(sessionStorage.getItem(Enums.SessionStorageKey.PathSiteValues)),
  sidebarFilterFold: Utils.JSON.parse(localStorage.getItem(Enums.LocalStorageKey.SidebarFilterFold), true),
  systemInfo: Utils.JSON.parse(localStorage.getItem(Enums.LocalStorageKey.SystemInfo), null),
  pathPageConfigs: Utils.JSON.parse(sessionStorage.getItem(Enums.SessionStorageKey.PathPageConfigs), null),

  setCurrentUser(value) {
    this.currentUser = value;
  },

  setAsideFold(value) {
    this.asideFold = value;
  },

  setBrand(value) {
    this.brand = value;
  },

  setPathSiteValues(value) {
    this.pathSiteValues = value;
  },

  setSidebarFilterFold(value) {
    this.sidebarFilterFold = value;
  },

  setSystemInfo(value) {
    this.systemInfo = value;
  },

  setPathPageConfigs(value) {
    this.pathPageConfigs = value;
  },
});

export default store;
