import styles from "./index.module.scss";

import { useState } from "react";
import { Button, Space, Modal, Form, Input, Typography } from "antd";
import { SaveOutlined, ExportOutlined } from "@ant-design/icons";

import Select from "components/common/Select";
import JSONEditor from "components/common/JSONEditor";

import Helper from "@/helpers";
import axios from "@/fetchers/request";
import { sanitizeItems } from "../../common/utils";

const { Text } = Typography;

function ToolBar(props) {
  const { saveTemplate, templateSelect, changeTemplate, widgetForm } = props;
  const templateKey = saveTemplate?.key || "name";
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [saveTemplateOpen, setSaveTemplateOpen] = useState(false);
  const [saveTemplateForm] = Form.useForm();

  async function handleSaveTemplate() {
    try {
      const values = await saveTemplateForm.validateFields();
      await axios({
        url: saveTemplate.request.url,
        method: saveTemplate.request.method || "POST",
        data: {
          [templateKey]: values.name,
          data: sanitizeItems(widgetForm.items),
        },
      });

      setSaveTemplateOpen(false);
      saveTemplateForm.resetFields();
    } catch (error) {
      Helper.openMessage({ type: "error", content: "保存失败，请重试" });
    }
  }

  function handleExport() {
    setExportModalOpen(true);
  }

  function handleCopy() {
    Helper.openMessage({ type: "success", content: "复制成功" });
  }

  return (
    <div className={styles.toolBar}>
      <Space>
        {templateSelect && (
          <Select
            allowClear
            showSearch
            optionFilterProp="label"
            onChange={changeTemplate}
            placeholder="请选择模板"
            style={{ minWidth: 150 }}
            {...templateSelect?.props}
          />
        )}
        {saveTemplate && (
          <Button type="primary" size="small" icon={<SaveOutlined />} onClick={() => setSaveTemplateOpen(true)}>
            保存模板
          </Button>
        )}
        <Button size="small" icon={<ExportOutlined />} onClick={handleExport}>
          导出代码
        </Button>
      </Space>

      <Modal
        title="定制属性代码"
        width={1200}
        open={exportModalOpen}
        footer={[
          <Text
            key="copy"
            copyable={{
              text: JSON.stringify(sanitizeItems(widgetForm.items), null, 2),
              onCopy: handleCopy,
              icon: ["default", "success"].map((item) => (
                <Button key={item} type="primary" block>
                  复制代码
                </Button>
              )),
              tooltips: false,
            }}
          ></Text>,
        ]}
        onCancel={() => setExportModalOpen(false)}
      >
        <div className={styles.exportModal}>
          <JSONEditor value={JSON.stringify(sanitizeItems(widgetForm.items), null, 2)} />
        </div>
      </Modal>

      <Modal
        title="模版名字"
        width={600}
        open={saveTemplateOpen}
        onCancel={() => {
          setSaveTemplateOpen(false);
          saveTemplateForm.resetFields();
        }}
        onOk={handleSaveTemplate}
      >
        <Form form={saveTemplateForm}>
          <Form.Item name={templateKey} rules={[{ required: true, message: "请填写模板名字" }]}>
            <Input placeholder="请填写模板名字" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}

export default ToolBar;
