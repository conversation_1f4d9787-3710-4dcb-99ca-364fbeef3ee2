import styles from "./index.module.scss";

import { useEffect, useRef, useState, forwardRef, useImperativeHandle } from "react";

import HtmlBlock from "components/common/HtmlBlock";

import { observer } from "mobx-react-lite";
import PropTypes from "prop-types";
import classNames from "classnames";

import defaultConfig from "./config";
import { afterRender } from "functions";
import Utils from "@/utils";
import { createUploadAdapter, getEditorConfig } from "./vars";

let id = 0;
const lockId = "lockId";

function RichTextEditor(props, ref) {
  const {
    config = defaultConfig,
    onReady,
    onError,
    minHeight = 300,
    value = "",
    onChange,
    onFocus,
    onBlur,
    validate,
    placeholder,
    disabled,
    uploadConfig,
  } = props;
  const [editor, setEditor] = useState();
  const [editorId, setEditorId] = useState();
  const [scriptReady, setScriptReady] = useState(false);
  const editorRef = useRef();
  const paramsRef = useRef({ initialized: false });

  paramsRef.current = {
    ...paramsRef.current,
    config,
    minHeight,
    onReady,
    onError,
    onChange,
    onFocus,
    onBlur,
    validate,
    placeholder,
    disabled,
    editor,
  };

  useImperativeHandle(ref, () => ({
    editor,
    setData(value) {
      editor?.data?.set(value);
    },
  }));

  // 加载 CKEditor 脚本
  useEffect(() => {
    async function init() {
      try {
        await Utils.loadScript("/js/ckeditor5/ckeditor.min.js");
        setScriptReady(true);
      } catch (error) {
        const { onError } = paramsRef.current;
        onError?.(error);
      }
    }

    init();
  }, []);

  useEffect(() => {
    (async () => {
      if (scriptReady && !paramsRef.current.initialized) {
        paramsRef.current.initialized = true;
        const { config, placeholder } = paramsRef.current;
        const { uploadApi } = uploadConfig || {};

        const editorConfig = getEditorConfig(config, uploadApi);

        // 创建编辑器实例
        const editor = await window.CKEDITOR.ClassicEditor.create(editorRef.current, {
          ...editorConfig,
          initialData: value ?? {},
          placeholder,
        });

        if (uploadApi) {
          editor.plugins.get("FileRepository").createUploadAdapter = createUploadAdapter(uploadConfig);
        }

        const handleChange = () => {
          const value = editor.getData();
          paramsRef.current?.onChange?.(value);
        };

        editor.model.document.on("change", handleChange);

        // 监听源代码编辑模式
        editor.plugins.get("SourceEditing").on("change", (e, name, visible, oldValue) => {
          if (visible) {
            const textarea = editor.ui.element.querySelector(".ck-source-editing-area textarea");
            if (textarea) {
              textarea.placeholder = placeholder;
              textarea.addEventListener("input", (e) => {
                paramsRef.current?.onChange?.(e.target.value);
              });
            }
          }
        });

        // 监听焦点变化
        editor.ui.focusTracker.on("change:isFocused", (e, name, isFocused) => {
          const { onFocus, onBlur, validate } = paramsRef.current;
          if (!isFocused) {
            onBlur?.();
            if (editor.ui.element.closest(".ant-form-item").className.indexOf("ant-form-item-has-error") === -1) {
              validate?.();
            }
          } else {
            onFocus?.();
          }
        });

        setEditor(editor);
        setEditorId(`ckeditor${++id}`);
        afterRender(() => {
          paramsRef.current?.onReady?.();
        });
      }
    })();
  }, [scriptReady, value, uploadConfig]);

  // 设置只读
  useEffect(() => {
    if (editor) {
      if (disabled) {
        editor.enableReadOnlyMode(lockId);
      } else {
        editor.disableReadOnlyMode(lockId);
      }
    }
  }, [editor, disabled]);

  // 销毁 CKEditor 实例
  useEffect(() => {
    return () => {
      if (editor) {
        const { onError } = paramsRef.current;
        editor.destroy().catch((error) => onError?.(error));
      }
    };
  }, [editor]);

  return (
    <div id={editorId} className={classNames(styles.richEditor, { [styles.disabled]: disabled })}>
      <style jsx="true">{`
        #${editorId} {
          --ckeditor-min-height: ${minHeight}px;
        }
      `}</style>
      <HtmlBlock ref={editorRef} style={{ display: "none" }} />
    </div>
  );
}

RichTextEditor = forwardRef(RichTextEditor);

RichTextEditor = observer(RichTextEditor);

RichTextEditor.propTypes = {
  config: PropTypes.object,
  onReady: PropTypes.func,
  onError: PropTypes.func,
  minHeight: PropTypes.number,
  value: PropTypes.string,
  onChange: PropTypes.func,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
  validate: PropTypes.func,
  disabled: PropTypes.bool,
  placeholder: PropTypes.string,
  uploadConfig: PropTypes.object,
};

export default RichTextEditor;
