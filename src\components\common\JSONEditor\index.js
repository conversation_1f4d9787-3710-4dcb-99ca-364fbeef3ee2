import styles from "./index.module.scss";
import ace from "ace-builds";
import Beautify from "ace-builds/src-noconflict/ext-beautify";
import AceEditor from "react-ace";
import "ace-builds/src-noconflict/mode-json";
import "ace-builds/src-noconflict/theme-eclipse";
// import "ace-builds/webpack-resolver";
import classNames from "classnames";
import { useEffect, useRef } from "react";
import Utils from "@/utils";

// eslint-disable-next-line import/no-webpack-loader-syntax
import jsonWorkerUrl from "file-loader!ace-builds/src-noconflict/worker-json";
ace.config.setModuleUrl("ace/mode/json_worker", jsonWorkerUrl);

function JSONEditor(props) {
  const { value, onChange, width = "100%", height = "350px", validate, disabled } = props;
  const containerRef = useRef();
  const paramsRef = useRef({ editor: null });

  paramsRef.current = {
    ...paramsRef.current,
    value,
  };

  // function beautify(code) {
  //   let beautified = code;
  //   try {
  //     beautified = JSON.stringify(JSON.parse(code), null, 2);
  //   } catch (e) {}
  //   paramsRef.current.editor.session.doc.setValue(beautified);
  // }

  // useEffect(() => {
  //   setTimeout(() => {
  //     beautify(paramsRef.current.value);
  //   });
  // }, []);

  useEffect(() => {
    Beautify.beautify(paramsRef.current.editor.session);
  }, []);

  useEffect(() => {
    if (disabled) {
      paramsRef.current.editor.setOptions({
        readOnly: true,
        highlightActiveLine: false,
        highlightGutterLine: false,
      });
    } else {
      paramsRef.current.editor.setOptions({
        readOnly: false,
        highlightActiveLine: true,
        highlightGutterLine: true,
      });
    }
  }, [disabled]);

  return (
    <div
      ref={containerRef}
      className={classNames(styles.jsonEditor, "ant-input", { [styles.disabled]: disabled })}
      disabled={disabled}
    >
      <AceEditor
        mode="json"
        theme="eclipse"
        value={value}
        onChange={onChange}
        width={width}
        height={height}
        showPrintMargin={false}
        // setOptions={{ useWorker: false }}   // useWorker:false 可以解决Failed to execute 'importScripts' 报错，但也禁用了JSON语法检查
        tabSize={2}
        fontSize={14}
        lineHeight={19}
        commands={Beautify.commands}
        onFocus={() => {
          containerRef.current?.classList.add(styles.jsonEditorFocus);
        }}
        onBlur={() => {
          validate();
          containerRef.current?.classList.remove(styles.jsonEditorFocus);
        }}
        onPaste={async (value) => {
          await Utils.sleep();
          Beautify.beautify(paramsRef.current.editor.session);
        }}
        onLoad={(editor) => {
          paramsRef.current.editor = editor;
        }}
      />
    </div>
  );
}

export default JSONEditor;
