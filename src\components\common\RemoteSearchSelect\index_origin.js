import { Empty, Select, Spin } from "antd";
import { useEffect, useMemo, useState } from "react";
import debounce from "lodash.debounce";
import PropTypes from "prop-types";
import request from "@/fetchers/request";

function RemoteSearchSelect(props) {
  const { api, fetchDelay = 400, value, onChange } = props;
  const [defaultOptions, setDefaultOptions] = useState([]);
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);

  const debounceFetcher = useMemo(() => {
    return debounce(async (keywords) => {
      if (keywords) {
        try {
          setOptions([]);
          setLoading(true);
          const result = await request(api, { method: "GET", params: { query: keywords } }).then((res) => res.data);
          setOptions(result?.data || []);
        } catch (e) {
          setOptions([]);
        } finally {
          setLoading(false);
        }
      }
    }, fetchDelay);
  }, [api, fetchDelay]);

  useEffect(() => {
    (async () => {
      try {
        if (value) {
          setLoading(true);
          const result = await request(api, {
            method: "GET",
            params: { query: value instanceof Array ? value.join(",") : value },
          }).then((res) => res.data);
          const defaultOptions = result?.data || [];
          setDefaultOptions(defaultOptions);
          setOptions(defaultOptions);
        }
      } finally {
        setLoading(false);
      }
    })();
  }, [api, value]);

  return (
    <Select
      showSearch
      onSearch={debounceFetcher}
      filterOption={false}
      defaultActiveFirstOption={false}
      notFoundContent={
        <div style={{ display: "flex", justifyContent: "center" }}>
          {loading ? <Spin /> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
        </div>
      }
      onDropdownVisibleChange={(open) => {
        if (open) {
          setOptions(defaultOptions);
        }
      }}
      {...props}
      options={options}
      onChange={onChange}
    />
  );
}

RemoteSearchSelect.propTypes = {
  api: PropTypes.string,
  fetchDelay: PropTypes.number,
};

export default RemoteSearchSelect;
