import Validator from "helpers/validator";
import Enums from "enums";
import Utils from "utils";

export function getFormRules(item) {
  const { component, rules = [] } = item;
  if ([Enums.Components.Checkbox, Enums.Components.Tree].includes(component)) {
    return rules?.map((rule) => {
      if (rule.required) {
        rule.validator = (rule, value) => {
          return new Promise((resolve, reject) => {
            if (Array.isArray(value) && value?.length > 0) {
              resolve();
            } else {
              reject(rule.message);
            }
          });
        };
      }
      return rule;
    });
  } else if ([Enums.Components.JSONEditor].includes(component)) {
    const { required, message } = rules?.[0] || {};
    return [
      {
        required: required,
        validateTrigger: ["onBlur"],
        validator: (rule, value) => {
          const promises = [];
          if (required) {
            promises.push(
              new Promise((resolve, reject) => {
                if (!value?.trim?.()) {
                  reject(message);
                } else {
                  resolve();
                }
              })
            );
          }
          promises.push(
            new Promise((resolve, reject) => {
              try {
                JSON.parse(value);
                resolve();
              } catch (e) {
                reject("Invalid JSON!");
              }
            })
          );
          return Promise.all(promises);
        },
      },
    ];
  } else if (component === Enums.Components.ImageUpload || component === Enums.Components.FileUpload) {
    return rules.map((rule) => {
      if (rule.required) {
        const requiredMessage = rule.message;
        delete rule.message;
        rule.validator = (rule, fileList) => {
          const promises = [
            new Promise((resolve, reject) => {
              if (fileList?.length > 0) {
                resolve();
              } else {
                reject(requiredMessage);
              }
            }),
            new Promise((resolve, reject) => {
              const validFile = fileList.find((file) => file.status !== Enums.UploadFileStatus.Done);
              if (validFile) {
                const message =
                  validFile.status === Enums.UploadFileStatus.Error
                    ? "Please delete the failed file"
                    : "Please wait for the file upload to complete";
                reject(message);
              } else {
                resolve();
              }
            }),
            new Promise((resolve, reject) => {
              const validFile = fileList.find((file) => !file.fid || !file.url);
              if (validFile) {
                const field = !validFile.fid ? "fid" : "url";
                const message = `The data field ${field} is missing, please try again or contact the developer`;
                reject(message);
              } else {
                resolve();
              }
            }),
          ];
          return Promise.all(promises);
        };
      }
      return rule;
    });
  } else {
    return rules?.map((rule) => {
      if (rule?.validatorName) {
        rule.validator = Validator[rule.validatorName];
      }
      return rule;
    });
  }
}

export function getValuePropName(item) {
  const { component } = item;
  if (component === Enums.Components.Switch) {
    return "checked";
  } else if (component === Enums.Components.Tree) {
    return "checkedKeys";
  } else if (component === Enums.Components.ImageUpload || component === Enums.Components.FileUpload) {
    return "fileList";
  } else {
    return "value";
  }
}

export function getTrigger(item) {
  const { component } = item;
  if (component === Enums.Components.Tree) {
    return "onCheck";
  }
}

function isContainerComponent(component) {
  return [Enums.Components.Row, Enums.Components.Col, Enums.Components.Card, Enums.Components.Collapse].includes(
    component
  );
}

function isSelectableComponent(component) {
  return [
    Enums.Components.Select,
    Enums.Components.Checkbox,
    Enums.Components.TreeSelect,
    Enums.Components.Cascader,
    Enums.Components.Transfer,
    Enums.Components.Radio,
  ].includes(component);
}

function getChildren(item, component) {
  if (component === Enums.Components.Collapse) {
    return item.props?.items || [];
  }
  return item?.children || [];
}

function getOptions(props, component) {
  switch (component) {
    case Enums.Components.Select:
    case Enums.Components.Checkbox:
    case Enums.Components.Radio:
      return props?.options || [];
    case Enums.Components.Cascader:
      return Utils.flatTree({ treeData: props?.options }) || [];
    case Enums.Components.TreeSelect:
      return Utils.flatTree({ treeData: props?.treeData }) || [];
    case Enums.Components.Transfer:
      return props?.dataSource || [];
    default:
      return [];
  }
}

function getValueLabel(value, options, component) {
  if (Array.isArray(value)) {
    return value.map((val) => {
      const option = options.find((option) => {
        if (component === Enums.Components.Transfer) {
          return option.key === val;
        }
        return option?.value === val;
      });
      return option?.label || option?.title || val;
    });
  } else {
    const option = options.find((option) => {
      if (component === Enums.Components.Transfer) {
        return option?.key === value;
      }
      return option?.value === value;
    });
    return option?.label || option?.title || value;
  }
}

export function generateLabels({ formItems, values }) {
  const labels = {};

  const queue = [{ formItems, values }];
  while (queue.length > 0) {
    const { formItems, values } = queue.shift();

    if (!formItems) continue;

    formItems.forEach((item) => {
      const { key, component, props } = item;
      const value = values?.[key];
      if (isContainerComponent(component)) {
        const children = getChildren(item, component);
        if (children) {
          children.forEach((childItem) => {
            queue.push({ formItems: childItem?.children || [childItem], values });
          });
        }
      } else if (isSelectableComponent(component)) {
        const options = getOptions(props, component);
        labels[key] = getValueLabel(value, options, component);
      }
    });
  }

  return labels;
}

export function clearFields({ form, fields }) {
  if (!fields) return;

  fields.forEach((field) => {
    form.setFieldsValue({ [field]: "" });
  });
}
