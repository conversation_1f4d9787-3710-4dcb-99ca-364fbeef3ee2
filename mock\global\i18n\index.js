module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      Poll: "轮询",
      Stop: "停止",
      ColumnSettings: "列设置",
      FullScreen: "全屏",
      ExitFullScreen: "退出全屏",
      Clear: "清除",
      Confirm: "确定",
      LogOut: "退出登录",
      FileExportList: "文件导出列表",
      ClickToDownload: "点击下载",
      ExportList: "导出列表",
      Delete: "删除",
      AreYouSureYouWantToDeleteThisFile: "确定删除该文件吗？",
      AreYouSureYouWantToDelete: "确定删除吗？",
      SelectAll: "全选",
      Upload: "上传",
      FilterParams: "筛选条件",
      FixedOnTheLeftSide: "固定在左侧",
      FixedOnTheRightSide: "固定在右侧",
      FixedOnTheBeginning: "固定在列首",
      FixedOnTheTail: "固定在列尾",
      Unfixed: "不固定",
      ColumnShow: "列显示",
      Reset: "重置",
    },
    command: [],
  });
};
