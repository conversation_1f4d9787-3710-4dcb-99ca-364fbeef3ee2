module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      content: {
        component: "JSONComponents",
        type: "json",
        children: [
          {
            component: "Card",
            props: { title: "产品信息", styles: { header: { backgroundColor: "#343a40", color: "#FFFFFF" } } },
            children: [
              {
                component: "NativeTable",
                props: {
                  // style: { width: "100px" },
                },
                children: [
                  [
                    {
                      tag: "th",
                      valueType: "text",
                      value: "运费险",
                      props: {
                        style: { width: "50px" },
                      },
                    },
                    {
                      tag: "td",
                      valueType: "text",
                      value: "0(usd)",
                    },
                    {
                      tag: "td",
                      valueType: "text",
                      value: "客户等级(当前等级/下单时等级)",
                      props: { rowSpan: 3 },
                    },
                  ],
                  [
                    {
                      tag: "th",
                      valueType: "text",
                      value: "运费险",
                    },
                    {
                      tag: "td",
                      valueType: "text",
                      value: "0(usd)",
                    },
                  ],
                  [
                    {
                      tag: "th",
                      valueType: "text",
                      value: "运费险",
                    },
                    {
                      tag: "td",
                      valueType: "text",
                      value: "0(usd)",
                    },
                  ],
                  [
                    {
                      tag: "th",
                      valueType: "text",
                      value: "定制属性",
                    },
                    {
                      tag: "td",
                      valueType: "native_table",
                      value: {
                        props: {},
                        children: [
                          [
                            {
                              tag: "th",
                              valueType: "text",
                              value: "类型",
                            },
                            {
                              tag: "td",
                              valueType: "text",
                              value: "镜片",
                            },
                            {
                              tag: "td",
                              valueType: "text",
                              value: "镜框",
                            },
                          ],
                          [
                            {
                              tag: "th",
                              valueType: "text",
                              value: "处方类型",
                            },
                            {
                              tag: "td",
                              valueType: "text",
                              value: "处方",
                              props: { colSpan: 2 },
                            },
                          ],
                          [
                            {
                              tag: "th",
                              valueType: "text",
                              value: "Glasses Type",
                            },
                            {
                              tag: "td",
                              valueType: "text",
                              value: "处方眼镜",
                            },
                            {
                              tag: "td",
                              valueType: "text",
                              value: "渐进处方",
                            },
                          ],
                        ],
                      },
                      props: {},
                    },
                  ],
                ],
              },
            ],
          },
          {
            component: "Card",
            props: { title: "出厂前数据" },
            children: [
              {
                component: "NativeTable",
                props: {},
                children: [
                  [
                    {
                      tag: "th",
                      valueType: "text",
                      value: "SPU",
                    },
                    {
                      tag: "td",
                      valueType: "text",
                      value: "89554",
                    },
                    {
                      tag: "th",
                      valueType: "text",
                      value: "唯一码",
                    },
                    {
                      tag: "td",
                      valueType: "text",
                      value: "102036",
                    },
                    {
                      tag: "th",
                      valueType: "text",
                      value: "尺码",
                    },
                    {
                      tag: "td",
                      valueType: "html",
                      value: '<span style="color:red;font-weight:bold;">5.0</span>',
                    },
                  ],
                  [
                    {
                      tag: "th",
                      valueType: "text",
                      value: "产品图",
                    },
                    {
                      tag: "td",
                      valueType: "image",
                      value: [
                        {
                          width: 50,
                          height: 50,
                          src: "https://res2022.shesaidyes.com/uploads/produce-image/order/1286564/stone_setting_fids/2/3/660d16799b332.jpg",
                          text: "头-斑马: 序号47",
                          // props: {
                          //   preview: {
                          //     src: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png",
                          //   },
                          // },
                        },
                        {
                          width: 50,
                          height: 50,
                          src: "https://assets.cnzlerp.com/prod/product/uploads/product/8/b/664ea95fedcb8.png",
                          text: "头-大象: 序号41",
                        },
                        {
                          width: 50,
                          height: 50,
                          src: "https://assets.cnzlerp.com/prod/product/uploads/product/0/4/65a1099d76d40.png",
                          text: "头-狮子: 序号43",
                        },
                        {
                          width: 50,
                          height: 50,
                          src: "https://assets.cnzlerp.com/prod/product/uploads/product/0/a/665994179afa0.png",
                          text: "头-斑马: 序号41",
                        },
                      ],
                    },
                    {
                      tag: "th",
                      valueType: "text",
                      value: "标签",
                    },
                    {
                      tag: "td",
                      valueType: "tag",
                      value: [
                        {
                          text: "加急",
                          props: {
                            color: "orange",
                          },
                        },
                        {
                          text: "高级定制",
                          props: {
                            color: "red",
                          },
                        },
                      ],
                    },
                    {
                      tag: "th",
                      valueType: "text",
                      value: "尺码",
                    },
                    {
                      tag: "td",
                      valueType: "html",
                      value: '<span style="color:red;font-weight:bold;">5.0</span>',
                    },
                  ],
                ],
              },
            ],
          },
        ],
      },
    },
  });
};
