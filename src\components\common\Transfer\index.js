import { Transfer as AntdTransfer } from "antd";
import { useState } from "react";

function Transfer(props) {
  const { value, render, onChange } = props;
  const [targetKeys, setTargetKeys] = useState(value || props?.targetKeys || []);

  function handleOnChange(nextTargetKeys, direction, moveKeys) {
    setTargetKeys(nextTargetKeys);
    onChange?.(nextTargetKeys);
  }

  return (
    <AntdTransfer
      {...props}
      targetKeys={targetKeys}
      onChange={handleOnChange}
      render={render || ((item) => item.title)}
    />
  );
}

export default Transfer;
