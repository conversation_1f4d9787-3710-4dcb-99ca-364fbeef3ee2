import { useState } from "react";
import { Image } from "antd";
import { EyeOutlined, DeleteOutlined } from "@ant-design/icons";

function PreviewImage(props) {
  const { data, onRemove, onPreview } = props;
  const [visible, setVisible] = useState(false);

  return (
    <Image
      width={84}
      height={84}
      src={data.url}
      preview={{
        visible: visible,
        src: data.url,
        onVisibleChange: (value) => {
          setVisible(value);
        },
        mask: (
          <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
            <EyeOutlined onClick={() => setVisible(true)} style={{ marginRight: 8 }} />
            <DeleteOutlined
              onClick={(e) => {
                e.stopPropagation();
                onRemove?.(data);
              }}
            />
          </div>
        ),
      }}
    />
  );
}

export default PreviewImage;
