import styles from "./index.module.scss";
import { observer } from "mobx-react-lite";
import { useEffect, useRef, useState } from "react";
import { <PERSON>er, But<PERSON>, Space } from "antd";
import { DeleteOutlined } from "@ant-design/icons";

import Utils from "@/utils";

import JSONForm from "./components/JSONForm";

function ConfigDrawer({ open, component, onClose, onDelete, onSave }) {
  const { component_config } = component ?? {};
  const [initialValues, setInitialValues] = useState({});
  const formRef = useRef(null);

  const handleSubmit = () => {
    formRef.current.validateFields().then((values) => {
      const newComponent = Utils.cloneDeep(component);
      newComponent.component_config.preview_component = component?.component_config?.preview_component;

      onSave?.({ ...newComponent, ...values });
      onClose?.();
    });
  };

  useEffect(() => {
    if (open && component) {
      const { component_config, ...restValues } = component;
      setInitialValues(restValues);
    }

    return () => {
      setInitialValues({});
    };
  }, [open, component]);

  return (
    <Drawer
      className={styles.configDrawer}
      title="组件配置"
      placement="right"
      width={500}
      onClose={onClose}
      open={open}
      // maskClosable={false}
      destroyOnClose={true}
      keyboard={false}
      extra={
        <Space>
          {onDelete && (
            <Button danger icon={<DeleteOutlined />} onClick={() => onDelete(component?.key)}>
              删除
            </Button>
          )}
          <Button onClick={onClose}>取消</Button>
          <Button type="primary" onClick={handleSubmit}>
            保存
          </Button>
        </Space>
      }
    >
      {component_config?.widget_config && Object.keys(initialValues).length > 0 && (
        <JSONForm
          ref={formRef}
          data={{
            ...component_config?.widget_config,
            props: { ...component_config?.widget_config?.props, initialValues },
          }}
          identifiers={component?.component_config?.identifiers}
          componentKey={component?.key}
        />
      )}
    </Drawer>
  );
}

ConfigDrawer = observer(ConfigDrawer);

export default ConfigDrawer;
