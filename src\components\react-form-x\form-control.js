import React, { forwardRef, useContext, useEffect, useImperative<PERSON>andle, useMemo, useRef, useState } from "react";
import PropTypes from "prop-types";
import FormContext from "./context";
import FormEnums from "./enums";
import styles from "./form-control.module.scss";
import EventBus from "./event-bus";
import {
  classNames,
  debounce,
  findField,
  getFormFieldValue,
  getObjectCursor,
  getValueFromEvent,
  isServer,
  uniqueId,
  updatedForms,
  validateRequired,
} from "./common";
import { useForm } from "./index";

function FormControl(props, ref) {
  const { name, rule, render, valueField = "value", keyPath, style } = props;

  if (!name) {
    throw new Error("[FormControl] name is required!");
  }

  const controlRef = useRef();
  const { formId, onFormChange, formListenerReady } = useContext(FormContext);
  const [errorMessage, setErrorMessage] = useState("");
  const form = useForm({ formId });

  const paramsRef = useRef({ formControlId: uniqueId() });
  paramsRef.current = {
    ...paramsRef.current,
    props,
    valueField,
    thisFormId: formId,
    // getValidator,
    validateField,
    keyPath,
    form,
    // getValidateMessage,
  };

  const debounceSetErrorMessage = useMemo(() => {
    return debounce((message, callback) => {
      setErrorMessage(message);
      callback?.();
    }, 100);
  }, []);

  function getValidator({ value }) {
    let validator = () => {};
    if (rule) {
      if (rule.validator) {
        validator = rule.validator({ name, value, rule });
      } else if (rule.required) {
        validator = validateRequired({ name, value, rule });
      }
    }
    return validator;
  }

  async function validateField({ value }) {
    const { formControlId } = paramsRef.current;
    const validator = getValidator({ value });
    const { isValid, message } = await Promise.allSettled([validator]).then((results) => {
      const [result] = results;
      const isValid = result.status === "fulfilled";
      const message = !isValid ? getValidateMessage({ result }) : "";
      return { isValid, message };
    });
    await new Promise((resolve) => {
      function handleSetErrorMessage() {
        EventBus.removeEventListener(FormEnums.Events.SetErrorMessage, handleSetErrorMessage);
        resolve();
      }
      EventBus.addEventListener(FormEnums.Events.SetErrorMessage, handleSetErrorMessage);
      debounceSetErrorMessage(message, () => {
        EventBus.dispatchEvent(FormEnums.Events.SetErrorMessage);
      });
    });
    return { formId, formControlId, name, keyPath, value, rule, isValid, message };
  }

  function getValidateResultMessage({ result }) {
    return typeof result.reason === "object" ? result.reason.message : result.reason;
  }

  function getValidateMessage({ result }) {
    return getValidateResultMessage({ result }) || rule?.message || "";
  }

  useEffect(() => {
    const handlers = {
      [FormEnums.Actions.UpdateFormControl]: ({ name, payload }) => {
        const { thisFormId, validateField, keyPath = [] } = paramsRef.current;
        const { formId, partialData, callback } = payload;
        const matched = Object.keys(partialData || {}).some((key) =>
          keyPath.length > 0 ? key === keyPath[0] : key === name
        );
        if (formId === thisFormId && matched) {
          const cursor = getObjectCursor({ object: partialData, keyPath });
          if (cursor) {
            const value = cursor[name];
            controlRef.current.value = value;
            validateField({ value });
          }
        }
        callback();
      },
      [FormEnums.Actions.ValidateForm]: async ({ name, payload }) => {
        const { thisFormId, formControlId, validateField, keyPath } = paramsRef.current;
        const { formId, formValues } = payload;
        if (formId === thisFormId) {
          const value = getFormFieldValue({ formValues, keyPath, name });
          const result = await validateField({ value });
          EventBus.dispatchEvent(FormEnums.Events.Message, {
            action: FormEnums.Actions.ValidateFormCallback,
            payload: { formId, formControlId, result },
          });
        }
      },
      [FormEnums.Actions.ValidateFields]: async ({ name, payload }) => {
        const { thisFormId, keyPath: thisKeyPath, validateField, form } = paramsRef.current;
        const { formId, fields = [], callback } = payload;
        if (formId === thisFormId) {
          const field = fields.find(
            (field) => field.name === name && JSON.stringify(field.keyPath) === JSON.stringify(thisKeyPath)
          );
          if (field) {
            const value = getFormFieldValue({
              formValues: form.value,
              name: field.name,
              keyPath: field.keyPath,
            });
            const result = await validateField({ value });
            callback(result);
          } else {
            callback();
          }
        }
      },
      [FormEnums.Actions.ClearErrorMessage]: async ({ name, payload }) => {
        const { thisFormId, keyPath: thisKeyPath } = paramsRef.current;
        const { formId, fields } = payload;
        if (formId === thisFormId) {
          if (fields?.length > 0) {
            const matched = !!findField({ fields, name, keyPath: thisKeyPath });
            if (matched) {
              setErrorMessage("");
            }
          } else {
            setErrorMessage("");
          }
        }
      },
    };

    async function handleMessage(data) {
      const { action, payload = {} } = data;
      const { props } = paramsRef.current;
      const { name, rule } = props;
      await handlers[action]?.({ name, rule, action, payload });
    }

    EventBus.addEventListener(FormEnums.Events.Message, handleMessage);

    return function () {
      EventBus.removeEventListener(FormEnums.Events.Message, handleMessage);
    };
  }, []);

  useEffect(() => {
    if (!isServer() && name && formListenerReady) {
      const { valueField, keyPath, formControlId, form } = paramsRef.current;
      let value, defaultValue;
      // 1.如果组件是延迟渲染的，比如第一次不渲染，满足某些条件才渲染，初始化的时候要给组件赋值
      // 2.如果组件在form.updateValue执行之后才渲染，也需要给组件赋值
      if (!updatedForms[formId]) {
        value = controlRef.current?.[valueField];
        const defaultValueName = `default${valueField.replace(/^./, (matched) => matched.toUpperCase())}`;
        defaultValue = controlRef.current?.[defaultValueName];
      } else {
        value = getFormFieldValue({ formValues: form.value, keyPath, name });
        if (controlRef.current) {
          controlRef.current[valueField] = value ?? null;
        }
      }
      EventBus.dispatchEvent(FormEnums.Events.Message, {
        action: FormEnums.Actions.InitFormControl,
        payload: { name, value, defaultValue, valueField, keyPath, formId, formControlId },
      });
    }

    return function () {
      if (name && formListenerReady) {
        const { formControlId } = paramsRef.current;
        EventBus.dispatchEvent(FormEnums.Events.Message, {
          action: FormEnums.Actions.UnmountFormControl,
          payload: { formId, formControlId, name },
        });
      }
    };
  }, [formId, formListenerReady, name]);

  useImperativeHandle(
    ref,
    () => {
      return Object.freeze({
        validate: () => {
          const { validateField, form, name, keyPath } = paramsRef.current;
          const value = getFormFieldValue({ formValues: form.value, name, keyPath });
          return validateField({ value });
        },
      });
    },
    []
  );

  return render ? (
    <div className={classNames("form-x-control", styles.formControl, { "has-error": errorMessage })} style={style}>
      <div>
        {render(
          {
            ref: controlRef,
            name,
            onChange(event, customValue) {
              const { formControlId } = paramsRef.current;
              const value = getValueFromEvent({ event, name, value: customValue });
              onFormChange?.(event, { [name]: value }, { formControlId, name, keyPath });
              validateField({ value });
            },
            onPaste(event, customValue) {
              const { formControlId } = paramsRef.current;
              const value = getValueFromEvent({ event, name, value: customValue });
              onFormChange?.(event, { [name]: value }, { formControlId, name, keyPath });
              validateField({ value });
            },
          },
          // 不适合传给组件的数据放在这里传
          { valueField, rule, keyPath, validateField, setErrorMessage, formControlId: paramsRef.current.formControlId }
        )}
      </div>
      <div className={classNames("form-x-error-message", styles.errorMessage)}>{errorMessage}</div>
    </div>
  ) : (
    <></>
  );
}

FormControl = forwardRef(FormControl);

FormControl.propTypes = {
  name: PropTypes.string.isRequired,
  render: PropTypes.func,
  rule: PropTypes.shape({ required: PropTypes.bool, message: PropTypes.any, validator: PropTypes.any }),
  valueField: PropTypes.string,
  keyPath: PropTypes.array,
  style: PropTypes.object,
};

export default FormControl;
