import styles from "./index.module.scss";
import { useEffect, useRef, useState } from "react";
import { useLocation, useParams } from "react-router-dom";
import Fetchers from "fetchers";
import Helper from "helpers";

import Iframe from "components/common/JSONComponent/components/Iframe";
import Breadcrumbs from "components/common/Breadcrumbs";
import Loading from "components/common/Loading";

function IframePage() {
  const location = useLocation();
  const params = useParams();
  const [iframeData, setIframeData] = useState({});
  const [loading, setLoading] = useState(true);
  const iframeRef = useRef();

  const url_key = Helper.getUrlKey({ location, params });
  const headers = Helper.getCommonHeaders();

  function handleIframeLoad() {
    if (iframeRef.current) {
      const origin = "*";
      const iframe = iframeRef.current;
      iframe.contentWindow.postMessage(
        {
          action: "setHeaders",
          data: headers,
        },
        origin
      );
    }
  }

  useEffect(() => {
    async function loadIframeData() {
      setLoading(true);
      const result = await Fetchers.getIframePageData({ url_key }).then((res) => res.data);
      setIframeData(result?.data);
      setLoading(false);
    }

    loadIframeData();
  }, [location, params, url_key]);

  return (
    <div className={styles.iframePage}>
      <Loading loading={loading}>
        <div className={styles.pageHeader}>
          <Breadcrumbs data={iframeData?.breadcrumbs}></Breadcrumbs>
        </div>
        {iframeData?.url && (
          <Iframe
            data={{
              props: {
                ref: iframeRef,
                src: iframeData.url,
                width: "100%",
                frameBorder: "0",
                allowFullScreen: true,
                onLoad: handleIframeLoad,
              },
            }}
            title={iframeData.title || "内嵌页面"}
          />
        )}
      </Loading>
    </div>
  );
}

export default IframePage;
