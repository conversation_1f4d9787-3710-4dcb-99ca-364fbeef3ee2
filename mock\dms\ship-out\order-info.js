module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      order_id: 616844,
      order_no: "785555",
      tags: [
        { text: "加急", color: "error" },
        { text: "风控", style: { color: "#fff", backgroundColor: "#f00", borderColor: "#f00" } },
      ],
      qty: 5,
      basic_info: {
        component: "JSONComponents",
        type: "json",
        children: [
          {
            component: "NativeTable",
            props: {},
            children: [
              [
                {
                  tag: "th",
                  valueType: "text",
                  value: "地址",
                  props: {
                    style: {
                      width: "50px",
                    },
                  },
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "ASDJL10230",
                  props: {
                    style: {
                      width: "50px",
                    },
                  },
                },
              ],
              [
                {
                  tag: "th",
                  valueType: "text",
                  value: "订单编号",
                  props: {
                    style: {
                      width: "50px",
                    },
                  },
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "ASDJL11230",
                  props: {
                    style: {
                      width: "50px",
                    },
                  },
                },
              ],
              [
                {
                  tag: "th",
                  valueType: "text",
                  value: "物流",
                  props: {
                    style: {
                      width: "50px",
                    },
                  },
                },
                {
                  tag: "td",
                  valueType: "html",
                  value: `<span style='color: red;font-weight: bold;'>DHL</span>`,
                  props: {
                    style: {
                      width: "50px",
                    },
                  },
                },
              ],
            ],
          },
        ],
      },
      address: {
        component: "JSONComponents",
        type: "json",
        children: [
          {
            component: "NativeTable",
            props: {},
            children: [
              [
                {
                  tag: "th",
                  valueType: "text",
                  value: "地址",
                  props: {
                    style: {
                      width: "50px",
                    },
                  },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "详情",
                  props: {
                    style: {
                      width: "50px",
                    },
                  },
                },
              ],
            ],
          },
        ],
      },
      remark: {
        component: "JSONComponents",
        type: "json",
        children: [
          {
            component: "NativeTable",
            props: {},
            children: [
              [
                {
                  tag: "th",
                  valueType: "text",
                  value: "内容详情",
                  props: {
                    style: {
                      width: "50px",
                    },
                  },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "补充信息",
                  props: {
                    style: {
                      width: "50px",
                    },
                  },
                },
              ],
            ],
          },
        ],
      },
      items: [
        {
          item_id: "102039554",
          item_code: 39554,
          image: { src: "https://picsum.photos/200/300" },
          custom_props: [
            { label: "SPU", value: "569554" },
            { label: "SKU", value: "39554" },
            { label: "商品编码", value: "102039554" },
            { label: "颜色规格", value: `<span style="color: #ff0000;font-weight: bold;">电黄+6.5(US)</span>` },
            { label: "刻字", value: "BLYGJN" },
          ],
        },
        {
          item_id: "102039555",
          item_code: 39555,
          image: { src: "https://picsum.photos/200/300" },
          custom_props: [
            { label: "SPU", value: "569554" },
            { label: "SKU", value: "39555" },
            { label: "商品编码", value: "102039555" },
            { label: "颜色规格", value: `<span style="color: #ff0000;font-weight: bold;">电黄+6.5(US)</span>` },
            { label: "刻字", value: "BLYGJN" },
          ],
        },
        {
          item_id: "102039556",
          item_code: 39556,
          image: { src: "https://picsum.photos/200/300" },
          custom_props: [
            { label: "SPU", value: "569554" },
            { label: "SKU", value: "39556" },
          ],
          card_title: "Taylin",
          card_content:
            "I'll never forget the day we started talking because that day I met my best friend, someone wanted in my life forever ❤️",
        },
        {
          item_id: "102039557",
          item_code: 39557,
          image: { src: "https://picsum.photos/200/300" },
          custom_props: [
            { label: "SPU", value: "569554" },
            { label: "SKU", value: "39557" },
          ],
          card_title: "Taylin",
          card_content:
            "I'll never forget the day we started talking because that day I met my best friend, someone wanted in my life forever ❤️",
        },
      ],
      way_bills: [
        {
          src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/27/171682111566549c7babddb0.png",
        },
        {
          src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/30/1717080509665891bd60f400.png",
        },
        {
          src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/29/17169541166656a404adbca0.png",
        },
        {
          src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/28/1716848967665509476374f0.png",
        },
        {
          src: "https://erp5-dms.cnzlerp.com/uploads/logistics/hsd/waybill/2024/05/27/171678882566541e59bc80b0.png?1=%2Fuploads%2Flogistics%2Fhsd%2Fwaybill%2F2024%2F05%2F27%2F171678882566541e59bc80b1.png",
        },
      ],
    },
  });
};
