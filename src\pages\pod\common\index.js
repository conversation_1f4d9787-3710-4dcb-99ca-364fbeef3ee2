import Fetchers from "@/fetchers";
import { v4 as uuid } from "uuid";
import I18nSelect from "@/pages/pod/components/i18n-select";
import { defaultObjectProps, FabricObjectType } from "@/pages/pod/common/init-fabric-types";
import { fabric } from "fabric";
import Helper from "@/helpers";
import Utils from "@/utils";

export const config = {
  newObjectGap: { top: 20, left: 20 },
  historyMaxLength: 50,
  layerId: 0,
};

export let layerId = 0;

export const FabricEvent = {
  ObjectAdded: "object:added",
  ObjectRemoved: "object:removed",
  ObjectModified: "object:modified",
  SelectChange: "select:change",
  SelectionCreated: "selection:created",
  SelectionUpdated: "selection:updated",
  SelectionCleared: "selection:cleared",
};

export const LayerType = Object.freeze({
  TextBox: "textBox",
  CurvedText: "curvedText",
  ImagePlaceholder: "imagePlaceholder",
  vectorPlaceholder: "vectorPlaceholder",
  Map: "map",
  DynamicImage: "dynamicImage",
  DynamicVector: "dynamicVector",
  StarMap: "starMap",
  Layout: "layout",
});

export const LayerName = Object.freeze({
  [LayerType.TextBox]: `Text {{index}}`,
  [LayerType.ImagePlaceholder]: `Image Upload {{index}}`,
  [LayerType.DynamicImage]: `Image Selection {{index}}`,
  [LayerType.Layout]: `Layout {{index}}`,
});

export const LibraryType = {
  Image: "image",
  Color: "color",
  Font: "font",
};

export const OptionType = {
  Swatch: "Swatch",
  Dropdown: "Dropdown",
  TextInput: "TextInput",
  ImageUpload: "ImageUpload",
};

export const CustomFunctionType = {
  Text: "text",
  TextColor: "textColor",
  FontType: "fontType",
  DynamicImage: "dynamicImage",
  ImageColor: "imageColor",
  DynamicVector: "dynamicVector",
  DynamicVectorColor: "dynamicVectorColor",
  ChangeTemplate: "changeTemplate",
  AddProduct: "addProduct",
  LayoutVisibility: "layoutVisibility",
  UploadImage: "uploadImage",
};

export const loadImage = (src) => {
  return new Promise((resolve, reject) => {
    const image = new window.Image();
    image.onload = (event) => {
      resolve(event.target);
    };
    image.onerror = reject;
    image.src = src;
  });
};

export const checkImageSize = async (url, maxSizeKB = 300) => {
  try {
    new URL(url);
  } catch (error) {
    throw new Error(`"${url}" 无效的图片 URL`);
  }

  const response = await fetch(url, { method: "HEAD" });
  const size = response.headers.get("content-length");
  const sizeInKB = size / 1024;
  if (sizeInKB > maxSizeKB) {
    throw new Error(`"${url}" 图片大小超过 ${maxSizeKB}KB 限制: ${Math.round(sizeInKB)}KB`);
  }
  return url;
};

export async function createImageLibraryItems({
  previewTemplate,
  prodTemplate,
  thumbTemplate,
  optionNameTemplate = `Option`,
  optionNameTemplateI18nKey = `Option`,
}) {
  const items = [];
  for (let i = 1; i < 1000; i++) {
    const previewSrc = previewTemplate.replace(`{{index}}`, i);
    const prodSrc = prodTemplate.replace(`{{index}}`, i);
    const thumbSrc = (thumbTemplate || previewTemplate).replace(`{{index}}`, i);

    try {
      await Promise.all([checkImageSize(previewSrc), checkImageSize(thumbSrc, 100)]);
    } catch (error) {
      throw error;
    }

    const optionName = optionNameTemplate;
    const optionNameI18nKey = optionNameTemplateI18nKey;
    const result = await new Promise((resolve) => {
      Promise.all([loadImage(previewSrc), loadImage(prodSrc), loadImage(thumbSrc)])
        .then(([preview, prod, thumb]) => {
          items.push({
            preview_image: { src: preview.src, width: preview.width, height: preview.height },
            prod_image: { src: prod.src, width: prod.width, height: prod.height },
            thumb_image: { src: thumb.src, width: thumb.width, height: thumb.height },
            option_id: i,
            option_name: optionName,
            option_name_i18n_key: optionNameI18nKey,
            option_name_i18n_params: { index: i },
          });
          resolve({ hasMore: true });
        })
        .catch(() => {
          resolve({ hasMore: false });
        });
    });
    if (!result.hasMore) {
      break;
    }
  }
  return items;
}

export function getLayoutsFromObjects({ objects }) {
  const layouts = {};
  objects.forEach((object) => {
    if (object.extraData.layout) {
      layouts[object.extraData.layout.id] = object.extraData.layout;
    }
  });
  return Object.values(layouts);
}

export function createEmptyControl({ type }) {
  if ([OptionType.Dropdown, OptionType.Swatch].includes(type)) {
    return {
      id: null,
      type,
      label: "",
      required: false,
      help_text: "",
      css_class: "",
      hide_option: false,
      functions: [],
      conditions: [],
      options: [],
      library: null,
    };
  } else if ([OptionType.TextInput].includes(type)) {
    return {
      id: null,
      type,
      label: "",
      required: false,
      help_text: "",
      placeholder: "",
      initial_value: "",
      max_length: null,
      is_textarea: false,
      restriction: null,
      capitalization: null,
      css_class: "",
      hide_option: false,
      functions: [],
      conditions: [],
    };
  } else if ([OptionType.ImageUpload].includes(type)) {
    return {
      id: null,
      type,
      label: "",
      required: false,
      help_text: "",
      initial_image: "",
      button_text: "",
      button_class: "",
      image_min_width: null,
      image_min_height: null,
      hide_option: false,
      functions: [],
      conditions: [],
    };
  }
}

export function createDynamicImageOptions({
  hasLayout,
  library,
  libraryFullCategories,
  getControlId,
  layoutOption,
  layoutControl,
  controls,
  libraryFullItems,
  object,
  libraryItems,
}) {
  const createImageOptions = ({ items }) => {
    return items.map((item, index) => {
      return {
        id: uuid(),
        label: item.option_name,
        label_i18n_key: item.option_name_i18n_key,
        label_i18n_params: item.option_name_i18n_params,
        value: index + 1,
        prod_image: item.prod_image,
        preview_image: item.preview_image,
        thumb_image: item.thumb_image,
        image_option_id: item.option_id,
      };
    });
  };
  if (library?.list_type === "category") {
    const categories = libraryFullCategories.filter((x) => x.library_id === library.id);
    const categoryControl = createEmptyControl({ type: OptionType.Swatch });
    categoryControl.id = getControlId();
    categoryControl.label = library.name;
    categoryControl.label_i18n_key = library.name_i18n_key;
    categoryControl.label_i18n_params = library.name_i18n_params;
    categoryControl.options = [];
    categoryControl.library = library;
    if (hasLayout) {
      categoryControl.conditions = [
        { id: 1, action: "show", desired_value: [layoutOption?.id], watch_control: layoutControl.id },
      ];
    }
    controls.push(categoryControl);
    for (let j = 0; j < categories.length; j++) {
      const category = categories[j];
      const items = libraryFullItems.filter((item) => item.category_id === category.id);
      const control = createEmptyControl({ type: OptionType.Swatch });
      const categoryControlOption = {
        id: uuid(),
        label: category.name,
        value: j + 1,
        thumb_image: category.cover,
      };
      categoryControl.options.push(categoryControlOption);
      control.id = getControlId();
      control.elementId = object.extraData.elementId;
      control.label = category.name;
      control.label_i18n_key = category.name_i18n_key;
      control.label_i18n_params = category.name_i18n_params;
      control.options = createImageOptions({ items });
      control.category = category;
      control.functions = library
        ? [{ id: 1, type: CustomFunctionType.DynamicImage, image_id: object.extraData.elementId }]
        : [];
      control.conditions = [
        { id: 1, action: "show", desired_value: [categoryControlOption.id], watch_control: categoryControl.id },
      ];
      controls.push(control);
    }
  } else {
    const control = createEmptyControl({ type: OptionType.Swatch });
    control.id = getControlId();
    control.elementId = object.extraData.elementId;
    control.label = object.extraData.name;
    control.label_i18n_key = object.extraData.name_i18n_key;
    control.label_i18n_params = object.extraData.name_i18n_params;
    control.functions = library
      ? [{ id: 1, type: CustomFunctionType.DynamicImage, image_id: object.extraData.elementId }]
      : [];
    control.options = createImageOptions({ items: libraryItems });
    control.library = library;
    if (control.options.length === 0) {
      control.hide_option = true;
    }
    if (hasLayout) {
      control.conditions = [
        {
          id: 1,
          action: "show",
          desired_value: [layoutOption?.id],
          watch_control: layoutControl.id,
        },
      ];
    }
    controls.push(control);
  }
}

export async function createOptionSets({ objects }) {
  let id = 0;
  const controls = [];
  const getControlId = () => ++id;
  const libraryIds = Array.from(
    new Set(objects.filter((item) => item.extraData?.library).map((item) => item.extraData?.library?.id))
  );
  let libraryFullItems = [];
  let libraryFullCategories = [];
  if (libraryIds.length > 0) {
    [libraryFullItems, libraryFullCategories] = await Promise.all([
      Fetchers.getPodLibraryItems({ query: { library_ids: libraryIds.join(",") } }).then(
        (res) => res.data.data.items || []
      ),
      Fetchers.getPodLibraryCategories({ query: { library_ids: libraryIds.join(",") } }).then(
        (res) => res.data.data.items || []
      ),
    ]);
  }
  const layouts = getLayoutsFromObjects({ objects });
  let layoutControl;
  if (layouts.length > 0) {
    layoutControl = createEmptyControl({ type: OptionType.Dropdown });
    layoutControl.id = getControlId();
    layoutControl.required = true;
    layoutControl.label = `Layout`;
    layoutControl.functions = [{ id: 1, type: CustomFunctionType.LayoutVisibility }];
    layoutControl.options = layouts.map((item, index) => ({
      id: uuid(),
      value: index + 1,
      label: item.name,
      layout_id: item.elementId ?? item.optionId,
    }));
    controls.push(layoutControl);
  }
  for (let i = 0; i < objects.length; i++) {
    id++;
    const object = objects[i];
    const layerType = object.extraData.layerType;
    const library = object.extraData.library;
    const libraryItems = libraryFullItems.filter((x) => +x.library_id === +library?.id);
    const hasLayout = !!object.extraData.layout;
    let layoutOption;
    if (hasLayout) {
      layoutOption = layoutControl.options.find(
        (item) => item.layout_id === object.extraData.layout.elementId ?? object.extraData.layout.optionId
      );
    }
    if (layerType === LayerType.DynamicImage) {
      createDynamicImageOptions({
        hasLayout,
        library,
        libraryFullCategories,
        getControlId,
        layoutOption,
        layoutControl,
        controls,
        libraryFullItems,
        object,
        libraryItems,
      });
    } else if (layerType === LayerType.TextBox) {
      const control = createEmptyControl({ type: OptionType.TextInput });
      control.id = getControlId();
      control.elementId = object.extraData.elementId;
      control.label = object.extraData.name;
      control.label_i18n_key = object.extraData.name_i18n_key;
      control.label_i18n_params = object.extraData.name_i18n_params;
      control.functions = [{ id: 1, type: CustomFunctionType.Text, text_id: object.extraData.elementId }];
      if (hasLayout) {
        control.conditions = [
          { id: 1, action: "show", desired_value: [layoutOption?.id], watch_control: layoutControl.id },
        ];
      }
      controls.push(control);
    } else if (layerType === LayerType.ImagePlaceholder) {
      const control = createEmptyControl({ type: OptionType.ImageUpload });
      control.id = getControlId();
      control.elementId = object.extraData.elementId;
      control.label = object.extraData.name;
      control.label_i18n_key = object.extraData.name_i18n_key;
      control.label_i18n_params = object.extraData.name_i18n_params;
      control.functions = [{ id: 1, type: CustomFunctionType.UploadImage, image_id: object.extraData.elementId }];
      controls.push(control);
    }
  }
  return controls;
}

export function getFileNameFromUrl(url) {
  const filename = /[^/]*$/.exec(url)?.[0] || "";
  return filename.replace(/\.\w+$/i, "");
}

export function renderI18nSelect({ form, keyPath, name, value, onChange }) {
  return (
    <I18nSelect
      value={value}
      onChange={(value, option = { label: "", value: "" }) => {
        const i18nParams = form.getFieldValue({ keyPath, name: `${name}_i18n_params` });
        const label = i18nParams ? option.label.replace("{{index}}", i18nParams?.index) : option.label;
        onChange(null, { [name]: label });
        form.setFieldValue({ keyPath, name: `${name}_i18n_key`, value: option.value });
      }}
      style={{ width: `100%` }}
    ></I18nSelect>
  );
}

export function getFontsFromFabricObjects({ objects }) {
  const fonts = [];
  objects.forEach((object) => {
    if (object.extraData?.font) {
      const { family, url } = object.extraData.font;
      if (url && !fonts.some((font) => font.family === family && font.url === url)) {
        fonts.push({ family, url });
      }
    }
  });
  return fonts;
}

export async function loadFabricObjectFonts({ canvas, fonts }) {
  await Promise.allSettled(
    fonts.map(({ family, url }) => {
      return new Promise((resolve) => {
        if (url && !Array.from(document.fonts.values()).some((font) => font.family === family && font.url === url)) {
          const font = new FontFace(family, `url(${url})`);
          font.url = url;
          document.fonts.add(font);
          font.load().then(resolve);
        } else {
          resolve();
        }
      });
    })
  ).finally(() => {
    canvas.getObjects().forEach((object) => {
      if (object.extraData?.font) {
        object.set({ dirty: true });
      }
    });
    canvas.renderAll();
  });
}

export function createObjectFromJSON({ data, layerType, canvas }) {
  let object;
  if (data.type === FabricObjectType.CustomImageBox) {
    object = new fabric.CustomImageBox(data);
  } else if (data.type === FabricObjectType.CustomTextBox) {
    object = new fabric.CustomTextBox("A", data);
  }
  setObjectExtraData({ object, layerType, canvas });
  setObjectDefaultProps({ canvas, object, data, config });
  return object;
}

export function setObjectExtraData({ object, layerType, canvas }) {
  object.extraData = createExtraData({ object, layerType, canvas, layerId });
  object.zIndex = object.extraData.zIndex;
}

export function createExtraData({ object, layerType, uniqueId, canvas }) {
  const id = createObjectId({ canvas, layerId });
  layerType = layerType || object?.extraData?.layerType;
  const name = LayerName[layerType];
  return {
    id: uniqueId ?? Helper.uniqueId,
    name: name.replace("{{index}}", id),
    name_i18n_key: name,
    name_i18n_params: { index: id },
    createTime: Date.now(),
    zIndex: id,
    elementId: id,
    optionId: id,
    layerType,
  };
}

export function createObjectId({ canvas }) {
  const objects = canvas.getObjects();
  if (objects?.length > 0) {
    layerId = Math.max(...objects.map((a) => a.extraData.elementId ?? 0)) + 1;
  } else {
    layerId++;
  }
  return layerId;
}

export function setObjectDefaultProps({ canvas, object, data }) {
  const activeObject = canvas.getActiveObject() || Utils.lastOne(canvas.getObjects());
  object.set({ ...defaultObjectProps, ...data });
  if (canvas.width > canvas.height) {
    object.scale(getObjectScale({ canvas, object, key: "height" }));
  } else {
    object.scale(getObjectScale({ canvas, object, key: "width" }));
  }
  if (activeObject) {
    object.set({
      top: activeObject.top + config.newObjectGap.top,
      left: activeObject.left + config.newObjectGap.left,
    });
  } else {
    object.center();
  }
  return object;
}

export function getObjectScale({ canvas, object, key, factor = 0.4, max = 500 }) {
  const size = (canvas[key] / canvas.getZoom()) * factor;
  return (size > max ? max : size) / object[key];
}

export function convertToPreviewCanvasJson(json) {
  const hasObjects = (obj) => Utils.hasOwnProperty(obj, "objects");

  const hasExtraData = (obj) => Utils.hasOwnProperty(obj, "extraData");

  const hasUpload = (obj) => Utils.hasOwnProperty(obj, "upload");

  const hasPreviewImage = (obj) => Utils.hasOwnProperty(obj, "preview_image");

  const hasSrc = (obj) => Utils.hasOwnProperty(obj, "src");

  const format = (obj) => {
    obj.objects.forEach((item) => {
      if (hasObjects(item)) {
        format(item);
      }

      if (hasExtraData(item) && hasSrc(item) && hasUpload(item.extraData) && hasPreviewImage(item.extraData.upload)) {
        const { preview_image } = item.extraData.upload;

        if (item.src !== preview_image) {
          item.src = preview_image;
          if (hasObjects(item)) {
            item.objects.forEach((o) => {
              if (hasSrc(o)) {
                o.src = preview_image;
              }
            });
          }
        }
      }
    });
  };

  if (!hasObjects(json)) return json;

  const result = Utils.cloneDeep(json);
  format(result);

  return result;
}
