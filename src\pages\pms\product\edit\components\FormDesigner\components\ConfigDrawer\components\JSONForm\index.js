import { useRef, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON> } from "react";

import CommonJSONForm from "components/common/JSONForm";
import Enums from "@/pages/pms/product/edit/components/FormDesigner/common/enums";

import DictionarySelect from "./components/DictionarySelect";
import FontSelectorWithPreview from "./components/FontSelectorWithPreview";
import CheckboxGroup from "./components/CheckboxGroup";
import DropdownOptionsSelector from "./components/DropdownOptionsSelector";
import OptionsSelector from "./components/OptionsSelector";
import PictureUpload from "./components/PictureUpload";
import GlassesTemplateSelect from "./components/GlassesTemplateSelect";
import GlassesPictureUpload from "./components/GlassesPictureUpload";
import PodEdit from "./components/PodEdit";
import SpotifyImageUpload from "./components/SpotifyImageUpload";
import StarMapPreview from "./components/StarMapPreview";
import Conditions from "./components/Conditions";

const createExtendComponents = (form<PERSON><PERSON>, component<PERSON><PERSON>, { widgetForm, ...props }) => ({
  [Enums.ConfigComponents.DictionarySelect]: ({ item }) => <DictionarySelect {...item?.props} {...props} />,
  [Enums.ConfigComponents.FontSelectorWithPreview]: ({ item }) => (
    <FontSelectorWithPreview {...item?.props} {...props} />
  ),
  [Enums.ConfigComponents.CheckboxGroup]: ({ item }) => <CheckboxGroup {...item?.props} {...props} />,
  [Enums.ConfigComponents.DropdownOptionsSelector]: ({ item }) => (
    <DropdownOptionsSelector {...item?.props} {...props} componentKey={componentKey} />
  ),
  [Enums.ConfigComponents.OptionsSelector]: ({ item }) => <OptionsSelector {...item?.props} {...props} />,
  [Enums.ConfigComponents.PictureUpload]: ({ item }) => <PictureUpload {...item?.props} {...props} />,
  [Enums.ConfigComponents.GlassesTemplateSelect]: ({ item }) => <GlassesTemplateSelect {...item?.props} {...props} />,
  [Enums.ConfigComponents.GlassesPictureUpload]: ({ item }) => <GlassesPictureUpload {...item?.props} {...props} />,
  [Enums.ConfigComponents.PodEdit]: ({ item }) => <PodEdit formRef={formRef} {...item?.props} {...props} />,
  [Enums.ConfigComponents.SpotifyImageUpload]: ({ item }) => (
    <SpotifyImageUpload formRef={formRef} {...item?.props} {...props} />
  ),
  [Enums.ConfigComponents.StarMapPreview]: ({ item }) => (
    <StarMapPreview formRef={formRef} {...item?.props} {...props} />
  ),
  [Enums.ConfigComponents.Conditions]: ({ item }) => (
    <Conditions {...item?.props} {...props} componentKey={componentKey} widgetForm={widgetForm} />
  ),
});

function JSONForm(props, ref) {
  const { data, identifiers, componentKey, widgetForm } = props;
  const formRef = useRef(null);

  const extendComponents = createExtendComponents(formRef, componentKey, { identifiers, widgetForm });

  const normalizePath = (path) => {
    if (Array.isArray(path)) {
      return path;
    }
    return path.includes(",") ? path.split(",") : path.split(".");
  };

  const hasPath = (obj, path) => {
    if (!obj || !path) return false;

    const keys = normalizePath(path);
    let current = obj;

    for (let key of keys) {
      if (current === null || current === undefined || !Object.prototype.hasOwnProperty.call(current, key.trim())) {
        return false;
      }
      current = current[key.trim()];
    }

    return true;
  };

  const getValue = (obj, path, defaultValue = undefined) => {
    if (!obj || !path) return defaultValue;

    const keys = normalizePath(path);
    let current = obj;

    for (let key of keys) {
      if (current === null || current === undefined || !Object.prototype.hasOwnProperty.call(current, key.trim())) {
        return defaultValue;
      }
      current = current[key.trim()];
    }

    return current === undefined ? defaultValue : current;
  };

  const handleValuesChange = (changedValues, allValues) => {
    if (data?.props?.watch) {
      Object.entries(data.props.watch).forEach(([fieldPath, config]) => {
        const pathToCheck = Array.isArray(fieldPath) ? fieldPath.join(",") : fieldPath;

        if (hasPath(changedValues, pathToCheck)) {
          const value = getValue(changedValues, pathToCheck);
          if (config.type === "sync" && config.target) {
            formRef.current.setFieldValue(config.target, value);
          }
        }
      });
    }
  };

  useImperativeHandle(ref, () => {
    return formRef.current;
  });

  return (
    <CommonJSONForm ref={formRef} data={data} extendComponents={extendComponents} onValuesChange={handleValuesChange} />
  );
}

JSONForm = forwardRef(JSONForm);

export default JSONForm;
