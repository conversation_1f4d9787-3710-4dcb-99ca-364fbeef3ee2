.componentItem {
  position: relative;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
  background-color: #fff;
  transition: all 0.3s;

  &:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .componentActions {
      opacity: 1;
    }
  }

  &.selected {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);

    .componentActions {
      opacity: 1;
    }
  }

  &.dragging {
    opacity: 0.5;
    z-index: 1;
  }

  .componentContent {
    min-height: 40px;
  }

  .componentActions {
    position: absolute;
    top: 4px;
    right: 4px;
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.3s;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    padding: 2px;
    z-index: 11;

    .dragHandle {
      cursor: move;
    }
  }

  .ghostLine {
    width: 100%;
    height: 2px;
    background-color: #1890ff;
  }
}
