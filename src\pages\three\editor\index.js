import { Fragment, useEffect, useRef, useState } from "react";
import * as THREE from "three";
import { DR<PERSON><PERSON>oader, G<PERSON>FLoader, DragControls, OrbitControls } from "three/addons";
import { ControlWrapper, Form, FormControl } from "@/components/react-form-x";
import styles from "./index.module.scss";
import Helper from "@/helpers";
import { App, Button, Divider, Input, Modal, Popconfirm, Tabs } from "antd";
import Fetchers from "@/fetchers";
import Utils from "@/utils";
import { CloseOutlined } from "@ant-design/icons";
import cloneDeep from "lodash.clonedeep";
import { View, Views } from "@/components/views";
import FormControlLabel from "@/pages/pod/components/form-control-label";
import { restoreScene as commonRestoreScene, restoreModel } from "@/pages/three/common";
import { uniqueId } from "@/components/react-form-x/common";

const ModalViews = {
  AddScene: "deleteScene",
};

function ThreeEditor(props) {
  const [modelData, setModelData] = useState(null);
  const [groupIndex, setGroupIndex] = useState(0);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalView, setModalView] = useState(ModalViews.AddScene);
  const [controlRange, setControlRange] = useState({ scale: { min: 0.01, max: 3 } });
  const { message: toast, modal } = App.useApp();
  const mountRef = useRef();
  const formRef = useRef();

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current, restoreScene, updateDragControls, currentSceneToJSON, controlRange };

  function withNumber({ element, number }) {
    return (
      <div style={{ display: "flex", alignItems: "center" }}>
        {element}
        <span style={{ width: 50, flexShrink: 0, textAlign: "end" }}>{number}</span>
      </div>
    );
  }

  function renderControl({ label, keyPath, name, inputProps = { type: "range", step: 0.001, min: -2, max: 2 } }) {
    const isRotation = keyPath.includes("rotation");
    return (
      <div key={name} className={styles.formRow}>
        <div className={styles.label}>{label}</div>
        <div className={styles.control}>
          <FormControl
            keyPath={keyPath}
            name={name}
            render={(props) => (
              <ControlWrapper
                {...props}
                defaultValue={inputProps.min}
                render={(props) =>
                  withNumber({
                    element: (
                      <input
                        {...props}
                        {...inputProps}
                        value={props.value}
                        onChange={(event) => {
                          props.onChange(event, { [props.name]: +event.target.value });
                        }}
                      />
                    ),
                    number: (isRotation ? THREE.MathUtils.radToDeg(props.value) : +props.value).toFixed(2),
                  })
                }
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
      </div>
    );
  }

  function renderPositionControls({ label, keyPath, inputProps = { type: "range", step: 0.001, min: -2, max: 2 } }) {
    return ["x", "y", "z"].map((name) => {
      return renderControl({ label: label + name + ":", keyPath, name, inputProps });
    });
  }

  function renderScaleControl({ modelName }) {
    return (
      <div className={styles.formRow}>
        <div className={styles.label}>缩放s: </div>
        <div className={styles.control}>
          <FormControl
            keyPath={[modelName, "scale"]}
            name="x"
            render={(props) => (
              <ControlWrapper
                {...props}
                defaultValue={controlRange.scale.min}
                render={(props) =>
                  withNumber({
                    element: (
                      <input
                        {...props}
                        type="range"
                        step="0.0001"
                        min={controlRange.scale.min}
                        max={controlRange.scale.max}
                        onChange={(event) => {
                          const model = formRef.current?.value[modelName];
                          const value = +event.target.value;
                          model.scale.x = model.scale.y = model.scale.z = value;
                          props.onChange(event, { [props.name]: value });
                        }}
                      />
                    ),
                    number: (+props.value).toFixed(2),
                  })
                }
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
      </div>
    );
  }

  function colorToHexString(color) {
    return color?.getHexString ? "#" + color.getHexString() : null;
  }

  function modelToJSON(model) {
    return {
      uuid: model.uuid,
      name: model.name,
      position: { x: model.position.x, y: model.position.y, z: model.position.z },
      rotation: { x: model.rotation.x, y: model.rotation.y, z: model.rotation.z },
      scale: model.scale.y,
      userData: cloneDeep(model.userData),
    };
  }

  function currentSceneToJSON({ name } = {}) {
    const { scene, model, camera, ambientLight, directionalLight } = paramsRef.current;
    const groupId = modelData?.coordinate_data?.groups[groupIndex].id || uniqueId();
    const groupName = name || modelData?.coordinate_data?.groups[groupIndex].name;
    return {
      id: groupId,
      name: groupName,
      scene: { background: colorToHexString(scene.background) },
      camera: {
        fov: camera.fov,
        aspect: camera.aspect,
        near: camera.near,
        far: camera.far,
        position: { x: camera.position.x, y: camera.position.y, z: camera.position.z },
      },
      model: modelToJSON(model),
      clonedModels: scene.children
        .filter((item) => item.userData.type === "model" && item.userData.cloned)
        .map((model) => modelToJSON(model)),
      ambientLight: { color: colorToHexString(ambientLight.color), intensity: ambientLight.intensity },
      directionalLight: {
        color: colorToHexString(directionalLight.color),
        intensity: directionalLight.intensity,
        position: {
          x: directionalLight.position.x,
          y: directionalLight.position.y,
          z: directionalLight.position.z,
        },
      },
    };
  }

  function restoreScene({ group }) {
    const { scene, model, camera, ambientLight, directionalLight } = paramsRef.current;

    commonRestoreScene({ scene, model, camera, ambientLight, directionalLight, group });

    // 恢复拖拽控制器
    updateDragControls();
  }

  function updateDragControls() {
    const { scene, dragControls } = paramsRef.current;
    if (dragControls) {
      dragControls.objects = scene.children.filter((x) => x.userData.type === "model");
    }
  }

  function updateForm({ group }) {
    const models = {};
    [group.model, ...group.clonedModels].forEach((item) => {
      models[item.name] = paramsRef.current.scene.children.find((x) => x.name === item.name);
    });
    formRef.current?.updateValue(models);
  }

  function addScene({ name }) {
    const { scene, model, defaultModelJSON, ambientLight, directionalLight } = paramsRef.current;
    scene.clear();
    restoreModel({ model, data: defaultModelJSON });
    scene.add(ambientLight, directionalLight, model);
    const group = currentSceneToJSON();
    group.name = name;
    modelData.coordinate_data.groups.push(group);
    setModelData({ ...modelData });
    setGroupIndex(modelData.coordinate_data.groups.length - 1);
    formRef.current?.updateValue({ model });
  }

  function removeScene(index) {
    modelData.coordinate_data.groups = modelData.coordinate_data.groups.filter((x, i) => +i !== +index);
    setModelData({ ...modelData });
    setGroupIndex(0);
    restoreScene({ group: modelData.coordinate_data.groups[0] });
  }

  useEffect(() => {
    Helper.pageLoading(true);

    (async () => {
      // 请求模板数据
      const query = Utils.getQueryParams(window.location.href);
      const modelData = await Fetchers.get3dModelData({ id: query.id }).then((res) => res.data.data);
      const mapData = modelData.cut_parts[0];

      // 场景
      const scene = new THREE.Scene();
      scene.background = new THREE.Color("#fff");
      paramsRef.current.scene = scene;
      window.scene = scene;

      // 监听子元素变化更新控制器
      const updateDragControls = paramsRef.current.updateDragControls;
      scene.addEventListener("childadded", () => {
        updateDragControls();
      });
      scene.addEventListener("childremoved", () => {
        updateDragControls();
      });

      // 相机
      const camera = new THREE.PerspectiveCamera(
        60,
        mountRef.current.clientWidth / mountRef.current.clientHeight,
        0.1,
        1000
      );
      camera.position.set(0, 0, -1.3);
      camera.lookAt(0, 0, 0);
      paramsRef.current.camera = camera;
      window.camera = camera;

      // 渲染器
      const renderer = new THREE.WebGLRenderer({ antialias: true, preserveDrawingBuffer: true });
      renderer.outputColorSpace = THREE.SRGBColorSpace;
      renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientWidth);
      renderer.setPixelRatio(window.devicePixelRatio);
      mountRef.current.appendChild(renderer.domElement);
      paramsRef.current.renderer = renderer;

      // 环境光
      const ambientLight = new THREE.AmbientLight("#fff", 1);
      scene.add(ambientLight);
      paramsRef.current.ambientLight = ambientLight;

      // 平行光
      const directionalLight = new THREE.DirectionalLight("#fff", 2.6);
      directionalLight.position.set(0, 0, -1.3);
      scene.add(directionalLight);
      paramsRef.current.directionalLight = directionalLight;

      // 轨道控制器
      const orbitControls = new OrbitControls(camera, renderer.domElement);
      orbitControls.minDistance = 0;
      orbitControls.maxDistance = 3;
      // // 阻尼效果
      // orbitControls.enableDamping = true;
      // // 阻尼系数
      // orbitControls.dampingFactor = 0.3;
      // 禁止鼠标右键平移
      orbitControls.enablePan = false;
      paramsRef.current.orbitControls = orbitControls;
      window.orbitControls = orbitControls;

      paramsRef.current.setColorMapParams = setColorMapParams;
      paramsRef.current.setTextureParams = setTextureParams;
      paramsRef.current.getCheckedMaps = getCheckedMaps;

      // 创建 DRACOLoader 实例
      const dracoLoader = new DRACOLoader();

      // 设置 DRACO 解码器文件的路径
      dracoLoader.setDecoderPath("https://www.gstatic.com/draco/v1/decoders/");

      // 加载模型
      const gltfLoader = new GLTFLoader();
      gltfLoader.setDRACOLoader(dracoLoader);

      function handleDragStart() {
        orbitControls.enabled = false;
      }

      function handleDrag(event) {
        formRef.current?.updateValue({ [event.object.name]: event.object });
      }

      function handleDragEnd() {
        orbitControls.enabled = true;
      }

      function setColorMapParams(texture) {
        setTextureParams(texture);
        texture.colorSpace = THREE.SRGBColorSpace;
      }

      function setTextureParams(texture) {
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.flipY = false;
        texture.userData = { mimeType: "image/png" };
      }

      function getCheckedMaps({ material }) {
        return Object.entries(material)
          .filter(([key, value]) => key.toLowerCase().endsWith("map") && value)
          .map(([key]) => key);
      }

      // 贴图加载器
      const textureLoader = new THREE.TextureLoader();
      paramsRef.current.textureLoader = textureLoader;

      const [gltf, maps] = await Promise.all([
        new Promise((resolve, reject) => {
          gltfLoader.load(modelData.model_file, resolve, null, reject);
        }),
        new Promise((resolve, reject) => {
          Promise.all([
            new Promise((resolve, reject) => {
              textureLoader.load(
                mapData.texture_map,
                (texture) => {
                  setColorMapParams(texture);
                  resolve(texture);
                },
                null,
                reject
              );
            }),
            new Promise((resolve, reject) => {
              textureLoader.load(
                mapData.normal_map,
                (texture) => {
                  setTextureParams(texture);
                  resolve(texture);
                },
                null,
                reject
              );
            }),
            new Promise((resolve, reject) => {
              textureLoader.load(
                mapData.ao_map,
                (texture) => {
                  setTextureParams(texture);
                  resolve(texture);
                },
                null,
                reject
              );
            }),
          ])
            .then(([map, normalMap, aoMap]) => {
              resolve({ map, normalMap, aoMap });
            })
            .catch(reject);
        }),
      ]);

      const model = gltf.scene;
      const meshes = [];

      paramsRef.current.gltf = gltf;
      paramsRef.current.model = model;
      paramsRef.current.maps = maps;

      window.gltf = gltf;
      window.THREE = THREE;
      window.model = model;

      // console.log(model);

      model.position.set(0, 0, 0);
      model.rotation.y = THREE.MathUtils.degToRad(180);

      model.traverse((child) => {
        if (child.isMesh) {
          child.material.metalness = 0;
          child.material.roughness = 1;
          child.material.color = new THREE.Color("#fff");
          meshes.push(child);
        }
      });

      // console.log(meshes);

      const meshOut = meshes.find((x) => x.name === "mesh_out");
      meshOut.material = new THREE.MeshStandardMaterial({
        color: "#fff",
        map: maps.map,
        normalMap: maps.normalMap,
        aoMap: maps.aoMap,
      });
      paramsRef.current.meshOut = meshOut;

      const meshIn = meshes.find((x) => x.name === "mesh_in");
      meshIn.material = new THREE.MeshStandardMaterial({ color: "#fff" });

      function normalizeModel(model, targetSize = 1) {
        // 1. 计算包围盒
        const box = new THREE.Box3().setFromObject(model);
        const size = new THREE.Vector3();
        box.getSize(size);
        const center = new THREE.Vector3();
        box.getCenter(center);

        // 2. 居中
        model.position.sub(center);

        // 3. 缩放
        const maxAxis = Math.max(size.x, size.y, size.z);
        model.scale.multiplyScalar(targetSize / maxAxis);
      }
      normalizeModel(model, 1);

      const { controlRange } = paramsRef.current;
      controlRange.scale.min = model.scale.x / 5;
      controlRange.scale.max = model.scale.x * 5;
      setControlRange({ ...controlRange });

      model.name = "model";
      model.userData.type = "model";
      model.userData.cloned = false;
      paramsRef.current.defaultModelJSON = modelToJSON(model);
      scene.add(model);

      // 拖拽控制器
      const dragControls = new DragControls(model.children, camera, renderer.domElement);
      dragControls.transformGroup = true;
      dragControls.addEventListener("dragstart", handleDragStart);
      dragControls.addEventListener("drag", handleDrag);
      dragControls.addEventListener("dragend", handleDragEnd);
      paramsRef.current.dragControls = dragControls;
      window.dragControls = dragControls;

      // 从接口数据恢复场景
      if (modelData.coordinate_data?.groups?.length) {
        const group = modelData.coordinate_data.groups[0];

        paramsRef.current.restoreScene({ group });

        // 等渲染完再赋值
        setModelData(modelData);
        Promise.resolve().then(() => {
          scene.children
            .filter((item) => item.userData.type === "model" && item.userData.cloned)
            .forEach((clonedModel) => {
              formRef.current?.updateValue({ [clonedModel.name]: clonedModel });
            });
        });
      } else {
        modelData.coordinate_data = {
          groups: [{ ...paramsRef.current.currentSceneToJSON(), name: "正面" }],
        };
        setModelData({ ...modelData });
      }

      formRef.current?.updateValue({
        backgroundColor: colorToHexString(scene.background),
        [model.name]: model,
        checkedMaps: getCheckedMaps({ material: meshOut.material }),
        ambientLight,
        directionalLight,
        ambientLightColor: colorToHexString(ambientLight.color),
        directionalLightColor: colorToHexString(directionalLight.color),
      });

      // 动画循环
      const animate = () => {
        requestAnimationFrame(animate);
        renderer.render(scene, camera);
      };
      animate();

      Helper.pageLoading(false);
    })().finally(() => {
      // Helper.pageLoading(false);
    });

    // 处理窗口大小变化
    const handleResize = async () => {
      const { camera, renderer } = paramsRef.current;
      camera.aspect = 1;
      camera.updateProjectionMatrix();
      renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientWidth);
    };
    window.addEventListener("resize", handleResize);

    return () => {
      const { renderer, orbitControls, dragControls } = paramsRef.current;
      window.removeEventListener("resize", handleResize);
      // eslint-disable-next-line react-hooks/exhaustive-deps
      mountRef.current?.removeChild(renderer.domElement);
      renderer?.dispose();
      orbitControls?.dispose();
      dragControls?.dispose();
    };
  }, []);

  return (
    <>
      <div className={styles.page}>
        <div className={styles.canvasWrapper}>
          <div ref={mountRef}></div>
        </div>
        <div style={{ flex: 1, maxWidth: 600, overflow: "auto" }}>
          <div>
            <Tabs
              key={groupIndex}
              activeKey={groupIndex}
              items={modelData?.coordinate_data?.groups.map((x, i) =>
                i === 0
                  ? { key: i, label: "正面", closable: false }
                  : { key: i, label: x.name ?? `场景${i}`, closable: false }
              )}
              type="editable-card"
              size="small"
              rootClassName={styles.tabs}
              moreIcon={null}
              onEdit={(key, action) => {
                // add第一个参数是event, remove第一个参数是key
                const { scene, model, ambientLight, directionalLight, defaultModelJSON } = paramsRef.current;
                if (action === "add") {
                  setModalView(ModalViews.AddScene);
                  setModalOpen(true);
                } else if (action === "remove") {
                }
              }}
              onChange={(activeKey) => {
                activeKey = +activeKey;
                const currentGroup = modelData.coordinate_data.groups[groupIndex];
                modelData.coordinate_data.groups[groupIndex] = { ...currentSceneToJSON(), name: currentGroup.name };
                setModelData({ ...modelData });
                setGroupIndex(activeKey);
                const nextGroup = modelData.coordinate_data.groups[activeKey];
                restoreScene({ group: nextGroup });
                updateForm({ group: nextGroup });
              }}
            ></Tabs>
          </div>
          <Form
            ref={formRef}
            updateMode="mutation"
            onSubmit={(event, values) => {
              try {
                modelData.coordinate_data.groups[groupIndex] = currentSceneToJSON();
                // console.log(modelData.coordinate_data);
                const query = Utils.getQueryParams(window.location.href);
                Fetchers.save3dModelData({ id: query.id, data: { coordinate_data: modelData.coordinate_data } })
                  .then(() => {
                    toast.success("保存成功");
                  })
                  .catch(() => {
                    toast.error("保存失败");
                  });
              } catch (err) {
                throw err;
              }
            }}
            className={styles.form}
          >
            {groupIndex > 0 ? (
              <Popconfirm
                title="确定删除吗？"
                onConfirm={() => {
                  removeScene(groupIndex);
                }}
              >
                <div className={styles.removeSceneButton}>
                  <CloseOutlined />
                </div>
              </Popconfirm>
            ) : null}
            <div className={styles.formContent}>
              {/*<div className={styles.grid} style={{ "--grid-column": 2 }}>
              <Button type="primary" block>
                更换模型
              </Button>
              <Button type="primary" block style={{ position: "relative" }}>
                <input
                  type="file"
                  className={styles.selectFile}
                  onChange={async (event) => {
                    const file = event.target.files[0];
                    const { textureLoader, meshOut } = paramsRef.current;
                    const url = URL.createObjectURL(file);
                    textureLoader.load(url, (texture) => {
                      paramsRef.current.setColorMapParams(texture);
                      meshOut.material.map = texture;
                      paramsRef.current.maps.map = texture;
                      URL.revokeObjectURL(url);
                    });
                  }}
                />
                <span>更换纹理贴图</span>
              </Button>
              <Button type="primary" block style={{ position: "relative" }}>
                <input
                  type="file"
                  className={styles.selectFile}
                  onChange={async (event) => {
                    const file = event.target.files[0];
                    const { textureLoader, meshOut } = paramsRef.current;
                    const url = URL.createObjectURL(file);
                    textureLoader.load(url, (texture) => {
                      paramsRef.current.setTextureParams(texture);
                      meshOut.material.normalMap = texture;
                      paramsRef.current.maps.normalMap = texture;
                      URL.revokeObjectURL(url);
                    });
                  }}
                />
                <span>更换法线贴图</span>
              </Button>
              <Button type="primary" block style={{ position: "relative" }}>
                <input
                  type="file"
                  className={styles.selectFile}
                  onChange={async (event) => {
                    const file = event.target.files[0];
                    const { textureLoader, meshOut } = paramsRef.current;
                    const url = URL.createObjectURL(file);
                    textureLoader.load(url, (texture) => {
                      paramsRef.current.setTextureParams(texture);
                      meshOut.material.aoMap = texture;
                      paramsRef.current.maps.aoMap = texture;
                      URL.revokeObjectURL(url);
                    });
                  }}
                />
                <span>更换AO贴图</span>
              </Button>
            </div>*/}
              <div style={{ display: "flex", alignItems: "center" }}>
                <div className={styles.title}>背景色：</div>
                <div>
                  <FormControl
                    name="backgroundColor"
                    render={(props) => (
                      <input
                        {...props}
                        type="color"
                        onChange={(event) => {
                          paramsRef.current.scene.background = new THREE.Color(event.target.value);
                          props.onChange(event);
                        }}
                      />
                    )}
                  ></FormControl>
                </div>
              </div>
              <div className={styles.title}>模型：</div>
              <div className={styles.formControls}>
                {renderPositionControls({
                  label: "位置",
                  keyPath: ["model", "position"],
                  inputProps: { type: "range", step: 0.001, min: -2, max: 2 },
                })}
                {renderPositionControls({
                  label: "旋转",
                  keyPath: ["model", "rotation"],
                  inputProps: {
                    type: "range",
                    step: 0.001,
                    min: THREE.MathUtils.degToRad(-200),
                    max: THREE.MathUtils.degToRad(200),
                  },
                })}
                {renderScaleControl({ modelName: "model" })}
              </div>
              {modelData?.coordinate_data?.groups?.[groupIndex]?.clonedModels?.map((item, i) => {
                const key = item.uuid;
                const modelName = item.name;
                return (
                  <Fragment key={key}>
                    <div
                      className={styles.title}
                      style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
                    >
                      <span>克隆模型{i + 1}：</span>
                      <span
                        style={{ display: "flex", padding: 4, cursor: "pointer" }}
                        onClick={() => {
                          const { scene, dragControls } = paramsRef.current;
                          const objects = scene.children.filter((x) => x.uuid !== item.uuid).map((x) => x.clone());
                          scene.clear();
                          scene.add(...objects);
                          dragControls.objects = objects.filter((x) => x.userData.type === "model");
                          modelData.coordinate_data.groups[groupIndex].clonedModels = objects
                            .filter((x) => x.userData.type === "model" && x.userData.cloned)
                            .map((model) => modelToJSON(model));
                          setModelData({ ...modelData });
                        }}
                      >
                        <CloseOutlined />
                      </span>
                    </div>
                    <div className={styles.formControls}>
                      {renderPositionControls({
                        label: "位置",
                        keyPath: [modelName, "position"],
                        inputProps: { type: "range", step: 0.001, min: -2, max: 2 },
                      })}
                      {renderPositionControls({
                        label: "旋转",
                        keyPath: [modelName, "rotation"],
                        inputProps: {
                          type: "range",
                          step: 0.001,
                          min: THREE.MathUtils.degToRad(-200),
                          max: THREE.MathUtils.degToRad(200),
                        },
                      })}
                      {renderScaleControl({ modelName })}
                    </div>
                  </Fragment>
                );
              })}
              <div>
                <Divider style={{ marginTop: 5, marginBottom: 0 }}></Divider>
              </div>
              {/*<div>
              <FormControl
                name="checkedMaps"
                render={(props) => (
                  <ControlWrapper
                    {...props}
                    render={({ name, value, onChange }) => {
                      return (
                        <Checkbox.Group
                          value={value}
                          options={[
                            { label: "纹理贴图", value: "map" },
                            { label: "法线贴图", value: "normalMap" },
                            { label: "AO贴图", value: "aoMap" },
                          ]}
                          onChange={(checkedValue) => {
                            const { meshOut } = paramsRef.current;
                            const material = new THREE.MeshStandardMaterial({
                              color: "#fff",
                            });
                            checkedValue.forEach((name) => {
                              material[name] = paramsRef.current.maps[name];
                            });
                            meshOut.material = material;
                            const checkedMaps = paramsRef.current.getCheckedMaps({ material });
                            onChange(null, { [name]: checkedMaps });
                          }}
                        ></Checkbox.Group>
                      );
                    }}
                  ></ControlWrapper>
                )}
              ></FormControl>
            </div>*/}
              <div style={{ display: "flex", alignItems: "center", gap: 5 }}>
                <div style={{ flex: 1 }}>
                  {renderControl({
                    label: "环境光:",
                    keyPath: ["ambientLight"],
                    name: "intensity",
                    inputProps: { type: "range", step: 0.1, min: 0, max: 5 },
                  })}
                </div>
                <div>
                  <FormControl
                    name="ambientLightColor"
                    render={(props) => {
                      return (
                        <input
                          {...props}
                          type="color"
                          onChange={(event) => {
                            paramsRef.current.ambientLight.color = new THREE.Color(event.target.value);
                            props.onChange(event);
                          }}
                          style={{ width: 27 }}
                        />
                      );
                    }}
                  ></FormControl>
                </div>
              </div>
              <div style={{ display: "flex", alignItems: "center", gap: 5 }}>
                <div style={{ flex: 1 }}>
                  {renderControl({
                    label: "平行光:",
                    keyPath: ["directionalLight"],
                    name: "intensity",
                    inputProps: { type: "range", step: 0.1, min: 0, max: 5 },
                  })}
                </div>
                <div>
                  <FormControl
                    name="directionalLightColor"
                    render={(props) => {
                      return (
                        <input
                          {...props}
                          type="color"
                          onChange={(event) => {
                            paramsRef.current.directionalLight.color = new THREE.Color(event.target.value);
                            props.onChange(event);
                          }}
                          style={{ width: 27 }}
                        />
                      );
                    }}
                  ></FormControl>
                </div>
              </div>
              <div>
                <Divider style={{ marginTop: 0, marginBottom: 0 }}></Divider>
              </div>
              <div>
                <div>操作方法：</div>
                <div style={{ display: "grid", gridTemplateColumns: "repeat(2, minmax(0, 1fr))" }}>
                  <div>按住鼠标左键拖动位置</div>
                  <div>按住鼠标右键拖动旋转</div>
                  <div>鼠标滚轮控制相机远近</div>
                  <div>其他属性使用右侧控件</div>
                </div>
              </div>
              <div style={{ display: "flex", flexDirection: "column", gap: 10 }}>
                <Button
                  type="default"
                  block
                  onClick={() => {
                    const { scene, model, dragControls } = paramsRef.current;
                    const number = (modelData.coordinate_data?.groups?.[groupIndex]?.clonedModels?.length || 0) + 1;
                    const name = `clonedModel${number}`;
                    const clonedModel = model.clone();
                    clonedModel.name = name;
                    clonedModel.userData.type = "model";
                    clonedModel.userData.cloned = true;
                    ["x", "y"].forEach((key) => {
                      clonedModel.position[key] -= 0.1;
                    });
                    dragControls.objects.push(clonedModel);
                    scene.add(clonedModel);
                    formRef.current?.updateValue({ [name]: clonedModel });
                    modelData.coordinate_data.groups[groupIndex].clonedModels.push(modelToJSON(clonedModel));
                    setModelData({ ...modelData });
                  }}
                >
                  克隆当前模型
                </Button>
                <Button type="primary" block htmlType="submit">
                  保存坐标数据
                </Button>
                {/*<Button
                type="primary"
                block
                onClick={async () => {
                  const { renderer } = paramsRef.current;
                  renderer.domElement.toBlob((blob) => {
                    const filename = "image.png";
                    const file = new File([blob], filename);
                    const url = URL.createObjectURL(file);
                    const link = document.createElement("a");
                    link.download = filename;
                    link.href = url;
                    link.click();
                    URL.revokeObjectURL(url);
                  });
                }}
              >
                拍照
              </Button>*/}
              </div>
            </div>
          </Form>
        </div>
      </div>
      <Modal
        open={modalOpen}
        onCancel={() => {
          setModalOpen(false);
        }}
        footer={null}
      >
        <Views active={modalView}>
          <View name={ModalViews.AddScene}>
            <Form
              onSubmit={(event, values) => {
                addScene(values);
                setModalOpen(false);
              }}
              style={{ display: "grid", gap: 16 }}
            >
              <div>
                <FormControl
                  name="name"
                  rule={{ required: true }}
                  render={(props, { rule }) => (
                    <ControlWrapper
                      {...props}
                      render={(props) => (
                        <FormControlLabel label="添加场景：" required={rule.required}>
                          <Input {...props} />
                        </FormControlLabel>
                      )}
                    ></ControlWrapper>
                  )}
                ></FormControl>
              </div>
              <div>
                <Button type="primary" htmlType="submit">
                  提交
                </Button>
              </div>
            </Form>
          </View>
        </Views>
      </Modal>
    </>
  );
}

export default ThreeEditor;
