import { useState, useCallback, useRef } from "react";
import { Card, Form, Input, Checkbox, Select, Tabs, Button, Modal } from "antd";
import { PlusOutlined } from "@ant-design/icons";

import styles from "./index.module.scss";
import Utils from "@/utils";
import Helper from "@/helpers";

import HeaderActionsEditor from "./components/HeaderActionsEditor";
import ToolbarActionsEditor from "./components/ToolbarActionsEditor";
import ColumnConfigEditor from "./components/ColumnConfigEditor";
import EditTable from "components/common/EditTable";

const breadcrumbsColumns = [
  {
    dataIndex: "title",
    title: "标题",
    width: 150,
    editable: {
      component: "Input",
      props: {
        placeholder: "请输入标题",
      },
    },
  },
  {
    dataIndex: "url",
    title: "链接",
    width: 150,
    editable: {
      component: "Input",
      props: {
        placeholder: "请输入链接",
      },
    },
  },
];

const sidebarActionsColumns = [
  {
    dataIndex: "title",
    title: "标题",
    width: 120,
    editable: {
      component: "Input",
      props: {
        placeholder: "请输入标题",
      },
    },
  },
  {
    dataIndex: "icon",
    title: "图标",
    width: 120,
    editable: {
      component: "Input",
      props: {
        placeholder: "请输入图标URL",
      },
    },
  },
  {
    dataIndex: ["command", "type"],
    title: "命令类型",
    width: 120,
    editable: {
      component: "Select",
      props: {
        placeholder: "请选择命令类型",
        options: [
          { label: "请求", value: "request" },
          { label: "弹窗", value: "modal" },
          { label: "抽屉", value: "drawer" },
          { label: "跳转", value: "redirect" },
        ],
      },
    },
  },
];

function PropertyPanel({ pageData, onUpdatePageData }) {
  const [form] = Form.useForm();
  const columnConfigEditorRef = useRef();
  const [activeKey, setActiveKey] = useState("basic");
  const [modalState, setModalState] = useState({
    visible: false,
    title: "",
    type: "",
    columns: [],
    defaultNewRow: {},
    data: [],
  });

  const initialValues = {
    breadcrumbs: pageData?.breadcrumbs || [],
    dynamicDataApi: pageData?.dynamicDataApi || "",
    headerActions: pageData?.headerActions || [],
    sidebarActions: pageData?.sidebarActions || [],
    toolbarActions: pageData?.toolbarActions || [],
    tableProps: {
      bordered: pageData?.tableProps?.bordered || false,
      rowKey: pageData?.tableProps?.rowKey || "id",
      rowSelection: pageData?.tableProps?.rowSelection || false,
      size: pageData?.tableProps?.size || "small",
      virtual: pageData?.tableProps?.virtual || false,
      columns: pageData?.tableProps?.columns || [],
    },
  };

  const handleValuesChange = useCallback(
    (changedValues, allValues) => {
      const newPageData = Utils.cloneDeep({
        ...pageData,
        ...allValues,
      });
      onUpdatePageData(newPageData);
    },
    [pageData, onUpdatePageData]
  );

  const openConfigModal = useCallback(
    (type, title, columns, defaultNewRow, fieldName) => {
      const formValues = form.getFieldsValue();
      let data = [];

      if (typeof fieldName === "string") {
        data = formValues[fieldName] || [];
      } else if (Array.isArray(fieldName)) {
        let value = formValues;
        for (const key of fieldName) {
          value = value[key];
          if (value === undefined) {
            value = [];
            break;
          }
        }
        data = value;
      }

      setModalState({
        visible: true,
        title,
        type,
        columns,
        defaultNewRow,
        data,
        fieldName,
      });
    },
    [form]
  );

  const closeConfigModal = useCallback(() => {
    setModalState((prev) => ({ ...prev, visible: false }));
  }, []);

  // 保存配置弹窗数据
  const saveConfigModal = useCallback(
    async (newData) => {
      const { fieldName, type } = modalState;

      // 如果是表格列配置，从ColumnConfigEditor获取数据
      if (type === "tableColumns" && columnConfigEditorRef.current) {
        try {
          const values = await columnConfigEditorRef.current.validateFields();
          newData = values.columns || [];
        } catch (error) {
          console.error("表单验证失败:", error);
          return;
        }
      }

      const formValues = form.getFieldsValue();

      // 根据字段名更新数据
      if (typeof fieldName === "string") {
        formValues[fieldName] = newData;
      } else if (Array.isArray(fieldName)) {
        let value = formValues;
        for (let i = 0; i < fieldName.length - 1; i++) {
          if (!value[fieldName[i]]) {
            value[fieldName[i]] = {};
          }
          value = value[fieldName[i]];
        }
        value[fieldName[fieldName.length - 1]] = newData;
      }

      form.setFieldsValue(formValues);
      handleValuesChange({}, formValues);
      closeConfigModal();
    },
    [modalState, form, handleValuesChange, closeConfigModal]
  );

  const tabItems = [
    {
      key: "basic",
      label: "基础配置",
      children: (
        <Card title="基础配置" size="small" bordered={false}>
          <Form.Item label="数据源API" name="dynamicDataApi">
            <Input placeholder="请输入数据源API地址" />
          </Form.Item>

          <Form.Item label="面包屑导航" name="breadcrumbs">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() =>
                openConfigModal(
                  "breadcrumbs",
                  "面包屑导航配置",
                  breadcrumbsColumns,
                  { title: "", url: "" },
                  "breadcrumbs"
                )
              }
              className={styles.configButton}
            >
              添加面包屑导航
            </Button>
            {pageData?.breadcrumbs?.length > 0 && (
              <div className={styles.configSummary}>已配置 {pageData.breadcrumbs.length} 项</div>
            )}
          </Form.Item>
        </Card>
      ),
    },
    {
      key: "table",
      label: "表格配置",
      children: (
        <Card title="表格属性" size="small" bordered={false}>
          <Form.Item label="行数据唯一标识" name={["tableProps", "rowKey"]}>
            <Input placeholder="请输入行数据唯一标识" />
          </Form.Item>

          <Form.Item label="表格大小" name={["tableProps", "size"]}>
            <Select
              options={[
                { label: "小", value: "small" },
                { label: "中", value: "middle" },
                { label: "大", value: "large" },
              ]}
            />
          </Form.Item>

          <Form.Item name={["tableProps", "bordered"]} valuePropName="checked">
            <Checkbox>显示边框</Checkbox>
          </Form.Item>

          <Form.Item name={["tableProps", "rowSelection"]} valuePropName="checked">
            <Checkbox>允许选择行</Checkbox>
          </Form.Item>

          <Form.Item name={["tableProps", "virtual"]} valuePropName="checked">
            <Checkbox>启用虚拟滚动</Checkbox>
          </Form.Item>

          <Form.Item label="表格列配置" name={["tableProps", "columns"]}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() =>
                openConfigModal("tableColumns", "添加表格列", [], { dataIndex: "", title: "", width: 100 }, [
                  "tableProps",
                  "columns",
                ])
              }
              className={styles.configButton}
            >
              添加表格列
            </Button>
            {pageData?.tableProps?.columns?.length > 0 && (
              <div className={styles.configSummary}>已配置 {pageData.tableProps.columns.length} 列</div>
            )}
          </Form.Item>
        </Card>
      ),
    },
    {
      key: "toolbar",
      label: "操作配置",
      children: (
        <>
          <Card title="头部操作按钮" size="small" bordered={false}>
            <Form.Item name="headerActions">
              <HeaderActionsEditor />
            </Form.Item>
          </Card>

          <Card title="工具栏操作" size="small" bordered={false} style={{ marginTop: 16 }}>
            <Form.Item name="toolbarActions">
              <ToolbarActionsEditor />
            </Form.Item>
          </Card>

          {/* <Card title="侧边栏操作" size="small" bordered={false} style={{ marginTop: 16 }}>
              <Form.Item name="sidebarActions">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() =>
                    openConfigModal(
                      "sidebarActions",
                      "侧边栏操作配置",
                      sidebarActionsColumns,
                      {
                        title: "",
                        icon: "",
                        command: { type: "request" },
                      },
                      "sidebarActions"
                    )
                  }
                  className={styles.configButton}
                >
                  编辑侧边栏操作
                </Button>
                {pageData?.sidebarActions?.length > 0 && (
                  <div className={styles.configSummary}>已配置 {pageData.sidebarActions.length} 项</div>
                )}
              </Form.Item>
            </Card> */}
        </>
      ),
    },
  ];

  return (
    <div className={styles.propertyPanel}>
      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onValuesChange={handleValuesChange}
        className={styles.form}
      >
        <Tabs activeKey={activeKey} onChange={setActiveKey} items={tabItems} />
      </Form>

      {/* 配置弹窗 */}
      <Modal
        title={modalState.title}
        open={modalState.visible}
        onCancel={closeConfigModal}
        onOk={() => saveConfigModal(modalState.data)}
        style={{ top: 50 }}
        width={1300}
        destroyOnClose
      >
        {modalState.type === "tableColumns" ? (
          <ColumnConfigEditor
            // ref={columnConfigEditorRef}
            value={modalState.data}
            onChange={(newData) => setModalState((prev) => ({ ...prev, data: newData }))}
          />
        ) : (
          <EditTable
            value={modalState.data}
            columns={modalState.columns}
            onChange={(newData) => setModalState((prev) => ({ ...prev, data: newData }))}
            defaultNewRow={modalState.defaultNewRow}
            isShowAddRow={true}
          />
        )}
      </Modal>
    </div>
  );
}

export default PropertyPanel;
