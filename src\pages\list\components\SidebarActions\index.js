import { lazy, Suspense, forwardRef } from "react";
import { Spin } from "antd";
import Enums from "enums";

const Components = {
  [Enums.SideBarActionType.SidebarList]: lazy(() => import("./components/SidebarList")),
  [Enums.SideBarActionType.SidebarFilter]: lazy(() => import("./components/SidebarFilter")),
};

function SidebarActions(props, ref) {
  const { data } = props;
  const { type = "sidebarFilter" } = data;
  const Component = Components[type];

  return (
    <Suspense
      fallback={
        <div style={{ textAlign: "center" }}>
          <Spin />
        </div>
      }
    >
      <Component ref={ref} {...props} />
    </Suspense>
  );
}

SidebarActions = forwardRef(SidebarActions);

export default SidebarActions;
