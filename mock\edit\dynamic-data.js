module.exports = async (req, res) => {
  const initialValues = {
    input1: "input1",
    input4: "input4",
    input5: "1",
    input2: "input2",
    input3: "input3",
    input: "123456",
    selectCascader: "1",
    selectCascader2: "4",
    selectCascaded: "1",
    treeSelect: ["leaf-1"],
    inputAddon: "jeuliaoutlet",
    inputNumber: 1,
    cascader: ["shanxi", "xian", "yanta"],
    datePicker: "2023-01-01",
    rangePicker: ["2023-01-01", "2023-01-31"],
    textarea: "123\n456\n789",
    select: "3",
    selectMultiple: ["2", "3"],
    selectSearchRemote: ["DRN0392", "DRN0720"],
    radio: 3,
    checkbox: ["1", "2", "3"],
    tree: ["1-2-2"],
    switch: true,
    richTextEditor: `<p>Hello, RichTextEditor!</p><p>这是富文本框的默认值</p>`,
    follow: true,
    transfer: ["1", "2", "3", "4"],
    jsonEditor: JSON.stringify({
      label: "JSON编辑器",
      component: "JSONEditor",
      props: {},
      rules: [{ required: true, message: "This is a required field" }],
    }),
    imageUpload: [...Array(2).fill(null)].map((a, i) => {
      const id = (i + 1).toString();
      return {
        uid: id,
        fid: id,
        name: "photo",
        status: "done",
        url: "https://res2022.jeulia.com/product/2/1/1000x1000/62fde0f091112.jpg",
      };
    }),
    fileUpload: [...Array(2).fill(null)].map((a, i) => {
      const id = (i + 1).toString();
      return {
        uid: id,
        fid: id,
        name: `photo${id}.jpg`,
        status: "done",
        url: "https://res2022.jeulia.com/product/2/1/1000x1000/62fde0f091112.jpg",
      };
    }),
    cameraPhotoUpload: [...Array(1).fill(null)].map((a, i) => {
      const id = (i + 1).toString();
      return {
        uid: id,
        fid: id,
        name: "photo",
        status: "done",
        url: "https://res2022.jeulia.com/product/2/1/1000x1000/62fde0f091112.jpg",
      };
    }),
    edit_table: Array(2)
      .fill(null)
      .map((_, i) => ({
        image: "https://img.yzcdn.cn/vant/leaf.jpg",
        // image_list: [
        //   {
        //     uid: "1",
        //     fid: "1",
        //     name: "photo",
        //     status: "done",
        //     url: "https://res2022.jeulia.com/product/2/1/1000x1000/62fde0f091112.jpg",
        //   },
        // ],
        sku: `SKU${i + 1}`,
        num: i + 1,
        type: "jp",
        type_remote: "DRH0130",
      })),
  };

  return res.status(200).json({
    success: true,
    data: { initialValues },
  });
};
