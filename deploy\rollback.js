const fs = require("node:fs/promises");
const { runCommand, backupDir, sendWechatMessage } = require("./common");
const config = require("./config");
const { updateNginxConfig } = require("./helper");
const moment = require("moment");

async function main() {
  const rollbackCount = parseInt(process.argv?.[2] ?? 1);
  const dirs = await fs.readdir(backupDir);
  const stats = await Promise.all(
    dirs.map(async (dirname) => {
      const pathname = `${backupDir}/${dirname}`;
      const stat = await fs.stat(pathname);
      return { dirname, pathname, stat };
    })
  );
  stats.sort((a, b) => b.stat.birthtime - a.stat.birthtime);
  const currentStat = stats[rollbackCount];
  if (!currentStat) {
    console.error(`Out of backup range, backup max index is ${stats.length - 1}`);
    return;
  }
  const commit = currentStat.dirname;
  const source = currentStat.pathname;
  const cwd = process.cwd();
  const app = cwd.split("/")[cwd.split("/").length - 1];
  const deployDir = `${config.publicPath}/${app}/deploy`;
  const tempDeployDir = `${config.publicPath}/temp/react-erp/deploy`;

  console.log(``);
  console.log(`version: ${commit}\n`);

  // 获取最新部署脚本并复制到临时文件夹
  await runCommand(`git fetch`);
  await runCommand(`git remote prune origin`);
  await runCommand(`git pull`);
  await runCommand(`mkdir -p ${tempDeployDir}`);
  await runCommand(`cp -R ${deployDir}/* ${tempDeployDir}`);

  // 回滚代码
  await runCommand(`git reset --hard ${commit}`);
  await runCommand(`git clean -df`);
  // await runCommand(`npm i`);
  await runCommand(`cp -R ${source}/build/* ${config.publicPath}/${app}/build`);

  // 更新nginx
  await updateNginxConfig({ app });

  const time = moment(currentStat.stat.birthtime).utcOffset(8).format(`YYYY-MM-DD HH:mm:ss`);
  await sendWechatMessage({ content: `ERP6已回滚至版本：${commit} ${time}` });
}

try {
  main().then();
} catch (e) {
  console.error(e);
}
