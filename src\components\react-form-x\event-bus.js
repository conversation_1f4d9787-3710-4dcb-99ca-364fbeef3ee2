let Events = [];

const EventBus = Object.freeze({
  addEventListener(name, handler, id = Math.random().toString().substring(2)) {
    Events.push({ name, handler, id });
  },

  removeEventListener(name, handler) {
    if (typeof handler === "string") {
      Events = Events.filter((item) => !(item.name === name && item.id === handler));
    } else {
      Events = Events.filter((item) => !(item.name === name && item.handler === handler));
    }
  },

  dispatchEvent(name, ...args) {
    const events = Events.filter((item) => item.name === name);
    for (const event of events) {
      event.handler(...args);
    }
  },
});

export default EventBus;
