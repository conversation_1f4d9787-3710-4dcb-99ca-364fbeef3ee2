const common = require("./common");

module.exports = {
  "GET /rest/v1/ams/menu": (req, res) => {
    res.json({
      status: "00",
      success: true,
      data: {
        items: [
          {
            label: "Homepage",
            key: "menu0",
            url: "/",
            icon_url: "https://static.bizseas.com/static/icon/file.svg",
            title: "Homepage",
          },
          // {
          //   label: "Menu 1",
          //   key: "menu1",
          //   icon_url: "https://static.bizseas.com/static/icon/file.svg",
          //   children: [
          //     {
          //       label: "Sub Menu 1-0",
          //       key: "menu2",
          //       icon_url: "https://static.bizseas.com/static/icon/file.svg",
          //       url: "/menu1-0",
          //     },
          //     {
          //       label: "Sub Menu 1-1",
          //       key: "menu3",
          //       icon_url: "https://static.bizseas.com/static/icon/file.svg",
          //       url: "/menu1-1",
          //     },
          //   ],
          // },
          // {
          //   label: "Menu 2",
          //   key: "menu4",
          //   icon_url: "https://static.bizseas.com/static/icon/file.svg",
          //   children: [
          //     {
          //       label: "Sub Menu 2-0",
          //       key: "menu5",
          //       icon_url: "https://static.bizseas.com/static/icon/file.svg",
          //       url: "/list/admin/user/list",
          //     },
          //     {
          //       label: "Sub Menu 2-1",
          //       key: "test",
          //       icon_url: "https://static.bizseas.com/static/icon/file.svg",
          //       url: "/test",
          //     },
          //   ],
          // },
          {
            label: "Order List",
            key: "menu7",
            icon_url: "https://static.bizseas.com/static/icon/file.svg",
            url: "/list/order-list",
            active_menu_url: ["/edit/order"],
          },
          {
            label: "弹窗中心",
            key: "menu8",
            icon_url: "https://static.bizseas.com/static/icon/file.svg",
            children: [
              {
                label: "弹窗模版",
                key: "menu9",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/list/popup-template",
                active_menu_url: ["/popup/edit-popup/template"],
              },
              {
                label: "弹窗管理",
                key: "menu10",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/list/popup-management",
                active_menu_url: ["/popup/edit-popup/template"],
              },
            ],
          },
          {
            label: "产品管理",
            key: common.uniqueId(),
            icon_url: "https://static.bizseas.com/static/icon/file.svg",
            children: [
              {
                label: "模板管理",
                key: common.uniqueId(),
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/pod/templates",
                active_menu_url: ["/pod/edit-template", "/pod/edit-template-bg"],
              },
              {
                label: "素材管理",
                key: common.uniqueId(),
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/pod/libraries",
                active_menu_url: ["/pod/edit-library", "/pod/edit-category"],
              },
              {
                label: "选项管理",
                key: common.uniqueId(),
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/pod/option-sets",
                active_menu_url: ["/pod/edit-option-set"],
              },
            ],
          },
          {
            label: "DMS",
            key: "menu15",
            icon_url: "https://static.bizseas.com/static/icon/file.svg",
            children: [
              {
                label: "点数入仓",
                key: "menu11",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/dms/purchase-stock-in",
              },
              {
                label: "质检",
                key: "menu12",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/dms/quality-control",
              },
              {
                label: "发货",
                key: "menu14",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/dms/shipout",
              },
              {
                label: "订单详情",
                key: "menu_dms_order_detail",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/dms/order/detail",
              },
            ],
          },
          {
            label: "OMS",
            key: "menu_oms",
            icon_url: "https://static.bizseas.com/static/icon/file.svg",
            children: [
              {
                label: "订单详情",
                key: "menu_oms_order_detail",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/oms/order/detail?type=oms",
                active_menu_url: ["/oms/order/detail"],
              },
            ],
          },
          {
            label: "ticket",
            key: "menu16",
            icon_url: "https://static.bizseas.com/static/icon/file.svg",
            children: [
              {
                label: "ticket详情",
                key: "menu17",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/ticket/ticket/play",
              },
              {
                label: "ticket编辑",
                key: "menu18",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/ticket/ticket/update",
              },
            ],
          },
          {
            label: "pms",
            key: "pms",
            icon_url: "https://static.bizseas.com/static/icon/file.svg",
            children: [
              {
                label: "商品编辑",
                key: "pms-product-edit",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/pms/product/edit",
              },
            ],
          },
          {
            label: "网站配置项",
            key: "menu13",
            icon_url: "https://static.bizseas.com/static/icon/file.svg",
            url: "/configuration/website",
          },
          {
            label: "搜索页面",
            key: "search",
            icon_url: "https://static.bizseas.com/static/icon/file.svg",
            url: "/search/detail/product",
          },
          {
            label: "iframe页面",
            key: "iframe",
            icon_url: "https://static.bizseas.com/static/icon/file.svg",
            url: "/iframe/iframe-page",
          },
          {
            label: "低代码",
            key: "low-code",
            icon_url: "https://static.bizseas.com/static/icon/file.svg",
            children: [
              {
                label: "低代码平台",
                key: "low-code-entry",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/low-code",
              },
              {
                label: "编辑页编辑器",
                key: "low-code-edit-page",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/low-code/edit-page",
              },
              {
                label: "列表页编辑器",
                key: "list-page-editor",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/low-code/list-page",
              },
              {
                label: "低代码编辑器",
                key: "low-code-editor",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/low-code/editor",
              },
            ],
          },
          {
            label: "shop",
            key: "shop",
            icon_url: "https://static.bizseas.com/static/icon/file.svg",
            children: [
              {
                label: "商品排序",
                key: "product-sort",
                icon_url: "https://static.bizseas.com/static/icon/file.svg",
                url: "/shop/product-sort",
              },
            ],
          },
        ],
      },
    });
  },
};
