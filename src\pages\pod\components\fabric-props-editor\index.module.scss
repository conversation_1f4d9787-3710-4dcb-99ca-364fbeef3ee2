.props {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;

  .layerItems {
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(auto-fill, 90px);

    > * {
      width: 90px;
      height: 90px;
      border-radius: 8px;
      box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #aaa;
      user-select: none;
      cursor: pointer;
    }

    .icon {
      display: flex;
      width: 70px;
      height: 50px;
    }

    .icon1 {
      position: relative;
      top: 4px;
    }

    .label {
      font-weight: 600;
      line-height: 1.2;
    }
  }

  .transform {
    display: flex;
    flex-direction: column;
    gap: 16px;

    form {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    label {
      white-space: nowrap;
    }

    .formRow {
      display: flex;
      gap: 16px;
      grid-template-columns: repeat(3, minmax(0px, 1fr));

      > * {
        flex: 1;
      }
    }

    .privateProps {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      align-items: end;

      > * {
        min-width: 150px;
      }

      [class~="form-x-control"] {
        flex: 1;
      }

      [class~="ant-upload-wrapper"] {
        display: block;

        * {
          display: block;
        }
      }

      .controlWrapper {
        flex-wrap: wrap;
      }

      .label {
        width: 100%;
      }

      .textBoxContent {
        grid-column: 1 / 5;
      }
    }

    .transformProps {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16px;

      label {
        width: 13px;
        white-space: nowrap;
        flex-shrink: 0;
        text-align: center;
      }
    }

    .skews {
      display: flex;
      gap: 8px;

      label {
        white-space: nowrap;
      }
    }

    .buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }

  .controlWrapper {
    display: flex;
    align-items: center;
    gap: 2px;
  }

  .iconButton {
    width: 32px;
    height: 32px;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;

    > * {
      display: flex;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
    }
  }

  .antBorder {
    display: block;
    width: 100%;
    box-sizing: border-box;
    outline: 0;
    border: 1px solid #d9d9d9;
    padding: 4px 11px;
    font-family: inherit;
    font-size: inherit;
    resize: vertical;
    transition: all 0.2s linear;
    background: #fff;
    border-radius: 2px;

    &:hover {
      border-color: #4096ff;
      background-color: #fff;
    }

    &:focus {
      border-color: #1677ff;
      box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
      background-color: #fff;
    }
  }

  .uploadFont {
    align-self: end;
    display: flex;
    gap: 8px;

    > * {
      flex: 1;
      padding: 0;

      &:nth-child(1) {
        flex: 0.7;
      }
    }
  }

  .resizeControl {
    flex: 1;
  }
}

@media (width < 1500px) {
  .props {
    .transform {
      .privateProps {
        display: grid;
        grid-template-columns: repeat(2, 1fr);

        .controlWrapper {
          flex-wrap: nowrap;
        }

        .label {
          width: auto;
        }

        .textBoxContent {
          grid-column: 1 / 3;
        }
      }
    }
  }
}
