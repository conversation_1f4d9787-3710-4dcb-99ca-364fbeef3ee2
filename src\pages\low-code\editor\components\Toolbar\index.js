import styles from "./index.module.scss";
import { useState, useCallback } from "react";
import { Button, Space, Layout, Modal, Form, Input, Select } from "antd";
import { SaveOutlined } from "@ant-design/icons";

import { sanitizeItems } from "../../utils";

import JSONEditor from "components/common/JSONEditor";
import Export from "@/pages/low-code/edit-page/components/Export";

const { Header } = Layout;

const initialValue = {
  props: {
    id: "form",
    layout: "vertical",
  },
};

function Toolbar({ components, onSave, pageConfig, showExportButton, showInnerSaveButton }) {
  const [saveForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saveModalVisible, setSaveModalVisible] = useState(false);

  // 导出JSON
  const exportJSON = useCallback(() => {
    const data = {
      // content: {
      //   type: "json",
      //   component: "JSONComponents",
      //   children: []
      // },
      children: [
        {
          component: "Form",
          type: "json",
          formItems: sanitizeItems(components),
        },
      ],
    };
    return data;
  }, [components]);

  // 保存页面
  const handleSave = useCallback(async () => {
    try {
      // const { props, ...values } = await saveForm.validateFields();
      setLoading(true);

      const data = {
        children: [
          {
            // ...values,
            // props: JSON.parse(props),
            props: initialValue.props,
            component: "Form",
            type: "json",
            formItems: sanitizeItems(components),
          },
        ],
      };

      console.log("保存页面", data);
      setSaveModalVisible(false);

      onSave?.(data);
      // saveForm.resetFields();
      return data;
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [components, onSave]);

  const handleInnerSave = useCallback(async () => {
    try {
      // const values = await saveForm.validateFields();
      setLoading(true);

      const data = {
        // ...values,
        children: sanitizeItems(components),
      };

      console.log("保存页面", data);
      setSaveModalVisible(false);

      onSave?.(data);
      // saveForm.resetFields();
      return data;
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [components, onSave]);

  const validate = () => saveForm.current?.validateFields("props");

  return (
    <>
      <Header className={styles.header}>
        <div className={styles.title}>低代码编辑器</div>
        <Space>
          {showExportButton && <Export data={exportJSON()} />}
          <Button type="primary" icon={<SaveOutlined />} onClick={showInnerSaveButton ? handleInnerSave : handleSave}>
            保存
          </Button>
        </Space>
      </Header>

      <Modal
        title="保存页面"
        open={saveModalVisible}
        onOk={handleSave}
        onCancel={() => setSaveModalVisible(false)}
        confirmLoading={loading}
      >
        <Form form={saveForm} layout="vertical">
          <Form.Item
            name="props"
            label="Form Props"
            rules={[{ required: true, message: "请输入页面标题" }]}
            initialValue={JSON.stringify(initialValue.props)}
          >
            <JSONEditor validate={validate} />
          </Form.Item>
          {/* <Form.Item name="title" label="页面标题" rules={[{ required: true, message: "请输入页面标题" }]}>
            <Input placeholder="请输入页面标题" />
          </Form.Item>
          <Form.Item name="description" label="页面描述">
            <Input.TextArea placeholder="请输入页面描述" rows={4} />
          </Form.Item> */}
        </Form>
      </Modal>
    </>
  );
}

export default Toolbar;
