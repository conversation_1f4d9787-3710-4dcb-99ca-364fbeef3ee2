.canvasPanel {
  min-height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 16px;
  transition: all 0.3s;

  &.isOver {
    background-color: #e6f7ff;
    border: 1px dashed #1890ff;
  }

  .empty {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .componentList {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}
