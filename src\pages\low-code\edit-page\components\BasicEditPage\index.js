import classNames from "classnames";
import { Card, Empty } from "antd";

import Breadcrumbs from "components/common/Breadcrumbs";
import JSONComponents from "components/common/JSONComponent";
import SubmitButton from "components/common/Button";
import Helper from "@/helpers";

import styles from "./index.module.scss";

function BasicEditPage({ data }) {
  const contentChildren = data?.content?.children;
  const hasContent = Array.isArray(contentChildren) && contentChildren.length > 0;

  return (
    <div className={styles.editPage}>
      <div className={classNames("page-header", styles.pageHeader)}>
        <Breadcrumbs data={data?.breadcrumbs} />
        <div className="page-header-actions">
          {data?.actions?.map((item, index) => {
            return (
              <SubmitButton
                key={index}
                type="primary"
                {...item.props}
                command={item.command}
                onClick={async () => {
                  await Helper.commandHandler({ command: item.command });
                }}
              >
                {item.title}
              </SubmitButton>
            );
          })}
        </div>
      </div>
      <div className={styles.content}>
        <Card>{hasContent ? <JSONComponents data={data?.content} /> : <Empty description={false} />}</Card>
      </div>
    </div>
  );
}

export default BasicEditPage;
