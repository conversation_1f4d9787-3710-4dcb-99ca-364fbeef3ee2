import { useState, useRef, useCallback, useMemo } from "react";
import { arrayMove } from "@dnd-kit/sortable";

import {
  generateUniqueId,
  flattenTree,
  sanitizeItems,
  buildItems,
  removeComponent,
  moveComponent,
  isInContainer,
  cloneComponent,
} from "../utils";

import { DroppableIds } from "../enums";
import { afterRender } from "functions";

function getDndData(prop) {
  return prop?.data?.current ?? {};
}

function createGhost({ id, isGroup = false, relKey }) {
  return {
    id,
    component: "ghost",
    isGroup,
    relKey,
  };
}

function useDragAndDrop({ components, setComponents, setSelectedComponent, onChange }) {
  const [dragState, setDragState] = useState({
    activeId: null,
    dragData: null,
    isOverDropZone: false,
  });

  const ghostInsertedRef = useRef(false);
  const currentDragComponentRef = useRef(null);

  const flattenedComponents = useMemo(() => flattenTree(components), [components]);

  // 判断是否在画布内
  const isOverCanvas = useCallback(
    (over) => {
      if (!over) return false;

      return over.id === DroppableIds.Canvas || flattenedComponents.some((item) => item.id === over.id);
    },
    [flattenedComponents]
  );

  const handleDragStart = useCallback(
    (event) => {
      const { active } = event;
      const activeData = getDndData(active);

      setDragState((prev) => ({
        ...prev,
        activeId: active.id,
        dragData: activeData,
      }));

      if (activeData?.fromSidebar) {
        const { component } = activeData;
        const clonedComponent = cloneComponent(component);

        // 特属处理Col组件的id
        // if (clonedComponent?.children) {
        //   clonedComponent.children.forEach((component, index) => {
        //     if (component.component === "Col") {
        //       component.id = generateUniqueId(`${component.component}${index}`);
        //     }
        //   });
        // }
        currentDragComponentRef.current = {
          ...clonedComponent,
          id: generateUniqueId(component.component),
        };

        return;
      }

      const { component } = activeData;

      currentDragComponentRef.current = component;

      // 递归查找并替换为 ghost
      const replaceWithGhost = (items) => {
        return items.map((item) => {
          // 如果找到了被拖拽的组件
          if (item.id === active.id) {
            return createGhost({
              id: active.id,
              isGroup: item.component_config?.isGroup,
            });
          }

          // 如果是组容器，递归查找其子项
          if (item.component_config?.isGroup) {
            const childrenName = item.component_config?.childrenName || "children";
            const updatedChildren = replaceWithGhost(item[childrenName] || []);

            // 如果子项有变化，说明找到并替换了目标组件
            if (updatedChildren.some((child) => child.component === "ghost")) {
              return {
                ...item,
                [childrenName]: updatedChildren,
              };
            }
          }

          return item;
        });
      };

      setComponents((prev) => replaceWithGhost(prev));
    },
    [setComponents]
  );

  const handleDragMove = useCallback(
    (event) => {
      const { active, over, collisions } = event;
      if (!over) return;

      let hovered = null;
      if (collisions) {
        for (let i = collisions.length - 1; i >= 0; i--) {
          if (collisions[i]?.data?.hovered) {
            hovered = collisions[i];
            break;
          }
        }
      }

      const activeData = getDndData(active);
      const activeId = activeData?.fromSidebar ? currentDragComponentRef.current?.id : active.id;

      if (activeId === over.id) return;

      if (hovered?.data?.isActiveContainer) {
        const containerData = hovered.data?.droppableContainer?.data?.current;

        if (!containerData?.component?.component_config?.isGroup) return;

        const componentConfig = containerData?.component?.component_config;
        const { childrenName = "children" } = componentConfig ?? {};

        const dropKey = hovered.id;

        const removeComponentAndGhost = (items) => {
          return items
            .map((item) => {
              // 移除ghost和原组件(不是从Sidebar拖拽来的)
              if (item.component === "ghost" || (!activeData?.fromSidebar && item.id === activeId)) {
                return null;
              }
              if (item.component_config?.isGroup) {
                const itemChildrenName = item.component_config?.childrenName || "children";
                const children = item[itemChildrenName] || [];
                const filteredChildren = removeComponentAndGhost(children).filter(Boolean);
                return {
                  ...item,
                  [itemChildrenName]: filteredChildren,
                };
              }
              return item;
            })
            .filter(Boolean);
        };

        setComponents((prev) => {
          const itemsWithoutComponent = removeComponentAndGhost(prev);
          const ghost = createGhost({
            id: activeId,
            isGroup: currentDragComponentRef.current?.component_config?.isGroup,
            relKey: active.id,
          });

          // 在目标组容器中添加ghost
          const addGhostToTarget = (items) => {
            return items.map((item) => {
              if (item.id === dropKey) {
                const children = item[childrenName] || [];
                return {
                  ...item,
                  [childrenName]: [...children, ghost],
                };
              }
              if (item.component_config?.isGroup) {
                const itemChildrenName = item.component_config?.childrenName || "children";
                return {
                  ...item,
                  [itemChildrenName]: addGhostToTarget(item[itemChildrenName] || []),
                };
              }
              return item;
            });
          };

          return addGhostToTarget(itemsWithoutComponent);
        });
      } else if (isInContainer(components, activeId)) {
        if (over?.id === DroppableIds.Canvas || (!over?.data?.current?.isGroup && over?.data?.current?.index > -1)) {
          const removeFromGroup = (items) => {
            return items.map((item) => {
              if (item.component_config?.isGroup) {
                const childrenName = item.component_config?.childrenName || "children";
                const children = item[childrenName] || [];

                const filteredChildren = children.filter((child) => child.id !== activeId);

                if (filteredChildren.length !== children.length) {
                  return {
                    ...item,
                    [childrenName]: filteredChildren,
                  };
                }

                return {
                  ...item,
                  [childrenName]: removeFromGroup(children),
                };
              }
              return item;
            });
          };

          // 移动 ghost 到目标位置
          setComponents((prev) => {
            const newItems = removeFromGroup(prev);
            const ghost = createGhost({
              id: activeId,
              isGroup: currentDragComponentRef.current?.component_config?.isGroup,
              relKey: active.id,
            });

            let targetIndex;
            if (over?.data?.current?.index !== undefined) {
              targetIndex = over.data.current.index;
            } else {
              targetIndex = newItems.length;
            }

            return [...newItems.slice(0, targetIndex), ghost, ...newItems.slice(targetIndex, newItems.length)];
          });
        }
      }
    },
    [components, setComponents]
  );

  const handleDragOver = useCallback(
    (event) => {
      const { active, over } = event;
      const activeData = getDndData(active);
      const overData = getDndData(over);

      if (!isOverCanvas(over) && activeData?.fromSidebar) {
        // 拖拽到画布外，寻找ghost并删除
        setComponents((prev) => {
          const newItems = activeData?.fromSidebar
            ? removeComponent({ items: prev, component: "ghost" })
            : prev.filter((item) => item.component !== "ghost");

          return newItems;
        });

        ghostInsertedRef.current = false;
        return;
      }

      // 拖拽的是左边小组件
      if (activeData?.fromSidebar) {
        const isOver = isOverCanvas(over);
        setDragState((prev) => ({
          ...prev,
          isOverDropZone: isOver,
        }));

        // 插入ghost
        if (!ghostInsertedRef.current) {
          const ghost = createGhost({
            id: currentDragComponentRef.current.id,
            relKey: active.id,
            isGroup: activeData.component.component_config?.isGroup,
          });

          setComponents((prev) => {
            let newItems;
            if (!prev.length) {
              newItems = [ghost];
            } else {
              const newIndex = overData.index > -1 ? overData.index : prev.length;

              newItems = [...prev.slice(0, newIndex), ghost, ...prev.slice(newIndex, prev.length)];
            }

            ghostInsertedRef.current = true;

            return newItems;
          });
        } else {
          // 从左边拖拽来的，而不是canvas内排序
          if (currentDragComponentRef.current.id !== over.id) {
            const newItems = moveComponent({
              items: components,
              activeId: currentDragComponentRef.current.id,
              overId: over.id,
            });

            setComponents((prev) => newItems);
          }
        }
      }
    },
    [isOverCanvas, setComponents, components]
  );

  const handleDragEnd = useCallback(
    (event) => {
      const { active, over } = event;
      const activeData = getDndData(active);

      if (!isOverCanvas(over) && activeData?.fromSidebar) {
        handleCleanUp();
        setComponents((prev) => prev.filter((item) => item.component !== "ghost"));
        return;
      }

      const nextComponent = currentDragComponentRef.current;
      if (nextComponent) {
        const overData = getDndData(over);
        setSelectedComponent(nextComponent);

        setComponents((prev) => {
          // 递归查找 ghost 并获取其位置信息
          const findGhostPath = (items, path = []) => {
            for (let i = 0; i < items.length; i++) {
              const item = items[i];
              if (item.component === "ghost") {
                return { path, index: i };
              }

              if (item.component_config?.isGroup) {
                const childrenName = item.component_config?.childrenName || "children";
                const children = item[childrenName] || [];
                const result = findGhostPath(children, [...path, { item, prop: childrenName }]);
                if (result) return result;
              }
            }
            return null;
          };

          const replaceGhost = (items, ghostLocation) => {
            if (!ghostLocation) return items;

            const { path, index } = ghostLocation;

            if (path.length === 0) {
              const newItems = [...items.slice(0, index), nextComponent, ...items.slice(index + 1)];
              return activeData?.fromSidebar ? newItems : arrayMove(newItems, index, overData.index);
            }

            return items.map((item) => {
              const currentPath = path[0];
              if (item?.id === currentPath.item?.id) {
                const childrenName = currentPath.prop;
                const children = item[childrenName] || [];

                const updatedChildren = replaceGhost(children, {
                  path: path.slice(1),
                  index,
                });

                return {
                  ...item,
                  [childrenName]: updatedChildren,
                };
              }
              return item;
            });
          };

          const ghostLocation = findGhostPath(prev);
          const newItems = replaceGhost(prev, ghostLocation);

          if (!ghostLocation) return prev;

          afterRender(() => {
            const sanitizedItems = sanitizeItems(newItems);
            const data = { ...prev, items: sanitizedItems };
            onChange?.(data);
          });

          return replaceGhost(prev, ghostLocation);
        });
      }

      handleCleanUp();
    },
    [isOverCanvas, setSelectedComponent, setComponents, onChange]
  );

  const handleCleanUp = () => {
    ghostInsertedRef.current = false;
    currentDragComponentRef.current = null;

    setDragState((prev) => ({
      activeId: null,
      dragData: null,
      isOverDropZone: false,
    }));
  };

  return {
    ...dragState,
    flattenedComponents,
    handleDragStart,
    handleDragMove,
    handleDragOver,
    handleDragEnd,
    handleCleanUp,
  };
}

export default useDragAndDrop;
