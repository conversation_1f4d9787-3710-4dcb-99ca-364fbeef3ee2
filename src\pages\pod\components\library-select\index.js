import { Button, Table } from "antd";
import useSWR from "swr";
import Api from "@/fetchers/api";
import Fetchers from "@/fetchers";
import PropTypes from "prop-types";
import { useState } from "react";
import TableHelper from "@/helpers/table-helper";

function LibrarySelect(props) {
  const { type = "image", onSelect } = props;
  const [tableLoading, setTableLoading] = useState(false);
  const [pagination, setPagination] = useState({});

  const { data: result } = useSWR([Api.getPodLibraries, pagination], async () => {
    setTableLoading(true);
    const { current: page, pageSize } = pagination;
    return Fetchers.getPodLibraries({ type, page, pageSize })
      .then((res) => {
        const result = res.data;
        if (result.success) {
          TableHelper.createColumnsRender({ columns: result.data.tableProps.columns });
          result.data.tableProps.columns.push({
            width: 1,
            dataIndex: "id",
            render(id, item, index) {
              return (
                <div>
                  <Button
                    size="small"
                    type="primary"
                    onClick={() => {
                      onSelect?.({ item, index });
                    }}
                  >
                    选择
                  </Button>
                </div>
              );
            },
          });
        }
        return result;
      })
      .finally(() => {
        setTableLoading(false);
      });
  });

  return (
    <div>
      <Table
        rowKey="id"
        {...result?.data?.tableProps}
        size="small"
        loading={tableLoading}
        onChange={(pagination, filters, sorter, extra) => {
          setPagination({ ...pagination });
        }}
      ></Table>
    </div>
  );
}

LibrarySelect.propTypes = {
  type: PropTypes.string,
  onSelect: PropTypes.func,
};

export default LibrarySelect;
