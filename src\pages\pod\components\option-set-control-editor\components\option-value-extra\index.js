import PropTypes from "prop-types";
import { Col, ColorPicker, Input, InputNumber, Row } from "antd";
import { CustomFunctionType } from "@/pages/pod/common";
import { ControlWrapper, FormControl } from "@/components/react-form-x";

function OptionValueExtra(props) {
  const { optionSet, control, keyPath } = props;
  const { hasLayoutVisibility, hasImageOption, hasText, hasTextColor, hasFontOption } = getExtraCheckResult();

  function getExtraCheckResult() {
    let result = {};
    control.functions.forEach((item) => {
      if (item.type === CustomFunctionType.LayoutVisibility) {
        result.hasLayoutVisibility = true;
      } else if (item.type === CustomFunctionType.DynamicImage) {
        result.hasImageOption = true;
      } else if (item.type === CustomFunctionType.Text) {
        result.hasText = true;
      } else if (item.type === CustomFunctionType.TextColor) {
        result.hasTextColor = true;
      } else if (item.type === CustomFunctionType.FontType) {
        result.hasFontOption = true;
      }
    });
    return result;
  }

  return (
    <>
      {hasLayoutVisibility ? (
        <Row>
          <Col span={24}>
            <FormControl
              keyPath={keyPath}
              name="layout_id"
              render={(props) => {
                return (
                  <ControlWrapper
                    {...props}
                    render={(props) => {
                      const { name, value, onChange } = props;
                      return (
                        <div>
                          <div>Layout ID:</div>
                          <div>
                            <InputNumber
                              value={value}
                              onChange={(nextValue) => {
                                onChange(null, { [name]: nextValue });
                              }}
                              style={{ width: `100%` }}
                            ></InputNumber>
                          </div>
                        </div>
                      );
                    }}
                  ></ControlWrapper>
                );
              }}
            ></FormControl>
          </Col>
        </Row>
      ) : null}
      {hasImageOption ? (
        <Row>
          <Col span={24}>
            <FormControl
              keyPath={keyPath}
              name="image_option_id"
              render={(props) => {
                return (
                  <ControlWrapper
                    {...props}
                    render={(props) => {
                      const { name, value, onChange } = props;
                      return (
                        <div>
                          <div>Image Option:</div>
                          <div>
                            <InputNumber
                              value={value}
                              onChange={(nextValue) => {
                                onChange(null, { [name]: nextValue });
                              }}
                              style={{ width: `100%` }}
                            ></InputNumber>
                          </div>
                        </div>
                      );
                    }}
                  ></ControlWrapper>
                );
              }}
            ></FormControl>
          </Col>
        </Row>
      ) : null}
      {hasText ? (
        <Row>
          <Col span={24}>
            <FormControl
              keyPath={keyPath}
              name="text"
              render={(props) => {
                return (
                  <ControlWrapper
                    {...props}
                    render={(props) => {
                      return (
                        <div>
                          <div>Text:</div>
                          <div>
                            <Input {...props}></Input>
                          </div>
                        </div>
                      );
                    }}
                  ></ControlWrapper>
                );
              }}
            ></FormControl>
          </Col>
        </Row>
      ) : null}
      {hasTextColor ? (
        <Row>
          <Col span={24}>
            <FormControl
              keyPath={keyPath}
              name="text_color"
              render={(props) => {
                return (
                  <ControlWrapper
                    {...props}
                    render={(props) => {
                      const { name, value, onChange } = props;
                      return (
                        <div>
                          <div>Text Color:</div>
                          <div>
                            <ColorPicker
                              value={value}
                              onChange={(value) => {
                                onChange(null, { [name]: value.toRgbString() });
                              }}
                              showText={(color) => color.toRgbString()}
                              style={{ width: `100%`, justifyContent: "start" }}
                            ></ColorPicker>
                          </div>
                        </div>
                      );
                    }}
                  ></ControlWrapper>
                );
              }}
            ></FormControl>
          </Col>
        </Row>
      ) : null}
      {hasFontOption ? (
        <Row>
          <Col span={24}>
            <FormControl
              keyPath={keyPath}
              name="font_id"
              render={(props) => {
                return (
                  <ControlWrapper
                    {...props}
                    render={(props) => {
                      const { name, value, onChange } = props;
                      return (
                        <div>
                          <div>Font Option:</div>
                          <div>
                            <InputNumber
                              value={value}
                              onChange={(nextValue) => {
                                onChange(null, { [name]: nextValue });
                              }}
                              style={{ width: `100%` }}
                            ></InputNumber>
                          </div>
                        </div>
                      );
                    }}
                  ></ControlWrapper>
                );
              }}
            ></FormControl>
          </Col>
        </Row>
      ) : null}
    </>
  );
}

OptionValueExtra.propTypes = {
  optionSet: PropTypes.object,
  control: PropTypes.object,
  keyPath: PropTypes.array,
};

export default OptionValueExtra;
