import AntdSelect from "components/common/Select";
import styles from "./index.module.scss";
import { useRef, useState } from "react";
import debounce from "lodash.debounce";
import HTMLBlock from "components/common/HtmlBlock";
import classNames from "classnames";
import Loading from "components/common/Loading";
import request from "@/fetchers/request";
import Helper from "helpers";

function SelectWithPreview(props) {
  const { item, options, ticketId, form, onChange, richTextEditorRef, showTemplate = true, ...otherProps } = props;
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewTemplateMap, setPreviewTemplateMap] = useState({});
  const [hoverSelectItemId, setHoverSelectItemId] = useState(null);
  const previewTemplateRef = useRef(null);

  async function getTemplate(id) {
    if (previewTemplateMap[id]) {
      return previewTemplateMap[id];
    } else {
      try {
        return await request(item?.templateApi, {
          method: "GET",
          params: { id, ticket_id: ticketId },
        }).then((res) => res.data?.data);
      } finally {
      }
    }
  }

  const handleHoverSelectItem = debounce(async (e, option) => {
    try {
      if (item?.templateApi) {
        setPreviewOpen(true);
        setPreviewLoading(true);
        setHoverSelectItemId(option.value);
        const x = e.clientX;
        const y = e.clientY;
        previewTemplateRef.current.style.left = `${x + 500}px`;
        previewTemplateRef.current.style.top = `${y - 200}px`;
        if (!previewTemplateMap[option?.value]) {
          const template = await getTemplate(option?.value);
          setPreviewTemplateMap((previewTemplateMap) => ({ ...previewTemplateMap, [option?.value]: template }));
        }
      }
    } finally {
      setPreviewLoading(false);
    }
  }, 200);

  function optionRender(option) {
    return (
      <div
        onMouseEnter={(e) => handleHoverSelectItem(e, option)}
        onMouseLeave={() => {
          setPreviewOpen(false);
        }}
      >
        {option.label}
      </div>
    );
  }

  return (
    <div className={styles.selectWrapper}>
      {item?.addonBefore ? (
        <span className={styles.addonBeforeText} {...item?.addonBefore?.props}>
          {item.addonBefore?.content}
        </span>
      ) : null}
      <AntdSelect
        allowClear
        showSelectAll={false}
        {...item.props}
        {...otherProps}
        options={options || item?.props?.options}
        optionRender={optionRender}
        onBlur={() => setPreviewOpen(false)}
        onChange={async (value) => {
          onChange(value);
          try {
            if (item?.cascader) {
              Helper.pageLoading(true);
              const templates = await Promise.all(
                value?.map(async (id) => {
                  return await getTemplate(id);
                })
              );
              const htmlStr = templates.join("");
              richTextEditorRef.current?.setData(htmlStr);
            }
          } finally {
            Helper.pageLoading(false);
          }
        }}
      />
      {showTemplate ? (
        <div
          ref={previewTemplateRef}
          className={classNames(styles.previewTemplate, { [styles.previewTemplateOpen]: previewOpen })}
        >
          <Loading loading={previewLoading}>
            {hoverSelectItemId ? <HTMLBlock tag="span" html={previewTemplateMap[hoverSelectItemId]} /> : null}
          </Loading>
        </div>
      ) : null}
    </div>
  );
}

export default SelectWithPreview;
