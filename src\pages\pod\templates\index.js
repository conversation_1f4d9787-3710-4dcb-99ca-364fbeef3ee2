import { But<PERSON>, Col, Form, Input, InputNumber, Modal, Popconfirm, Row, Select, Table } from "antd";
import { useEffect, useRef, useState } from "react";
import Fetchers from "@/fetchers";
import styles from "./index.module.scss";
import Link from "@/components/Link";
import TableHelper from "@/helpers/table-helper";
import I18nSelect from "@/pages/pod/components/i18n-select";
import { useLocation } from "react-router-dom";
import Utils from "@/utils";

function Templates(props) {
  const {} = props;
  const [tableData, setTableData] = useState();
  const [tableLoading, setTableLoading] = useState(false);
  const [addTemplateModalOpen, setAddTemplateModalOpen] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const location = useLocation();

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current, loadTableData };

  async function loadTableData() {
    setTableLoading(true);
    const { page, pageSize } = Utils.getQueryParams(window.location.href);
    Fetchers.getPodTemplates({ query: { page, pageSize } })
      .then((res) => {
        const result = res.data;
        if (result.success) {
          TableHelper.createColumnsRender({ columns: result.data.tableProps.columns });
          result.data.tableProps.columns.push({
            width: 1,
            render(text, item, index) {
              return (
                <div className="table-operations-column">
                  <Link to="/pod/edit-template" query={{ id: item.id }}>
                    <Button type="primary" size="small">
                      编辑
                    </Button>
                  </Link>
                  <Popconfirm
                    title="确定删除吗？"
                    onConfirm={() => {
                      setTableLoading(true);
                      Fetchers.deletePodTemplate({ id: item.id }).then(() => {
                        loadTableData();
                      });
                    }}
                  >
                    <Button type="primary" size="small">
                      删除
                    </Button>
                  </Popconfirm>
                </div>
              );
            },
          });
          setTableData(result.data);
        }
      })
      .finally(() => {
        setTableLoading(false);
      });
  }

  useEffect(() => {
    const { loadTableData } = paramsRef.current;
    loadTableData();
  }, [location.search]);

  return (
    <>
      <div className={styles.page}>
        <div>
          <Button
            type="primary"
            onClick={() => {
              setAddTemplateModalOpen(true);
            }}
          >
            添加模板
          </Button>
        </div>
        <div>
          <Table rowKey="id" {...tableData?.tableProps} loading={tableLoading} onChange={TableHelper.onChange}></Table>
        </div>
      </div>
      <Modal
        open={addTemplateModalOpen}
        onCancel={() => {
          setAddTemplateModalOpen(false);
        }}
        footer={null}
        title="添加模板"
      >
        <Form
          layout="vertical"
          initialValues={{ initWidth: 1000, initHeight: 1000, unit: "px" }}
          onFinish={(values) => {
            setSubmitLoading(true);
            Fetchers.createPodTemplate(values)
              .then(() => {
                setAddTemplateModalOpen(false);
                loadTableData();
              })
              .finally(() => {
                setSubmitLoading(false);
              });
          }}
        >
          <Form.Item name="name_i18n_key" label="模板名称" rules={[{ required: true }]}>
            <I18nSelect></I18nSelect>
          </Form.Item>
          <Row gutter={16}>
            <Col span={9}>
              <Form.Item name="initWidth" label="宽" rules={[{ required: true }]}>
                <InputNumber className="input-number-block"></InputNumber>
              </Form.Item>
            </Col>
            <Col span={9}>
              <Form.Item name="initHeight" label="高" rules={[{ required: true }]}>
                <InputNumber className="input-number-block"></InputNumber>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="unit" label="单位" rules={[{ required: true }]}>
                <Select options={[{ value: "px", title: "px" }]}></Select>
              </Form.Item>
            </Col>
          </Row>
          <div className="custom-modal-footer">
            <Button type="primary" htmlType="submit" loading={submitLoading}>
              确定
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  );
}

export default Templates;
