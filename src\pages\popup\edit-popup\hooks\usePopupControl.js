function throttled(fn, delay) {
  let timer = null;
  let starttime = Date.now();
  return function () {
    let curTime = Date.now();
    let remaining = delay - (curTime - starttime);
    let context = this;
    let args = arguments;
    clearTimeout(timer);
    if (remaining <= 0) {
      fn.apply(context, args);
      starttime = Date.now();
    } else {
      timer = setTimeout(fn, remaining);
    }
  };
}

function isExpire({ expirationTime }) {
  return Date.now() > expirationTime;
}

function usePopupControl({ React, data, subscribed, temporarilyClosedKey }) {
  const [open, setOpen] = React.useState(false);
  const condition = React.useMemo(() => data?.template_info?.condition, [data?.template_info?.condition]);
  const temporarilyClosedData = React.useMemo(
    () => JSON.parse(localStorage.getItem(temporarilyClosedKey) || null),
    [temporarilyClosedKey]
  );
  const paramsRef = React.useRef({ subscribed });

  function handleTemporarilyClose() {
    const ttl = condition?.interval * 60 * 60 * 1000;
    const data = {
      expirationTime: Date.now() + ttl,
      value: true,
    };
    localStorage.setItem(temporarilyClosedKey, JSON.stringify(data));
    setOpen(false);
  }

  React.useEffect(() => {
    (() => {
      const { subscribed } = paramsRef.current;
      if (!condition || subscribed || temporarilyClosedData) return;

      const { timing, delay, scroll_distance } = condition || {};

      if (timing) {
        if (timing === "delayed") {
          setTimeout(() => setOpen(true), +delay * 1000);
          return;
        } else if (timing === "scroll") {
          const distance = Number(scroll_distance);
          const throttledHandleInitialize = throttled(() => {
            if (window.scrollY >= distance) {
              setOpen(true);
              window.removeEventListener("scroll", throttledHandleInitialize);
            }
          }, 100);
          window.addEventListener("scroll", throttledHandleInitialize);
          return;
        } else if (timing === "none") {
          return;
        }
      }

      setOpen(true);
    })();
  }, [condition, temporarilyClosedData]);

  React.useEffect(() => {
    function handleListeningInterval() {
      const { subscribed } = paramsRef.current;
      if (open || subscribed || !temporarilyClosedData?.expirationTime) return;

      const checkExpiration = () => {
        if (isExpire(temporarilyClosedData.expirationTime)) {
          setOpen(true);
          localStorage.removeItem(temporarilyClosedKey);
        }
      };

      const intervalId = setInterval(checkExpiration, 1000);
      return () => clearInterval(intervalId);
    }

    const subscription = handleListeningInterval();

    return () => subscription?.();
  }, [open, temporarilyClosedData, temporarilyClosedKey]);

  return { open, setOpen, handleTemporarilyClose };
}

export default usePopupControl;
