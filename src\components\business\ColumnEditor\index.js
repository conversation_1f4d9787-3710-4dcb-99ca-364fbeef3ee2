import React, { useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card } from "antd";
import PropTypes from "prop-types";
import { EditOutlined } from "@ant-design/icons";
import EditTable from "@/components/common/EditTable";
import styles from "./index.module.scss";

function ColumnEditor(props) {
  const { value, onChange, columns: tableColumns = [] } = props;
  const [visible, setVisible] = useState(false);
  const [columns, setColumns] = useState(value || []);
  const originalColumnsRef = useRef([]);

  // 默认新行数据
  const defaultNewRow = {
    dataIndex: "",
    title: "",
    width: 100,
    ellipsis: false,
  };

  const handleOpen = () => {
    originalColumnsRef.current = JSON.parse(JSON.stringify(value || []));
    setColumns(value || []);
    setVisible(true);
  };

  const handleCancel = () => {
    setColumns(originalColumnsRef.current);
    setVisible(false);
  };

  const handleSave = () => {
    onChange?.(columns);
    setVisible(false);
  };

  const handleTableChange = (newColumns) => {
    setColumns(newColumns);
  };

  return (
    <div className={styles.columnEditor}>
      <Button type="primary" icon={<EditOutlined />} onClick={handleOpen} className={styles.editButton}>
        编辑表格列
      </Button>
      {value && Array.isArray(value) && value.length > 0 && (
        <div className={styles.summary}>已配置 {value.length} 列</div>
      )}

      <Modal title="表格列配置" open={visible} onCancel={handleCancel} onOk={handleSave} width={1600} destroyOnClose>
        <Card size="small" bordered={false}>
          <EditTable
            value={columns}
            columns={tableColumns}
            onChange={handleTableChange}
            scroll={{ x: 900 }}
            defaultNewRow={defaultNewRow}
            isShowAddRow={true}
            // operations={[
            //   {
            //     key: "moveUp",
            //     title: "上移",
            //     action: ({ row, index }) => {
            //       if (index === 0) return;
            //       const newData = [...columns];
            //       [newData[index - 1], newData[index]] = [newData[index], newData[index - 1]];
            //       handleTableChange(newData);
            //     },
            //     disabled: ({ index }) => index === 0,
            //   },
            //   {
            //     key: "moveDown",
            //     title: "下移",
            //     action: ({ row, index }) => {
            //       if (index === columns.length - 1) return;
            //       const newData = [...columns];
            //       [newData[index], newData[index + 1]] = [newData[index + 1], newData[index]];
            //       handleTableChange(newData);
            //     },
            //     disabled: ({ index }) => index === columns.length - 1,
            //   },
            // ]}
          />
        </Card>
      </Modal>
    </div>
  );
}

ColumnEditor.propTypes = {
  value: PropTypes.array,
  onChange: PropTypes.func,
  props: PropTypes.object,
};

export default ColumnEditor;
