const taxonomy = require("./taxonomy");
const downloadList = require("./download-list");
const deleteDownloadFile = require("./delete-download-file");
const messageQuery = require("./message/query");
const messageResolved = require("./message/resolved");
const pageInfo = require("./page-info");
const i18n = require("./i18n");

module.exports = {
  ...taxonomy,
  "GET /rest/v1/global/download/list": async (req, res) => downloadList(req, res),
  "DELETE /rest/v1/global/download/delete/:id": async (req, res) => deleteDownloadFile(req, res),
  "GET /rest/v1/global/message/query": async (req, res) => messageQuery(req, res),
  "POST /rest/v1/global/message/resolved": async (req, res) => messageResolved(req, res),
  "GET /rest/v1/global/page-info": async (req, res) => pageInfo(req, res),
  "GET /rest/v1/global/i18n/:lang": async (req, res) => i18n(req, res),
};
