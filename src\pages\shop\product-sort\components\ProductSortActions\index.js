import styles from "./index.module.scss";
import { Row, Col, Button, Form, Input, Select, InputNumber } from "antd";
import Fetchers from "fetchers";
import Helper from "helpers";

const { Option } = Select;

function ProductSortActions({ id, selectedItems, page }) {
  const [keepOnTopForm] = Form.useForm();
  const [moveForm] = Form.useForm();

  async function handleKeepOnTopSubmit(values) {
    try {
      const data = { id, ...values };
      const result = await Fetchers.upSpuList({ data }).then((res) => res.data);
      if (result.success) {
        Helper.modal.info({
          title: "操作成功",
          onOk: () => window.location.reload(),
        });
      }
    } catch (error) {}
  }

  async function handleSortSubmit(values) {
    if (!selectedItems.length) {
      return Helper.openMessage({ type: "error", content: "请先选择商品" });
    }

    try {
      const data = { id, ...values, products: selectedItems, page };
      const result = await Fetchers.setTopNum({ data }).then((res) => res.data);
      if (result.success) {
        Helper.modal.info({
          title: "操作成功",
          onOk: () => window.location.reload(),
        });
      }
    } catch (error) {}
  }

  return (
    <Row className={styles.productSortActions} gutter={16}>
      <Col span={12}>
        <Form form={keepOnTopForm} className={styles.form} layout="inline" onFinish={handleKeepOnTopSubmit}>
          <Form.Item className={styles.formItem} name="up_spu_list" rules={[{ required: true }]}>
            <Input placeholder="输入spu，多个spu用英文逗号分隔，支持前缀模糊匹配spu" />
          </Form.Item>
          <Button type="primary" htmlType="submit">
            置顶
          </Button>
        </Form>
      </Col>
      <Col span={12}>
        <Form form={moveForm} className={styles.form} layout="inline" onFinish={handleSortSubmit}>
          <Form.Item name="sort_type" label="移动商品至" initialValue={"custom"}>
            <Select>
              <Option value="custom">自定义</Option>
              <Option value="top">顶部</Option>
              <Option value="bottom">底部</Option>
            </Select>
          </Form.Item>
          <Form.Item shouldUpdate={(prev, curr) => prev.sort_type !== curr.sort_type}>
            {({ getFieldValue }) =>
              getFieldValue("sort_type") === "custom" && (
                <Form.Item name="position" rules={[{ required: true, message: "请输入位置数字" }]}>
                  <InputNumber min={1} placeholder="请输入位置数字" />
                </Form.Item>
              )
            }
          </Form.Item>
          <Button type="primary" htmlType="submit">
            移动
          </Button>
        </Form>
      </Col>
    </Row>
  );
}

export default ProductSortActions;
