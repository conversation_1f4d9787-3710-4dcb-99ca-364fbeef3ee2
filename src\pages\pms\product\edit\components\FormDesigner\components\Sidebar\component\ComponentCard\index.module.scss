$hover-color: #409eff;
$primary-background-color: #f4f6fc;

.componentCard {
  display: block;
  width: 100%;
  font-size: 12px;
  line-height: 26px;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 4px 0;
  color: #333;
  border: 1px solid $primary-background-color;
  cursor: move;
  transition: all 0.3s;

  .componentCardContent {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 6px;
    background: $primary-background-color;
    border: 1px solid $primary-background-color;
    padding-left: 8px;
  }

  &:hover {
    color: $hover-color;
    border: 1px dashed $hover-color;
  }
}
