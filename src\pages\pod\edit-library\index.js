import Utils from "@/utils";
import { useEffect, useRef, useState } from "react";
import Fetchers from "@/fetchers";
import { Button, Form, Input, Modal, Popconfirm, Space, Table, App } from "antd";
import styles from "./index.module.scss";
import classNames from "classnames";
import AddLibraryItemsForm from "@/pages/pod/components/add-library-items-form";
import { useNavigate } from "react-router-dom";
import UpdateLibraryItemForm from "@/pages/pod/components/update-library-item-form";
import TableHelper from "@/helpers/table-helper";
import LibraryItemsTable from "@/pages/pod/components/library-items-table";
import Helper from "@/helpers";
import I18nSelect from "@/pages/pod/components/i18n-select";

const addButtonText = { image: "添加图片", color: "添加颜色", font: "添加字体" };

function EditLibrary() {
  const queryParams = Utils.getQueryParams(window.location.href);
  const [tableData, setTableData] = useState();
  const [tableLoading, setTableLoading] = useState(false);
  const [addCategoryOpen, setAddCategoryOpen] = useState(false);
  const [addLibraryItemsOpen, setAddLibraryItemsOpen] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [editItemModalOpen, setEditItemModalOpen] = useState(false);
  const [editItemData, setEditItemData] = useState();
  const [coverModalOpen, setCoverModalOpen] = useState();
  const navigate = useNavigate();
  const { message } = App.useApp();
  const addCategoryFormRef = useRef();

  const library = tableData?.library;

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current, loadTableData, library };

  async function loadTableData({ query } = {}) {
    setTableLoading(true);
    await Fetchers.getPodLibrary({ ...query, type: queryParams.type, id: queryParams.id })
      .then((res) => {
        const result = res.data;
        if (result.success) {
          const tableData = result.data;
          TableHelper.createColumnsRender({ columns: tableData.tableProps.columns, reloadData: loadTableData });
          tableData.tableProps.columns?.push({
            width: 1,
            title: "操作",
            className: styles.operationColumn,
            render: (text, item, index) => {
              return (
                <div style={{ display: "flex", gap: 8 }}>
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => {
                      const { library } = paramsRef.current;
                      if (library.list_type === "category") {
                        navigate(Utils.setQueryParams("/pod/edit-category", { id: item.id }));
                      } else if (library.list_type === "items") {
                        setEditItemModalOpen(true);
                        setEditItemData(item);
                      }
                    }}
                  >
                    编辑
                  </Button>
                  <Popconfirm
                    title="确定删除吗？"
                    placement="topRight"
                    onConfirm={() => {
                      const { library } = paramsRef.current;
                      if (library.list_type === "category") {
                        Fetchers.deletePodLibraryCategory({ id: item.id }).then(() => {
                          loadTableData();
                        });
                      } else {
                        Fetchers.deletePodLibraryItem({ id: item.id }).then(() => {
                          loadTableData();
                        });
                      }
                    }}
                  >
                    <Button type="primary" size="small">
                      删除
                    </Button>
                  </Popconfirm>
                </div>
              );
            },
          });
          setTableData(tableData);
        }
      })
      .finally(() => {
        setTableLoading(false);
      });
  }

  useEffect(() => {
    const { loadTableData } = paramsRef.current;
    loadTableData();
  }, [queryParams.type, queryParams.id]);

  return (
    <>
      <div className={classNames("page", styles.page)}>
        <div style={{ marginBottom: 20 }}>
          <Space style={{ visibility: library ? "visible" : "hidden" }}>
            <Button
              type="primary"
              onClick={() => {
                setAddCategoryOpen(true);
              }}
              disabled={library?.list_type && library?.list_type !== "category"}
            >
              添加分类
            </Button>
            <Button
              type="primary"
              onClick={() => {
                setAddLibraryItemsOpen(true);
              }}
              disabled={library?.list_type && library?.list_type !== "items"}
            >
              {addButtonText[library?.type]}
            </Button>
            {/*<Button
              type="primary"
              onClick={() => {
                setCoverModalOpen(true);
              }}
            >
              设置封面
            </Button>*/}
          </Space>
        </div>
        <div>
          {library?.list_type === "items" ? (
            <LibraryItemsTable
              tableData={tableData}
              setTableData={setTableData}
              tableLoading={tableLoading}
              setTableLoading={setTableLoading}
              loadTableData={loadTableData}
            ></LibraryItemsTable>
          ) : (
            <Table rowKey="id" {...tableData?.tableProps} loading={tableLoading}></Table>
          )}
        </div>
      </div>
      <Modal
        open={addCategoryOpen}
        onCancel={() => {
          setAddCategoryOpen(false);
        }}
        footer={null}
        closable={!submitLoading}
        maskClosable={!submitLoading}
        destroyOnClose
      >
        <Form
          ref={addCategoryFormRef}
          layout="vertical"
          onFinish={async (values) => {
            setSubmitLoading(true);
            const cover = await new Promise((resolve, reject) => {
              const image = new window.Image();
              image.onload = () => {
                resolve({ src: image.src, width: image.width, height: image.height });
              };
              image.onerror = reject;
              image.src = values.cover;
            });
            Fetchers.addPodLibraryCategory({
              ...values,
              cover,
              library_id: +queryParams.id,
            })
              .then(() => {
                setAddCategoryOpen(false);
                loadTableData();
              })
              .finally(() => {
                setSubmitLoading(false);
              });
          }}
        >
          <Form.Item name="name_i18n_key" label="Category Name" rules={[{ required: true }]}>
            <I18nSelect></I18nSelect>
          </Form.Item>
          <Form.Item name="cover" label="Category Cover" rules={[{ required: true }]}>
            <Input></Input>
          </Form.Item>
          <div style={{ textAlign: "end" }}>
            <Button type="primary" htmlType="submit" loading={submitLoading}>
              添加
            </Button>
          </div>
        </Form>
      </Modal>
      <Modal
        open={addLibraryItemsOpen}
        onCancel={() => {
          setAddLibraryItemsOpen(false);
        }}
        footer={null}
        closable={!submitLoading}
        maskClosable={!submitLoading}
        destroyOnClose
        width={600}
      >
        <AddLibraryItemsForm
          libraryId={library?.id}
          libraryType={library?.type}
          onSuccess={() => {
            setAddLibraryItemsOpen(false);
            loadTableData();
          }}
        ></AddLibraryItemsForm>
      </Modal>
      <Modal
        open={editItemModalOpen}
        onCancel={() => {
          setEditItemModalOpen(false);
        }}
        footer={null}
        destroyOnClose
      >
        <UpdateLibraryItemForm
          data={editItemData}
          onSuccess={() => {
            setEditItemModalOpen(false);
            loadTableData();
          }}
        ></UpdateLibraryItemForm>
      </Modal>
      <Modal
        open={coverModalOpen}
        onCancel={() => {
          setCoverModalOpen(false);
        }}
        footer={null}
      >
        <Form
          layout="vertical"
          onFinish={async (data) => {
            setSubmitLoading(true);
            const image = await Helper.createImage({ src: data.cover });
            const cover = { src: image.src, width: image.width, height: image.height };
            Fetchers.updatePodLibrary({ id: library.id, data: { cover } })
              .then(() => {
                setCoverModalOpen(false);
                message.success(`更新成功！`);
              })
              .finally(() => {
                setSubmitLoading(false);
              });
          }}
        >
          <Form.Item name="cover" label="素材库封面" rules={[{ required: true }]}>
            <Input></Input>
          </Form.Item>
          <div style={{ textAlign: "end" }}>
            <Button type="primary" htmlType="submit" loading={submitLoading}>
              确定
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  );
}

export default EditLibrary;
