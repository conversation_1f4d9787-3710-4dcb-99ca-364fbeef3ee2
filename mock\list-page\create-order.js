const Api = require("../../src/fetchers/api");

module.exports = async (req, res) => {
  res.status(200).json({
    status: "00",
    success: true,
    data: {
      content: {
        component: "JSONComponents",
        type: "json",
        props: {},
        children: [
          {
            component: "Form",
            type: "json",
            props: {
              id: "form2",
              initialValues: {},
              // labelCol: {
              //   span: 10,
              // },
              wrapperCol: {
                span: 24,
              },
            },
            formItems: [
              {
                component: "Row",
                children: [
                  {
                    component: "Col",
                    props: {
                      xs: {
                        span: 24,
                      },
                      lg: {
                        span: 24,
                      },
                    },

                    children: [
                      {
                        component: "Row",
                        props: { gutter: 5 },
                        children: [
                          {
                            component: "Col",
                            props: {
                              xs: {
                                span: 6,
                              },
                            },
                            children: [
                              {
                                key: "selectCascader",
                                label: "指定：",
                                component: "Select",
                                props: {
                                  options: [
                                    { label: "选项1", value: "1" },
                                    { label: "选项2", value: "2" },
                                    { label: "选项3", value: "3" },
                                  ],
                                },
                                cascader: {
                                  name: "selectCascaded",
                                  searchApi: Api.searchProductSpu,
                                },
                                rules: [{ required: true, message: "This is a required field" }],
                              },
                            ],
                          },
                          {
                            component: "Col",
                            props: {
                              xs: {
                                span: 18,
                              },
                            },
                            children: [
                              {
                                key: "selectCascaded",
                                label: "",
                                component: "Select",
                                props: {
                                  options: [
                                    { label: "选项1", value: "1" },
                                    { label: "选项2", value: "2" },
                                  ],
                                },
                                rules: [{ required: true, message: "This is a required field" }],
                              },
                            ],
                          },
                        ],
                      },
                    ],
                  },
                  {
                    component: "Col",
                    props: {
                      xs: {
                        span: 24,
                      },
                      lg: {
                        span: 24,
                      },
                    },
                    children: [
                      {
                        component: "Row",
                        props: { gutter: 5 },
                        children: [
                          {
                            component: "Col",
                            props: {
                              xs: {
                                span: 6,
                              },
                            },
                            children: [
                              {
                                key: "field3",
                                label: "收件国家：",
                                component: "Select",
                                props: {
                                  options: [
                                    { label: "选项1", value: "1" },
                                    { label: "选项2", value: "2" },
                                    { label: "选项3", value: "3" },
                                  ],
                                },
                                rules: [{ required: true, message: "This is a required field" }],
                              },
                            ],
                          },
                          {
                            component: "Col",
                            props: {
                              xs: {
                                span: 18,
                              },
                            },
                            children: [
                              {
                                key: "field4",
                                label: "",
                                component: "Select",
                                props: {
                                  width: "100%",
                                  options: [
                                    { label: "选项1", value: "1" },
                                    { label: "选项2", value: "2" },
                                  ],
                                },
                                rules: [{ required: true, message: "This is a required field" }],
                              },
                            ],
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
            submit: {
              request: {
                url: Api.customer,
                data: {
                  default: "test",
                },
              },
            },
          },
        ],
      },
    },
  });
};
