import Editable from "@/components/Editable";
import I18nSelect from "@/pages/pod/components/i18n-select";
import PropTypes from "prop-types";

function I18nEditable(props) {
  const { children, onSave } = props;

  return (
    <Editable
      onSave={onSave}
      renderControl={({ controlRef, handleSave, setIsEditing }) => {
        return (
          <I18nSelect
            ref={controlRef}
            onSelect={(value, option) => {
              handleSave({ value: option.label, option });
            }}
            style={{ width: `100%`, height: `100%` }}
            onKeyDown={(event) => {
              const key = event.key.toLowerCase();
              if (key === "escape") {
                setIsEditing(false);
              }
            }}
            popupMatchSelectWidth={false}
            onBlur={() => {
              setIsEditing(false);
            }}
          ></I18nSelect>
        );
      }}
    >
      {children}
    </Editable>
  );
}

I18nEditable.propTypes = {
  onSave: PropTypes.func,
};

export default I18nEditable;
