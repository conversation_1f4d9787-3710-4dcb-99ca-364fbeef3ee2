import styles from "./index.module.scss";
import { Select as AntdSelect, Empty, Spin, Button, Tooltip, Checkbox, Divider } from "antd";
import request from "@/fetchers/request";
import useSWR from "swr";
import Helper from "helpers";
import { useEffect, useState, useRef, forwardRef } from "react";
import { ReloadOutlined } from "@ant-design/icons";
import PropTypes from "prop-types";
import { useI18n } from "@/context/I18nContext";

function Select(props, ref) {
  const { optionsApi, options, addOptions, onChange, showSelectAll = true, ...otherProps } = props;
  const { mode } = otherProps;
  const [currentOptions, setCurrentOptions] = useState();
  const [checkedList, setCheckedList] = useState([]);
  const indeterminate = checkedList.length > 0 && checkedList.length < getOptionsLength(currentOptions);
  const checkAll = checkedList?.length > 0 && getOptionsLength(currentOptions) === checkedList.length;
  const [loading, setLoading] = useState(false);
  const paramsRef = useRef();
  const { t } = useI18n();

  paramsRef.current = {
    ...paramsRef.current,
    getCurrentOptions,
  };

  const { data, mutate: updateOptions } = useSWR(
    optionsApi,
    async () => {
      try {
        setLoading(true);
        const result = await fetcher(optionsApi).then((res) => res?.data);
        setCurrentOptions(result || []);
        return result;
      } finally {
        setLoading(false);
      }
    },
    {
      dedupingInterval: 60000,
    }
  );

  async function fetcher(url, params) {
    return request(url, {
      method: "GET",
    }).then((res) => res.data);
  }

  async function getCurrentOptions() {
    if (optionsApi && data) {
      setCurrentOptions(data);
    }
  }

  function getOptionsLength(options) {
    return getOptionsValues(options)?.length;
  }

  function getOptionValue(option) {
    return option?.value ?? "";
  }

  function getOptionsValues(options) {
    const valuesGroup = options?.map((option) => {
      if (option?.options) {
        return getOptionsValues(option?.options);
      } else {
        return getOptionValue(option);
      }
    });

    return valuesGroup.flat(Infinity);
  }

  const handleCheckAllChange = (e) => {
    const value = getOptionsValues(currentOptions);
    const checkedValue = e.target.checked ? value : [];
    onChange?.(checkedValue, currentOptions);
    setCheckedList(checkedValue);
  };

  const dropdownRender = (menu) => {
    return (
      <div className={styles.dropdown}>
        {mode === "multiple" && showSelectAll ? (
          <>
            <div className={styles.checkAllWrapper}>
              <Checkbox onChange={handleCheckAllChange} indeterminate={indeterminate} checked={checkAll}>
                {t({ key: "SelectAll", defaultText: "全选" })}
              </Checkbox>
            </div>
            <Divider style={{ margin: "0" }} />
          </>
        ) : null}
        {menu}
      </div>
    );
  };

  useEffect(() => {
    setCurrentOptions(options || data);
  }, [options, data]);

  useEffect(() => {
    const { getCurrentOptions } = paramsRef.current;
    getCurrentOptions();
  }, []);

  return (
    <div className={styles.container}>
      <AntdSelect
        ref={ref}
        optionFilterProp="label"
        {...otherProps}
        notFoundContent={
          <div style={{ display: "flex", justifyContent: "center" }}>
            {loading ? <Spin /> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
          </div>
        }
        onChange={(value) => {
          onChange?.(value, currentOptions);
          if (mode === "multiple") {
            setCheckedList(value);
          }
        }}
        options={currentOptions}
        dropdownRender={dropdownRender}
      />
      {addOptions?.command ? (
        <div className={styles.commandWrapper}>
          <Button
            type="link"
            style={{ padding: "4px 0" }}
            onClick={() => {
              Helper.commandHandler({ command: addOptions?.command });
            }}
          >
            {addOptions?.title ? addOptions?.title : "维护"}
          </Button>
          <span className={styles.refresh}>
            <Tooltip title="刷新选项">
              <Button shape="circle" size="small" icon={<ReloadOutlined />} loading={loading} onClick={updateOptions} />
            </Tooltip>
          </span>
        </div>
      ) : null}
    </div>
  );
}

Select = forwardRef(Select);

Select.propTypes = {
  optionsApi: PropTypes.string,
  options: PropTypes.array,
  addOptions: PropTypes.object,
  onChange: PropTypes.func,
};

export default Select;
