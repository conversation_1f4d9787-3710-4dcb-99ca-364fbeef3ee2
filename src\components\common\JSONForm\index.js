import { Cascader, Checkbox, Form, InputNumber, Radio, Switch, Row, Col, Card, Typography, Spin } from "antd";
import { forwardRef, useEffect, useImperativeHandle, useRef, useState, lazy, Suspense } from "react";
import PropTypes from "prop-types";
import useS<PERSON> from "swr";
import axios from "@/fetchers/request";
import Enums from "enums";
import Utils from "utils";
import Helper from "helpers";
import Select from "components/common/Select";
import TreeSelect from "components/common/TreeSelect";
import Tree from "components/common/Tree";
import RemoteSearchSelect from "@/components/common/RemoteSearchSelect";
import RichTextEditor from "components/common/RichTextEditor";
import JSONEditor from "components/common/JSONEditor";
import ImageUpload from "components/common/ImageUpload";
import DatePicker from "components/common/DatePicker";
import Loading from "components/common/Loading";
import Transfer from "components/common/Transfer";
import EditTable from "components/common/EditTable";
import Collapse from "components/common/JSONComponent/components/Collapse";
import CollapseLabel from "components/common/JSONComponent/components/Collapse/components/label";
import Button from "components/common/Button";
import Text from "components/common/JSONComponent/components/Text";
import CameraPhotoUpload from "components/common/CameraPhotoUpload";
import Input from "components/business/Input";
import TextArea from "components/business/TextArea";
import ColorPicker from "components/common/ColorPicker";
import SpaceWrapper from "components/business/SpaceWrapper";
import LangTabs from "components/common/JSONComponent/components/LangTabs";
import CheckboxSingle from "components/business/CheckboxSingle";
import CardLabel from "components/common/JSONComponent/components/Card/components/CardLabel";
import { getFormRules, getValuePropName, getTrigger, generateLabels, clearFields } from "./vars";
import { useSelectCascader, useVisibleFields, useFieldsProps } from "./hooks";
import TextEditable from "../JSONComponent/components/TextEditable";
import FileUpload from "components/common/FileUpload";

function JSONForm(props, ref) {
  const { data, onFinish, onFinishFailed, extendComponents, extraData, ...otherProps } = props;
  const { type = "json", fetcher } = data || {};
  const [formData, setFormData] = useState(data);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const formRef = useRef(null);
  const paramsRef = useRef({});
  const dataString = JSON.stringify(data);
  const { cascaderOptions, handleSelectChange } = useSelectCascader();
  const { visibleFields, handleFieldVisibility } = useVisibleFields({
    formData,
  });
  const [fieldsProps, handleFieldProps] = useFieldsProps({ formData });

  paramsRef.current = {
    ...paramsRef.current,
    onHandleFinish,
    submitOthersData: extraData ?? {},
    fetcher,
    type,
  };

  // const { data: formData } = useSWR(dataString, async () => {
  //   if (type === "json") {
  //     return data;
  //   } else if (type === "api") {
  //     try {
  //       setLoading(true);
  //       const { url, ...options } = fetcher?.request;
  //       return await axios({
  //         url,
  //         method: "GET",
  //         ...options,
  //         data: { ...options.data },
  //       }).then((res) => res.data?.data || {});
  //     } finally {
  //       setLoading(false);
  //     }
  //   }
  // });

  function renderComponent(item, index) {
    const { component, props } = item;

    if (component === Enums.Components.Row) {
      return (
        <Row key={index} {...props}>
          {item?.children?.map((formItem, j) => {
            return renderComponent(formItem, j);
          })}
        </Row>
      );
    } else if (component === Enums.Components.Col) {
      return (
        <Col key={index} {...props}>
          {item?.children?.map((formItem, j) => {
            return renderComponent(formItem, j);
          })}
        </Col>
      );
    } else if (component === Enums.Components.Card) {
      const { extra, ...otherProps } = props;

      return (
        <Card key={index} size="small" {...otherProps} title={<CardLabel data={props} />}>
          {item?.children?.map((formItem, j) => {
            return renderComponent(formItem, j);
          })}
        </Card>
      );
    } else if (component === Enums.Components.Collapse) {
      const { props } = item;
      const { items } = props;
      const panels = items?.map((panel) => {
        return {
          ...panel,
          label: <CollapseLabel data={panel} />,
          children: (
            <>
              {panel?.children?.map((formItem, j) => {
                return renderComponent(formItem, j);
              })}
            </>
          ),
        };
      });

      return <Collapse key={index} data={item} items={panels} />;
    } else if (component === Enums.Components.SpaceWrapper) {
      const { children } = item;
      const renderedChildren = children.map((child, index) => renderComponent(child, index));
      return (
        <SpaceWrapper key={index} {...props}>
          {renderedChildren}
        </SpaceWrapper>
      );
    } else if (component === Enums.Components.LangTabs) {
      return <LangTabs key={index} {...props} />;
    } else {
      const { key, label } = item;

      if (visibleFields[key] === false) {
        return null;
      }

      if (fieldsProps[key]) {
        item.props = { ...item.props, ...fieldsProps[key] };
      }

      const valuePropName = getValuePropName(item);
      const changeTrigger = getTrigger(item);
      const rules = getFormRules(item);

      return (
        <Form.Item
          key={key}
          name={key}
          label={label}
          rules={rules}
          valuePropName={valuePropName}
          trigger={changeTrigger}
          extra={props?.extra}
          {...item?.formItemProps}
        >
          {renderFormItem(item)}
        </Form.Item>
      );
    }
  }

  function renderFormItem(item) {
    const { component: type, key: name, cascader } = item;
    const validate = () => formRef.current?.validateFields([name]);
    const setFieldValue = (value) => formRef.current?.setFieldValue(name, value);

    const onHandlePressEnter = (e) => {
      e.preventDefault();
      formRef.current.submit();
    };

    const basicComponentsMap = {
      [Enums.Components.Select]: ({ item }) => {
        if (!item?.props) {
          item.props = {};
        }
        if (cascaderOptions?.[name] !== undefined) {
          item.props.options = cascaderOptions[name];
        }

        if (item.searchApi) {
          return (
            <RemoteSearchSelect {...item.props} api={item.searchApi} isGetDefaultOptions={item?.isGetDefaultOptions} />
          );
        } else {
          return (
            <Select
              {...item.props}
              onChange={(value) => {
                handleFieldVisibility({ value, item, form: formRef.current });
                if (cascader) {
                  const { name: cascaderName, searchApi } = cascader;
                  handleSelectChange({
                    value,
                    cascaderName,
                    searchApi,
                    form: formRef.current,
                  });
                }
                item.props.onChange?.(value);
              }}
            />
          );
        }
      },
      [Enums.Components.Radio]: ({ item }) => (
        <Radio.Group
          {...item.props}
          onChange={(e) => {
            const value = e.target.value;
            handleFieldVisibility({ value, item, form: formRef.current });
            handleFieldProps({ value, item });
            item?.props?.onChange?.(e);
          }}
        />
      ),
      [Enums.Components.Checkbox]: ({ item }) => (
        <Checkbox.Group
          {...item.props}
          onChange={(values) => {
            handleFieldVisibility({ value: values, item, form: formRef.current });
            handleFieldProps({ value: values, item });
            item?.props?.onChange?.(values);
          }}
        />
      ),
      [Enums.Components.Switch]: ({ item }) => (
        <Switch
          {...item.props}
          onChange={(values) => {
            handleFieldVisibility({ value: values, item, form: formRef.current });
            item?.props?.onChange?.(values);
          }}
        />
      ),
      [Enums.Components.Tree]: ({ item }) => {
        let defaultExpandedKeys = [];
        if (item.props.defaultExpandedCheckedParents) {
          const treeData = Utils.buildTreeId({
            treeData: item?.props?.treeData,
          });
          const keys = formData?.props?.initialValues?.[name] || [];

          if (treeData && keys) {
            defaultExpandedKeys = Utils.fillTreeKeysParents({
              treeData,
              keys,
            }).map((item) => item.key);
          }
        }
        return <Tree data={item} defaultExpandedKeys={defaultExpandedKeys} />;
      },
      [Enums.Components.Textarea]: ({ item }) => {
        const { isPressEnterSubmit, ...itemOtherProps } = item.props || {};
        return <TextArea {...itemOtherProps} onPressEnter={isPressEnterSubmit ? onHandlePressEnter : undefined} />;
      },
      [Enums.Components.RichTextEditor]: ({ item }) => <RichTextEditor {...item.props} validate={validate} />,
      [Enums.Components.JSONEditor]: ({ item }) => <JSONEditor {...item.props} validate={validate} />,
      [Enums.Components.ImageUpload]: ({ item }) => <ImageUpload {...item.props} setFieldValue={setFieldValue} />,
      [Enums.Components.Cascader]: ({ item }) => <Cascader {...item.props} />,
      [Enums.Components.DatePicker]: ({ item }) => <DatePicker {...item.props} />,
      [Enums.Components.RangePicker]: ({ item }) => <DatePicker.RangePicker {...item.props} />,
      [Enums.Components.InputNumber]: ({ item }) => <InputNumber {...item.props} />,
      [Enums.Components.Text]: ({ item }) => <Text data={item} {...item.props}></Text>,
      [Enums.Components.Transfer]: ({ item }) => <Transfer {...item.props} />,
      [Enums.Components.EditTable]: ({ item }) => <EditTable {...item.props} />,
      [Enums.Components.TreeSelect]: ({ item }) => <TreeSelect {...item.props} />,
      [Enums.Components.Button]: ({ item }) => (
        <Button
          {...item.props}
          onClick={async () => {
            await Helper.commandHandler({ command: item.command });
          }}
        >
          {item?.text}
        </Button>
      ),
      [Enums.Components.Input]: ({ item }) => {
        const { isPressEnterSubmit, isAutoFocus, isSelected, ...itemOtherProps } = item.props || {};
        return (
          <Input
            {...itemOtherProps}
            autoFocus={isAutoFocus}
            isSelected={isSelected}
            onPressEnter={isPressEnterSubmit ? onHandlePressEnter : undefined}
          />
        );
      },
      [Enums.Components.CameraPhotoUpload]: ({ item }) => <CameraPhotoUpload {...item.props} />,
      [Enums.Components.ColorPicker]: ({ item }) => <ColorPicker {...item.props} />,
      [Enums.Components.TextEditable]: ({ item }) => <TextEditable data={item} {...item.props} />,
      [Enums.Components.Input]: ({ item }) => <Input {...item.props} />,
      [Enums.Components.FileUpload]: ({ item }) => <FileUpload {...item.props} />,
      [Enums.Components.CheckboxSingle]: ({ item }) => (
        <CheckboxSingle
          {...item.props}
          onChange={(value) => {
            handleFieldVisibility({ value, item, form: formRef.current });
            handleFieldProps({ value, item });
            item?.props?.onChange?.(value);
          }}
        />
      ),
    };

    const Components = { ...basicComponentsMap, ...extendComponents };

    if (Components[type]) {
      return Components[type]({ item, form, cascaderOptions, validate, setFieldValue });
    }
    return null;
  }

  function handleValidateFailed(values) {
    onFinishFailed?.(values);
    setTimeout(() => {
      const element = document.querySelector(".ant-form-item-has-error");
      element?.scrollIntoView({ behavior: "smooth", block: "center" });
    }, 100);
    Utils.dispatchEvent(Enums.EventName.SetSubmitButtonLoading, {
      loading: false,
    });
  }

  async function onHandleFinish(values) {
    const { url, ...options } = formData?.submit?.request;
    const { fieldsToClearOnSubmit, preserveInitialValues = false } = formData?.submit;
    const { submitOthersData: others } = paramsRef.current;
    let finalValues = values;
    // const labels = generateLabels({ formItems: formData?.formItems, values });

    if (preserveInitialValues) {
      const initialValues = formData?.props?.initialValues || {};
      finalValues = { ...initialValues, ...values };
    }

    try {
      Helper.pageLoading(true);
      const result = await axios({
        url,
        method: "POST",
        ...options,
        data: { ...options.data, ...others, ...finalValues },
      }).then((res) => res?.data || {});

      clearFields({ form: formRef.current, fields: fieldsToClearOnSubmit });

      return result;
    } finally {
      Helper.pageLoading(false);
      Utils.dispatchEvent(Enums.EventName.SetSubmitButtonLoading, {
        loading: false,
      });
    }
  }

  useImperativeHandle(ref, () => {
    return formRef.current;
  });

  useEffect(() => {
    (async () => {
      const { type, fetcher } = paramsRef.current;
      if (type === "api") {
        try {
          setLoading(true);
          const { url, ...options } = fetcher?.request;
          const data = await axios({
            url,
            method: "GET",
            ...options,
            data: { ...options.data },
          }).then((res) => res.data?.data || {});
          setFormData(data);
        } finally {
          setLoading(false);
        }
      }
    })();
  }, []);

  useEffect(() => {
    function submit(data) {
      const { formId, ...others } = data;
      Utils.dispatchEvent(Enums.EventName.SetSubmitButtonLoading, {
        loading: true,
      });
      paramsRef.current.submitOthersData = others;
      if (formId === formData?.props?.id) {
        Utils.dispatchEvent(Enums.EventName.SetSubmitButtonLoading, {
          loading: true,
        });
        formRef.current.submit();
      } else {
        Utils.dispatchEvent(Enums.EventName.SetSubmitButtonLoading, {
          loading: false,
        });
      }
    }

    Utils.addEventListener(Enums.EventName.FormSubmit, submit);

    return function () {
      Utils.removeEventListener(Enums.EventName.FormSubmit, submit);
    };
  }, [formData]);

  return (
    <Loading loading={loading}>
      {formData ? (
        <Form
          form={form}
          ref={formRef}
          {...otherProps}
          // labelCol={{
          //   span: 8,
          // }}
          // wrapperCol={{
          //   span: 16,
          // }}
          {...formData?.props}
          onFinish={onFinish ? (values) => onFinish(values, { submit: formData?.submit }) : onHandleFinish}
          onFinishFailed={handleValidateFailed}
        >
          {formData?.formItems?.map((row, index) => {
            return renderComponent(row, index);
          })}
        </Form>
      ) : null}
    </Loading>
  );
}

JSONForm = forwardRef(JSONForm);

JSONForm.propTypes = {
  data: PropTypes.object,
  onFinish: PropTypes.func,
  onFinishFailed: PropTypes.func,
};

export default JSONForm;
