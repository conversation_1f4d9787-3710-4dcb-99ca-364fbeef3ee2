import styles from "./index.module.scss";
import { Input } from "antd";
import { useEffect, useState } from "react";
import { observer } from "mobx-react-lite";
import { reaction } from "mobx";

import store from "@/pages/pms/product/edit/components/FormDesigner/store";
import Select from "../DictionarySelect";

function FontSelectorWithPreview(props) {
  const { value, onChange } = props;
  const [fontStyle, setFontStyle] = useState({});
  const [preview, setPreview] = useState("");

  async function loadFonts(fontFamily, fontUrl) {
    const font = new FontFace(fontFamily, `url(${fontUrl})`);
    await font.load();
    document.fonts.add(font);
    return font;
  }

  function handleChange(font) {
    const currentFont = store.optionsData?.font?.find((item) => item.value === font);
    onChange?.({ ...value, family: currentFont.value, url: currentFont?.res_url ?? "" });
  }

  function handleInputChange(e) {
    setPreview(e.target.value);
  }

  useEffect(() => {
    const disposer = reaction(
      () => ({
        value,
        fontFamily: store.optionsData?.font,
      }),
      async ({ value, fontFamily }) => {
        if (value && fontFamily) {
          const currentFont = fontFamily.find((item) => item.value === value.family);
          if (currentFont) {
            const font = await loadFonts(currentFont.label, currentFont.res_url);
            setFontStyle({ fontFamily: font.family });
          }
        }
      },
      { fireImmediately: true }
    );

    return () => disposer();
  }, [value]);

  return (
    <div>
      <Select {...props} value={value?.family} onChange={handleChange} />
      <div className={styles.fontPreview}>
        <div>字体预览</div>
        <Input className={styles.previewInput} onChange={handleInputChange} value={preview} placeholder="请输入" />
        {preview && (
          <div className={styles.preview} style={fontStyle}>
            {preview}
          </div>
        )}
      </div>
    </div>
  );
}

FontSelectorWithPreview = observer(FontSelectorWithPreview);

export default FontSelectorWithPreview;
