import styles from "./index.module.scss";

import { lazy, useMemo, Suspense } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import useSWR from "swr";
import classNames from "classnames";

import { <PERSON><PERSON>, Card, Spin } from "antd";

import Fetchers from "@/fetchers";
import Helper from "@/helpers";
import Utils from "@/utils";

const Breadcrumbs = lazy(() => import("components/common/Breadcrumbs"));
const JSONComponents = lazy(() => import("components/common/JSONComponent"));
const SubmitButton = lazy(() => import("components/common/Button"));
const Form = lazy(() => import("./components/JSONForm"));

function Edit() {
  const location = useLocation();
  const navigate = useNavigate();
  const isInsideIframe = Helper.isInsideIframe();

  const params = useMemo(() => Utils.getQueryParams(decodeURIComponent(location.search)), [location.search]);

  const extendComponents = {
    Form,
  };

  const { data } = useSWR(params, async () => {
    try {
      Helper.pageLoading(true);
      return await Fetchers.getTemplateProperty({ params }).then((res) => res?.data?.data);
    } finally {
      Helper.pageLoading(false);
    }
  });

  return (
    <div className={styles.container}>
      <div className={classNames("page-header", { [styles.pageHeader]: isInsideIframe })}>
        {!isInsideIframe ? (
          <Suspense fallback={<Spin size="small" />}>
            <Breadcrumbs data={data?.breadcrumbs}></Breadcrumbs>
          </Suspense>
        ) : null}

        <div className="page-header-actions">
          {!isInsideIframe && (
            <Button type="default" onClick={() => navigate(-1)}>
              返回
            </Button>
          )}

          {data?.actions?.map((item, index) => {
            return (
              <Suspense key={index} fallback={<Spin size="small" />}>
                <SubmitButton
                  type="primary"
                  {...item.props}
                  command={item.command}
                  onClick={async () => {
                    await Helper.commandHandler({ command: item.command });
                  }}
                >
                  {item.title}
                </SubmitButton>
              </Suspense>
            );
          })}
        </div>
      </div>
      <div className={styles.content}>
        <Card size="small">
          {data?.content && (
            <Suspense fallback={<Spin />}>
              <JSONComponents data={data?.content} extendComponents={extendComponents} />
            </Suspense>
          )}
        </Card>
      </div>
    </div>
  );
}

export default Edit;
