.page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  form {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
  }

  input,
  select {
    min-width: 180px;
    min-height: 30px;
    box-sizing: border-box;
  }
}

.formContent {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.formControls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.formRow {
  display: flex;
  gap: 5px;

  .label {
    line-height: 1;
  }

  .control {
    flex: 1;
    line-height: 1;

    input {
      margin: 0;
      width: 100%;
    }
  }
}
