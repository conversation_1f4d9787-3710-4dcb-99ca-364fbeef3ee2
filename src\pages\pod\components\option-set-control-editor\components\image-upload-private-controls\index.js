import PropTypes from "prop-types";
import { ControlWrapper, FormControl } from "@/components/react-form-x";
import { Input, InputNumber } from "antd";
import styles from "./index.module.scss";

function ImageUploadPrivateControls() {
  return (
    <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
      <div>
        <FormControl
          name="initial_image"
          render={(props) => (
            <ControlWrapper
              {...props}
              render={(props) => (
                <div>
                  <div>Initial Image:</div>
                  <div>
                    <Input {...props}></Input>
                  </div>
                </div>
              )}
            ></ControlWrapper>
          )}
        ></FormControl>
      </div>
      <div className={styles.flexRow}>
        <div>
          <FormControl
            name="button_text"
            render={(props) => (
              <ControlWrapper
                {...props}
                render={(props) => (
                  <div>
                    <div>Button Text:</div>
                    <div>
                      <Input {...props}></Input>
                    </div>
                  </div>
                )}
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
        <div>
          <FormControl
            name="button_class"
            render={(props) => (
              <ControlWrapper
                {...props}
                render={(props) => (
                  <div>
                    <div>Button Class:</div>
                    <div>
                      <Input {...props}></Input>
                    </div>
                  </div>
                )}
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
        <div>
          <FormControl
            name="image_min_width"
            render={(props) => (
              <ControlWrapper
                {...props}
                render={({ name, value, onChange }) => (
                  <div>
                    <div>Image Min Width:</div>
                    <div>
                      <InputNumber
                        value={value}
                        onChange={(value) => {
                          onChange(null, { [name]: value });
                        }}
                        style={{ width: `100%` }}
                      ></InputNumber>
                    </div>
                  </div>
                )}
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
        <div>
          <FormControl
            name="image_min_height"
            render={(props) => (
              <ControlWrapper
                {...props}
                render={({ name, value, onChange }) => (
                  <div>
                    <div>Image Min Height:</div>
                    <div>
                      <InputNumber
                        value={value}
                        onChange={(value) => {
                          onChange(null, { [name]: value });
                        }}
                        style={{ width: `100%` }}
                      ></InputNumber>
                    </div>
                  </div>
                )}
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
      </div>
    </div>
  );
}

ImageUploadPrivateControls.propTypes = {
  keyPath: PropTypes.array.isRequired,
};

export default ImageUploadPrivateControls;
