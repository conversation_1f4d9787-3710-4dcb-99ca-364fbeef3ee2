import { STONE_IDENTIFIERS, STONE_TYPE } from "../enums";

const calculator = Object.freeze({
  calculate({ stones, identifiers, form }) {
    if (!Array.isArray(stones) || !form) return;

    const currentValues = form.getFieldsValue();

    // 计算主钻属性
    const mainStoneValues = this.calculateTypeProperties({
      stones,
      identifiers,
      stoneType: STONE_TYPE.MAIN,
    });

    // 计算副钻属性
    const secondaryStoneValues = this.calculateTypeProperties({
      stones,
      identifiers,
      stoneType: STONE_TYPE.SECONDARY,
    });

    form.setFieldsValue({
      ...currentValues,
      ...mainStoneValues,
      ...secondaryStoneValues,
    });
  },

  /**
   * 计算宝石属性
   * @param {Object} params
   * @param {Array} params.stones - 石头列表
   * @param {String} params.stoneType - 石头类型
   * @returns {Object}
   */
  calculateTypeProperties({ stones, identifiers, stoneType }) {
    // 根据类型筛选石头
    const typeStones = stones.filter((stone) => stone.type === stoneType);

    const identifiersKeyMap = identifiers[stoneType];

    if (!identifiersKeyMap) return {};

    // 是否为副钻
    const isSecondary = stoneType === STONE_TYPE.SECONDARY;
    const IDENTIFIERS = STONE_IDENTIFIERS[stoneType];

    // 计算结果对象
    const result = {};

    // 1. 计算克拉总重
    result[identifiersKeyMap[IDENTIFIERS.GRAM_WEIGHT]] = this.calculateTotalCaratWeight(typeStones);

    // 2. 石头形状(产品页展示)
    result[identifiersKeyMap[IDENTIFIERS.STONE_SHAPE_PRODUCT_DISPLAY]] = this.getUniqueValues(
      typeStones,
      "shape_value"
    );

    // 3. 石头颜色(产品页展示)
    result[identifiersKeyMap[IDENTIFIERS.COLOR_STONE_PRODUCT_DISPLAY]] = this.getUniqueValues(
      typeStones,
      "color_value"
    );

    // 4. 石头形状(类目页筛选)
    // 副钻只考虑克拉数≥1的
    const shapeStonesForFilter = isSecondary ? this.filterLargeStones(typeStones) : typeStones;
    result[identifiersKeyMap[IDENTIFIERS.STONE_SHAPE]] = this.getUniqueValues(shapeStonesForFilter, "shape_value");

    // 5. 石头颜色(类目页筛选)
    // 副钻只考虑克拉数≥1的
    const colorStonesForFilter = isSecondary ? this.filterLargeStones(typeStones) : typeStones;
    result[identifiersKeyMap[IDENTIFIERS.COLOR_STONE]] = this.getUniqueValues(colorStonesForFilter, "color_value");

    // 6. 石头规格
    result[identifiersKeyMap[IDENTIFIERS.STONE_SIZE]] = this.getUniqueValues(typeStones, "specifications").join(",");

    return result;
  },

  /**
   * 计算总克拉数
   * @param {Array} stones - 石头列表
   * @returns {number}
   */
  calculateTotalCaratWeight(stones) {
    return stones.reduce((sum, stone) => {
      const caratWeight = parseFloat(stone.carat_weight) || 0;
      const quantity = parseInt(stone.amount) || 1;
      const numericSum = parseFloat(sum) || 0;
      const result = numericSum + caratWeight * quantity;
      return result.toFixed(5);
    }, 0);
  },

  /**
   * 获取唯一值数组
   * @param {Array} stones - 宝石列表
   * @param {string} field - 字段名
   * @returns {Array}
   */
  getUniqueValues(stones, field) {
    return [...new Set(stones.map((stone) => stone[field]).filter(Boolean))];
  },

  /**
   * 筛选大克拉数宝石（≥1ct）
   * @param {Array} stones - 宝石列表
   * @returns {Array}
   */
  filterLargeStones(stones) {
    return stones.filter((stone) => {
      const caratWeight = parseFloat(stone.carat_weight) || 0;
      return caratWeight >= 1;
    });
  },
});

export default calculator;
