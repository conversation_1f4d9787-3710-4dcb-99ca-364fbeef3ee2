import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import HttpBackend from "i18next-http-backend";
import { autorun } from "mobx";

import store from "@/stores";
import Api from "@/fetchers/api";
import tools from "@/fetchers/tools";
import Fetchers from "fetchers";

const DEFAULT_LANGUAGE = "zh-CN";

const defaultResources = {
  "zh-CN": {
    translation: {
      Poll: "轮询",
      Stop: "停止",
      ColumnSettings: "列设置",
      FullScreen: "全屏",
      ExitFullScreen: "退出全屏",
      Clear: "清除",
      Confirm: "确定",
      LogOut: "退出登录",
      FileExportList: "文件导出列表",
      ClickToDownload: "点击下载",
      ExportList: "导出列表",
      Delete: "删除",
      AreYouSureYouWantToDeleteThisFile: "确定删除该文件吗？",
      AreYouSureYouWantToDelete: "确定删除吗？",
      SelectAll: "全选",
      Upload: "上传",
      FilterParams: "筛选条件",
      FixedOnTheLeftSide: "固定在左侧",
      FixedOnTheRightSide: "固定在右侧",
      FixedOnTheBeginning: "固定在列首",
      FixedOnTheTail: "固定在列尾",
      Unfixed: "不固定",
      ColumnShow: "列显示",
      Reset: "重置",
      PaginationTotal: "第{{range0}}-{{range1}}条/总共{{total}}条",
    },
  },
};

function getCurrentLanguage() {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get("language") || DEFAULT_LANGUAGE;
}

i18n
  .use(HttpBackend)
  .use(initReactI18next)
  .init({
    lng: getCurrentLanguage(),
    fallbackLng: DEFAULT_LANGUAGE, // 翻译不可用时，默认语言
    debug: false,
    detection: {
      order: [],
    },
    load: "currentOnly", // 仅加载当前语言，不加载回退语言
    interpolation: {
      escapeValue: false,
    },
    backend: {
      loadPath: (lng, namespaces) => {
        const lang = lng[0];
        return tools.setPathParams(Api.getI18nResource, { lang });
      },
      request: (options, url, payload, callback) => {
        if (!store.systemInfo?.language) {
          const lang = url.split("/").pop();
          const resource = defaultResources[lang]?.translation || defaultResources[DEFAULT_LANGUAGE].translation;
          callback(null, { status: 200, data: JSON.stringify({ success: true, data: resource }) });
          return;
        }

        Fetchers.getI18nResource({ url })
          .then((res) => {
            return callback(null, { status: 200, data: res.data?.data });
          })
          .catch((error) => console.warn("Failed to load remote i18n resource:", error));
      },
    },
    resources: defaultResources,
  });

let hasReloaded = false;
autorun(() => {
  if (store.systemInfo?.language && !hasReloaded) {
    hasReloaded = true;
    const currentLang = getCurrentLanguage();
    i18n.reloadResources(currentLang); // 触发backend。request
    // .then(() => {
    //   console.log("i18n resources reloaded for language:", currentLang);
    // });
  }
});

export default i18n;
