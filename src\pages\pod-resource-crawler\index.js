import React, { useState } from "react";
import { Form, Input, But<PERSON>, Row, Col } from "antd";
import { mutate } from "swr";

import Page from "components/Page";
import Fetchers from "fetchers";
import Api from "@/fetchers/api";
import Helper from "helpers";

import PodResources from "./list";
import styles from "./inde.module.scss";

const PodResourceCrawler = () => {
  const [data, setData] = useState({ downloadUrl: "" });
  const [loading, setLoading] = useState(false);

  const onFinish = async (values) => {
    try {
      setLoading(true);
      const response = await Fetchers.crawlPodResources({ data: values }).then((res) => res.data);

      if (response.success) {
        setData(response.data);
        updateList();
        Helper.openMessage({ type: "success", content: "抓取成功" });
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const updateList = () => {
    mutate([Api.getPodResources]);
  };

  return (
    <Page>
      <div className={styles.container}>
        <div className={styles.form}>
          <Form onFinish={onFinish} size="large">
            <Row justify="center" wrap={false} gutter={10}>
              <Col span={22}>
                <Form.Item label="POD 产品链接" name="url" rules={[{ required: true }, { type: "url" }]}>
                  <Input placeholder="请输入有效 URL" allowClear type="url" />
                </Form.Item>
              </Col>
              <Col>
                <Form.Item>
                  <Button type="primary" htmlType="submit" loading={loading}>
                    抓取
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
        <PodResources currentDownloadUrl={data.downloadUrl} onUpdate={updateList} />
      </div>
    </Page>
  );
};

export default PodResourceCrawler;
