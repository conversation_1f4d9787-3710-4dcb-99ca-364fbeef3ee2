import EditOptionSet from "@/pages/pod/edit-option-set";
import Utils from "@/utils";
import { Button, Select } from "antd";
import styles from "./index.module.scss";
import useSWR from "swr";
import Fetchers, { Api } from "@/fetchers";
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import Helper from "@/helpers";
import { CloseOutlined } from "@ant-design/icons";

function IframeEditOptionSet(props) {
  const [editorVisible, setEditorVisible] = useState(false);
  const { optionSetId } = Utils.getQueryParams(window.location.href);
  const [templateId, setTemplateId] = useState();
  const [optionSet, setOptionSet] = useState();
  const { data: templates } = useSWR([Api.getPodTemplates], async () => {
    return await Fetchers.getPodTemplates().then((res) => res.data?.data?.tableProps.dataSource);
  });
  const editorRef = useRef();
  const navigate = useNavigate();

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current };

  function postMessage(message) {
    const origin = "*";
    if (window.opener) {
      window.opener.postMessage(message, origin);
    } else if (window.top) {
      window.top.postMessage(message, origin);
    }
  }

  useEffect(() => {
    (async () => {
      let optionSet;
      setEditorVisible(true);
      const editor = editorRef.current;
      editor?.setPageLoading(true);
      if (optionSetId && editor?.optionSet?.id !== optionSetId) {
        optionSet = await Fetchers.getPodOptionSet({ id: optionSetId }).then((res) => res.data.data.item);
        setTemplateId(optionSet?.template_id);
      }
      if (optionSet) {
        setOptionSet(optionSet);
      } else {
        setTemplateId(null);
        setEditorVisible(false);
      }
      editor?.setPageLoading(false);
    })();
  }, [optionSetId]);

  useEffect(() => {
    editorRef.current?.setOptionSet(optionSet);
  }, [optionSet]);

  return (
    <div className={styles.page}>
      <div>
        <div>Template:</div>
        <div>
          {optionSetId ? (
            <Button
              icon={<CloseOutlined />}
              iconPosition="end"
              style={{ display: "flex", alignItems: "center", justifyContent: "center" }}
              onClick={async () => {
                if (!optionSet.create_from_template) {
                  await Fetchers.deletePodOptionSet({ id: optionSetId });
                }
                navigate(window.location.pathname);
              }}
            >
              {optionSet?.title}
            </Button>
          ) : (
            <Select
              options={templates?.map((item) => ({ label: item.name, value: item.id }))}
              placeholder="Please select template"
              style={{ width: `100%` }}
              value={templateId}
              onChange={async (value) => {
                const editor = editorRef.current;
                setTemplateId(value);
                if (value) {
                  setEditorVisible(true);
                  editor?.setPageLoading(true);
                  const optionSet = await Fetchers.getPodTemplateOptionSet({ template_id: value }).then(
                    (res) => res.data.data.item
                  );
                  setOptionSet(optionSet);
                  editor?.setPageLoading(false);
                } else {
                  setEditorVisible(false);
                  editor?.setPageLoading(false);
                }
              }}
              allowClear
            ></Select>
          )}
        </div>
      </div>
      <div className={styles.editOptionSet} style={{ display: editorVisible ? "block" : "none" }}>
        <EditOptionSet
          ref={editorRef}
          onSubmit={async (event, values) => {
            Helper.pageLoading(true);
            const promises = [];
            let optionSet = Utils.cloneDeep(values);
            optionSet.create_from_template = false;
            optionSet.linked_to_product = true;
            if (optionSetId) {
              promises.push(
                Fetchers.updatePodOptionSet({ id: optionSetId, data: optionSet }).then((res) => res.data.data.item)
              );
            } else {
              promises.push(Fetchers.addPodOptionSet(optionSet).then((res) => res.data.data.item));
            }
            promises.push(Fetchers.getPodTemplate({ id: templateId }).then((res) => res.data.data.item));
            const [newOptionSet, template] = await Promise.all(promises);
            postMessage({ action: "submit", payload: { option_set: newOptionSet, template } });
            Helper.pageLoading(false);
          }}
          className={styles.editOptionSetPage}
        ></EditOptionSet>
      </div>
    </div>
  );
}

export default IframeEditOptionSet;
