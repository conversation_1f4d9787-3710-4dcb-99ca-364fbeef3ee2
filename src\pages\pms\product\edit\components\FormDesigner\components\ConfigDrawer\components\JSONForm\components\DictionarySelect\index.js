import { Select as AntdSelect } from "antd";
import useS<PERSON> from "swr";

import Fetchers from "@/fetchers";
import store from "@/pages/pms/product/edit/components/FormDesigner/store";

function DictionarySelect(props) {
  const { id, identifiers, fieldNames = { value: "value", label: "title" }, ...restProps } = props;
  const identifier = identifiers[id];

  const { data: options = [] } = useSWR([identifier, Fetchers.Api.getCustomizeDictionaryOption], async () => {
    try {
      if (!identifier) return;

      if (store.optionsData[id]) {
        return store.optionsData[id];
      }

      const result = await Fetchers.getCustomizeDictionaryOption({ params: { dict: [identifier] } }).then(
        (res) => res?.data?.data[identifier]
      );

      store.setOptionsData({ id, options: result });
      return result;
    } finally {
    }
  });

  return (
    <AntdSelect
      fieldNames={fieldNames}
      options={options}
      showSearch
      placeholder="请选择"
      optionFilterProp="title"
      {...restProps}
    />
  );
}

export default DictionarySelect;
