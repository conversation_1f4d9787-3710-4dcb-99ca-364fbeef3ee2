import styles from "./index.module.scss";
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import Fetchers from "@/fetchers";
import Utils from "@/utils";
import { Button, Collapse, message, Tag } from "antd";
import { ControlWrapper, Form, FormControl } from "@/components/react-form-x";
import Loading from "@/components/common/Loading";
import OptionSetControlEditor from "@/pages/pod/components/option-set-control-editor";
import Helper from "@/helpers";
import useForceUpdate from "@/hooks/useForceUpdate";
import classNames from "classnames";
import PropTypes from "prop-types";
import { renderI18nSelect } from "@/pages/pod/common";

function EditOptionSet(props, ref) {
  const { onSubmit = handleSubmit, className } = props;
  const { id } = Utils.getQueryParams(window.location.href);
  const [optionSet, setOptionSet] = useState();
  const [pageLoading, setPageLoading] = useState(true);
  const [isFormChanged, setIsFormChanged] = useState(false);
  const forceUpdate = useForceUpdate();
  const formRef = useRef();

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current, props, optionSet };

  const collapseItems = optionSet?.controls?.map((item, index) => {
    const keyPath = ["controls", index];
    return {
      key: item.id,
      label: (
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <div style={{ display: "flex", gap: 10 }}>
            <div>{item.label}</div>
            {item.library ? <Tag>Library: {item.library.name}</Tag> : null}
            {item.category ? <Tag>Category: {item.category.name}</Tag> : null}
          </div>
          <div style={{ width: 95 }}>
            {item.elementId ? <div style={{ textAlign: "start" }}>ElementId: {item.elementId}</div> : null}
          </div>
        </div>
      ),
      children: (
        <OptionSetControlEditor
          keyPath={keyPath}
          control={item}
          optionSet={optionSet}
          onLabelChange={forceUpdate}
        ></OptionSetControlEditor>
      ),
    };
  });

  function handleSubmit(event, values) {
    Helper.pageLoading(true);
    const { id } = Utils.getQueryParams(window.location.href);
    Fetchers.updatePodOptionSet({ id, data: values })
      .then(() => {
        message.open({ type: "success", content: "保存成功！" });
      })
      .finally(() => {
        Helper.pageLoading(false);
      });
  }

  useImperativeHandle(
    ref,
    () => {
      return {
        get optionSet() {
          return Utils.cloneDeep(paramsRef.current.optionSet);
        },
        setOptionSet(optionSet) {
          setOptionSet(optionSet);
          formRef.current?.updateValue(optionSet);
        },
        setPageLoading,
      };
    },
    []
  );

  useEffect(() => {
    if (id) {
      Fetchers.getPodOptionSet({ id })
        .then((res) => {
          const result = res.data;
          if (result.success) {
            const { item: optionSet } = result.data;
            paramsRef.current.defaultFormValue = Utils.cloneDeep(optionSet);
            formRef.current?.updateValue(optionSet);
            setOptionSet(optionSet);
          }
        })
        .finally(() => {
          setPageLoading(false);
        });
    }
  }, [id]);

  return (
    <Loading loading={pageLoading} className={classNames(styles.page, className)}>
      <div>
        <Form
          ref={formRef}
          onChange={() => {
            setIsFormChanged(true);
          }}
          onSubmit={onSubmit}
          style={{ display: "flex", flexDirection: "column", gap: 20 }}
          updateMode="mutation"
        >
          <div>
            <FormControl
              name="title"
              render={(props) => {
                return (
                  <ControlWrapper
                    {...props}
                    render={({ name, value, onChange }) => {
                      return (
                        <div>
                          <div>Set Title:</div>
                          <div>{renderI18nSelect({ form: formRef.current, name, value, onChange })}</div>
                        </div>
                      );
                    }}
                  ></ControlWrapper>
                );
              }}
            ></FormControl>
          </div>
          <div className={styles.options}>
            {collapseItems?.length > 0 ? (
              <Collapse items={collapseItems} defaultActiveKey={[collapseItems?.[0]?.key]}></Collapse>
            ) : null}
          </div>
          <div style={{ display: "flex", justifyContent: "flex-end", gap: 16 }}>
            {isFormChanged ? (
              <Button
                onClick={() => {
                  setIsFormChanged(false);
                  const defaultFormValue = Utils.cloneDeep(paramsRef.current.defaultFormValue);
                  formRef.current?.updateValue(defaultFormValue);
                  setOptionSet(defaultFormValue);
                }}
              >
                Reset
              </Button>
            ) : null}
            <Button type="primary" htmlType="submit">
              Submit
            </Button>
          </div>
        </Form>
      </div>
    </Loading>
  );
}

EditOptionSet = forwardRef(EditOptionSet);

EditOptionSet.propTypes = {
  value: PropTypes.any,
  onSubmit: PropTypes.func,
  className: PropTypes.string,
  pageLoading: PropTypes.bool,
};

export default EditOptionSet;
