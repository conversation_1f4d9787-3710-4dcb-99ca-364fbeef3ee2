import { arrayMove } from "@dnd-kit/sortable";
import cloneDeep from "lodash.clonedeep";

import Fetchers from "@/fetchers";
import store from "@/pages/pms/product/edit/components/FormDesigner/store";

import { widgets } from "./widgets";
import Enums from "./enums";

export const reorderItems = (items, activeId, overId) => {
  const oldIndex = items.findIndex((item) => item.id === activeId);
  const newIndex = items.findIndex((item) => item.id === overId);
  return arrayMove(items, oldIndex, newIndex);
};

export const generateKey = ({ prefix, items }) => {
  if (!items?.length) {
    return `${prefix}1`;
  }

  const flatItems = flatten(items);

  const indexes = flatItems
    .map((item) => {
      const { key } = item;
      if (!key) return 0;
      const match = key.match(/\d+$/);
      return match ? parseInt(match[0]) : 0;
    })
    .filter((num) => !isNaN(num));

  const maxIndex = Math.max(...indexes);
  return `${prefix}${maxIndex + 1}`;
};

export function formatOldData(oldData) {
  const { items, ...rest } = oldData;
  return { ...rest, items: formatItems(items) };
}

/**
 * 格式化items，旧的items中需要把drakey， icon删除掉；把basicComponents中的component_config赋值给item
 * 如果有title_label,title_value,placeholder_label,则放入component_config中
 * @param {*} items
 * @returns
 */
function formatItems(items) {
  return items.map((item) => {
    const { drakey, icon, title_label, title_value, placeholder_label, ...rest } = item;
    const targetComponent = widgets.find((component) => component.type === item.type);
    const { component_config } = targetComponent ?? {};
    const childrenName = component_config?.childrenName ?? "children";
    const newItem = { ...rest, component_config: { ...component_config, title_label, title_value, placeholder_label } };

    if (item[childrenName]?.length) {
      newItem[childrenName] = formatItems(item[childrenName]);
    }

    return newItem;
  });
}

function flatten(items = [], parentKey = null) {
  return items.reduce((acc, item, index) => {
    return [...acc, { ...item, parentKey, index }, ...flatten(item.children || item.items, item.key)];
  }, []);
}

export function flattenTree(items) {
  return flatten(items);
}

// 处理items，过滤掉component_config和isDisabled
export function sanitizeItems(items) {
  const newItems = [...items];

  return newItems.map((item) => {
    const { component_config, dragDisabled, ...rest } = item;
    const childrenName = component_config?.childrenName || "children";
    if (item[childrenName]?.length) {
      rest[childrenName] = sanitizeItems(item[childrenName]);
    }
    return rest;
  });
}

export function findComponentByKey({ items, key }) {
  if (!items || !key) return null;

  for (const item of items) {
    if (item.key === key) return item;

    const childrenName = item.component_config?.childrenName || "children";

    const component = findComponentByKey({ items: item[childrenName] ?? [], key });
    if (component) return component;
  }

  return null;
}

export function buildItems({ items, activeKey, dropKey, childrenName }) {
  if (!items || !activeKey || !dropKey || !childrenName) {
    return items;
  }

  const newItems = [...items];

  // 要移动的组件
  const activeComponent = findComponentByKey({ items: newItems, key: activeKey });
  if (!activeComponent) {
    console.warn(`未找到key为${activeKey}的drag组件`);
    return newItems;
  }

  // 目标组件
  const dropComponent = findComponentByKey({ items: newItems, key: dropKey });
  if (!dropComponent) {
    console.warn(`未找到key为${dropKey}的drop组件`);
    return newItems;
  }

  if (!Array.isArray(dropComponent[childrenName])) {
    dropComponent[childrenName] = [];
  }

  // 从原位置删除组件
  function removeFromParent(items, targetKey) {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.key === targetKey) {
        items.splice(i, 1);
        return true;
      }

      const childrenName = item.component_config?.childrenName || "children";
      const children = item[childrenName] || [];
      if (children?.length && removeFromParent(children, targetKey)) {
        return true;
      }
    }
    return false;
  }

  removeFromParent(newItems, activeKey);

  // 添加到新位置
  dropComponent[childrenName].push(activeComponent);

  return addDisabledToComponents({ items: newItems, dropKey, childrenName });
}

export function addDisabledToComponents({ items, dropKey, childrenName }) {
  const newItems = [...items];

  const dropComponent = findComponentByKey({ items: newItems, key: dropKey });
  if (!dropComponent) {
    console.warn(`addDisabled未找到key为${dropKey}的组件`);
    return newItems;
  }

  function addDisabled(components) {
    if (!Array.isArray(components)) return;

    components.forEach((component) => {
      if (component.key === dropKey) {
        component.dragDisabled = true;

        if (component[childrenName]?.length) {
          component[childrenName].forEach((item) => {
            item.dragDisabled = false;
          });
        }
      } else {
        component.dragDisabled = true;
      }

      if (component.children?.length) {
        addDisabled(component.children);
      }
      if (component.items?.length) {
        addDisabled(component.items);
      }
    });
  }

  addDisabled(newItems);

  return newItems;
}

export function removeComponent({ items, key, type }) {
  if (!Array.isArray(items)) return items;

  return items
    .filter((item) => {
      if (type) {
        return item.type !== type;
      }
      return item.key !== key;
    })
    .map((item) => ({ ...item, items: removeComponent({ items: (item.items || item.children) ?? [], key, type }) }));
}

export function moveComponent({ items, activeKey, overKey }) {
  if (!items || !activeKey || !overKey) {
    return items;
  }

  const newItems = [...items];

  function findAndMove(components) {
    if (!Array.isArray(components)) return false;

    const activeIndex = components.findIndex((item) => item.key === activeKey);
    const overIndex = components.findIndex((item) => item.key === overKey);

    if (activeIndex !== -1 && overIndex !== -1) {
      if (activeIndex === overIndex) {
        return true;
      }

      const overComponent = components[overIndex];
      if (overComponent.type === "ghost") {
        return true;
      }

      const reordered = arrayMove(components, activeIndex, overIndex);
      components.splice(0, components.length, ...reordered);
      return true;
    }

    for (const component of components) {
      if (findAndMove(component.children) || findAndMove(component.items)) {
        return true;
      }
    }

    return false;
  }

  findAndMove(newItems);
  return newItems;
}

export function isInGroup(items, targetKey) {
  for (const item of items) {
    if (item.component_config?.isGroup) {
      const childrenName = item.component_config?.childrenName || "children";
      const children = item[childrenName] || [];

      if (children.some((child) => child.key === targetKey)) {
        return true;
      }

      if (isInGroup(children, targetKey)) {
        return true;
      }
    }
  }
  return false;
}

export function getComponentTitle(widget, key, options) {
  if (widget?.[key] && options) {
    const currentOption = options.find((item) => item.value === widget?.[key]);
    return currentOption ? `${currentOption.title} - ${widget.widget_name}` : widget.widget_name;
  }

  return widget?.widget_name;
}

export function getComponentPlaceholder(widget, options) {
  if (widget?.placeholder && options) {
    const currentOption = options.find((item) => item.value === widget.placeholder);
    return currentOption ? currentOption.title : widget.widget_name;
  }

  return widget?.placeholder;
}

export async function getComponentConfigOptions({ key, identifier }) {
  if (!identifier) return [];

  if (store.optionsData[key]) {
    return store.optionsData[key];
  }

  try {
    const result = await Fetchers.getCustomizeDictionaryOption({
      params: { dict: [identifier] },
    }).then((res) => res?.data?.data[identifier]);

    store.setOptionsData({ id: [key], options: result });
    return result;
  } catch (error) {
    console.error("Failed to fetch options:", error);
    return [];
  }
}

export function cloneComponent(component) {
  const { component_config, ...rest } = component;

  const cloned = { ...rest };

  if (component_config) {
    cloned.component_config = { ...component_config };
    if (component_config.preview_component) {
      cloned.component_config.preview_component = component_config.preview_component;
    }
  }

  const childrenName = component_config?.childrenName || "children";
  if (Array.isArray(component[childrenName])) {
    cloned[childrenName] = [];
  }

  Object.keys(rest).forEach((key) => {
    if (Array.isArray(rest[key])) {
      cloned[key] = [...rest[key]];
    } else if (typeof rest[key] === "object" && rest[key] !== null) {
      cloned[key] = { ...rest[key] };
    }
  });

  return cloned;
}

/**
 * 禁用条件配置的折叠面板。
 *
 * 忽略的属性类型：主属性、次属性
 * 当部件在组中
 *
 * @param {object} options - 配置对象
 * @param {object} options.component - 画布区选中的部件。
 * @param {object} options.widgetForm - 部件表单配置对象。
 * @returns {object} 返回一个新的部件配置对象，如果部件配置中包含条件配置，则禁用折叠面板。
 */
export function disableCollapsePanelOnCondition({ component, widgetForm } = {}) {
  if (Object.keys(component).length === 0) {
    return component;
  }

  const componentConfig = cloneDeep(component?.component_config);
  const key = component.key;
  const ignoredPropertyTypes = [Enums.PropertyType.master, Enums.PropertyType.second];
  const isIgnored = ignoredPropertyTypes.includes(key);

  if (isIgnored || isInGroup(widgetForm.items, key)) {
    const widgetConfig = componentConfig?.widget_config;
    if (widgetConfig) {
      const formItems = widgetConfig?.formItems;

      if (formItems?.length) {
        const foundCollapseItem = formItems.find((item) => item.component === "Collapse");
        const items = foundCollapseItem?.props?.items;

        if (Array.isArray(items) && items.length > 0) {
          for (const item of items) {
            if ((item?.children ?? [])?.some((child) => child.key === "conditions")) {
              item.collapsible = "disabled";
              break;
            }
          }
        }
      }
    }
  }

  return { ...component, component_config: componentConfig };
}

/**
 * 检查画布区部件中是否有部件的条件配置中使用了被删除的 key。
 *
 * @param {object} options - 配置对象
 * @param {object[]} options.items -  需要清理的画布区部件。
 * @param {string} options.key - 被删除部件的 key。
 * @returns {boolean} 如果有部件的条件配置中使用了被删除的 key，则返回 true，否则返回 false。
 */
export function isKeyUsedInConditions({ items, key } = {}) {
  let result = false;

  for (const item of items) {
    const conditions = item?.conditions ?? [];
    if (Array.isArray(conditions) && conditions.length > 0) {
      result = conditions.some((condition) => condition.field === key);
      if (result) {
        break;
      }
    }
  }

  return result;
}

/**
 * 清理画布区部件中与被删除部件相关的条件配置。
 *
 * @param {object} options - 配置对象
 * @param {object[]} options.items -  需要清理的部件。
 * @param {string} options.key - 被删除部件的 key。
 * @returns {object[]} 返回一个新的、经过清理的部件列表。
 */
export function cleanUpConditionsByDeletedWidgetKey({ items, key } = {}) {
  if (!Array.isArray(items) || items.length === 0) {
    return items;
  }

  return items.map((item) => {
    const conditions = item?.conditions ?? [];

    if (conditions.length > 0) {
      item.conditions = conditions.filter((condition) => condition.field !== key);
    }

    return item;
  });
}

/**
 * 获取需要更新条件配置的部件。
 *
 * 在保存时，检测当前选中的部件有没有更新 options,
 * 如果有，则返回需要更新条件配置的部件。
 *
 * @param {object} options - 配置对象
 * @param {object} options.component - 画布区选中的部件。
 * @param {object} options.widgetForm - 部件表单配置对象。
 * @returns {object[]} 返回一个需要更新条件配置的部件列表。
 */
function getWidgetsToUpdateConditions({ component, widgetForm } = {}) {
  const key = component?.key;
  const options = component?.options ?? [];
  const widgets = widgetForm?.items ?? [];

  if (!key || !options.length || !widgets.length) {
    return [];
  }

  let widgetsToUpdateConditions = [];
  const optionValues = options.map((item) => item.value);

  const isSubset = (superset, subset) => subset.every((item) => superset.includes(item));

  for (const widget of widgets) {
    const conditions = widget?.conditions ?? [];

    if (conditions.length === 0) {
      continue;
    }

    conditions.forEach(({ field, value = [] }) => {
      if (field === key && !isSubset(optionValues, value)) {
        widgetsToUpdateConditions.push(widget);
      }
    });
  }

  return widgetsToUpdateConditions;
}

/**
 * 通知用户需要更新条件配置的部件。
 *
 * @param {object} options - 配置对象
 * @param {object} options.component - 画布区选中的部件。
 * @param {object} options.widgetForm - 部件表单配置对象。
 * @param {object} options.notification - 通知组件，用于显示警告信息。
 */
export function notifyConditionsUpdate({ component, widgetForm, notification } = {}) {
  const widgetsToUpdateConditions = getWidgetsToUpdateConditions({ component, widgetForm });

  if (widgetsToUpdateConditions.length > 0) {
    notification?.warning({
      message: "由于部件的 Options 更新，以下部件需要及时更新条件配置",
      description: (
        <ul>
          {widgetsToUpdateConditions.map((item) => (
            <li key={item.key}>{`• ${item?.title} - ${item?.widget_name}`}</li>
          ))}
        </ul>
      ),
      placement: "bottomLeft",
      duration: 0,
    });
  }
}
