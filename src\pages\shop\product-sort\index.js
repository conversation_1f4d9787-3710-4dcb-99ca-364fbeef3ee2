import styles from "./index.module.scss";
import { useEffect, useState, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Row, Col, Card, Button } from "antd";

import Fetchers from "fetchers";
import Utils from "utils";

import Loading from "components/common/Loading";
import Breadcrumbs from "components/common/Breadcrumbs";
import TreePanel from "./components/TreePanel";
import ProductPanel from "./components/ProductPanel";
import Export from "./components/Export";
import SettingSorter from "./components/SettingSorter";
import ProductSortActions from "./components/ProductSortActions";

function ProductPanelHeader({ selectedNode = {}, productData = {}, id, selectedItems = [], page }) {
  const { title } = selectedNode;

  return (
    <div className={styles.productPanelHeader}>
      <div className={styles.productPanelTitle}>{`${title ? `类目名称${title}` : ""}（合计：${
        productData?.pagination?.total || 0
      }个商品）`}</div>
      <ProductSortActions id={id} selectedItems={selectedItems} page={page} />
    </div>
  );
}

function ShopProductSort() {
  const [loadings, setLoadings] = useState({
    tree: false,
    product: false,
    submit: false,
  });
  const [treeData, setTreeData] = useState({});
  const [productData, setProductData] = useState({ items: [], pagination: {} });
  const [selectedNode, setSelectedNode] = useState({});
  const [selectedIds, setSelectedIds] = useState([]);
  const [productDataPage, setProductDataPage] = useState(0);
  const location = useLocation();
  const navigate = useNavigate();
  const search = new URLSearchParams(location.search);
  const categoryId = search.get("id");
  const paramsRef = useRef({});

  const selectedItems = productData?.items?.filter((item) => selectedIds.includes(item.id));

  paramsRef.current = {
    search,
    categoryId,
    page: productDataPage,
    fetchData,
  };

  async function getTreeData() {
    return Fetchers.getShopProductSortTree().then((res) => res.data?.data);
  }

  async function fetchData() {
    setLoadings((loadings) => ({ ...loadings, tree: true }));

    try {
      const { categoryId } = paramsRef.current;
      const treeData = await getTreeData();
      setTreeData(treeData);

      // 有id时默认选中
      if (categoryId) {
        const flatTree = Utils.flatTree({ treeData: treeData?.tree || {} });
        const node = flatTree.find((item) => item.key === categoryId);
        setSelectedNode({ ...node, id: categoryId, title: node?.title });

        const result = await getProductData({ id: categoryId });
        setProductData(result);
      }
    } finally {
      setLoadings((loadings) => ({ ...loadings, tree: false }));
    }
  }

  async function getProductData({ id, customPage }) {
    if (!id) return;
    const { page } = paramsRef.current;

    const currentPage = customPage || page + 1;

    const result = await Fetchers.getShopProductSortList({ params: { id, page: currentPage } }).then(
      (res) => res.data?.data
    );

    setProductDataPage(currentPage);

    return result;
  }

  async function handleLoadMore() {
    const result = await getProductData({ id: categoryId });

    setProductData((data) => ({ ...data, items: [...data?.items, ...result?.items] }));
  }

  function updateSearchParams({ searchParams }) {
    navigate({ pathname: location.pathname, search: decodeURIComponent(searchParams.toString()) });
  }

  async function handleTreeChange(selectedNode) {
    const { id } = selectedNode;
    setSelectedNode(selectedNode);
    setProductDataPage(0);
    paramsRef.current.page = 0;

    search.set("id", id);
    updateSearchParams({ searchParams: search });

    setLoadings((loadings) => ({ ...loadings, product: true }));

    try {
      const result = await getProductData({ id });
      setProductData(result);
    } finally {
      setLoadings((loadings) => ({ ...loadings, product: false }));
    }
  }

  async function handleSubmit() {
    try {
      setLoadings((loadings) => ({ ...loadings, submit: true }));
      await Fetchers.updatePosition({ data: { id: categoryId, products: productData?.items } });
    } finally {
      setLoadings((loadings) => ({ ...loadings, submit: false }));
    }
  }

  useEffect(() => {
    const { fetchData } = paramsRef.current;

    fetchData();
  }, []);

  return (
    <div className={styles.container}>
      <div className="page-header">
        <Breadcrumbs data={treeData?.breadcrumbs}></Breadcrumbs>
        <div className="page-header-actions">
          <Export data={selectedItems.map((item) => item.spu).join(",")} />
          <SettingSorter data={selectedNode} categoryId={categoryId} refreshData={fetchData} />
          <Button type="primary" onClick={handleSubmit}>
            保存
          </Button>
        </div>
      </div>
      <div className={styles.content}>
        <Row gutter={16}>
          <Col className={styles.panelWrapper} span={6}>
            <Card title="类目列表">
              <Loading loading={loadings.tree}>
                {treeData?.tree && (
                  <TreePanel data={treeData} defaultSelectedKeys={[search.get("id")]} onSelect={handleTreeChange} />
                )}
              </Loading>
            </Card>
          </Col>
          <Col className={styles.panelWrapper} span={18}>
            <Card
              title={
                <ProductPanelHeader
                  selectedNode={selectedNode}
                  productData={productData}
                  id={categoryId}
                  selectedItems={selectedItems}
                  page={productDataPage}
                />
              }
            >
              <Loading loading={loadings.product}>
                <ProductPanel
                  data={productData?.items}
                  onChange={setProductData}
                  loadMore={handleLoadMore}
                  selectedIds={selectedIds}
                  setSelectedIds={setSelectedIds}
                />
              </Loading>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
}

export default ShopProductSort;
