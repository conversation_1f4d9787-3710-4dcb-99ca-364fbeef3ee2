.lowCodeEntry {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .header {
    margin-bottom: 32px;
    text-align: center;
  }

  .card {
    height: 100%;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
  }

  .cardContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .iconWrapper {
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .cardInfo {
    width: 100%;
  }

  .description {
    min-height: 60px;
    margin-bottom: 24px;
  }
}
