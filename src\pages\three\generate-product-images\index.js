import styles from "./index.module.scss";
import { App, <PERSON><PERSON> } from "antd";
import { useRadioGroup } from "@/hooks/useRadioGroup";
import classNames from "classnames";
import { useEffect, useRef, useState } from "react";
import Fetchers from "@/fetchers";
import Helper from "@/helpers";
import { load3dModel, takePhotos } from "@/pages/three/common";
import Utils from "@/utils";
import { useNavigate } from "react-router-dom";

function GenerateProductImages() {
  const [photos, setPhotos] = useState([]);
  const { value, onClick: handleRadioGroupClick } = useRadioGroup({ options: photos });
  const query = Utils.getQueryParams(window.location.href);
  const canvasBoxRef = useRef();
  const { message: toast } = App.useApp();
  const navigate = useNavigate();

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current, handleRadioGroupClick };

  async function handleSave() {
    Helper.pageLoading(true);
    const query = Utils.getQueryParams(window.location.href);
    const uploadResults = await Promise.all(
      photos.map((photo, index) => {
        const file = Utils.base64ToFile({ base64: photo.src, filename: `image${index + 1}.png` });
        return Fetchers.uploadFile({ file, headers: { "do-not-save": true } }).then((res) => res.data);
      })
    );
    const images = uploadResults.map((res) => res.data.host + res.data.image.src);
    await Fetchers.save3dProductImages({ data: { data_id: query.data_id, preview_images: images } })
      .catch((err) => {
        toast.error("图片上传失败");
        throw new Error(err.message);
      })
      .finally(() => {
        Helper.pageLoading(false);
      });
    toast.success("保存成功");
  }

  useEffect(() => {
    Helper.pageLoading(true);
    const query = Utils.getQueryParams(window.location.href);
    Fetchers.get3dProductData({ data_id: query.data_id })
      .then(async (res) => {
        const { data } = res.data;
        const { name, texture_map, normal_map, ao_map } = data.cut_parts[0];
        const { groups } = data.coordinate_data;
        const { scene, model, camera, renderer, ambientLight, directionalLight } = await load3dModel({
          model_url: data.model_file,
          mesh_name: name,
          map_url: texture_map,
          normal_map_url: normal_map,
          ao_map_url: ao_map,
        });
        const photos = await takePhotos({ scene, model, camera, renderer, ambientLight, directionalLight, groups });
        const options = photos.map((item) => ({ ...item, value: item.id }));
        setPhotos(options);
        paramsRef.current.handleRadioGroupClick(null, options[0].value);

        paramsRef.current.apiData = data;

        if (query.debug) {
          canvasBoxRef.current?.appendChild(renderer.domElement);
          renderer.render(scene, camera);
        }

        Helper.pageLoading(false);
      })
      .finally(() => {
        // Helper.pageLoading(false);
      });
  }, []);

  return (
    <>
      <div className={styles.container}>
        <div className={styles.wrapper}>
          <div className={styles.buttons}>
            <Button
              onClick={() => {
                const { apiData } = paramsRef.current;
                navigate(apiData.last_step_url);
              }}
            >
              上一步
            </Button>
            <Button
              type="primary"
              onClick={() => {
                handleSave();
              }}
            >
              保存
            </Button>
            <Button
              type="primary"
              onClick={async () => {
                await handleSave();
                navigate(paramsRef.current.apiData.next_step_url);
              }}
            >
              保存并创建下一个
            </Button>
          </div>
          <div className={styles.main}>
            <div>预览图</div>
            <div className={styles.preview}>
              <div className={styles.left}>
                <div className={styles.large}>
                  <img src={photos.find((item) => item.value === value)?.src} alt="" />
                </div>
              </div>
              <div className={styles.right}>
                <div className={styles.images}>
                  {photos.map((item, index) => {
                    const checked = value === item.value;
                    return (
                      <div
                        key={index}
                        onClick={(event) => {
                          handleRadioGroupClick(event, item.value);
                        }}
                        className={classNames({ [styles.checked]: checked })}
                      >
                        <img src={item.src} alt="" />
                      </div>
                    );
                  })}
                </div>
                <div>
                  <Button
                    type="primary"
                    onClick={async () => {
                      const link = document.createElement("a");
                      for (let i = 0; i < photos.length; i++) {
                        const photo = photos[i];
                        const file = Utils.base64ToFile({ base64: photo.src, filename: "image.png" });
                        const url = URL.createObjectURL(file);
                        link.download = `${photo.name}.png`;
                        link.href = url;
                        link.click();
                        setTimeout(() => {
                          URL.revokeObjectURL(url);
                        }, 0);
                      }
                      link.remove();
                    }}
                  >
                    一键下载
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {query.debug ? (
        <div ref={canvasBoxRef} className={classNames(styles.offscreen, styles.debugOffscreen)}></div>
      ) : null}
    </>
  );
}

export default GenerateProductImages;
