import { useState, useCallback } from "react";

function useFieldsProps({ formData = {} }) {
  const updateProps = useCallback(({ setPropsWhen, value }) => {
    if (!setPropsWhen) return {};

    const updates = {};
    Object.entries(setPropsWhen).forEach(([targetField, conditions]) => {
      if (!Array.isArray(conditions)) return;

      if (!updates[targetField]) updates[targetField] = {};

      for (const condition of conditions) {
        const { value: conditionValue, props } = condition;

        let isConditionMet = false;

        if (Array.isArray(value)) {
          isConditionMet = value.some((v) => v === conditionValue);
        } else {
          isConditionMet = value === conditionValue;
        }

        if (isConditionMet && props) {
          updates[targetField] = { ...props };
          break;
        }
      }
    });

    return updates;
  }, []);

  const [fieldsProps, setFieldsProps] = useState(initializeVisibleFields(formData));

  function initializeVisibleFields(formData) {
    if (!formData?.formItems) return {};
    let initialProps = {};

    function traverseItems(items) {
      items.forEach((item) => {
        // 递归处理 children
        if (item?.children) {
          traverseItems(item.children);
        }
        // 递归处理 Collapse 面板
        if (item?.component === "Collapse" && item?.props?.items) {
          item.props.items.forEach((panel) => {
            if (panel?.children) {
              traverseItems(panel.children);
            }
          });
        }
        // 处理 setPropsWhen
        if (item?.setPropsWhen && item?.key) {
          const initialValue = formData?.props?.initialValues?.[item.key];
          if (initialValue !== undefined) {
            const updates = updateProps({
              setPropsWhen: item.setPropsWhen,
              value: initialValue,
            });
            initialProps = { ...initialProps, ...updates };
          }
        }
      });
    }

    traverseItems(formData.formItems);

    return initialProps;
  }

  function handleFieldProps({ value, item }) {
    if (item?.setPropsWhen) {
      const updates = updateProps({ setPropsWhen: item.setPropsWhen, value });
      setFieldsProps((prevProps) => ({ ...prevProps, ...updates }));
    }
  }

  return [fieldsProps, handleFieldProps];
}

export default useFieldsProps;
