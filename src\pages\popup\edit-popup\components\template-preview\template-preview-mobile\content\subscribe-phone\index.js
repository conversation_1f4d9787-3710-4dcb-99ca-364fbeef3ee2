import React, { useEffect, useRef, useState, useCallback } from "react";
import styles from "./index.module.scss";

const eventSource = "sms_side_popup";
const temporarilyClosedKey = "subscribe_phone_popup_temporarily_closed";

function SuccessView({ content }) {
  return (
    <div className={styles.successWrapper}>
      <div className={styles.header}>
        <img alt="" {...content?.success?.icon?.image} />
        <div className={styles.title} style={content?.success?.title?.style}>
          {content?.success?.title?.text}
        </div>
      </div>
      <div className={styles.discount}>
        <div className={styles.tips} style={content?.success?.tips?.style}>
          {content?.success?.tips?.text}
        </div>
        <div className={styles.code} style={content?.success?.code?.style}>
          {content?.success?.code?.text}
        </div>
      </div>
      <div className={styles.message} style={content?.success?.message?.style}>
        {content?.success?.message?.text}
      </div>
    </div>
  );
}

function SubscribePhonePopupMobile(props) {
  const {
    data = {},
    name,
    popup_id,
    device,
    countryId = "US",
    getCountryList,
    fetchBlock,
    trackViewPopup,
    trackSubscribe,
    validator,
    submitFetcher,
    usePopupShow,
    usePopupControl,
    current,
    preview,
    isSubscribed,
  } = props;
  const [blockData, setBlockData] = useState(data);
  const [countryList, setCountryList] = useState([]);
  const subscribePhonePopupSubscribed = JSON.parse(isSubscribed ?? false);
  const [subscribed, setSubscribed] = useState(subscribePhonePopupSubscribed);
  const [selectedCountryId, setSelectedCountryId] = useState(countryId);
  const [errorMessage, setErrorMessage] = useState(null);
  const [privacyPolicyError, setPrivacyPolicyError] = useState(false);
  const [isShow] = usePopupShow({
    React,
    preview,
    condition: blockData?.template_info?.condition,
  });

  const { open, setOpen, handleTemporarilyClose } = usePopupControl({
    React,
    data: blockData,
    subscribed,
    temporarilyClosedKey,
  });

  const inputRef = useRef(null);
  const formRef = useRef(null);
  const paramsRef = useRef({});
  const privacyPolicyCheckboxRef = useRef(null);

  paramsRef.current = {
    name,
    popup_id,
    device,
    fetchBlock,
    getCountryList,
    trackViewPopup,
    template_info: blockData?.template_info,
    preview,
  };

  const handleOpen = useCallback(() => {
    const { preview } = paramsRef.current;
    setOpen(true);
    if (!preview) {
      document.body.style.overflow = "hidden";
      localStorage.removeItem(temporarilyClosedKey);
    }
  }, [setOpen]);

  const handleClose = useCallback(() => {
    const { preview } = paramsRef.current;
    handleTemporarilyClose?.();
    if (!preview) {
      document.body.removeAttribute("style");
    }
  }, [handleTemporarilyClose]);

  function validatePrivacyPolicyCheckbox() {
    const { show, error_message } = blockData?.content?.main?.form?.privacy_policy_checkbox ?? {};
    if (show && !privacyPolicyCheckboxRef.current?.checked) {
      formRef.current.classList.add("has-error");
      setPrivacyPolicyError(error_message);
      return false;
    }
    formRef.current?.classList.remove("has-error");
    setPrivacyPolicyError(null);
    return true;
  }

  async function handleSubmit(e) {
    e.preventDefault();

    if (!validatePrivacyPolicyCheckbox()) {
      return;
    }

    const phone = inputRef.current.value;
    const errors = await validator?.({ phone });
    if (errors?.length > 0) {
      formRef.current.classList.add("has-error");
      setErrorMessage(errors[0].message);
      return;
    }

    formRef.current?.classList.remove("has-error");
    try {
      const dial = countryList?.filter((item) => item?.country_id === selectedCountryId)?.[0]?.dia;
      const data = { country_id: selectedCountryId, phone, dial };
      const result = await submitFetcher?.(data);
      if (result?.status === "00") {
        setSubscribed(true);
        trackSubscribe?.({ eventSource, data });
      } else {
        setErrorMessage(result?.message);
        formRef.current?.classList.add("has-error");
      }
    } catch (error) {
      console.log("[subscribe-phone-popup-pc] handleSubmit error", error);
    }
  }

  useEffect(() => {
    (async () => {
      const { fetchBlock, name, popup_id, device, getCountryList } = paramsRef.current;
      if (fetchBlock) {
        const block = await fetchBlock({ name, id: popup_id, device });
        setBlockData(block);
      }

      if (getCountryList) {
        setCountryList(await getCountryList());
      }
    })();
  }, []);

  useEffect(() => {
    if (open) {
      const { trackViewPopup } = paramsRef.current;
      trackViewPopup?.({ eventSource });
    }
  }, [open]);

  useEffect(() => {
    if (preview && data) {
      setBlockData(data);
    }
  }, [preview, data]);

  return blockData && isShow ? (
    <>
      <div className={styles.smsPopup} onClick={handleOpen} style={blockData?.content?.take_back?.style}>
        <img alt="" {...blockData?.content?.take_back?.icon?.image} />
      </div>
      {open ? (
        <div className={styles.dialog}>
          <div className={styles.backdrop}></div>

          <div className={styles.container} style={blockData?.content?.main?.style}>
            <div className={styles.content} style={blockData?.content?.main?.content?.style}>
              <div className={styles.close} onClick={handleClose}>
                <img
                  width={12}
                  height={12}
                  src="https://static.bizseas.com/static/popup/sms-popup/mobile/close.png"
                  alt=""
                />
              </div>
              {subscribed || current === 2 ? (
                <SuccessView content={blockData?.content} />
              ) : (
                <div className={styles.contentWrapper}>
                  <div className={styles.title} style={blockData?.content?.main?.title?.style}>
                    {blockData?.content?.main?.title?.text}
                  </div>
                  <form ref={formRef} className={styles.form}>
                    <div className={styles.label} style={blockData?.content?.main?.form?.label?.style}>
                      {blockData?.content?.main?.form?.label?.text}
                    </div>
                    <div className={styles.subscribeWrapper}>
                      <select
                        className={styles.countryArea}
                        value={selectedCountryId}
                        onChange={(e) => setSelectedCountryId(e.target.value)}
                      >
                        {countryList.map((option) => (
                          <option key={option.country_id} value={option.country_id}>
                            {`${option?.country_id}${option?.dia ? ` (${option?.dia})` : ""}`}
                          </option>
                        ))}
                      </select>
                      <div className={styles.inputWrapper}>
                        <input
                          ref={inputRef}
                          placeholder={blockData?.content?.main?.form?.input?.placeholder}
                          style={blockData?.content?.main?.form?.input?.style}
                        />
                      </div>
                    </div>
                    <div className={styles.errorMessage} style={blockData?.content?.main?.form?.error_message?.style}>
                      {errorMessage}
                    </div>
                    <div className={styles.tipsWrapper}>
                      {!blockData?.content?.main?.form?.privacy_policy_checkbox?.show && (
                        <span className={styles.asterisk}>* </span>
                      )}
                      <label className={styles.tipsLabel}>
                        <input
                          ref={privacyPolicyCheckboxRef}
                          className={`${styles.privacyPolicyCheckbox} ${
                            blockData?.content?.main?.form?.privacy_policy_checkbox?.show ? styles.show : ""
                          }`}
                          type="checkbox"
                          defaultChecked={blockData?.content?.main?.form?.privacy_policy_checkbox?.value}
                        />
                        <span className={styles.tips} style={blockData?.content?.main?.form?.tips?.style}>
                          {blockData?.content?.main?.form?.tips?.text}
                        </span>
                        <a
                          className={styles.privacyPolicy}
                          href={blockData?.content?.main?.form?.privacy_policy?.href}
                          style={blockData?.content?.main?.form?.privacy_policy?.style}
                          target="_blank"
                          rel="noreferrer"
                        >
                          {blockData?.content?.main?.form?.privacy_policy?.text}
                        </a>
                      </label>
                    </div>
                    {privacyPolicyError && <div className={styles.errorMessage}>{privacyPolicyError}</div>}
                    <div className={styles.submitWrapper}>
                      <button style={blockData?.content?.main?.form?.button?.style} onClick={handleSubmit}>
                        {blockData?.content?.main?.form?.button?.text}
                      </button>
                    </div>
                  </form>
                </div>
              )}
            </div>
          </div>
        </div>
      ) : null}
    </>
  ) : null;
}

export default SubscribePhonePopupMobile;
