.smsPopup {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  text-align: center;
  border-radius: 6px;
  background: #fce9da;
  user-select: none;
  cursor: pointer;
  position: fixed;
  right: 10px;
  bottom: 80px;
  z-index: 100;
}

.container {
  display: flex;
  // width: 338px;
  // min-height: 368px;
  // height: 395px;
  // overflow: hidden;
  position: fixed;
  right: 0;
  z-index: 1300;
  transform: translateX(calc(100% - 38px));

  .content {
    width: 290px;
    border: 1px solid #d4d4d4;
    box-sizing: border-box;
    border-right: none;
    background: #fffaf7;
    position: relative;
    z-index: 1;
    overflow: hidden;
    transform: translateX(-328px);
    transition: transform 0.2s;

    .contentWrapper {
      padding: 36px 20px;

      .title {
        font-size: 16px;
        font-weight: 500;
        text-align: left;
        color: #231816;
        line-height: 24px;
      }

      .form {
        .label {
          font-size: 16px;
          font-weight: 400;
          text-align: left;
          color: #231816;
          line-height: 24px;
          letter-spacing: 0.16px;
          margin-top: 12px;
        }

        .subscribeWrapper {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: 100%;
          height: 42px;
          background: #f1eae5;
          margin-top: 10px;
          padding: 0 10px;
          overflow: hidden;

          .inputWrapper {
            flex: 1;
            overflow: hidden;
          }

          select,
          input {
            border: none;
            background-color: transparent;
            outline: none;
          }

          select {
            cursor: pointer;
          }

          input {
            font-size: 14px;
            font-weight: 400;
            text-align: left;
            color: #231816;
            line-height: 24px;
            letter-spacing: 0.14px;
          }
        }

        &[class~="has-error"] {
          .errorMessage {
            display: block;
            transform: scaleY(1);
            pointer-events: none;
          }
        }

        .errorMessage {
          display: none;
          font-size: 14px;
          font-weight: 400;
          text-align: left;
          color: #ff0000;
          line-height: 14px;
          transform: scaleY(0);
          transform-origin: top;
          transition: transform ease 0.3s;
          margin-top: 5px;
        }

        .tipsWrapper {
          margin-top: 10px;

          .tipsLabel {
            user-select: none;

            .privacyPolicyCheckbox {
              display: none;
              vertical-align: middle;
            }

            .show {
              display: inline-block;
            }
          }

          .asterisk,
          .tips {
            font-size: 15px;
            font-weight: 400;
            text-align: left;
            line-height: 22px;
          }

          .asterisk {
            color: #cd0b0b;
          }

          .tips {
            color: #000;
          }

          .privacyPolicy {
            color: #ff9600;
            text-decoration: underline;
          }
        }

        .submitWrapper {
          margin-top: 20px;

          button {
            width: 100%;
            height: 36px;
            background: #e1d5ce;
            border: none;
            cursor: pointer;
            font-size: 18px;
            font-weight: 400;
            color: #231816;
            line-height: 24px;
          }
        }

        .doNotShowAgain {
          font-size: 16px;
          font-weight: 400;
          text-align: center;
          color: #999999;
          line-height: 24px;
          cursor: pointer;
          margin-top: 10px;
        }
      }
    }

    .successWrapper {
      padding: 46px 20px;

      .header {
        display: flex;
        align-items: center;
        flex-direction: column;
        gap: 10px;

        .title {
          font-size: 18px;
          font-weight: 400;
          text-align: center;
          color: #231816;
          line-height: 24px;
        }
      }

      .discount {
        display: flex;
        align-items: center;
        flex-direction: column;
        gap: 8px;
        padding: 19px 10px;
        background: #f7f1ed;
        border: 1px dashed #d4d4d4;
        margin-top: 20px;

        .tips {
          font-size: 16px;
          font-weight: 400;
          text-align: center;
          color: #666666;
          line-height: 20px;
        }

        .code {
          font-size: 20px;
          text-align: center;
          color: #ff9600;
          line-height: 24px;
        }
      }

      .message {
        font-size: 14px;
        font-weight: 400;
        text-align: center;
        color: #888888;
        line-height: 20px;
        padding: 0 18px;
        margin-top: 20px;
      }
    }
  }

  .sideBar {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 7px;
    width: 38px;
    min-height: 368px;
    background: #f9dfce;
    border: 1px solid #d4d4d4;
    border-left: none;
    cursor: pointer;
    position: relative;
    z-index: 2;
    user-select: none;

    .arrow {
      width: 11px;
      height: 18px;
      position: absolute;
      top: 16px;
      transform: rotate(180deg);
      transition: transform 0.2s;
      transform-origin: center center;
    }

    .title {
      font-size: 18px;
      font-weight: 700;
      text-align: left;
      color: #231816;
      line-height: 36px;
      writing-mode: vertical-lr;
      transform: rotate(180deg);
    }
  }
}

.fold {
  .content {
    transform: translateX(0px);
  }

  .sideBar {
    .arrow {
      transform: rotate(0);
    }
  }
}
