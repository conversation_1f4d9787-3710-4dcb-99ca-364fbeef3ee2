import { createContext, useContext, useState, useEffect } from "react";
import Fetchers from "fetchers";
import Helper from "helpers";
import { autorun } from "mobx";
import store from "@/stores";

const I18nContext = createContext();

export function I18nProvider({ children }) {
  const [currentLanguage, setCurrentLanguage] = useState("zh-CN");
  const [message, setMessage] = useState({});

  function t({ key, defaultText }) {
    return message[key] ?? defaultText;
  }

  function changeLanguage(language) {
    Helper.clearSystemInfoCache();
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set("language", language);
    window.location.href = `${window.location.origin}${window.location.pathname}?${urlParams.toString()}`;
  }

  async function loadI18nResource(language) {
    try {
      const result = await Fetchers.getI18nResource({ lang: language }).then((res) => res.data);
      if (result.success) {
        setMessage(result?.data);
      }
    } catch (error) {
      console.warn("Failed to load remote i18n resource:", error);
    }
  }

  const value = {
    currentLanguage,
    t,
    changeLanguage,
    loadI18nResource,
    message,
  };

  useEffect(() => {
    const disposer = autorun(() => {
      if (store.systemInfo?.language) {
        const urlParams = new URLSearchParams(window.location.search);
        const language = urlParams.get("language") || "zh-CN";
        setCurrentLanguage(language);
        loadI18nResource(language);
      }
    });
    return () => disposer();
  }, []);

  return <I18nContext.Provider value={value}>{children}</I18nContext.Provider>;
}

export function useI18n() {
  const context = useContext(I18nContext);
  return context;
}
