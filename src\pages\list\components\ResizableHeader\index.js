// import { Resizable } from "react-resizable";

function ResizableHeader(props) {
  const { column = {}, ...restProps } = props;
  const { width, onResize } = column;

  if (!width) {
    return <th {...restProps} />;
  }

  return null;
  // <Resizable
  //   width={width}
  //   height={0}
  //   onResize={onResize}
  //   {...restProps}
  //   handle={
  //     <span
  //       className="react-resizable-handle"
  //       onClick={(e) => {
  //         e.stopPropagation();
  //         e.preventDefault();
  //       }}
  //     />
  //   }
  //   draggableOpts={{ enableUserSelectHack: false }}
  // >
  //   <th {...restProps} />
  // </Resizable>
}

export default ResizableHeader;
