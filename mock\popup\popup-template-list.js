const Api = require("../../src/fetchers/api");

const columns = [
  { dataIndex: "template_image", title: "弹窗说明图", width: 100, valueType: "image", image: { width: 45 } },
  {
    dataIndex: "id",
    title: "弹窗模版id",
    width: 100,
    ellipsis: true,
  },
  {
    dataIndex: "type",
    title: "弹窗类型",
    width: 150,
  },
  { dataIndex: "created_at", title: "创建时间", width: 200, sorter: { multiple: 2 } },
  { dataIndex: "updated_at", title: "更新时间", width: 200, sorter: { multiple: 2 } },
  {
    dataIndex: "user_name",
    title: "操作人",
    width: 150,
  },
  {
    dataIndex: "operations",
    valueType: "operation",
    title: "操作",
    width: 80,
    fixed: "right",
    align: "center",
    operations: [
      {
        key: "edit",
        icon: "https://images.aoolia.com/static/icons/edit.svg",
        title: "编辑",
        command: {
          type: "redirect",
          url: "/popup/edit-popup/template",
        },
      },
      {
        key: "delete",
        icon: "https://images.aoolia.com/static/icons/edit.svg",
        title: "删除",
        command: {
          type: "request",
          request: {
            url: Api.order,
            data: { action: "delete" },
          },
          confirm: "确定删除吗？",
          reload: true,
        },
      },
    ],
  },
];

const rows = [];

const headerActions = [
  {
    title: "新建订单",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    props: { type: "primary" },
    command: {
      type: "modal",
      closable: true,
      title: "新增",
      content: {
        form: {
          initialValues: {},
          formItems: [
            {
              key: "type",
              label: "弹窗类型",
              component: "Select",
              props: {
                options: [{ label: "订阅电话弹窗", value: "1" }],
              },
              rules: [{ required: true, message: "弹窗类型是必填项！" }],
            },
            {
              key: "images",
              label: "弹窗说明图",
              component: "ImageUpload",
              props: { listType: "picture-card", action: Api.uploadFile, multiple: true },
              rules: [{ required: true, message: "弹窗说明图是必填项！" }],
            },
          ],
          request: {
            url: Api.savePopupTemplate,
            data: {},
          },
        },
      },
    },
  },
];

const toolbarActions = [];
const PopupTypesMap = {
  1: { label: "电话订阅弹窗" },
  2: { label: "转盘订阅弹窗" },
};

module.exports = async (req, res) => {
  const query = req.query;
  const page = +(query.page || 1);
  const pageSize = +(query.pageSize || 20);
  const total = 500;
  const start = (page - 1) * pageSize;
  const end = page * pageSize >= total ? total : page * pageSize;
  rows.length = 0;
  for (let i = start; i < end; i++) {
    const id = i + 1;
    rows.push({
      id: id,
      template_type: "订阅邮箱+电话弹窗",
      template_image: [{ src: "https://test-je5-oms.cnzlerp.com/uploads/product/e/9/6015357862b9e.jpg" }],
      user_name: "yy",
      type: PopupTypesMap[id]?.label || null,
      created_at: "2022-12-15 04:15:15",
      updated_at: "2022-12-15 04:16:02",
    });
  }
  await new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, 3000);
  });
  res.status(200).json({
    status: "00",
    success: true,
    data: {
      breadcrumbs: [
        { title: "弹窗中心", url: "/" },
        { title: "弹窗模版", url: "/" },
      ],
      headerActions: headerActions,
      toolbarActions: toolbarActions,
      polling: 1000,
      tableProps: {
        rowKey: "id",
        dataSource: rows,
        columns: columns,
        pagination: {
          current: +page,
          pageSize: +pageSize,
          total: total,
          pageSizeOptions: [20, 50, 100, 200],
          showSizeChanger: true,
          position: ["bottomRight"],
          size: "small",
          showQuickJumper: false,
        },
        rowSelection: false,
        size: "small",
        bordered: true,
      },
    },
  });
};
