.listPage {
  height: 100%;
  padding: 0 6px;

  .listPageWrapper {
    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
    }

    .container {
      display: flex;
      position: relative;
      --sidebar-width: 250px;

      &.sidebarFolded {
        --sidebar-width: 0px;

        .sidebarActions {
          .sidebarActionsHeader {
            .sidebarFold {
              right: -20px;
            }
          }

          .sidebarFilterWrapper {
            display: none;
          }
        }
      }

      .sidebarActions {
        position: absolute;
        left: 0;
        width: var(--sidebar-width);
        padding-top: 8px;
        z-index: 1;
        transition: all ease 0.1s;

        .sidebarActionsHeader {
          min-height: 40px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          position: relative;

          .sidebarFold {
            position: absolute;
            cursor: pointer;
            padding: 5px;
            right: 0;
          }
        }

        .sidebarFilterWrapper {
          display: block;
          overflow-y: auto;
        }
      }

      .hasSidebarActions {
        padding-left: var(--sidebar-width);

        .proTable {
          padding-left: 6px;
        }
      }

      .tableContainer {
        width: 100%;
        flex: 1;
        padding-top: 6px;
        position: relative;
        transition: all ease 0.2s;

        .styledLine {
          display: block;
          height: 100%;
          width: 1px;
          background: #bfbfbf;
          position: absolute;
          z-index: 199999;
          bottom: 0;
          left: 0px;
        }

        .listToolbar {
          display: flex;
          justify-content: space-between;
          padding: 0 20px 8px;

          .listToolbarLeft,
          .listToolbarRight {
            display: flex;
            align-items: center;
            justify-content: flex-start;
          }

          .listToolbarRight {
            justify-content: flex-end;

            .tableActions {
              display: flex;
              gap: 8px;
              align-items: center;
              font-size: 16px;

              .tableActionsItem {
                cursor: pointer;

                &:hover {
                  color: #1677ff;
                }
              }
            }
          }
        }

        .tableRowActive {
          td,
          [class~="ant-table-cell"] {
            background-color: #ffc;
          }
        }

        [class~="ant-table-tbody"] {
          tr > [class~="ant-table-cell-row-hover"] {
            background: #bae0ff;
          }
        }

        [class~="rc-virtual-list"] {
          [class~="ant-table-row"] {
            [class~="ant-table-cell-row-hover"] {
              background: #bae0ff;
            }
          }
        }

        [class~="rc-virtual-list-scrollbar"] {
          visibility: visible !important;
        }

        [id~="active-table-cell"] {
          position: relative;
        }

        [id~="active-table-cell"]::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border: 1px solid #2196f3;
        }

        [class~="ant-table"] {
          [class~="ant-tag"] {
            & + [class~="ant-tag"] {
              margin-top: 5px;
            }
          }

          [class~="ant-table-container"] {
            [class~="ant-table-body"],
            [class~="rc-virtual-list"] {
              overflow: auto !important;

              &::-webkit-scrollbar {
                width: 8px;
                height: 8px;
                background-color: #fff;
              }

              &::-webkit-scrollbar-thumb {
                background-color: #e0e0e0;
                border-radius: 1000px;
              }

              .tableRowActive {
                [class~="ant-table-cell"] {
                  background-color: #ffc;
                }
              }

              .tableRowOperationsOverlay {
                li {
                  padding: 0;

                  .operation {
                    padding: 5px 12px;
                  }
                }
              }
            }
          }

          [class~="ant-table-footer"] {
            padding: 0;
          }
        }

        .tableCellText {
          display: inline-flex;
          align-items: center;
          vertical-align: middle;
        }

        .tableCellLink {
          &,
          * {
            color: var(--ant-primary-color);
          }
        }

        .tableCellContent {
          position: relative;
          display: flex;
          align-items: center;
          flex-wrap: wrap;

          &:hover {
            .copyableText {
              display: flex;
              opacity: 1;
            }
          }

          .copyableText {
            position: absolute;
            width: 20px;
            background-color: #fff;
            border-radius: 5px;
            top: 0;
            left: 0;
            display: none;
            opacity: 0;
            align-items: center;
            justify-content: center;

            [class~="ant-typography-copy"] {
              margin-inline-start: 0px;
            }
          }
        }

        &.tableFullscreen {
          position: fixed;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          z-index: 1000;
          width: 100%;
          height: 100%;
          padding: 8px 0 0 0;
          background-color: #fff;
        }

        [class~="ant-table-filter-trigger"] {
          align-items: center;
          justify-content: center;
          width: 22px;
          height: 22px;
          margin: 0;
          padding: 0;
          color: #333;
          font-size: 14px;
        }

        [class~="ant-table-pagination"] {
          margin-top: 8px;
          margin-bottom: -16px;
        }

        [class~="react-resizable"] {
          position: relative;
          background-clip: padding-box;
        }

        [class~="react-resizable-handle"] {
          position: absolute;
          // right: -5px;
          left: 100%;
          bottom: 0;
          z-index: 1;
          width: 6px;
          height: 100%;
          cursor: col-resize;
          background-color: #c3b8b8;
          margin-left: -3px;
          opacity: 0;

          &:hover {
            opacity: 1;
          }
        }
      }

      .filterTags {
        display: flex;
        align-items: flex-start;

        [class~="ant-tag"] {
          padding: 2px 8px;
          background-color: #fff;
        }

        .clearAll {
          cursor: pointer;
          user-select: none;
        }

        .filterTotal {
          display: flex;
          align-items: center;
          height: 26px;
        }

        .filterTagTitle {
          word-break: break-word;
          white-space: normal;
        }
      }

      .tableActions {
        display: flex;
        gap: 8px;
        align-items: center;
        font-size: 16px;

        .tableActionsItem {
          cursor: pointer;

          &:hover {
            color: #1677ff;
          }

          .tableActionButtonWrapper {
            display: flex;
            align-items: center;

            button {
              padding: 0;
            }
          }
        }
      }
    }
  }
}

.filterControl {
  position: relative;

  .filterCheckbox {
    max-height: 400px;
    padding: 8px 10px;
    overflow-y: scroll;
    padding-bottom: 45px;

    [class~="ant-checkbox-group"] {
      display: flex;
      flex-direction: column;
    }

    [class~="ant-form-item"] {
      margin-bottom: 0;
    }
  }

  [class~="ant-input-group-addon"] {
    button {
      span {
        width: 100%;
        height: 100%;
        justify-content: center;
      }
    }
  }

  .actions {
    width: 100%;
    position: absolute;
    bottom: 0;
    padding: 10px;
    text-align: right;
    border-top: 1px solid var(--theme-border-color);
    background-color: #fff;
  }
}

[class~="ant-badge"] {
  margin: 2px;
}

.imageBadge {
  margin: 5px;
}

body {
  .operation {
    display: flex;
    align-items: center;
    white-space: nowrap;
  }

  .operationIcon {
    display: inline-flex;
    width: 14px;
    height: 14px;
    margin-right: 3px;
  }
}

.disablePageScroll {
  overflow: hidden !important;
}

[class~="ant-table-wrapper"] [class~="ant-table"] [class~="ant-table-cell"][class~="imageCell"] {
  padding: 2px;
}
