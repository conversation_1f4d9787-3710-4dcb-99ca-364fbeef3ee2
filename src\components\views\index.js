import styles from "./views.module.scss";
import { createContext, useContext } from "react";
import classNames from "classnames";
import PropTypes from "prop-types";

const ViewsContext = createContext({ active: "", keepMounted: false });

function Views(props) {
  const { active, children, keepMounted, className, style } = props;

  return (
    <ViewsContext.Provider value={{ active, keepMounted }}>
      <div className={classNames(styles.views, className, { [styles.viewsKeepMounted]: keepMounted })} style={style}>
        {children}
      </div>
    </ViewsContext.Provider>
  );
}

function View(props) {
  const { name, children } = props;
  const { active, keepMounted } = useContext(ViewsContext);
  const isActive = name === active;

  if (keepMounted || isActive) {
    return <div className={classNames(styles.view, { [styles.active]: isActive })}>{children}</div>;
  } else {
    return null;
  }
}

Views.propTypes = {
  active: PropTypes.string,
  keepMounted: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object,
};

View.propTypes = {
  name: PropTypes.string.isRequired,
};

export { Views, View };
