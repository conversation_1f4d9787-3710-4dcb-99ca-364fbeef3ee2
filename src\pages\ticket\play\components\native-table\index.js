import styles from "./index.module.scss";
import { Input } from "antd";
import NativeTable from "components/common/JSONComponent/components/NativeTable";
import Helper from "helpers";

function Table(props) {
  const { ticketId, incrementId, updateData } = props;

  async function handleOnblur(e, command) {
    if (command) {
      await Helper.commandHandler({
        command: {
          ...command,
          request: {
            ...command.request,
            data: { ...command.request.data, ticket_id: ticketId, increment_id: e.target.value },
          },
        },
      });
    }
    updateData({ ticket_id: ticketId, increment_id: e.target.value });
  }

  function renderExtraComponent(cell) {
    const { valueType, value, view, command } = cell;

    if (valueType === "input") {
      return (
        <div className={styles.inputWrapper}>
          <Input
            defaultValue={incrementId}
            onBlur={(e) => {
              handleOnblur(e, command);
            }}
          />
          {view ? (
            <a {...view} target="_blank">
              View
            </a>
          ) : null}
        </div>
      );
    }
    return value;
  }

  return <NativeTable renderExtraComponent={renderExtraComponent} {...props} />;
}

export default Table;
