import styles from "./index.module.scss";

import useS<PERSON> from "swr";
import { useEffect, useState } from "react";
import classNames from "classnames";
import { useLocation, useParams, useNavigate } from "react-router-dom";

import Fetchers from "@/fetchers";
import Helper from "@/helpers";

import { Row, Col } from "antd";
import Breadcrumbs from "components/common/Breadcrumbs";
import Menu from "components/Menu";
import JSONComponents from "components/common/JSONComponent";
import SubmitButton from "components/common/Button";

function Configuration() {
  const location = useLocation();
  const params = useParams();
  const navigate = useNavigate();
  const url_key = Helper.getUrlKey({ location, params });
  const isInsideIframe = Helper.isInsideIframe();
  const searchParams = new URLSearchParams(location.search);
  const section = searchParams.get("section");
  const [loading, setLoading] = useState(false);

  const { data } = useSWR(
    [url_key],
    async () => {
      try {
        Helper.pageLoading(true);
        setLoading(true);
        return await Fetchers.getConfigurationList({ url_key }).then((res) => res?.data?.data);
      } finally {
        Helper.pageLoading(false);
        setLoading(false);
      }
    },
    { dedupingInterval: 0 }
  );

  function updateSearchParams({ searchParams }) {
    navigate({ pathname: location.pathname, search: decodeURIComponent(searchParams.toString()) });
  }

  function handleMenuClick(e) {
    const searchParams = new URLSearchParams(location.search);
    const section = searchParams.get("section");
    if (section === e?.key) return;

    if (e?.key) {
      searchParams.set("section", e.key);
    }
    updateSearchParams({ searchParams });
  }

  return (
    <div className={styles.container}>
      <div className={classNames("page-header", { [styles.pageHeader]: isInsideIframe })}>
        {!isInsideIframe ? (
          <div className={styles.breadcrumbsWrapper}>
            <Breadcrumbs data={data?.breadcrumbs}></Breadcrumbs>
          </div>
        ) : null}
        <div className="page-header-actions">
          {data?.actions?.map((item, index) => {
            return (
              <SubmitButton
                key={index}
                type="primary"
                {...item.props}
                command={item.command}
                onClick={async () => {
                  await Helper.commandHandler({ command: item.command });
                }}
              >
                {item.title}
              </SubmitButton>
            );
          })}
        </div>
      </div>
      <div className={styles.content}>
        <Row gutter={16}>
          <Col span={4}>
            <Menu
              items={data?.menus}
              theme="light"
              selectedKeys={[section ?? data?.menus?.[0]?.key]}
              onClick={handleMenuClick}
            />
          </Col>
          {!loading && <Col span={20}>{data?.content ? <JSONComponents data={data?.content} /> : null}</Col>}
        </Row>
      </div>
    </div>
  );
}

export default Configuration;
