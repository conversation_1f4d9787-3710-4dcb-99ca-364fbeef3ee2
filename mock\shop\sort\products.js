const products = [
  {
    spu: "DRN1172",
    price: "43.95",
    image: {
      src: "https://test-je5-wms.cnzlerp.com/uploads/thumbnail/150x150/product/e/1/JEWW01071_5a52e0cc0541e.jpg",
    },
  },
  {
    spu: "DRH0653",
    price: "32.95",
    image: {
      src: "https://test-je5-wms.cnzlerp.com/uploads/thumbnail/150x150/product/3/1/639a88e07bb13.jpg",
    },
  },
  {
    spu: "DRH0652",
    price: "32.95",
    image: {
      src: "https://test-je5-wms.cnzlerp.com/uploads/thumbnail/150x150/product/8/2/639a88a996f28.jpg",
    },
  },
  {
    spu: "DRH0579",
    price: "32.95",
    image: {
      src: "https://test-je5-wms.cnzlerp.com/uploads/thumbnail/150x150/product/4/1/630dca2b26b14.jpg",
    },
  },
  {
    spu: "JEPR0119",
    price: "16.00",
    image: {
      src: "https://test-je5-wms.cnzlerp.com/uploads/thumbnail/150x150/product/0/f/674eb038b8ef0.jpg",
    },
  },
  {
    spu: "JECW0151T",
    price: "16.00",
    image: {
      src: "https://test-je5-wms.cnzlerp.com/uploads/thumbnail/150x150/product/0/3/675d58578a830.jpg",
    },
  },
  {
    spu: "JECW0151F",
    price: "89.95",
    image: {
      src: "https://test-je5-wms.cnzlerp.com/uploads/thumbnail/150x150/product/2/0/675d58a8cd302.jpg",
    },
  },
  {
    spu: "DRN1172",
    price: "43.95",
    image: {
      src: "https://test-je5-wms.cnzlerp.com/uploads/thumbnail/150x150/product/e/1/JEWW01071_5a52e0cc0541e.jpg",
    },
  },
  {
    spu: "DRH0653",
    price: "32.95",
    image: {
      src: "https://test-je5-wms.cnzlerp.com/uploads/thumbnail/150x150/product/3/1/639a88e07bb13.jpg",
    },
  },
  {
    spu: "DRH0652",
    price: "32.95",
    image: {
      src: "https://test-je5-wms.cnzlerp.com/uploads/thumbnail/150x150/product/8/2/639a88a996f28.jpg",
    },
  },
  {
    spu: "DRH0579",
    price: "32.95",
    image: {
      src: "https://test-je5-wms.cnzlerp.com/uploads/thumbnail/150x150/product/4/1/630dca2b26b14.jpg",
    },
  },
  {
    spu: "JEPR0119",
    price: "16.00",
    image: {
      src: "https://test-je5-wms.cnzlerp.com/uploads/thumbnail/150x150/product/0/f/674eb038b8ef0.jpg",
    },
  },
  {
    spu: "JECW0151T",
    price: "16.00",
    image: {
      src: "https://test-je5-wms.cnzlerp.com/uploads/thumbnail/150x150/product/0/3/675d58578a830.jpg",
    },
  },
  {
    spu: "JECW0151F",
    price: "89.95",
    image: {
      src: "https://test-je5-wms.cnzlerp.com/uploads/thumbnail/150x150/product/2/0/675d58a8cd302.jpg",
    },
  },
];

module.exports = async (req, res) => {
  const { id, page = 1, limit = 14 } = req.query;

  function getProducts(id, page) {
    const data = [];
    const numberPage = Number(page);

    for (let j = 0; j < limit; j++) {
      const element = products[j];
      const id = numberPage * limit + j + 1;
      data.push({ id, ...element, spu: `${element.spu}-${id}` });
    }

    return data;
  }

  const items = getProducts(id, page);

  res.status(200).json({
    success: true,
    data: {
      items,
      pagination: {
        current: page,
        total: 9527,
      },
    },
  });
};
