.componentPanel {
  height: 100%;
  overflow-y: auto;

  :global {
    .ant-tabs {
      height: 100%;

      .ant-tabs-content {
        height: 100%;
        overflow-y: auto;
      }
    }
  }
}

.componentList {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  padding: 8px 0;
}

.componentItem {
  cursor: move;
  user-select: none;
}

.componentCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background-color: #fafafa;
  transition: all 0.3s;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  }

  .icon {
    font-size: 20px;
    margin-bottom: 4px;
  }

  .title {
    font-size: 12px;
    text-align: center;
  }
}
