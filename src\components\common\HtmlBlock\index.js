import PropTypes from "prop-types";
import sanitizeHtml from "sanitize-html";
import { forwardRef } from "react";

function HTMLBlock(props, ref) {
  const { html, tag: Element = "div", ...otherProps } = props;

  function toSecurityHtml(html) {
    return sanitizeHtml(html || "", {
      allowedTags: false,
      allowedAttributes: false,
      allowVulnerableTags: true,
      parseStyleAttributes: false,
      disallowedTagsMode: "escape", // 转义
      textFilter(text, tagName) {
        if (tagName === "style") {
          return text.replace(/&gt;/g, ">");
        }
        return text;
      },
    });
  }

  function processHtml(html) {
    html = toSecurityHtml(html);
    return html;
  }

  return <Element {...otherProps} ref={ref} dangerouslySetInnerHTML={{ __html: processHtml(html) }} />;
}

HTMLBlock = forwardRef(HTMLBlock);

HTMLBlock.propTypes = {
  html: PropTypes.string,
  tag: PropTypes.string,
};

export default HTMLBlock;
