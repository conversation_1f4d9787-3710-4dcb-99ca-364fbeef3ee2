import { Popover as AntdPopover } from "antd";
import PropTypes from "prop-types";
import { forwardRef, useImperativeHandle, useState } from "react";

function Popover(props, ref) {
  const [open, setOpen] = useState(false);

  useImperativeHandle(
    ref,
    () => {
      return {
        setOpen,
      };
    },
    []
  );

  return (
    <AntdPopover
      {...props}
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
      }}
    ></AntdPopover>
  );
}

Popover = forwardRef(Popover);
Popover.propTypes = {
  content: PropTypes.any,
};

export default Popover;
