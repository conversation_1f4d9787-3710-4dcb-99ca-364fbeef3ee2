import styles from "./index.module.scss";
import { observer } from "mobx-react-lite";
import UserInfo from "./components/UserInfo";
import DownloadList from "./components/DownloadList";
import LangSelector from "./components/LangSelector";

function RightMenu() {
  return (
    <div className={styles.rightMenu}>
      <div className={styles.rightMenuItem}>
        <LangSelector />
      </div>
      <div className={styles.rightMenuItem}>
        <UserInfo />
      </div>
      <div className={styles.rightMenuItem}>
        <DownloadList />
      </div>
    </div>
  );
}

RightMenu = observer(RightMenu);

export default RightMenu;
