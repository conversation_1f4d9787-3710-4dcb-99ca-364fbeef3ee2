import { useState } from "react";

export const useRadioGroup = (props) => {
  const { value: propValue, cancelable, defaultValue, options = [], onItemClick, onChange: propOnChange } = props;
  const [internalValue, setInternalValue] = useState(defaultValue ?? null);

  const isControlled = Object.hasOwnProperty.call(props, "value");
  const value = isControlled ? propValue : internalValue;

  function handleChange(event, value) {
    setInternalValue(value);
    propOnChange?.(event, value, { item: options.find((item) => item.value === value) });
  }

  function handleClick(event, itemValue) {
    const checked = value === itemValue;
    const cancelChecked = cancelable && checked;
    const isNotRadio = event?.target?.nodeName?.toLowerCase() !== "input";
    const newValue = cancelChecked ? null : itemValue;

    // 如果不是input触发的,或者当前是取消选中, 需要手动触发onChange
    if (isNotRadio || cancelChecked) {
      handleChange(event, newValue);
    }

    if (!isControlled) {
      setInternalValue(newValue);
    }

    onItemClick?.(event, itemValue, {
      checked: cancelChecked ? false : checked,
      item: options.find((item) => item.value === itemValue),
    });
  }

  return { value, onChange: handleChange, onClick: handleClick };
};
