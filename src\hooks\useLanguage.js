import { useTranslation } from "react-i18next";
import { useEffect } from "react";
import { autorun } from "mobx";

import Helper from "helpers";
import store from "@/stores";

function useLanguage() {
  const { i18n } = useTranslation();

  function changeLanguage(language) {
    Helper.clearSystemInfoCache();
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set("language", language);
    window.location.href = `${window.location.origin}${window.location.pathname}?${urlParams.toString()}`;
  }

  useEffect(() => {
    const disposer = autorun(() => {
      if (store.systemInfo?.language) {
        const urlParams = new URLSearchParams(window.location.search);
        const language = urlParams.get("language") || "zh-CN";

        if (i18n.language !== language) {
          i18n.changeLanguage(language);
        }
      }
    });
    return () => disposer();
  }, [i18n]);

  return {
    changeLanguage,
    currentLanguage: i18n.language,
  };
}

export default useLanguage;
