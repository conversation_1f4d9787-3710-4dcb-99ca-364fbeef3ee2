const apiTools = Object.freeze({
  setPathParams(url = "", params) {
    Object.entries(params || {}).forEach(([key, value]) => {
      if (typeof value !== "undefined") {
        url = url.replace(`:${key}`, value);
      }
    });
    return url;
  },
  toFormData({ data, hasFileIndex = false }) {
    const formData = new FormData();
    Object.keys(data).forEach((key) => {
      const value = data[key];
      if (Array.isArray(value)) {
        value.forEach((item, index) => {
          formData.append(hasFileIndex ? `${key}[${index}]` : `${key}`, item.file);
        });
      } else {
        formData.append(key, value);
      }
    });
    return formData;
  },
});

export default apiTools;
