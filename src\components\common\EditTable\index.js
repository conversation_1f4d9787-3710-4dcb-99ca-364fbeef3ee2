import { Input, Select, Table, Button, Image, InputNumber } from "antd";
import { useRef, useState, useCallback, useMemo } from "react";
import { EyeOutlined } from "@ant-design/icons";
import PropTypes from "prop-types";

import Enums from "enums";
import Utils from "utils";

import RemoteSearchSelect from "@/components/common/RemoteSearchSelect";
import HTMLBlock from "components/common/HtmlBlock";
import ImageUpload from "components/common/ImageUpload";

function EditTable(props) {
  const {
    value,
    rowSelection = false,
    columns,
    operations = [],
    operationsWidth = 150,
    isShowAddRow = true,
    isShowSetDefault = false,
    onChange,
    rowSelectionChange,
    defaultNewRow = {},
  } = props;
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const paramsRef = useRef({});

  const dataSource = useMemo(() => {
    if (!value) return [];

    return value.map((item, index) => ({
      ...item,
      _table_key: item._table_key || index + 1,
      ...(isShowSetDefault && item.is_default === undefined ? { is_default: false } : {}),
    }));
  }, [value, isShowSetDefault]);

  const filterDataSource = useCallback((items) => {
    return items.map((item) => {
      const { _table_key, ...restItem } = item;
      return restItem;
    });
  }, []);

  const handleDeleteRow = useCallback(
    ({ row, index }) => {
      const newData = [...dataSource];
      newData.splice(index, 1);
      onChange?.(filterDataSource(newData));
      return newData;
    },
    [dataSource, onChange, filterDataSource]
  );

  paramsRef.current = {
    ...paramsRef.current,
    operations,
    value,
    columns,
    operationsWidth,
    isShowSetDefault,
    handleDeleteRow,
  };

  const handleAdd = useCallback(() => {
    const newData = [...dataSource];
    const newKey = newData[0] ? Math.max(...newData.map((item) => item._table_key)) + 1 : 1;
    newData.push({ _table_key: newKey, ...defaultNewRow });
    onChange?.(filterDataSource(newData));
  }, [dataSource, onChange, defaultNewRow, filterDataSource]);

  const createOperations = useCallback(({ operations, row, index }) => {
    return (
      <>
        <Button.Group>
          {operations.map((item, i) => {
            let title = item?.title;
            if (item.key === "setDefault") {
              if (row.is_default) {
                title = "默认";
              } else {
                title = "设为默认";
              }
            }
            return (
              <Button key={i} type="link" size="small" onClick={() => item?.action?.({ row, index })}>
                {title}
              </Button>
            );
          })}
        </Button.Group>
      </>
    );
  }, []);

  const updateValue = useCallback(
    ({ column, value, index }) => {
      const newData = Utils.cloneDeep(dataSource);

      // 处理dataIndex为数组的情况
      const dataIndex = column?.dataIndex;

      // 检查当前值是否相同，如果相同则不更新
      if (Array.isArray(dataIndex)) {
        // 获取嵌套路径的当前值
        let currentValue = newData[index];
        for (let i = 0; i < dataIndex.length; i++) {
          if (i === dataIndex.length - 1) {
            break;
          }
          if (!currentValue[dataIndex[i]]) {
            currentValue[dataIndex[i]] = {};
          }
          currentValue = currentValue[dataIndex[i]];
        }

        // 检查最后一级的值是否相同
        if (currentValue[dataIndex[dataIndex.length - 1]] === value) return;

        // 设置新值
        currentValue[dataIndex[dataIndex.length - 1]] = value;
      } else {
        // 原有逻辑，处理dataIndex为字符串的情况
        if (newData[index] && newData[index][dataIndex] === value) return;
        if (newData[index]) {
          newData[index][dataIndex] = value;
        }
      }

      onChange?.(filterDataSource(newData));
    },
    [dataSource, onChange, filterDataSource]
  );

  const editableRender = useCallback(
    ({ column, row, value, index }) => {
      const { editable } = column;
      const dataIndex = column?.dataIndex;

      // 获取嵌套路径的值
      let currentValue = value;
      if (Array.isArray(dataIndex)) {
        currentValue = row;
        for (const key of dataIndex) {
          if (currentValue && typeof currentValue === "object") {
            currentValue = currentValue[key];
          } else {
            currentValue = undefined;
            break;
          }
        }
      }

      const extraData = row[`${Array.isArray(dataIndex) ? dataIndex.join("_") : dataIndex}_extra_data`];
      const component = editable.component;

      const componentsMap = {
        [Enums.Components.Select]: ({ editable, extraData }) => {
          if (editable?.searchApi) {
            return (
              <RemoteSearchSelect
                {...editable.props}
                {...extraData?.props}
                api={editable.searchApi}
                value={currentValue}
                isGetDefaultOptions={editable?.isGetDefaultOptions || true}
                onChange={(value) => {
                  updateValue({ column, value, index });
                }}
              />
            );
          } else {
            return (
              <Select
                {...editable.props}
                {...extraData?.props}
                value={currentValue}
                onChange={(value) => {
                  updateValue({ column, value, index });
                }}
              />
            );
          }
        },
        [Enums.Components.InputNumber]: ({ editable, extraData }) => {
          return (
            <InputNumber
              {...editable.props}
              {...extraData?.props}
              value={currentValue}
              onChange={(val) => {
                updateValue({ column, value: val, index });
              }}
            />
          );
        },
        [Enums.Components.Input]: ({ editable, extraData }) => {
          return (
            <Input
              {...editable.props}
              {...extraData?.props}
              value={currentValue}
              onChange={(e) => {
                updateValue({ column, value: e.target.value, index });
              }}
            />
          );
        },
        [Enums.Components.ImageUpload]: ({ editable, extraData }) => {
          return (
            <ImageUpload
              {...editable.props}
              {...extraData?.props}
              fileList={currentValue || []}
              setFieldValue={(val) => updateValue({ column, value: val, index })}
              onChange={(fileList) => {
                updateValue({ column, value: fileList, index });
              }}
            />
          );
        },
      };

      const renderEditableComponent = componentsMap[component] || componentsMap[Enums.Components.Input];

      return renderEditableComponent({ editable, extraData });
    },
    [updateValue]
  );

  const columnRender = useCallback(
    ({ column, row, value, index, action }) => {
      const dataIndex = column?.dataIndex;

      // 获取嵌套路径的值显示
      let displayValue = value;
      if (Array.isArray(dataIndex)) {
        displayValue = row;
        for (const key of dataIndex) {
          if (displayValue && typeof displayValue === "object") {
            displayValue = displayValue[key];
          } else {
            displayValue = undefined;
            break;
          }
        }
      }

      if (column?.valueType === Enums.TableValueType.Operation) {
        return <div>{createOperations({ operations: column?.operations, row, index })}</div>;
      } else if (column?.editable) {
        return editableRender({ column, row, value: displayValue, index });
      } else if (column?.valueType === Enums.TableValueType.Image) {
        return (
          <Image src={displayValue} {...column.image} preview={{ mask: <EyeOutlined />, ...column.image?.preview }} />
        );
      } else if (column?.valueType === Enums.TableValueType.Html) {
        return <HTMLBlock html={displayValue} />;
      } else if (column?.valueType === "_table_key") {
        return <div style={{ textAlign: "center" }}>{index + 1}</div>;
      } else {
        return <span>{displayValue}</span>;
      }
    },
    [createOperations, editableRender]
  );

  const initColumnsValue = useCallback(
    (columns) => {
      const newColumns = [...columns];
      newColumns?.forEach((column) => {
        column.render = (value, row, index) => columnRender({ column, row, value, index });
      });
      return newColumns;
    },
    [columnRender]
  );

  const tableColumns = useMemo(() => {
    const { operations, columns, operationsWidth, isShowSetDefault } = paramsRef.current;

    if (!columns) return [];

    const newColumns = [...columns];
    const defaultOperations = [
      isShowSetDefault
        ? {
            key: "setDefault",
            title: "设为默认",
            action: ({ row, index }) => {
              if (row.is_default) return;
              const newData = [...dataSource];
              newData.forEach((item) => {
                item.is_default = item._table_key === row._table_key;
              });
              onChange?.(newData);
            },
          }
        : null,
      {
        key: "delete",
        title: "删除",
        action: handleDeleteRow,
      },
    ].filter(Boolean);

    newColumns.unshift({ title: "序号", valueType: "_table_key", dataIndex: "_table_key", width: 50 });
    newColumns.push({
      dataIndex: "operations",
      valueType: "operation",
      title: "操作",
      fixed: "right",
      align: "center",
      width: operationsWidth,
      operations: [...defaultOperations, ...operations],
    });
    return initColumnsValue(newColumns);
  }, [dataSource, onChange, handleDeleteRow, initColumnsValue]);

  return (
    <>
      <Table
        bordered
        rowKey="_table_key"
        size="small"
        pagination={false}
        scroll={{ x: "100%" }}
        {...props}
        rowSelection={
          rowSelection
            ? {
                selectedRowKeys,
                columnWidth: 32,
                onChange: (selectedRowKeys, selectedRows) => {
                  rowSelectionChange?.({ selectedRowKeys, selectedRows });
                  setSelectedRowKeys(selectedRowKeys);
                },
              }
            : false
        }
        columns={tableColumns}
        dataSource={dataSource}
      />
      {isShowAddRow ? (
        <Button
          onClick={handleAdd}
          type="primary"
          size="small"
          style={{
            marginTop: 6,
          }}
        >
          添加一行
        </Button>
      ) : null}
    </>
  );
}

EditTable.propTypes = {
  value: PropTypes.array,
  rowSelection: PropTypes.bool,
  columns: PropTypes.array.isRequired,
  operations: PropTypes.array,
  operationsWidth: PropTypes.number,
  isShowAddRow: PropTypes.bool,
  isShowSetDefault: PropTypes.bool,
  onChange: PropTypes.func,
  rowSelectionChange: PropTypes.func,
  defaultNewRow: PropTypes.object,
};

export default EditTable;
