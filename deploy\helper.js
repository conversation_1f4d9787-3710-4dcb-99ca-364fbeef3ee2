const fsPromises = require("node:fs/promises");
const { runCommand, readNginxConfig } = require("./common");

const Helper = Object.freeze({
  async updateNginxConfig({ app }) {
    if (!app) {
      throw new Error(`[update nginx config] app is not defined!`);
    }
    const file = `/etc/nginx/sites-available/default`;
    const content = await readNginxConfig();
    const regex = /root \/var\/www\/html\/frontend\/([^/]*)\/build/i;
    const newContent = content.replace(regex, `root /var/www/html/frontend/${app}/build`);
    await fsPromises.writeFile(file, newContent, { encoding: "utf-8" });
    await runCommand("sudo service nginx reload");
  },
});

module.exports = Helper;
