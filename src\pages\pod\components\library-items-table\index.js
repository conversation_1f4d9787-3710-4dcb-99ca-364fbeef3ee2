import Fetchers from "@/fetchers";
import SortableTable from "@/components/SortableTable";
import PropTypes from "prop-types";

function LibraryItemsTable(props) {
  const { tableData, setTableData, tableLoading, setTableLoading, loadTableData } = props;

  return (
    <SortableTable
      rowKey="id"
      pagination={false}
      {...tableData?.tableProps}
      setDataSource={(handler) => {
        setTableData((prevTableData) => ({
          ...prevTableData,
          tableProps: {
            ...prevTableData.tableProps,
            dataSource: handler(prevTableData.tableProps.dataSource),
          },
        }));
      }}
      loading={tableLoading}
      onSortChange={({ active, over, dataSource }) => {
        if (active?.id && over?.id) {
          setTableLoading(true);
          Fetchers.sortPodLibraryItems({ sortedIds: dataSource.map((item) => item.id) })
            .then(async () => {
              await loadTableData();
            })
            .finally(() => {
              setTableLoading(false);
            });
        }
      }}
      onChange={(pagination, filters, sorter, extra) => {
        loadTableData?.({ query: { page: pagination.current, pageSize: pagination.pageSize } });
      }}
    ></SortableTable>
  );
}

LibraryItemsTable.propTypes = {
  tableData: PropTypes.object,
  setTableData: PropTypes.func,
  tableLoading: PropTypes.bool,
  setTableLoading: PropTypes.func,
  loadTableData: PropTypes.func,
};

export default LibraryItemsTable;
