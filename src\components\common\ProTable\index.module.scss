body {
  .ant-spin-nested-loading {
    height: 100%;

    .ant-spin-container {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .ant-table-container {
      height: 100%;
      display: flex;
      flex-direction: column;

      .ant-table-body {
        position: relative;
        flex: 1;

        table {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
        }
      }
    }

    .ant-table {
      flex: 1;
    }
  }

  .tableColumnSettingPopover {
    width: 200px;

    [class~="ant-popover-inner"] {
      padding: 0;
      border-radius: 2px;
    }

    [class~="ant-popover-title"] {
      padding: 5px 16px 4px;
      color: rgba(0, 0, 0, 0.85);
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    }

    .tableColumnSettingTitle {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 32px;
      font-weight: 500;

      .tableColumnSettingResetButton {
        color: #1890ff;
        cursor: pointer;
        background-color: transparent;
        transition: color 0.3s;

        &:hover {
          color: #40a9ff;
        }
      }
    }

    .tableColumnSettingList {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding-bottom: 8px;

      .tableColumnSettingListTitle {
        margin: 0 6px;
        padding-left: 24px;
        color: rgba(0, 0, 0, 0.65);
        font-size: 12px;
      }

      .tableColumnSettingTree {
        [class~="ant-tree-draggable-icon"] {
          cursor: grab;

          &:active {
            cursor: grabbing;
          }
        }

        .treeTitle {
          display: flex;
          position: relative;

          .title {
            flex: 1;
          }

          &:hover {
            .treeItemActions {
              display: block;
            }
          }

          .treeItemActions {
            display: none;
            float: right;
            gap: 8px;

            svg {
              color: #1677ff;
              cursor: pointer;
            }

            > span + span {
              margin-inline-start: 8px;
            }
          }
        }
      }
    }
  }
}

.container {
  .listToolbar {
    display: flex;
    justify-content: space-between;
    padding: 0 20px 8px;

    .listToolbarLeft,
    .listToolbarRight {
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }

    .listToolbarRight {
      gap: 8px;

      .tableActions {
        display: flex;
        gap: 8px;
        align-items: center;
        font-size: 16px;

        .tableActionsItem {
          cursor: pointer;

          &:hover {
            color: #1677ff;
          }
        }
      }
    }
  }

  .tableWrapper {
    [class~="ant-table-small"] {
      [class~="ant-table-thead"] {
        > tr {
          th {
            padding: 4px;
          }
        }
      }

      [class~="ant-table-tbody"] {
        [class~="ant-table-row"] {
          [class~="ant-table-cell"] {
            padding: 2px 4px;
          }
        }
      }
    }
  }

  .summaryRow {
    [class~="ant-table-cell"] {
      padding: 2px 4px !important; /* 减小内边距降低行高 */
      background-color: #d7e0f3;
      font-weight: 500;
    }
  }

  [class~="ant-pagination-total-text"] {
    flex: 1;
  }

  .paginationWithFooter {
    display: flex;
    // flex: 1;
    // justify-content: space-between;
    // align-items: center;
    width: 100%;

    .footerContent {
      flex: 1;
    }

    .totalInfo {
      margin-left: 16px;
    }
  }
}
