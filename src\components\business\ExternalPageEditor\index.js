import styles from "./index.module.scss";

import PropTypes from "prop-types";
import { EditOutlined } from "@ant-design/icons";
import { useEffect, useCallback, useRef } from "react";
import axios from "@/fetchers/request";

function ExternalPageEditor(props) {
  const {
    children,
    target,
    targetUrl,
    submit,
    defaultValue,
    messageAction,
    defaultAction,
    extraSubmitData,
    filterPrimaryAndSecondary = false,
  } = props;
  const { url: submitUrl, method: submitMethod = "post", data: submitData } = submit?.request || {};
  const windowStateRef = useRef({
    currentWindow: null,
    intervalId: null,
    isListening: false,
  });

  const handleMessage = useCallback(
    async (event) => {
      const { data } = event;
      const currentState = windowStateRef.current;

      if (data.action === "connected") {
        // 弹窗内网站页面切换变体，会丢失窗体对象，需要重新connect获取，暂时注释
        // if (currentState.intervalId) {
        //   clearInterval(currentState.intervalId);
        //   currentState.intervalId = null;
        // }

        if (currentState.currentWindow && defaultAction) {
          currentState.currentWindow.postMessage({ action: defaultAction, data: defaultValue, targetUrl }, targetUrl);
        }
      }

      if (data.action === messageAction) {
        try {
          await axios({
            url: submitUrl,
            method: submitMethod,
            data: { ...event.data.data, ...submitData, ...extraSubmitData },
          });

          if (currentState.currentWindow) {
            currentState.currentWindow.close();
            window.removeEventListener("message", handleMessage);
            currentState.isListening = false;
            currentState.currentWindow = null;
          }
        } catch (error) {
          console.error(error);
        }
      }
    },
    [messageAction, submitData, submitMethod, submitUrl, defaultValue, targetUrl, defaultAction, extraSubmitData]
  );

  function handleOpen() {
    const currentState = windowStateRef.current;

    const newWindow = window.open(targetUrl, target, "popup,width=1280,height=800,modal=yes,status=no");
    let message = { action: "connect" };
    const intervalId = setInterval(() => {
      if (filterPrimaryAndSecondary) {
        message = { action: "connect", data: { filterPrimaryAndSecondary } };
      }
      if (newWindow) {
        newWindow.postMessage(message, targetUrl);
      }
    }, 1000);

    if (!currentState.isListening) {
      window.addEventListener("message", handleMessage);
    }

    currentState.currentWindow = newWindow;
    currentState.intervalId = intervalId;
    currentState.isListening = true;
  }

  useEffect(() => {
    const { currentWindow, intervalId, isListening } = windowStateRef.current;

    return () => {
      if (isListening) {
        window.removeEventListener("message", handleMessage);
      }

      if (intervalId) {
        clearInterval(intervalId);
      }

      if (currentWindow) {
        currentWindow.close();
      }
    };
  }, [handleMessage]);

  return (
    <>
      <span className={styles.externalPageEditor} onClick={handleOpen}>
        <EditOutlined />
        <span>{children}</span>
      </span>
    </>
  );
}

ExternalPageEditor.propTypes = {
  children: PropTypes.any,
  target: PropTypes.string,
  targetUrl: PropTypes.string.isRequired,
  submit: PropTypes.object.isRequired,
  defaultValue: PropTypes.any,
  messageAction: PropTypes.string.isRequired,
};

export default ExternalPageEditor;
