import { fabric } from "fabric";

const Utils = Object.freeze({
  hasOneOfOwnProperties(obj, keys) {
    return keys.some((key) => Object.hasOwnProperty.call(obj, key));
  },
  numberToFixed(value, fractionDigits) {
    const str = value.toFixed(fractionDigits + 1);
    return str.substring(0, str.length - 1);
  },
  getAspectRatioScaleSize({ width, height, targetSize, isWidth = true, fractionDigits }) {
    const originalSize = isWidth ? width : height;
    const scale = 1 + (targetSize - originalSize) / originalSize;
    const hasFractionDigits = typeof fractionDigits === "number";
    const targetWidth = hasFractionDigits ? +Utils.numberToFixed(width * scale, fractionDigits) : width * scale;
    const targetHeight = hasFractionDigits ? +Utils.numberToFixed(height * scale, fractionDigits) : height * scale;
    return { width: targetWidth, height: targetHeight };
  },
  textCapitalize(text = "") {
    return (text || "")
      .toLowerCase()
      .split(" ")
      .map((word) => word.replace(/^./, (char) => char.toUpperCase()))
      .join(" ");
  },
  cloneDeep(data) {
    return JSON.parse(JSON.stringify(data));
  },
});

export const FabricObjectType = Object.freeze({
  CustomImageBox: "CustomImageBox",
  Text: "text",
  Rect: "rect",
  Image: "image",
  CustomTextBox: "CustomTextBox",
  Group: "group",
  ActiveSelection: "activeSelection",
});

export const defaultControlProps = Object.freeze({
  stroke: "#000",
  strokeWidth: 1,
  strokeDashArray: [30],
  borderScaleFactor: 2,
  borderColor: "rgba(79, 128, 255, 0.8)",
});

export const defaultObjectProps = Object.freeze({
  left: 340,
  top: 340,
  width: 800,
  height: 800,
  scaleX: 0.4,
  scaleY: 0.4,
});

export const defaultTextProps = Object.freeze({
  editable: false,
  text: "A",
  textColor: "#000",
  textStroke: "transparent",
  textStrokeWidth: 0,
  fontFamily: "sans-serif",
  fontSize: 100,
  lineHeight: 1,
  fontStyle: "normal",
  fontWeight: "normal",
  minWidth: 20,
  direction: "ltr",
  charSpacing: 0,
  underline: false,
  linethrough: false,
  textAlign: "center",
  minFontSize: 80,
  maxFontSize: 400,
  horizontalAlign: "center",
  verticalAlign: "center",
});

function getScaledRealSize({ object }) {
  return {
    width: object.width * (1 + (object.scaleX - defaultObjectProps.scaleX) / defaultObjectProps.scaleX),
    height: object.height * (1 + (object.scaleY - defaultObjectProps.scaleY) / defaultObjectProps.scaleY),
  };
}

function setImagePropsAfterLoad({ image, object }) {
  const defaultProps = {
    top: 0,
    left: 0,
    scaleX: 1,
    scaleY: 1,
    angle: 0,
    originX: "center",
    originY: "center",
  };
  const props = {};
  const clonedObject = Utils.cloneDeep(object);
  Object.keys(defaultProps).forEach((key) => {
    props[key] = clonedObject[key];
  });
  image.set(props);
  image.filters = object.filters.map((data) => fabric.Image.filters[data.type].fromObject(data));
  image.applyFilters();
}

fabric.Object.NUM_FRACTION_DIGITS = 15;

fabric.CustomTextBox = fabric.util.createClass(fabric.Group, fabric.Observable, {
  type: FabricObjectType.CustomTextBox,
  initialize(text, props = {}) {
    const {
      width = defaultObjectProps.width,
      height = defaultObjectProps.height,
      objects = [],
      minFontSize = defaultTextProps.minFontSize,
      maxFontSize = defaultTextProps.maxFontSize,
      originX = "left",
      originY = "top",
      fontFamily,
    } = props;
    this.maxFontSize = maxFontSize;
    this.minFontSize = minFontSize;
    const rectProps = objects.find((item) => item.type === FabricObjectType.Rect);
    const rectObject = new fabric.Rect({
      ...defaultControlProps,
      width,
      height,
      top: 0,
      left: 0,
      fill: null,
      originX: "center",
      originY: "center",
      ...rectProps,
    });
    const textProps = objects.find((item) => item.type === FabricObjectType.Text);
    const textObject = new fabric.Text(text, {
      ...defaultTextProps,
      top: 0,
      left: 0,
      originX: "center",
      originY: "center",
      ...textProps,
      fill: props.textColor,
      stroke: props.textStroke,
      strokeWidth: props.textStrokeWidth,
    });
    const groupTextProps = {};
    Object.keys(defaultTextProps).forEach((key) => {
      groupTextProps[key] = textObject[key];
    });
    const groupProps = {
      originX,
      originY,
      ...defaultObjectProps,
      ...defaultControlProps,
      ...groupTextProps,
      minFontSize,
      maxFontSize,
      ...props,
    };
    this.callSuper("initialize", [rectObject, textObject], groupProps);
    this.fitText();
    this.alignText();
    if (fontFamily) {
      this.set({ fontFamily, dirty: true });
    }
  },
  set(key, value) {
    this.callSuper("set", key, value);
    const props = typeof key === "string" ? { [key]: value } : key;
    const textObject = this.getTextObject();
    if (Utils.hasOneOfOwnProperties(props, ["text"])) {
      textObject.set({ text: props.text || "" });
    } else if (Utils.hasOneOfOwnProperties(props, ["textColor"])) {
      textObject.set({ fill: props.textColor });
    } else if (Utils.hasOneOfOwnProperties(props, ["textStroke"])) {
      textObject.set({ stroke: props.textStroke });
    } else if (Utils.hasOneOfOwnProperties(props, ["textStrokeWidth"])) {
      textObject.set({ strokeWidth: props.textStrokeWidth });
    } else if (Utils.hasOneOfOwnProperties(props, ["fontFamily"])) {
      textObject.set({ fontFamily: props.fontFamily });
    }
    if (Utils.hasOneOfOwnProperties(props, ["visible"])) {
      textObject.set({ visible: props.visible });
    }
    if (Utils.hasOneOfOwnProperties(props, ["text", "minFontSize", "maxFontSize"])) {
      this.fitText();
    }
    if (Utils.hasOneOfOwnProperties(props, ["horizontalAlign", "verticalAlign"])) {
      this.alignText();
    }
    if (key === "canvas") {
      this._nestedObject._set(key, value);
    }
  },
  getTextObject() {
    return this.getObjects().find((item) => item.type === FabricObjectType.Text);
  },
  getRectObject() {
    return this.getObjects().find((item) => item.type === FabricObjectType.Rect);
  },
  getScaledSize() {
    return { width: this.width * this.scaleX, height: this.height * this.scaleY };
  },
  getScaledRealSize() {
    return getScaledRealSize({ object: this });
  },
  getDimensionOfOneFontSize() {
    const textObject = this.getTextObject();
    const originalFontSize = textObject.fontSize;
    const originalSize = { width: textObject.width, height: textObject.height };
    textObject.set({ fontSize: originalFontSize - 1 });
    const updatedSize = { width: textObject.width, height: textObject.height };
    textObject.set({ fontSize: originalFontSize });
    return { width: originalSize.width - updatedSize.width, height: originalSize.height - updatedSize.height };
  },
  getMaxFontSize() {
    const size = this.getScaledRealSize();
    const key = size.width <= size.height ? "width" : "height";
    const dimension = this.getDimensionOfOneFontSize();
    return size[key] / dimension[key];
  },
  getFitFontSize() {
    const maxFontSize = this.getMaxFontSize();
    let fitFontSize = maxFontSize;
    if (maxFontSize > this.maxFontSize) {
      fitFontSize = this.maxFontSize;
    } else if (maxFontSize < this.minFontSize) {
      fitFontSize = this.minFontSize;
    }
    return fitFontSize;
  },
  fitText() {
    const fontSize = this.getFitFontSize();
    const textObject = this.getTextObject();
    const size = this.getScaledRealSize();
    const updateProps = {
      width: size.width,
      height: size.height,
      scaleX: defaultObjectProps.scaleX,
      scaleY: defaultObjectProps.scaleY,
    };
    let textScale = 1;
    if (textObject.measureLine(0).width > size.width) {
      textScale = size.width / textObject.measureLine(0).width;
    }
    this.set({ ...updateProps, fontSize });
    const rectObject = this.getRectObject();
    rectObject.set({ ...updateProps, top: 0, left: 0, scaleX: 1, scaleY: 1 });
    textObject.set({ fontSize, width: updateProps.width, scaleX: textScale, scaleY: textScale });
  },
  fitContent() {
    this.fitText();
  },
  alignText() {},
});

fabric.CustomImageBox = fabric.util.createClass(fabric.Group, fabric.Observable, {
  type: FabricObjectType.CustomImageBox,
  initialize(props = {}) {
    const { src, originX = "left", originY = "top" } = props;
    const imageData = props.objects?.find((item) => item.type === FabricObjectType.Image) || {};
    if (!imageData.filters) {
      imageData.filters = [];
    }
    const imageObject = new fabric.Image({ src });
    const rectObject = new fabric.Rect({
      ...defaultObjectProps,
      ...defaultControlProps,
      fill: null,
      top: 0,
      left: 0,
      originX: "center",
      originY: "center",
      scaleX: 1,
      scaleY: 1,
    });
    // imageObject.filters = imageData.filters.map((data) => fabric.Image.filters[data.type].fromObject(data));
    this.callSuper("initialize", [imageObject, rectObject], { originX, originY, ...props });
    if (src) {
      this.setSrc(src, () => {
        // imageObject.set({
        //   top: 0,
        //   left: 0,
        //   originX: "center",
        //   originY: "center",
        //   scaleX: imageData?.scaleX || 1,
        //   scaleY: imageData?.scaleY || 1,
        // });
        // imageObject.applyFilters();
        setImagePropsAfterLoad({ image: imageObject, object: imageData });
        this.fitContent();
        this.canvas?.renderAll();
      });
    }
  },
  getImageObject() {
    return this.getObjects().find((item) => item.type === "image");
  },
  getRectObject() {
    return this.getObjects().find((item) => item.type === "rect");
  },
  getScaledSize() {
    return { width: this.width * this.scaleX, height: this.height * this.scaleY };
  },
  getScaledRealSize() {
    return getScaledRealSize({ object: this });
  },
  setSrc(src, callback, options = { crossOrigin: "anonymous" }) {
    this.set({ src, crossOrigin: options.crossOrigin });
    const imageObject = this.getImageObject();
    imageObject.setSrc(
      src,
      (...args) => {
        imageObject.set({ dirty: true });
        this.fitContent();
        callback?.(...args);
      },
      options
    );
  },
  set(key, value) {
    this.callSuper("set", key, value);
    if (key === "canvas") {
      this._nestedObject._set(key, value);
    }
  },
  objectFitContain() {
    this.extraData.objectFit = "contain";
    this.clipPath = null;
    const size = this.getScaledRealSize();
    const imageObject = this.getImageObject();
    const rectObject = this.getRectObject();
    const scaleWidthSize = Utils.getAspectRatioScaleSize({
      width: imageObject.width,
      height: imageObject.height,
      targetSize: size.width,
      isWidth: true,
      fractionDigits: 2,
    });
    const scaleHeightSize = Utils.getAspectRatioScaleSize({
      width: imageObject.width,
      height: imageObject.height,
      targetSize: size.height,
      isWidth: false,
      fractionDigits: 2,
    });
    const scaledSize =
      scaleWidthSize.width <= size.width && scaleWidthSize.height <= size.height ? scaleWidthSize : scaleHeightSize;
    const scaleX = scaledSize.width / imageObject.width;
    const scaleY = scaledSize.height / imageObject.height;
    const updateProps = {
      width: size.width,
      height: size.height,
      scaleX: defaultObjectProps.scaleX,
      scaleY: defaultObjectProps.scaleY,
    };
    this.set({ ...updateProps });
    rectObject.set({ ...updateProps, top: 0, left: 0, originX: "center", originY: "center", scaleX: 1, scaleY: 1 });
    imageObject.set({ scaleX, scaleY, top: 0, left: 0, originX: "center", originY: "center" });
  },
  objectFitCover() {
    this.extraData.objectFit = "cover";
    new Promise((resolve) => {
      if (!this.clipPath) {
        this.getRectObject().clone((clonedRect) => {
          this.clipPath = clonedRect;
          resolve();
        });
      } else {
        resolve();
      }
    }).then(() => {
      const image = this.getImageObject();
      const rect = this.getRectObject();
      const imageSize = { width: image.width * image.scaleX, height: image.height * image.scaleY };
      const rectSize = { width: rect.width * rect.scaleX, height: rect.height * rect.scaleY };
      let targetSize;
      if (image.width < image.height) {
        targetSize = Utils.getAspectRatioScaleSize({
          width: imageSize.width,
          height: imageSize.height,
          targetSize: rectSize.width,
          isWidth: true,
        });
      } else if (image.width > image.height) {
        targetSize = Utils.getAspectRatioScaleSize({
          width: imageSize.width,
          height: imageSize.height,
          targetSize: rectSize.height,
          isWidth: false,
        });
      }
      if (targetSize) {
        image.set({
          scaleX: targetSize.width / image.width,
          scaleY: targetSize.height / image.height,
        });
        this.canvas?.renderAll();
      }
    });
  },
  fitContent({ objectFit } = {}) {
    if (objectFit) {
      this.extraData.objectFit = objectFit;
    }
    if (this.extraData.objectFit === "cover") {
      this.objectFitCover();
    } else {
      this.objectFitContain();
    }
  },
});

const exportKeys = ["extraData", "selectable"];

const originalSet = fabric.Object.prototype.set;
fabric.Object.prototype.set = function (key, value) {
  originalSet.call(this, key, value);
  if ([FabricObjectType.CustomTextBox, FabricObjectType.CustomImageBox].includes(this.type)) {
    const props = typeof key === "string" ? { [key]: value } : key;
    if (Utils.hasOneOfOwnProperties(props, ["stroke"])) {
      this.getRectObject().set({ stroke: props.stroke });
    }
  }
};

const originalToObject = fabric.Object.prototype.toObject;
fabric.Object.prototype.toObject = function (additionalPropKeys = []) {
  if (this.type === FabricObjectType.CustomTextBox) {
    return originalToObject.call(this, [...additionalPropKeys, ...exportKeys, ...Object.keys(defaultTextProps)]);
  } else if (this.type === FabricObjectType.CustomImageBox) {
    return originalToObject.call(this, [...additionalPropKeys, ...exportKeys, ...["src", "crossOrigin"]]);
  }
  return originalToObject.call(this, [...additionalPropKeys, ...exportKeys]);
};

// 修复Image.fromObject不触发callback的bug
fabric.Image.fromObject = function (object, callback) {
  const image = new fabric.Image(Utils.cloneDeep(object));
  image.setSrc(
    object.src,
    () => {
      // image.filters = object.filters.map((data) => fabric.Image.filters[data.type].fromObject(data));
      // image.applyFilters();
      setImagePropsAfterLoad({ image, object });
      callback(image);
    },
    { crossOrigin: object.crossOrigin }
  );
};

fabric.CustomTextBox.fromObject = function (object, callback) {
  return fabric.Object._fromObject(FabricObjectType.CustomTextBox, object, callback, "text");
};

fabric.CustomImageBox.fromObject = function (object, callback) {
  return fabric.Object._fromObject(FabricObjectType.CustomImageBox, object, callback);
};

fabric.Canvas.prototype.toJSON = function (exportKeys) {
  return { ...this.toObject(exportKeys), width: this.getWidth(), height: this.getHeight(), zoom: this.getZoom() };
};

// 滤镜禁用webgl使用canvas2d
fabric.enableGLFiltering = false;

// 自定义黑白滤镜
fabric.Image.filters.BlackWhite = fabric.util.createClass(fabric.Image.filters.BaseFilter, {
  type: "BlackWhite",
  initialize(options) {
    this.options = options;
  },
  applyTo(params) {
    const data = params.imageData.data;
    const threshold = this.options.threshold ?? 200;
    for (let i = 0; i < data.length; i += 4) {
      const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;
      data[i] = data[i + 1] = data[i + 2] = avg > threshold ? 255 : 0;
    }
  },
  toObject() {
    return { type: this.type, options: this.options };
  },
});

fabric.Image.filters.BlackWhite.fromObject = function (object) {
  return new fabric.Image.filters.BlackWhite(object.options);
};
