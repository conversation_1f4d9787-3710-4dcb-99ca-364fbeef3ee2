import styles from "./index.module.scss";

import { useCallback, useEffect, useRef, useState } from "react";
import { Link, Outlet, useLocation } from "react-router-dom";
import useSWR from "swr";
import classNames from "classnames";
import { observer } from "mobx-react-lite";
import { autorun } from "mobx";

import Fetchers from "@/fetchers";
import Utils from "@/utils";
import Api from "@/fetchers/api";
import store from "@/stores";
import Helper from "@/helpers";
import Enums from "@/enums";
import usePageVisibility from "hooks/usePageVisibility";

import { Image } from "antd";
import Header from "@/components/Header";
import Menu from "@/components/Menu";
import Loading from "@/components/common/Loading";

const initPrinterService = async (systemInfo) => {
  if (!systemInfo?.printer_service_enable) return;

  try {
    await Utils.appendScript("http://localhost:18000/CLodopfuncs.js");
    const [secretA, secretB] = systemInfo?.printer_service_secret?.split(":") || [];
    window?.LODOP && window.LODOP.SET_LICENSES("", secretA, secretB, "");
  } catch (error) {
    console.warn("打印服务初始化失败:", error);
  }
};

const setFavicon = (faviconUrl) => {
  if (!faviconUrl) return;

  try {
    const link = document.querySelector("link[rel*='icon'") || document.createElement("link");
    link.type = "image/x-icon";
    link.rel = "shortcut icon";
    link.href = faviconUrl;
    document.getElementsByTagName("head")[0].appendChild(link);
  } catch (error) {
    console.log("Error setting favicon:", error);
  }
};

function Layout() {
  const location = useLocation();
  const isInsideIframe = Helper.isInsideIframe();
  const [flatMenuItems, setFlatMenuItems] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [openKeys, setOpenKeys] = useState([]);
  const pageVisibility = usePageVisibility();

  const paramsRef = useRef({ isInsideIframe });

  const { data: systemInfo } = useSWR(Api.getSystemInfo, async () => {
    if (store.systemInfo) {
      return store.systemInfo;
    }

    try {
      const result = await Fetchers.getSystemInfo().then((res) => res?.data?.data);

      if (result) {
        store.setSystemInfo(result);
        localStorage.setItem(Enums.LocalStorageKey.SystemInfo, Utils.JSON.stringify(result));
        return result;
      }
      return null;
    } catch (error) {
      console.warn("获取系统信息失败:", error);
    }
  });

  const { data: menuProps, isLoading } = useSWR(
    [Api.getMenuItems],
    () => !isInsideIframe && Fetchers.getMenuItems().then((res) => res?.data?.data)
  );

  const handleMenuSelect = useCallback((menuItems) => {
    const treeData = Utils.buildTreeId({ treeData: menuItems });
    const flatTreeData = Utils.flatTree({ treeData });
    const pathname = window.location.pathname;

    setFlatMenuItems(flatTreeData);

    const selectedMenuItem = flatTreeData.find(
      (item) => item.url === pathname || (item?.active_menu_url && item?.active_menu_url?.includes(pathname))
    );

    if (selectedMenuItem) {
      document.title = selectedMenuItem.title || "";
      setSelectedKeys([selectedMenuItem.key]);

      const keyPath = Utils.fillTreeNodesParents({
        treeNodes: [selectedMenuItem],
        flatTreeData,
      }).map((item) => item.key);

      setOpenKeys(keyPath);
    }
  }, []);

  const fetchPageInfo = useCallback(async () => {
    const pathname = location.pathname;

    try {
      // 获取缓存的页面配置
      const pathPageConfigs = Utils.JSON.parse(Helper.getPathPageConfigs()) || {};
      const pageInfo = pathPageConfigs[pathname];

      if (pageInfo) return;

      const result = await Fetchers.getPageInfo({
        params: { pathname: `${pathname}${location.search}` },
      }).then((res) => res?.data?.data);

      if (!result) return;

      if (result?.site_switcher) {
        const { default_value: defaultValue, enable } = result.site_switcher;
        const currentPathSiteValues = Helper.getCurrentPathSiteValues();
        const localPathSiteValues = Utils.JSON.parse(localStorage.getItem(Enums.LocalStorageKey.PathSiteValues));
        let newPathSiteValues = { ...currentPathSiteValues };

        // 如果 localStorage 中有值，直接使用并返回
        if (localPathSiteValues?.[pathname]) {
          newPathSiteValues = {
            ...currentPathSiteValues,
            ...localPathSiteValues,
          };
        } else if (!currentPathSiteValues[pathname] && defaultValue && enable) {
          newPathSiteValues[pathname] = defaultValue;
          localStorage.setItem(Enums.LocalStorageKey.PathSiteValues, Utils.JSON.stringify(newPathSiteValues));
        }

        store.setPathSiteValues(newPathSiteValues);
        Helper.setCurrentPathSiteValues(Utils.JSON.stringify(newPathSiteValues));
      }

      const currentPagesInfo = { ...pathPageConfigs, [pathname]: result };
      store.setPathPageConfigs(currentPagesInfo);
      Helper.setPathPageConfigs(Utils.JSON.stringify(currentPagesInfo));
    } catch (error) {
      store.setPathPageConfigs(null);
    }
  }, [location]);

  const handleMenuClick = useCallback(
    ({ key, domEvent }) => {
      if (domEvent?.ctrlKey) return;
      const menuItem = flatMenuItems?.find((item) => item.key === key);
      if (menuItem?.title) {
        document.title = menuItem.title;
      }
    },
    [flatMenuItems]
  );

  useEffect(() => {
    if (menuProps?.items?.length > 0) {
      handleMenuSelect(menuProps.items);
    }
  }, [menuProps, handleMenuSelect]);

  useEffect(() => {
    const clearDisposer = autorun(() => {
      initPrinterService(store.systemInfo);

      if (store.systemInfo?.favicon) {
        setFavicon(store.systemInfo.favicon);
      }
    });
    return () => clearDisposer();
  }, []);

  useEffect(() => {
    const { isInsideIframe } = paramsRef.current;

    if (pageVisibility && !isInsideIframe) {
      const pathSiteValues = Helper.getCurrentPathSiteValues();

      if (Object.keys(pathSiteValues)?.length) {
        localStorage.setItem(Enums.LocalStorageKey.PathSiteValues, Utils.JSON.stringify(pathSiteValues));
      }
    }
  }, [pageVisibility]);

  useEffect(() => {
    const { isInsideIframe } = paramsRef.current;

    if (systemInfo?.page_info_enable && !isInsideIframe) {
      fetchPageInfo();
    }
  }, [location, fetchPageInfo, systemInfo]);

  if (isInsideIframe) {
    return <Outlet />;
  }

  return (
    <div className={classNames(styles.main, { [styles.asideFold]: store.asideFold })}>
      <div className={styles.wrapper}>
        <div className={styles.aside}>
          <Link to="/">
            <div className={styles.asideHeader}>
              <Image src={store.systemInfo?.logo} width={30} height={30} preview={false} />
              {!store.asideFold ? <span>{store.systemInfo?.name}</span> : null}
            </div>
          </Link>
          <div className={styles.asideBody}>
            <Loading loading={isLoading}>
              <Menu
                mode="inline"
                {...menuProps}
                inlineCollapsed={store.asideFold}
                selectedKeys={selectedKeys}
                openKeys={openKeys}
                onClick={handleMenuClick}
                onSelect={({ key, keyPath, selectedKeys, domEvent }) => {
                  setSelectedKeys(selectedKeys);
                }}
                onOpenChange={(openKeys) => {
                  setOpenKeys(openKeys);
                }}
              ></Menu>
            </Loading>
          </div>
        </div>
        <div className={styles.body}>
          <div className={classNames(styles.header, "layout-header")}>
            <Header></Header>
          </div>
          <div className={styles.content}>
            <Outlet></Outlet>
          </div>
          {/*<div className={styles.footer}>Footer</div>*/}
        </div>
      </div>
    </div>
  );
}

Layout = observer(Layout);

export default Layout;
