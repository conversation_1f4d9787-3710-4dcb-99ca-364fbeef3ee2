.container {
  max-height: 90vh;
  display: flex;
  gap: 20px;
  overflow-y: auto;

  .gallery {
    .carouselWrapper {
      width: 500px;
    }
  }

  .content {
    flex: 1;
    height: 100%;
    overflow-y: scroll;
  }
}

.repair {
  .promptContent {
    font-size: 20px;
  }
}

.repairFooterWrapper {
  display: flex;
  gap: 10px;
  justify-content: right;
}

[class~="ant-carousel"] [class~="slick-prev"],
[class~="ant-carousel"] [class~="slick-next"] {
  z-index: 2;
  font-size: 30px;
  color: #ccc;

  &:hover {
    color: #fff;
  }

  &::after {
    display: none;
  }

  &[class~="slick-disabled"] {
    color: #aaa;
    cursor: no-drop;
  }
}

[class~="ant-carousel"] [class~="slick-prev"] {
  inset-inline-start: 0px;
}

[class~="ant-carousel"] [class~="slick-next"] {
  inset-inline-end: 12px;
}
