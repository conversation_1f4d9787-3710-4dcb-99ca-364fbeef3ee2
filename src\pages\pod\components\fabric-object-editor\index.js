import { Collapse } from "antd";
import PropTypes from "prop-types";
import FabricObjectTransform from "@/pages/pod/components/fabric-object-editor/components/transform";
import { observer } from "mobx-react-lite";
import ImageBehavior from "@/pages/pod/components/fabric-object-editor/components/image-behavior";
import podStore from "@/pages/pod/stores";
import { LayerType } from "@/pages/pod/common";

function FabricObjectEditor(props) {
  const { transformFormRef } = props;

  return (
    <div>
      <Collapse
        defaultActiveKey={["1"]}
        items={(() => {
          const items = [
            {
              key: "1",
              label: "Transform",
              forceRender: true,
              children: <FabricObjectTransform transformFormRef={transformFormRef}></FabricObjectTransform>,
            },
          ];
          if (podStore.activeObject?.extraData.layerType === LayerType.ImagePlaceholder) {
            items.push({
              key: "2",
              label: "Image Behavior",
              children: <ImageBehavior></ImageBehavior>,
            });
          }
          return items;
        })()}
        accordion
      ></Collapse>
    </div>
  );
}

FabricObjectEditor = observer(FabricObjectEditor);

FabricObjectEditor.propTypes = {
  canvas: PropTypes.object,
  transformFormRef: PropTypes.object,
};

export default FabricObjectEditor;
