import { Card as AntdCard } from "antd";
import JSONComponents from "components/common/JSONComponent";
import Card<PERSON>abel from "./components/CardLabel";

function Card({ data }) {
  const { extra, ...otherProps } = data?.props || {};

  return (
    <AntdCard size="small" {...otherProps} title={<CardLabel data={data?.props} />}>
      <JSONComponents data={data} />
    </AntdCard>
  );
}

export default Card;
