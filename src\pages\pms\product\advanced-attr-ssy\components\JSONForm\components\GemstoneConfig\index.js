import styles from "./index.module.scss";
import { useState, useRef, useEffect, memo, useCallback } from "react";
import PropTypes from "prop-types";

import {
  baseStoneShapeOptions,
  baseStoneTypeOptions,
} from "@/pages/pms/product/advanced-attr-ssy/components/JSONForm/common/vars";
import Enums from "@/pages/pms/product/advanced-attr-ssy/components/JSONForm/common/enums";
import Fetchers from "fetchers";
import Helper from "helpers";
import Utils from "utils";
import CommonEnums from "enums";

import { Card, Radio, InputNumber, Input, Button, Space, Select, Checkbox } from "antd";
import StoneAccessories from "../StoneAccessories";

const StoneCard = memo(({ index, onDelete, children }) => {
  return (
    <Card
      title={`G${index + 1}`}
      extra={
        <Button type="primary" danger onClick={() => onDelete(index)}>
          删除
        </Button>
      }
      styles={{
        header: {
          backgroundColor: "#343a40",
          color: "#FFFFFF",
        },
      }}
      size="small"
    >
      {children}
    </Card>
  );
});

function GemstoneConfig(props) {
  const {
    value = [],
    onChange,
    tables = [],
    stone_shape_options,
    stone_type_options,
    isCustomPrice = false,
    form,
    params,
  } = props;
  const [stones, setStones] = useState(value);
  const stoneShapeOptions = stone_shape_options || baseStoneShapeOptions;
  const stoneTypeOptions = stone_type_options || baseStoneTypeOptions;

  const paramsRef = useRef({});
  paramsRef.current = {
    stones,
    onChange,
    calculatePrices,
    isCustomPrice,
  };

  /**
   * 添加石头组
   */
  function handleAdd() {
    const newStone = {
      info: {
        [Enums.InfoKeys.Shape]: "round",
        [Enums.InfoKeys.Num]: 1,
        [Enums.InfoKeys.TotalCarat]: 0.001,
        [Enums.InfoKeys.StoneInfo]: "",
        [Enums.InfoKeys.CaratList]: [],
        [Enums.InfoKeys.DefaultStoneType]: "simulated",
        [Enums.InfoKeys.DefaultStoneValue]: "crystal",
      },
      data: {},
    };

    // 初始化石头分组数据
    if (tables.length > 0) {
      tables.forEach((table) => {
        if (table?.columns) {
          table.columns.forEach((column, index) => {
            const stoneKey = column.dataIndex;
            const isActive =
              (table.key === "genuine" && ["moissanite"].includes(stoneKey)) || table.key === "simulated";

            if (!newStone.data[table.key]) {
              newStone.data[table.key] = {
                data: {},
              };
            }

            newStone.data[table.key].data[column.dataIndex] = {
              index,
              active: isActive,
              price_list: {},
              stone_type: "gao",
            };
          });
        }
      });
    }

    const newStones = [...stones, newStone];
    setStones(newStones);
    onChange?.(newStones);

    // 如果不是自定义价格，则计算新添加石头的价格
    if (!isCustomPrice) {
      calculatePrices({
        index: newStones.length - 1,
        stones: newStones,
        stonesData: [newStone],
      });
    }
  }

  /**
   * 删除石头
   * @param {Number} 石头索引
   */
  function handleDelete(index) {
    const newStones = stones.filter((_, i) => i !== index);
    setStones(newStones);
    onChange?.(newStones);
  }

  /**
   * 石头信息更新
   * @param {Number} 石头索引
   * @param {String} 字段key
   * @param {*} 属性值
   */
  const handleInfoChange = useCallback((index, key, value) => {
    const { calculatePrices, isCustomPrice, onChange, stones } = paramsRef.current;
    const newStones = [...stones];

    if (key === Enums.InfoKeys.TotalCarat) {
      newStones[index].info[Enums.InfoKeys.CaratList] = [];
    }

    newStones[index].info[key] = value;
    setStones(newStones);
    onChange?.(newStones);

    if (key !== Enums.InfoKeys.StoneInfo && !isCustomPrice) {
      // 传入当前修改的石头数据
      calculatePrices({
        index,
        stones: newStones,
        stonesData: [newStones[index]],
      });
    }
  }, []);

  /**
   * 更新石头配件
   */
  const handleUpdateStoneAccessories = useCallback(({ index, options, stoneType: tableKey, stoneKey: dataIndex }) => {
    const { calculatePrices, isCustomPrice, onChange, stones } = paramsRef.current;

    const newStones = [...stones];

    newStones[index].data[tableKey].data[dataIndex].options = options;

    setStones(newStones);
    onChange?.(newStones);
  }, []);

  /**
   * 更新默认石头值
   * @param {Number} index 石头索引
   * @param {String} stoneValue 石头值
   */
  function handleDefaultStoneValueChange({ index, stoneValue, stoneType }) {
    const newStones = [...stones];
    newStones[index].info[Enums.InfoKeys.DefaultStoneValue] = stoneValue;
    newStones[index].info[Enums.InfoKeys.DefaultStoneType] = stoneType;
    setStones(newStones);
    onChange?.(newStones);
  }

  /**
   * 切换石头激活状态
   * @param {Number} index 石头索引
   * @param {String} stoneType 石头类型
   * @param {String} stoneKey 石头键名
   * @param {Boolean} checked 是否选中
   */
  function handleStoneActiveChange({ index, stoneType, stoneKey, checked }) {
    const newStones = [...stones];
    newStones[index].data[stoneType].data[stoneKey].active = checked;
    setStones(newStones);
    onChange?.(newStones);
  }

  /**
   * 更新石头材质类型
   * @param {Number} index 石头索引
   * @param {String} stoneType 石头类型
   * @param {String} stoneKey 石头键名
   * @param {String} value 石头材质
   */
  function handleStoneTypeChange(index, stoneType, stoneKey, value) {
    const newStones = [...stones];
    newStones[index].data[stoneType].data[stoneKey].stone_type = value;
    setStones(newStones);
    onChange?.(newStones);
  }

  /**
   * 切换全选状态
   * @param {String} stoneType 石头类型
   * @param {Boolean} checked 是否选中
   */
  function handleSelectAllChange({ index, stoneType, checked }) {
    const newStones = [...stones];
    if (newStones[index].data[stoneType] && newStones[index].data[stoneType].data) {
      Object.keys(newStones[index].data[stoneType].data).forEach((key) => {
        const item = newStones[index].data[stoneType].data[key];
        item.active = checked;
      });
    }

    setStones(newStones);
    onChange?.(newStones);
  }

  /**
   * 更新石头价格
   * @param {Number} index 石头索引
   * @param {String} stoneType 石头类型
   * @param {String} stoneKey 石头键名
   * @param {Number} caratValue 克拉数
   * @param {Number} price 价格
   */
  function handlePriceChange(index, stoneType, stoneKey, caratValue, price) {
    const newStones = [...stones];
    if (!newStones[index].data[stoneType].data[stoneKey].price_list) {
      newStones[index].data[stoneType].data[stoneKey].price_list = {};
    }

    newStones[index].data[stoneType].data[stoneKey].price_list[caratValue] = price;
    setStones(newStones);
    onChange?.(newStones);
  }

  /**
   * 批量设置石头材质
   * @param {Number} index 石头索引
   * @param {String} stoneType 石头类型
   * @param {String} value 石头材质
   */
  function handleBatchStoneTypeChange({ index, stoneType, value }) {
    const newStones = [...stones];
    if (newStones[index].data[stoneType] && newStones[index].data[stoneType].data) {
      Object.keys(newStones[index].data[stoneType].data).forEach((key) => {
        const item = newStones[index].data[stoneType].data[key];
        if (!item.disabled && item.active) {
          item.stone_type = value;
        }
      });
    }
    setStones(newStones);
    onChange?.(newStones);
  }

  /**
   * 获取石头价格
   * @param {Number} index 石头索引
   * @param {String} stoneType 石头类型
   * @param {String} stoneKey 石头键名
   * @param {Number} caratValue 克拉数
   * @returns {Number} 价格
   */
  function getStonePrice({ stoneData, caratValue }) {
    let price = 0;
    const { price_list } = stoneData;

    if (price_list && price_list[caratValue]) {
      price = price_list[caratValue];
    }

    return price;
  }

  /**
   * 获取钻石价格
   * @param {Object} stoneData 钻石数据
   */
  function getDiamondPrice({ stoneData }) {
    if (!stoneData?.options) return 0;

    const priceList = stoneData.options?.map((item) => item?.price);

    if (!priceList?.length) return 0;

    return Math.min(...priceList);
  }

  /**
   * 计算石头价格
   * @param {Number} index 石头索引
   * @param {Object} stonesData 被修改石头数据
   * @returns
   */
  async function calculatePrices({ index, stones, stonesData, customPrice = isCustomPrice }) {
    if (customPrice || !stonesData) return;

    const product_id = form.getFieldValue("product_id") || params?.product_id;
    const materialsData = form.getFieldValue("materials");

    try {
      Helper.pageLoading(true);
      const result = await Fetchers.calculateStonePrice({
        data: { gemstones: stonesData, materials: materialsData, product_id },
      }).then((res) => res.data?.data);
      const { gemstones, materials } = result;

      if (typeof index === "number") {
        if (gemstones) {
          const newStones = [...stones];
          newStones[index] = gemstones?.[0];

          setStones(newStones);
          onChange?.(newStones);
        }
      } else {
        setStones(gemstones);
        onChange?.(gemstones);
      }

      if (materials) {
        // 通过form更新materials字段
        form.setFieldValue("materials", materials);
      }
    } finally {
      Helper.pageLoading(false);
    }
  }

  useEffect(() => {
    function handleCalculateStonePrice() {
      const { stones, calculatePrices } = paramsRef.current;
      const newStones = [...stones];

      calculatePrices({ stonesData: newStones, customPrice: false });
    }
    Utils.addEventListener(CommonEnums.EventName.CalculateStonePrice, handleCalculateStonePrice);

    return function () {
      Utils.removeEventListener(CommonEnums.EventName.CalculateStonePrice, handleCalculateStonePrice);
    };
  }, []);

  return (
    <div className={styles.container}>
      {stones.map((stone, index) => (
        <StoneCard key={index} index={index} stone={stone} onDelete={handleDelete}>
          <Space className={styles.stoneWrapper} direction="vertical">
            <Space>
              <div className={styles.labelWrapper}>
                <div className={styles.label}>石头形状</div>
                <Radio.Group
                  value={stone.info[Enums.InfoKeys.Shape]}
                  options={stoneShapeOptions}
                  onChange={(e) => handleInfoChange(index, Enums.InfoKeys.Shape, e.target.value)}
                ></Radio.Group>
              </div>

              <div className={styles.labelWrapper}>
                <div className={styles.label}>石头数量</div>
                <InputNumber
                  value={stone.info[Enums.InfoKeys.Num]}
                  min={1}
                  precision={0}
                  step={1}
                  size="small"
                  placeholder="石头数量"
                  onChange={(value) => handleInfoChange(index, Enums.InfoKeys.Num, value)}
                />
              </div>

              {/* 没有多选克拉数的业务场景了，统一使用input */}
              <div className={styles.labelWrapper}>
                <div className={styles.label}>克拉数</div>
                <InputNumber
                  value={stone.info[Enums.InfoKeys.TotalCarat]}
                  min={0.001}
                  precision={3}
                  step={0.001}
                  size="small"
                  placeholder="克拉数"
                  onChange={(value) => handleInfoChange(index, Enums.InfoKeys.TotalCarat, value)}
                />
              </div>

              <div className={styles.labelWrapper}>
                <div className={styles.label}>石头信息</div>
                <Input
                  value={stone.info[Enums.InfoKeys.StoneInfo]}
                  size="small"
                  placeholder="石头信息"
                  onChange={(e) => handleInfoChange(index, Enums.InfoKeys.StoneInfo, e.target.value)}
                />
              </div>
            </Space>

            {/* 石头配置表格 */}
            {tables.map((table, tableIndex) => {
              const tableKey = table?.key;

              return (
                <table key={(tableKey, tableIndex)} className={styles.stoneTable}>
                  <thead>
                    <tr>
                      <th>{table?.title}</th>
                      {table?.columns?.map((column) => {
                        return (
                          <th key={column?.dataIndex}>
                            <div className={styles.stoneHeader}>
                              <Radio
                                checked={
                                  stone.info[Enums.InfoKeys.DefaultStoneValue] === column?.dataIndex &&
                                  stone.info[Enums.InfoKeys.DefaultStoneType] === tableKey
                                }
                                onChange={() =>
                                  handleDefaultStoneValueChange({
                                    index,
                                    stoneValue: column?.dataIndex,
                                    stoneType: tableKey,
                                  })
                                }
                              >
                                <img className={styles.stoneImage} src={column?.title} alt={column?.dataIndex} />
                              </Radio>
                            </div>
                          </th>
                        );
                      })}
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      {/* 石头类型列 */}
                      <td>
                        <div className={styles.stoneTdWrapper}>
                          <div className={styles.stoneItemContent}>{stone.info[Enums.InfoKeys.TotalCarat] ?? 0}ct</div>
                          <div className={styles.stoneItemContent}>
                            <Checkbox
                              onChange={(e) =>
                                handleSelectAllChange({
                                  index,
                                  stoneType: tableKey,
                                  checked: e.target.checked,
                                })
                              }
                            >
                              全部启用
                            </Checkbox>
                          </div>
                          {tableKey === "simulated" && (
                            <div className={styles.stoneItemContent}>
                              <Select
                                onChange={(value) => handleBatchStoneTypeChange({ index, stoneType: tableKey, value })}
                                style={{ width: "100%" }}
                                options={stoneTypeOptions}
                                size="small"
                              />
                            </div>
                          )}
                        </div>
                      </td>

                      {/* 石头内容列 */}
                      {table?.columns?.map((column) => {
                        const dataIndex = column?.dataIndex;
                        const stoneData = stone.data[tableKey]?.data[dataIndex];

                        return (
                          <td key={dataIndex}>
                            <div className={styles.stoneTdWrapper}>
                              <div className={styles.stoneItemContent}>
                                {isCustomPrice ? (
                                  <InputNumber
                                    size="small"
                                    min={0}
                                    precision={0}
                                    value={getStonePrice({
                                      stoneData,
                                      caratValue: stone.info[Enums.InfoKeys.TotalCarat],
                                    })}
                                    onChange={(value) =>
                                      handlePriceChange(
                                        index,
                                        tableKey,
                                        dataIndex,
                                        stone.info[Enums.InfoKeys.TotalCarat],
                                        value
                                      )
                                    }
                                    style={{ width: "100%" }}
                                  />
                                ) : (
                                  <span className={styles.priceValue}>
                                    {/* ${getStonePrice({ stoneData, caratValue: stone.info[Enums.InfoKeys.TotalCarat] })} */}
                                    {tableKey === "diamond"
                                      ? `$${getDiamondPrice({ stoneData })}`
                                      : `$${getStonePrice({
                                          stoneData,
                                          caratValue: stone.info[Enums.InfoKeys.TotalCarat],
                                        })}`}
                                  </span>
                                )}
                              </div>

                              <div className={styles.stoneItemContent}>
                                <Checkbox
                                  checked={stoneData?.active}
                                  onChange={(e) =>
                                    handleStoneActiveChange({
                                      index,
                                      stoneType: tableKey,
                                      stoneKey: dataIndex,
                                      checked: e.target.checked,
                                    })
                                  }
                                >
                                  启用
                                </Checkbox>
                              </div>

                              {tableKey === "diamond" && (
                                <StoneAccessories
                                  data={stoneData}
                                  params={params}
                                  info={stone.info}
                                  onChange={(options) =>
                                    handleUpdateStoneAccessories({
                                      index,
                                      options,
                                      stoneType: tableKey,
                                      stoneKey: dataIndex,
                                    })
                                  }
                                />
                              )}

                              {/* 免费石头材质选择 */}
                              {tableKey === "simulated" && (
                                <div className={styles.stoneItemContent}>
                                  <Select
                                    value={stoneData.stone_type}
                                    onChange={(value) => handleStoneTypeChange(index, tableKey, dataIndex, value)}
                                    style={{ width: "100%" }}
                                    options={stoneTypeOptions}
                                    size="small"
                                  />
                                </div>
                              )}
                            </div>
                          </td>
                        );
                      })}
                    </tr>
                  </tbody>
                </table>
              );
            })}
          </Space>
        </StoneCard>
      ))}

      <Button type="dashed" color="default" block onClick={handleAdd}>
        添加石头
      </Button>
    </div>
  );
}

GemstoneConfig.propTypes = {
  value: PropTypes.array,
  onChange: PropTypes.func,
  tables: PropTypes.array,
  isCustomPrice: PropTypes.bool,
};

export default GemstoneConfig;
