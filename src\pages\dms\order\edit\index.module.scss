.container {
  width: 100%;
  height: calc(100vh - 50px);
  display: flex;
  background-color: #f3f3f3;
  gap: 10px;

  &.insideIframe {
    height: 100vh;
  }

  .navBar {
    width: 90px;
    background: #fff;
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-bottom: 1px solid #ccc;

    .navBarItem {
      padding: 6px 0;
      cursor: pointer;
      text-align: center;

      a {
        color: #000;

        &:hover {
          color: #0086f8;
          font-weight: bold;
        }
      }
    }
  }

  .content {
    flex: 1;
    width: 100%;
    height: 100%;
    overflow-y: scroll;

    [class~="ant-collapse"] {
      [class~="ant-collapse-item"] {
        &:not(:last-child) {
          margin-bottom: 10px;
        }

        [class~="ant-collapse-header"] {
          background: #fff;
        }
      }
    }

    .editIcon {
      padding: 2px;
      color: var(--ant-primary-color);
      cursor: pointer;
    }

    .panel {
      background-color: #fff;

      [class~="ant-form-item"] {
        margin-bottom: 0;
      }
    }

    [class~="container"] {
      min-height: auto;
    }
  }

  .actions {
    height: 100%;
    width: 202px;
    overflow: hidden;
    overflow-y: auto;
    padding-bottom: 30px;
    // border-top: 1px solid #ccc;
    // border-right: 1px solid #ccc;
    // border-bottom: 1px solid #ccc;

    .actionsWrapper {
      background-color: #fff;
      margin-bottom: 5px;
      border: 1px solid #ccc;
      border-bottom: none;

      .operation {
        line-height: 34px;
        padding-left: 16px;
        color: #0094ff;
        cursor: pointer;
        margin-top: -1px;
        border-top: 1px solid #ccc;
        border-bottom: 1px solid #ccc;

        &:hover {
          background-color: #dcebfc;
        }
      }
    }

    .tags {
      padding: 10px;
      border: 1px solid #ccc;
      margin-bottom: 5px;
      background-color: #fff;

      .addTags {
        margin-top: 10px;
      }
    }
  }
}
