import { forwardRef, useImperativeHandle, useRef, useState } from "react";
import debounce from "lodash.debounce";
import styles from "./index.module.scss";
import { Input, Empty, Spin } from "antd";
import classNames from "classnames";
import PropTypes from "prop-types";
import Utils from "utils";
import request from "@/fetchers/request";
import useSWR from "swr";

function FilterSelect(props, ref) {
  const { options, value, onClick, searchable, searchApi } = props;
  const [innerOptions, setInnerOptions] = useState(options);
  const [loading, setLoading] = useState(false);
  const selectRef = useRef();

  const { data: defaultOptions } = useSWR([searchApi, value], async () => {
    try {
      if (value && searchApi) {
        setLoading(true);
        const queryValue = value;
        const result = await request(searchApi, {
          method: "GET",
          params: { query: queryValue instanceof Array ? queryValue.join(",") : queryValue },
        }).then((res) => res.data);
        const options = result?.data || [];
        setInnerOptions(options);
        return options;
      }
    } finally {
      setLoading(false);
    }
  });

  const handleInput = debounce(async (e) => {
    const keywords = e.target.value;
    if (keywords) {
      if (searchApi) {
        try {
          setInnerOptions([]);
          setLoading(true);
          const result = await request(searchApi, { method: "GET", params: { query: keywords } }).then(
            (res) => res.data
          );
          setInnerOptions(result?.data || []);
        } catch (e) {
          setInnerOptions([]);
        } finally {
          setLoading(false);
        }
      } else {
        setInnerOptions(options?.filter((item) => Utils.hasKeywords(item.label, keywords)));
      }
    } else {
      setInnerOptions(options);
    }
  }, 300);

  useImperativeHandle(ref, () => {
    return {
      scrollToSelected() {
        selectRef.current
          ?.querySelector(".ant-select-item-option-selected")
          ?.scrollIntoView({ block: "center", inline: "center" });
      },
      getOptions() {
        return innerOptions;
      },
    };
  });

  return (
    <div className={styles.filterSelect}>
      {searchable ? (
        <div style={{ marginBottom: 8 }}>
          <Input onInput={handleInput} />
        </div>
      ) : null}
      <div ref={selectRef} className={styles.select}>
        {loading ? (
          <Spin />
        ) : innerOptions?.length === 0 ? (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        ) : (
          innerOptions?.map((item, index) => {
            const selected = value === item.value;
            return (
              <div
                key={index}
                className={classNames("ant-select-item", "ant-select-item-option", {
                  "ant-select-item-option-selected": selected,
                })}
                onMouseEnter={(e) => {
                  e.currentTarget.classList.add("ant-select-item-option-active");
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.classList.remove("ant-select-item-option-active");
                }}
                onClick={(e) => {
                  onClick?.(e, item);
                }}
                aria-selected={selected}
              >
                {item.label}
              </div>
            );
          })
        )}
      </div>
    </div>
  );
}

FilterSelect = forwardRef(FilterSelect);
FilterSelect.propTypes = {
  options: PropTypes.array,
  value: PropTypes.any,
  onClick: PropTypes.func,
  searchable: PropTypes.bool,
};

export default FilterSelect;
