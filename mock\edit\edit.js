const Api = require("../../src/fetchers/api");

const formItems = [
  {
    component: "Row",
    props: { gutter: [16, 32] },
    children: [
      {
        component: "Col",
        props: { xs: { span: 24 }, lg: { span: 24 } },
        children: [
          {
            component: "Card",
            props: {
              title: "form里的card",
              bordered: true,
              hoverable: true,
              size: "small",
              styles: { header: { backgroundColor: "#343a40", color: "#FFFFFF" } },
              extra: [
                {
                  label: "取消面单",
                  props: {
                    type: "primary",
                    danger: true,
                  },
                  command: {
                    type: "modal",
                    closable: true,
                    title: "Api Components",
                    props: {
                      width: 1000,
                    },
                    footer: [
                      {
                        title: "确定",
                        props: { type: "primary" },
                        command: {
                          type: "submit",
                          id: "form1",
                        },
                      },
                    ],
                    content: {
                      component: "JSONComponents",
                      type: "api",
                      props: {},
                      fetcher: {
                        request: {
                          // url: "https://test-dms-erp6.cnzlerp.com/rest/v1/ticket/ticket/update?sku=101&id=101",
                          url: "http://*************:8082/rest/v1/ticket/ticket/update?sku=101&id=101",
                          data: {},
                        },
                      },
                    },
                  },
                },
                {
                  label: "生成面单",
                  command: {
                    type: "modal",
                    closable: true,
                    title: "Api Components",
                    props: {
                      width: 1000,
                    },
                    footer: [
                      {
                        title: "确定",
                        props: { type: "primary" },
                        command: {
                          type: "submit",
                          id: "form1",
                        },
                      },
                    ],
                    content: {
                      component: "JSONComponents",
                      type: "api",
                      props: {},
                      fetcher: {
                        request: {
                          url: Api.getApiJsonComponents,
                          data: {},
                        },
                      },
                    },
                  },
                },
                {
                  label: "撤销发货",
                  command: {
                    type: "modal",
                    closable: true,
                    title: "Api Components",
                    props: {
                      width: 1000,
                    },
                    footer: [
                      {
                        title: "确定",
                        props: { type: "primary" },
                        command: {
                          type: "submit",
                          id: "form1",
                        },
                      },
                    ],
                    content: {
                      component: "JSONComponents",
                      type: "api",
                      props: {},
                      fetcher: {
                        request: {
                          url: Api.getApiJsonComponents,
                          data: {},
                        },
                      },
                    },
                  },
                },
              ],
            },
            children: [
              {
                key: "input1",
                label: "单行文本框1",
                component: "Input",
                props: { placeholder: "Please input your value" },
                rules: [{ required: true, message: "This is a required field" }],
              },
            ],
          },
        ],
      },
      {
        component: "Col",
        props: { xs: { span: 24 }, lg: { span: 24 } },
        children: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: [1],
              items: [
                {
                  key: 1,
                  label: "form item panel header",
                  actions: [
                    {
                      label: "取消面单",
                      props: {
                        type: "primary",
                        danger: true,
                      },
                      command: {
                        type: "modal",
                        closable: true,
                        title: "Api Components",
                        props: {
                          width: 1000,
                        },
                        footer: [
                          {
                            title: "确定",
                            props: { type: "primary" },
                            command: {
                              type: "submit",
                              id: "form1",
                            },
                          },
                        ],
                        content: {
                          component: "JSONComponents",
                          type: "api",
                          props: {},
                          fetcher: {
                            request: {
                              // url: "https://test-dms-erp6.cnzlerp.com/rest/v1/ticket/ticket/update?sku=101&id=101",
                              url: "http://*************:8082/rest/v1/ticket/ticket/update?sku=101&id=101",
                              data: {},
                            },
                          },
                        },
                      },
                    },
                    {
                      label: "生成面单",
                      command: {
                        type: "modal",
                        closable: true,
                        title: "Api Components",
                        props: {
                          width: 1000,
                        },
                        footer: [
                          {
                            title: "确定",
                            props: { type: "primary" },
                            command: {
                              type: "submit",
                              id: "form1",
                            },
                          },
                        ],
                        content: {
                          component: "JSONComponents",
                          type: "api",
                          props: {},
                          fetcher: {
                            request: {
                              url: Api.getApiJsonComponents,
                              data: {},
                            },
                          },
                        },
                      },
                    },
                    {
                      label: "撤销发货",
                      command: {
                        type: "modal",
                        closable: true,
                        title: "Api Components",
                        props: {
                          width: 1000,
                        },
                        footer: [
                          {
                            title: "确定",
                            props: { type: "primary" },
                            command: {
                              type: "submit",
                              id: "form1",
                            },
                          },
                        ],
                        content: {
                          component: "JSONComponents",
                          type: "api",
                          props: {},
                          fetcher: {
                            request: {
                              url: Api.getApiJsonComponents,
                              data: {},
                            },
                          },
                        },
                      },
                    },
                  ],
                  children: [
                    {
                      key: "input4",
                      label: "单行文本框4",
                      component: "Input",
                      props: { placeholder: "Please input your value" },
                      rules: [{ required: true, message: "This is a required field" }],
                    },
                    {
                      key: "input5",
                      label: "单行文本框5",
                      component: "Select",
                      props: {
                        showSearch: true,
                        options: [
                          { value: "1", label: "选项1" },
                          { value: "2", label: "选项2" },
                        ],
                      },
                      rules: [{ required: true, message: "This is a required field" }],
                    },
                  ],
                },
              ],
            },
          },
          {
            component: "Card",
            props: { title: "Card2", bordered: true },
            children: [
              {
                key: "input2",
                label: "单行文本框2",
                component: "Input",
                props: { placeholder: "Please input your value" },
                rules: [{ required: true, message: "This is a required field" }],
              },
            ],
          },
        ],
      },
    ],
  },
  {
    key: "edit_table",
    label: "可编辑表格",
    component: "EditTable",
    props: {
      columns: [
        {
          dataIndex: "image",
          title: "图片",
          width: 100,
          ellipsis: true,
          valueType: "image",
          image: { width: 100 },
        },
        {
          dataIndex: "image_list",
          title: "上传图片",
          width: 100,
          ellipsis: true,
          editable: {
            component: "ImageUpload",
            props: { listType: "picture-card", action: Api.uploadFile, data: { disk: "s3-static" }, maxCount: 1 },
          },
        },
        {
          dataIndex: "sku",
          title: "SKU",
          width: 100,
          ellipsis: true,
          editable: {
            type: "Input",
            props: {
              placeholder: "请输入",
            },
            rules: [{ required: true, message: "SKU必填！" }],
          },
        },
        {
          dataIndex: "custom_props",
          title: "主属性",
          ellipsis: true,
          width: 100,
          editable: {
            type: "Input",
            props: {
              placeholder: "请输入",
            },
          },
        },
        {
          dataIndex: "num",
          title: "数量",
          width: 120,
          editable: {
            component: "InputNumber", // InputNumber/Input
            props: {},
          },
        },
        {
          dataIndex: "num1",
          title: "数量1",
          width: 120,
          editable: {
            component: "InputNumber", // InputNumber/Input
            props: {},
          },
        },
        {
          dataIndex: "type",
          title: "类型",
          width: 120,
          editable: {
            component: "Select",
            props: {
              showSearch: true,
              optionFilterProp: "label", // 搜索时匹配的属性
              options: [
                { label: "英语", value: "en" },
                { label: "日语", value: "jp" },
                { label: "法语", value: "fr" },
                { label: "德语", value: "de" },
                { label: "意大利语", value: "it" },
              ],
            },
          },
        },
        {
          dataIndex: "type_remote",
          title: "类型remote",
          width: 120,
          editable: {
            component: "Select",
            // 支持远程搜索
            searchApi: Api.searchProductSpu,
          },
        },
      ],
      rowSelection: true,
      isShowAddRow: true,
      isShowSetDefault: true,
      defaultNewRow: {
        // 添加行默认值配置
        sku: "默认SKU",
        num: 1,
        type: "en",
        type_remote: "DRH0130",
        custom_props: "默认属性",
      },
    },
  },
  {
    key: "text",
    label: "text组件",
    component: "Text",
    props: {},
    content: "text组件内容",
  },
  {
    key: "input3",
    label: "单行文本框3",
    component: "Input",
    props: { placeholder: "Please input your value" },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "inputAddon",
    label: "单行文本框（Addon）",
    component: "Input",
    props: { placeholder: "Please input your value", addonBefore: "https://", addonAfter: ".com" },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "inputNumber",
    label: "数字输入框",
    component: "InputNumber",
    props: { addonBefore: "+", addonAfter: "$", min: 1 },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "transfer",
    label: "穿梭框",
    component: "Transfer",
    props: {
      dataSource: [
        {
          key: "1",
          title: "content1",
        },
        {
          key: "2",
          title: "content2",
        },
        {
          key: "3",
          title: "content3",
        },
        {
          key: "4",
          title: "content4",
        },
        {
          key: "5",
          title: "content5",
        },
        {
          key: "6",
          title: "content6",
        },
        {
          key: "7",
          title: "content7",
        },
        {
          key: "8",
          title: "content8",
        },
      ],
      titles: ["source", "target"],
      // targetKeys: ["1", "2", "3", "4"],
      showSearch: true,
      listStyle: {
        width: 250,
        height: 300,
      },
    },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "cascader",
    label: "级联选择",
    component: "Cascader",
    props: {
      placeholder: "请选择",
      options: [
        {
          label: "陕西省",
          value: "shanxi",
          children: [
            {
              label: "西安市",
              value: "xian",
              children: [
                {
                  label: "雁塔区",
                  value: "yanta",
                },
                {
                  label: "未央区",
                  value: "weiyang",
                },
                {
                  label: "灞桥区",
                  value: "baqiao",
                },
                {
                  label: "莲湖区",
                  value: "lianhu",
                },
                {
                  label: "碑林区",
                  value: "beilin",
                },
              ],
            },
          ],
        },
        {
          label: "上海市",
          value: "shanghai",
          children: [
            {
              label: "静安区",
              value: "jingan",
            },
            {
              label: "徐汇区",
              value: "xuhui",
            },
            {
              label: "浦东新区",
              value: "pudong",
            },
            {
              label: "普陀区",
              value: "putuo",
            },
            {
              label: "虹口区",
              value: "hongkou",
            },
          ],
        },
      ],
    },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "datePicker",
    label: "日期选择",
    component: "DatePicker",
    props: {},
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "rangePicker",
    label: "日期范围选择",
    component: "RangePicker",
    props: {},
    rules: [{ required: true, message: "This is a required field", validatorName: "rangePickerValidator" }],
  },
  {
    key: "textarea",
    label: "多行文本框",
    component: "Textarea",
    props: { rows: 3, disabled: false },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "richTextEditor",
    label: "富文本编辑器",
    component: "RichTextEditor",
    props: {
      minHeight: 300,
      placeholder: "Enter your text",
      disabled: false,
      uploadConfig: { uploadApi: Api.uploadFile, data: { disk: "s3-static" } },
    },
    formItemProps: {
      style: { marginBottom: 0 },
    },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "follow",
    label: "",
    component: "CheckboxSingle",
    props: {
      children: "跟随标准类目",
    },
    // propsWhen: {
    //   richTextEditor: {
    //     true: { disabled: true },
    //     false: { disabled: false },
    //   },
    //   input3: {
    //     true: {
    //       style: {
    //         backgroundColor: "red",
    //       },
    //     },
    //   },
    // },
    setPropsWhen: {
      richTextEditor: [
        {
          value: true,
          props: { disabled: true },
        },
        {
          value: false,
          props: { disabled: false },
        },
      ],
      input3: [
        {
          value: true,
          props: {
            style: {
              backgroundColor: "red",
            },
          },
        },
      ],
    },
  },
  {
    key: "jsonEditor",
    label: "JSON编辑器",
    component: "JSONEditor",
    props: { disabled: false },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "select",
    label: "下拉框",
    component: "Select",
    props: {
      options: [
        { label: "选项1", value: "1" },
        { label: "选项2", value: "2" },
        { label: "选项3", value: "3" },
        { label: "选项4", value: "4" },
        { label: "选项5", value: "5" },
      ],
    },
    showFieldsWhen: {
      jsonEditor: ["1", "2"],
      selectSearchRemote: ["2", "3"],
    },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "selectMultiple",
    label: "下拉框多选",
    component: "Select",
    props: {
      mode: "multiple",
      options: [
        {
          value: "jack",
          label: "杰克",
        },
        {
          value: "lucy",
          label: "路西",
        },
        {
          value: "tom",
          label: "汤姆",
        },
        { label: "选项1", value: "1" },
        { label: "选项2", value: "2" },
        { label: "选项3", value: "3" },
        { label: "选项4", value: "4" },
        { label: "选项5", value: "5" },
      ],
      addOptions: {
        command: {
          type: "message",
          config: {
            type: "success",
            content: "提示文案",
            duration: 3,
          },
        },
      },
      // optionsApi: "/rest/v1/global/taxonomy/search?identifier=defective_tag",
    },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "selectSearchRemote",
    label: "下拉框服务端搜索",
    component: "Select",
    props: {
      mode: "multiple",
      options: [],
      addOptions: {
        command: {
          type: "message",
          config: {
            type: "success",
            content: "提示文案",
            duration: 3,
          },
        },
      },
    },
    searchApi: Api.searchProductSpu,
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "selectCascader",
    label: "下拉框级联1",
    component: "Select",
    props: {
      options: [
        { label: "选项1", value: "xuanxiang1" },
        { label: "选项2", value: "2" },
        { label: "选项3", value: "3" },
      ],
    },
    cascader: {
      name: "selectCascaded",
      searchApi: Api.searchProductSpu,
    },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "selectCascader2",
    label: "下拉框级联2",
    component: "Select",
    props: {
      options: [
        { label: "选项4", value: "4" },
        { label: "选项5", value: "5" },
      ],
    },
    cascader: {
      name: "selectCascaded",
      searchApi: Api.searchProductSpu,
    },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "selectCascaded",
    label: "下拉框联动",
    component: "Select",
    props: {
      options: [
        { label: "选项1", value: "1" },
        { label: "选项2", value: "2" },
      ],
    },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "treeSelect",
    label: "树形选择框",
    component: "TreeSelect",
    props: {
      treeCheckable: true,
      showSearch: true,
      allowClear: true,
      multiple: true,
      treeDefaultExpandAll: true,
      treeData: [
        {
          value: "parent-1",
          title: "parent 1",
          children: [
            {
              value: "parent-1-0",
              title: "parent 1-0",
              children: [
                {
                  value: "leaf-1",
                  title: "leaf1",
                },
                {
                  value: "leaf-2",
                  title: "leaf2",
                },
              ],
            },
            {
              value: "parent 1-1",
              title: "parent 11",
              children: [
                {
                  value: "leaf-3",
                  title: "leaf3",
                },
              ],
            },
          ],
        },
      ],
    },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "radio",
    label: "单选框",
    component: "Radio",
    props: {
      options: [
        { label: "选项1", value: 1 },
        { label: "选项2", value: 2 },
        { label: "选项3", value: 3 },
        { label: "选项4", value: 4 },
        { label: "选项5", value: 5 },
      ],
    },
    showFieldsWhen: {
      tree: ["3"],
    },
    setPropsWhen: {
      // input3: {
      //   1: { disabled: true, placeholder: "disabled" },
      //   2: { readOnly: true, placeholder: "readOnly" },
      //   3: { placeholder: "请输入", disabled: false },
      // },
      input3: [
        {
          value: 1,
          props: { disabled: true, placeholder: "disabled" },
        },
        {
          value: 2,
          props: { readOnly: true, placeholder: "readOnly" },
        },
        {
          value: 3,
          props: { placeholder: "请输入", disabled: false },
        },
      ],
    },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "checkboxSingle",
    label: "复选框",
    component: "CheckboxSingle",
    props: {},
  },
  {
    key: "checkbox",
    label: "复选框组",
    component: "Checkbox",
    props: {
      options: [
        { label: "选项1", value: "1", disabled: true },
        { label: "选项2", value: "2" },
        { label: "选项3", value: "3" },
        { label: "选项4", value: "4" },
        { label: "选项5", value: "5" },
      ],
    },
    showFieldsWhen: {
      tree: ["1", "2"],
    },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "tree",
    label: "树组件",
    component: "Tree",
    props: {
      checkable: true,
      selectable: true,
      defaultExpandedCheckedParents: true,
      defaultExpandAll: false,
      treeData: [
        {
          key: "1",
          title: "节点1",
          children: [
            { key: "1-1", title: "节点1-1" },
            {
              key: "1-2",
              title: "节点1-2",
              children: [
                { key: "1-2-1", title: "节点1-2-1" },
                { key: "1-2-2", title: "节点1-2-2" },
              ],
            },
            { key: "1-3", title: "节点1-3" },
          ],
        },
        {
          key: "2",
          title: "节点2",
          children: [
            { key: "2-1", title: "节点2-1" },
            { key: "2-2", title: "节点2-2" },
            { key: "2-3", title: "节点2-3" },
          ],
        },
        {
          key: "3",
          title: "节点3",
          children: [
            { key: "3-1", title: "节点3-1" },
            { key: "3-2", title: "节点3-2" },
            { key: "3-3", title: "节点3-3" },
          ],
        },
      ],
    },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "switch",
    label: "开关",
    component: "Switch",
    showFieldsWhen: {
      tree: [true],
    },
  },
  {
    key: "spaceWrapper",
    component: "SpaceWrapper",
    props: {
      size: "small",
    },
    children: [
      {
        key: "spaceWrapper1",
        component: "Input",
        props: { placeholder: "Please input your value" },
        rules: [{ required: true, message: "This is a required field" }],
      },
      {
        key: "spaceWrapper2",
        component: "Select",
        props: {
          style: { width: 200 },
          showSearch: true,
          options: [
            { value: "1", label: "选项1" },
            { value: "2", label: "选项2" },
          ],
        },
        rules: [{ required: true, message: "This is a required field" }],
      },
      {
        key: "spaceWrapper3",
        component: "Select",
        props: {
          style: { width: 200 },
          showSearch: true,
          options: [
            { value: "1", label: "选项1" },
            { value: "2", label: "选项2" },
          ],
        },
        rules: [{ required: true, message: "This is a required field" }],
      },
    ],
  },
  {
    key: "imageUpload",
    label: "上传图片",
    component: "ImageUpload",
    props: { listType: "picture-card", action: Api.uploadFile, data: { disk: "s3-static" }, multiple: true },
    rules: [{ required: true, message: "This is a required field" }],
  },
  {
    key: "fileUpload",
    label: "上传文件",
    component: "FileUpload",
    props: {
      action: "http://*************:8081/rest/v1/upload/file",
      data: { disk: "s3-static" },
      multiple: true,
      accept: ".jpg, .jpeg, .png, .mp4, .pdf, .js",
      maxCount: 5,
      maxSize: "5MB", // 单位为MB
      showUploadList: {
        showDownloadIcon: true,
      },
    },
  },
  {
    key: "cameraPhotoUpload",
    label: "拍照上传",
    component: "CameraPhotoUpload",
    props: {
      width: 200,
      height: 200,
      screenshot: { width: 1000, height: 1000 },
      action: Api.uploadFile,
      autoUpload: false,
      data: { disk: "s3-static" },
    },
    rules: [{ required: true, message: "This is a required field" }],
  },
];

const initialValues = {
  input1: "input1",
  input4: "input4",
  input5: "1",
  input2: "input2",
  input3: "input3",
  input: "123456",
  selectCascader: "1",
  selectCascader2: "4",
  selectCascaded: "1",
  treeSelect: ["leaf-1"],
  inputAddon: "jeuliaoutlet",
  inputNumber: 1,
  cascader: ["shanxi", "xian", "yanta"],
  datePicker: "2023-01-01",
  rangePicker: ["2023-01-01", "2023-01-31"],
  textarea: "123\n456\n789",
  select: "3",
  selectMultiple: ["2", "3"],
  selectSearchRemote: ["DRN0392", "DRN0720"],
  radio: 3,
  checkbox: ["1", "2", "3"],
  tree: ["1-2-2"],
  switch: true,
  richTextEditor: `<p>Hello, RichTextEditor!</p><p>这是富文本框的默认值</p>`,
  follow: true,
  transfer: ["1", "2", "3", "4"],
  jsonEditor: JSON.stringify({
    label: "JSON编辑器",
    component: "JSONEditor",
    props: {},
    rules: [{ required: true, message: "This is a required field" }],
  }),
  imageUpload: [...Array(5).fill(null)].map((a, i) => {
    const id = (i + 1).toString();
    return {
      uid: id,
      fid: id,
      name: "photo",
      status: "done",
      url: "https://res2022.jeulia.com/product/2/1/1000x1000/62fde0f091112.jpg",
    };
  }),
  fileUpload: [...Array(2).fill(null)].map((a, i) => {
    const id = (i + 1).toString();
    return {
      uid: id,
      fid: id,
      name: `photo${id}.jpg`,
      status: "done",
      url: "https://res2022.jeulia.com/product/2/1/1000x1000/62fde0f091112.jpg",
    };
  }),
  cameraPhotoUpload: [...Array(1).fill(null)].map((a, i) => {
    const id = (i + 1).toString();
    return {
      uid: id,
      fid: id,
      name: "photo",
      status: "done",
      url: "https://res2022.jeulia.com/product/2/1/1000x1000/62fde0f091112.jpg",
    };
  }),
  edit_table: Array(2)
    .fill(null)
    .map((_, i) => ({
      image: "https://img.yzcdn.cn/vant/leaf.jpg",
      // image_list: [
      //   {
      //     uid: "1",
      //     fid: "1",
      //     name: "photo",
      //     status: "done",
      //     url: "https://res2022.jeulia.com/product/2/1/1000x1000/62fde0f091112.jpg",
      //   },
      // ],
      sku: `SKU${i + 1}`,
      num: i + 1,
      type: "jp",
      type_remote: "DRH0130",
    })),
};

module.exports = {
  // 测试树编辑接口，页面url为/edit/tree
  "GET /rest/v1/edit/tree": (req, res) => {
    res.status(200).json({
      status: "00",
      success: true,
      data: {
        breadcrumbs: [
          {
            title: "类目管理",
            url: "/",
          },
          {
            title: "类目编辑",
            url: "/",
          },
        ],
        actions: [
          {
            title: "确定",
            props: {
              type: "primary",
            },
            command: {
              type: "submit",
              id: "edit-form",
            },
          },
        ],
        content: {
          component: "JSONComponents",
          props: {},
          type: "json",
          children: [
            {
              component: "Form",
              type: "json",
              formItems: [
                {
                  key: "tree",
                  label: "树组件",
                  component: "Tree",
                  props: {
                    checkable: true,
                    selectable: false,
                    defaultExpandedCheckedParents: true,
                    defaultExpandAll: false,
                    draggable: true,
                    treeData: [
                      {
                        key: "1",
                        title: "节点1",
                        children: [
                          {
                            key: "1-1",
                            title: "节点1-1",
                          },
                          {
                            key: "1-2",
                            title: "节点1-2",
                            children: [
                              {
                                key: "1-2-1",
                                title: "节点1-2-1",
                              },
                              {
                                key: "1-2-2",
                                title: "节点1-2-2",
                              },
                            ],
                          },
                          {
                            key: "1-3",
                            title: "节点1-3",
                          },
                        ],
                      },
                      {
                        key: "2",
                        title: "节点2",
                        children: [
                          {
                            key: "2-1",
                            title: "节点2-1",
                          },
                          {
                            key: "2-2",
                            title: "节点2-2",
                          },
                          {
                            key: "2-3",
                            title: "节点2-3",
                          },
                        ],
                      },
                      {
                        key: "3",
                        title: "节点3",
                        children: [
                          {
                            key: "3-1",
                            title: "节点3-1",
                          },
                          {
                            key: "3-2",
                            title: "节点3-2",
                          },
                          {
                            key: "3-3",
                            title: "节点3-3",
                          },
                        ],
                      },
                    ],
                  },
                  actions: [
                    {
                      title: "新增",
                      key: "add",
                      props: {},
                      command: {
                        type: "modal",
                        closable: true,
                        title: "新增子节点",
                        props: {
                          width: 800,
                          closable: false,
                          maskClosable: true,
                        },
                        footer: [
                          {
                            title: "保存",
                            props: {
                              type: "primary",
                            },
                            command: {
                              type: "submit",
                              id: "form",
                            },
                          },
                        ],
                        content: {
                          component: "JSONComponents",
                          type: "api",
                          props: {},
                          fetcher: {
                            request: {
                              url: Api.getApiJsonComponents,
                              data: {},
                              method: "POST",
                            },
                          },
                        },
                      },
                    },
                    {
                      title: "编辑",
                      key: "edit",
                      props: {},
                      command: {
                        type: "modal",
                        closable: true,
                        title: "编辑节点",
                        props: {
                          width: 800,
                        },
                        footer: [
                          {
                            title: "保存",
                            props: {
                              type: "primary",
                            },
                            command: {
                              type: "submit",
                              id: "form",
                            },
                          },
                        ],
                        content: {
                          component: "JSONComponents",
                          type: "api",
                          props: {},
                          fetcher: {
                            request: {
                              url: Api.getApiJsonComponents,
                              data: {},
                              method: "PUT",
                            },
                          },
                        },
                      },
                    },
                    {
                      title: "删除",
                      key: "delete",
                      props: {
                        danger: true,
                      },
                      command: {
                        type: "request",
                        request: {
                          url: Api.order,
                          data: {
                            action: "batchDelete",
                          },
                        },
                        confirm: "确定删除吗？",
                      },
                    },
                  ],
                  submit: {
                    request: {
                      url: Api.customer,
                      data: {},
                    },
                  },
                  rules: [
                    {
                      required: true,
                      message: "This is a required field",
                    },
                  ],
                },
              ],
              props: {
                id: "edit-form",
                initialValues: {
                  tree: ["1-2-2", "1-2-1", "1-2"],
                },
                layout: "vertical",
              },
              submit: {
                request: {
                  url: Api.customer,
                  data: {
                    default: "test",
                  },
                },
              },
            },
          ],
        },
      },
    });
  },

  "GET /rest/v1/edit/:url_key": (req, res) => {
    res.status(200).json({
      status: "00",
      success: true,
      data: {
        breadcrumbs: [
          { title: "商品管理", url: "/" },
          { title: "商品编辑", url: "/" },
        ],
        actions: [
          {
            title: "确定",
            props: {
              type: "primary",
            },
            command: {
              type: "submit",
              id: "form",
              data: {
                action: "save",
              },
            },
          },
          {
            title: "保存并提交",
            props: {
              type: "primary",
            },
            command: {
              type: "submit",
              id: "form",
              data: {
                action: "saveAndSubmit",
                status: "submitted",
              },
            },
          },
        ],
        content: {
          component: "JSONComponents",
          props: {},
          type: "json",
          children: [
            {
              component: "LangTabs",
              props: {},
            },
            {
              component: "Card",
              props: {
                title: "JSONComponents 中的 Card",
                extra: [
                  {
                    label: "取消面单",
                    props: {
                      type: "primary",
                      danger: true,
                    },
                    command: {
                      type: "modal",
                      closable: true,
                      title: "Api Components",
                      props: {
                        width: 1000,
                      },
                      footer: [
                        {
                          title: "确定",
                          props: { type: "primary" },
                          command: {
                            type: "submit",
                            id: "form1",
                          },
                        },
                      ],
                      content: {
                        component: "JSONComponents",
                        type: "api",
                        props: {},
                        fetcher: {
                          request: {
                            // url: "https://test-dms-erp6.cnzlerp.com/rest/v1/ticket/ticket/update?sku=101&id=101",
                            url: "http://*************:8082/rest/v1/ticket/ticket/update?sku=101&id=101",
                            data: {},
                          },
                        },
                      },
                    },
                  },
                  {
                    label: "生成面单",
                    command: {
                      type: "modal",
                      closable: true,
                      title: "Api Components",
                      props: {
                        width: 1000,
                      },
                      footer: [
                        {
                          title: "确定",
                          props: { type: "primary" },
                          command: {
                            type: "submit",
                            id: "form1",
                          },
                        },
                      ],
                      content: {
                        component: "JSONComponents",
                        type: "api",
                        props: {},
                        fetcher: {
                          request: {
                            url: Api.getApiJsonComponents,
                            data: {},
                          },
                        },
                      },
                    },
                  },
                  {
                    label: "撤销发货",
                    command: {
                      type: "modal",
                      closable: true,
                      title: "Api Components",
                      props: {
                        width: 1000,
                      },
                      footer: [
                        {
                          title: "确定",
                          props: { type: "primary" },
                          command: {
                            type: "submit",
                            id: "form1",
                          },
                        },
                      ],
                      content: {
                        component: "JSONComponents",
                        type: "api",
                        props: {},
                        fetcher: {
                          request: {
                            url: Api.getApiJsonComponents,
                            data: {},
                          },
                        },
                      },
                    },
                  },
                ],
              },
            },
            {
              component: "Form",
              type: "json",
              formItems,
              props: {
                id: "form",
                // initialValues: { ...initialValues, input: 1 + 10 * Math.random() },
                layout: "vertical",
              },
              submit: {
                request: {
                  url: "http://*************:8081/rest/v1/customer",
                  data: {
                    default: "test",
                  },
                },
              },
            },
          ],
        },
        dynamicDataApi: "http://*************:8081/rest/v1/edit-order/dynamic-data",
        // 旧格式
        // form: {
        //   formItems,
        //   props: {
        //     initialValues,
        //     layout: "vertical",
        //   },
        // },
      },
    });
  },

  "POST /rest/v1/edit/:url_key": (req, res) => {
    res.status(200).json({
      status: "00",
      success: true,
      data: {},
      message: "操作成功",
    });
  },
};
