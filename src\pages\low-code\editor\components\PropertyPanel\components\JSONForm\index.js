import { useRef, forwardRef, useImperative<PERSON>andle } from "react";

import CommonJSONForm from "components/common/JSONForm";
import RowColBuilder from "./components/RowColBuilder";
import ColumnEditor from "components/business/ColumnEditor";
import JSONEditorToParse from "./components/JSONEditorToParse";

const createExtendComponents = (formRef, componentKey, props) => ({
  // [Enums.ConfigComponents.DictionarySelect]: ({ item }) => <DictionarySelect {...item?.props} {...props} />,
  RowColBuilder: ({ item }) => <RowColBuilder {...item?.props} {...props} />,
  ColumnEditor: ({ item }) => <ColumnEditor {...item?.props} {...props} />,
  JSONEditorToParse: ({ item }) => <JSONEditorToParse {...item?.props} {...props} />,
});

function JSONForm(props, ref) {
  const { data, componentKey, onChange, flattenedComponents } = props;
  const formRef = useRef(null);
  const extendComponents = createExtendComponents(formRef, componentKey, flattenedComponents);

  const normalizePath = (path) => {
    if (Array.isArray(path)) {
      return path;
    }
    return path.includes(",") ? path.split(",") : path.split(".");
  };

  const hasPath = (obj, path) => {
    if (!obj || !path) return false;

    const keys = normalizePath(path);
    let current = obj;

    for (let key of keys) {
      if (current === null || current === undefined || !Object.prototype.hasOwnProperty.call(current, key.trim())) {
        return false;
      }
      current = current[key.trim()];
    }

    return true;
  };

  const getValue = (obj, path, defaultValue = undefined) => {
    if (!obj || !path) return defaultValue;

    const keys = normalizePath(path);
    let current = obj;

    for (let key of keys) {
      if (current === null || current === undefined || !Object.prototype.hasOwnProperty.call(current, key.trim())) {
        return defaultValue;
      }
      current = current[key.trim()];
    }

    return current === undefined ? defaultValue : current;
  };

  const handleValuesChange = (changedValues, allValues) => {
    if (data?.props?.watch) {
      Object.entries(data.props.watch).forEach(([fieldPath, config]) => {
        const pathToCheck = Array.isArray(fieldPath) ? fieldPath.join(",") : fieldPath;

        if (hasPath(changedValues, pathToCheck)) {
          const value = getValue(changedValues, pathToCheck);
          if (config.type === "sync" && config.target) {
            formRef.current.setFieldValue(config.target, value);
          }
        }
      });
    }
    // if (data?.dataTransform) {
    //   console.log(data);
    // }
    onChange?.(changedValues, allValues);
  };

  useImperativeHandle(ref, () => {
    return formRef.current;
  });

  return (
    <CommonJSONForm ref={formRef} data={data} extendComponents={extendComponents} onValuesChange={handleValuesChange} />
  );
}

JSONForm = forwardRef(JSONForm);

export default JSONForm;
