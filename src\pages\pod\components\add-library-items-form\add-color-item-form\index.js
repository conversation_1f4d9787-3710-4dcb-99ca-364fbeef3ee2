import { Button, ColorPicker, Input, InputNumber } from "antd";
import { ControlWrapper, Form, FormControl, useForm } from "@/components/react-form-x";
import FormControlLabel from "../../../components/form-control-label";
import { useRef, useState } from "react";
import Fetchers from "@/fetchers";

function AddColorItemForm(props) {
  const { libraryId, onSuccess } = props;
  const formRef = useRef();
  const [submitLoading, setSubmitLoading] = useState(false);

  return (
    <Form
      ref={formRef}
      style={{ display: "flex", flexDirection: "column", gap: 22 }}
      onSubmit={(event, values) => {
        setSubmitLoading(true);
        Fetchers.addPodLibraryItems({ library_id: libraryId, items: [values] })
          .then(() => {
            onSuccess?.();
          })
          .finally(() => {
            setSubmitLoading(false);
          });
      }}
    >
      <div>
        <FormControl
          name="color"
          rule={{ required: true }}
          render={(props, { rule }) => (
            <ControlWrapper
              {...props}
              render={(props) => {
                const { name, value, onChange } = props;
                return (
                  <FormControlLabel label="Color:" required={rule.required}>
                    <ColorPicker
                      value={value}
                      onChange={(value) => {
                        onChange(null, { [name]: value.toRgbString() });
                      }}
                      showText={(color) => color.toRgbString()}
                      style={{ width: `100%`, justifyContent: "start" }}
                    ></ColorPicker>
                  </FormControlLabel>
                );
              }}
            ></ControlWrapper>
          )}
        ></FormControl>
      </div>
      <div>
        <FormControl
          name="option_id"
          rule={{ required: true }}
          render={(props, { rule }) => (
            <ControlWrapper
              {...props}
              render={(props) => {
                const { name, value, onChange } = props;
                return (
                  <FormControlLabel label="Option Id:" required={rule.required}>
                    <InputNumber
                      style={{ width: `100%` }}
                      value={value}
                      onChange={(nextValue) => {
                        onChange(null, { [name]: nextValue });
                      }}
                    ></InputNumber>
                  </FormControlLabel>
                );
              }}
            ></ControlWrapper>
          )}
        ></FormControl>
      </div>
      <div>
        <FormControl
          name="option_name"
          rule={{ required: true }}
          render={(props, { rule }) => (
            <ControlWrapper
              {...props}
              render={(props) => (
                <FormControlLabel label="Option Name:" required={rule.required}>
                  <Input {...props}></Input>
                </FormControlLabel>
              )}
            ></ControlWrapper>
          )}
        ></FormControl>
      </div>
      <div>
        <Button type="primary" htmlType="submit" loading={submitLoading}>
          Submit
        </Button>
      </div>
    </Form>
  );
}

export default AddColorItemForm;
