import { lazy, Suspense } from "react";
import { Spin } from "antd";
function LazyLoad({ path }) {
  const Component = lazy(() => import(`${path}`));

  const fallback = (
    <div className="root-fallback">
      <div className="root-fallback-wrapper">
        <Spin></Spin>
      </div>
    </div>
  );

  return (
    <Suspense fallback={fallback}>
      <Component />
    </Suspense>
  );
}

export default LazyLoad;
