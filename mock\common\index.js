const common = {
  uniqueId() {
    return Math.random().toString().substring(2);
  },

  sleep(milliseconds) {
    return new Promise((resolve) => {
      setTimeout(resolve, milliseconds);
    });
  },

  toNumber(str, defaultValue = null) {
    const result = +str;
    return isNaN(result) ? defaultValue : result;
  },

  JSON: {
    parse(str, defaultValue = {}) {
      try {
        return JSON.parse(str) || defaultValue;
      } catch (e) {
        return defaultValue;
      }
    },
    stringify(data, defaultValue = "") {
      try {
        return JSON.stringify(data);
      } catch (e) {
        return defaultValue;
      }
    },
  },

  matchRoute({ req, routes }) {
    const key = Object.keys(routes).find((key) => {
      const arr = key.split(" ");
      const method = arr[0];
      const pathname = arr[1] || "";
      return this.matchPathname({ url: req.url, pathname }) && req.method.toUpperCase() === method.toUpperCase();
    });
    const arr = (key || "").split(" ");
    return { key: key, method: arr[0], pathname: arr[1], handler: routes[key] };
  },

  matchPathname({ url, pathname }) {
    if (pathname.indexOf(":") > -1) {
      const isSameLevel = url.split("/").length === pathname.split("/").length;
      return isSameLevel && new RegExp(pathname.replace(/:[^/]*/g, "[^/]*"), "i").test(url);
    } else {
      return new URL(`http://localhost${url}`).pathname === pathname;
    }
  },

  getPathParams({ req, route }) {
    const params = {};
    const keys = [];
    const str = route.pathname.replace(/:([^/]*)/g, (str, p1) => {
      keys.push(p1);
      return "(.*)";
    });
    const values = new RegExp(str, "ig").exec(req.url);
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      params[key] = values[i + 1];
    }
    return params;
  },
};

module.exports = common;
