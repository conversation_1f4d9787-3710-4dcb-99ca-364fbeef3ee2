import styles from "./index.module.scss";
import { useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import { Button, Form, Input, Divider } from "antd";

import Fetchers from "fetchers";
import Enums from "enums";
import Helper from "helpers";
import EnvHelper from "helpers/env-helper";

import Utils from "utils";

const Apis = {
  [Enums.LoginType.Account]: Fetchers.login,
  [Enums.LoginType.Code]: Fetchers.getToken,
};

function Login() {
  const location = useLocation();
  const params = Utils.toQueryParams(window.location.href);
  const { redirect } = params;
  const redirectUrl = new URL(redirect || window.location.origin);
  const [submitLoading, setSubmitLoading] = useState(false);
  const paramsRef = useRef();

  paramsRef.current = {
    ...paramsRef.current,
    handleLogin,
  };

  // 飞书登录url
  const feishuLoginUrl = `https://accounts.feishu.cn/open-apis/authen/v1/authorize?client_id=cli_a7e101f1fc315013&redirect_uri=${EnvHelper.RedirectOrigin}/quick-login-redirect&state=${redirectUrl}&scope=contact:contact.base:readonly`;

  async function handleLogin(values, type = "account") {
    try {
      setSubmitLoading(true);
      const token = await Apis[type](values).then((res) => res?.data?.data || null);
      if (token) {
        if (redirect) {
          window.location.href = `${redirectUrl.origin}/passport${location.search}&token=${token}`;
        } else {
          Helper.setCookies({ [Enums.CookieName.Token]: token });
          window.location.href = "/";
        }
      }
    } finally {
      setSubmitLoading(false);
    }
  }

  return (
    <div className={styles.login}>
      <div className={styles.wrapper}>
        <h1 className={styles.header}>ERP 6.0</h1>
        <div className={styles.card}>
          <div className={styles.formTitle}>账号登录</div>
          <Divider style={{ margin: "0px 0 10px" }} />
          <Form initialValues={{ email: "", password: "" }} onFinish={handleLogin}>
            <Form.Item
              name="email"
              rules={[
                {
                  required: true,
                  message: "用户名是必填的！",
                },
                // { type: "email", message: "Email格式不正确！" },
              ]}
            >
              <Input placeholder="请输入用户名！" />
            </Form.Item>
            <Form.Item
              name="password"
              rules={[
                {
                  required: true,
                  message: "密码是必填的！",
                },
              ]}
            >
              <Input placeholder="请输入密码！" type="password" />
            </Form.Item>
            <div className={styles.footer}>
              <a href={feishuLoginUrl}>飞书登录</a>

              <Button loading={submitLoading} type="primary" htmlType="submit">
                登录
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
}

export default Login;
