const { prefix } = require("./config");

const Api = {
  getMenuItems: `${prefix}/rest/v1/ams/menu`,
  getSystemInfo: `${prefix}/rest/v1/system/info`,
  login: `${prefix}/rest/v1/admin/user/login`,
  getToken: `${prefix}/rest/v1/admin/user/login/get-token`,
  commandEnd: `${prefix}/rest/v1/command/end`,
  download: `${prefix}/rest/v1/download`,
  serialRequest: `${prefix}/rest/v1/serial-request`,
  customer: `${prefix}/rest/v1/customer`,
  order: `${prefix}/rest/v1/order`,
  orderModifyHistory: `${prefix}/rest/v1/order/modify/history`,
  getListPageData: `${prefix}/rest/v1/:url_key`,
  getListPageHeader: `${prefix}/rest/v1/:url_key`,
  getEditPageData: `${prefix}/rest/v1/:url_key`,
  saveEditPageData: `${prefix}/rest/v1/:url_key`,
  getDetailPageData: `${prefix}/rest/v1/:url_key`,
  getSearchDetailPageData: `${prefix}/rest/v1/search/detail/:url_key`,
  searchProductSpu: `${prefix}/rest/v1/product/spu`,
  uploadFile: `${prefix}/rest/v1/upload`,
  savePopupTemplate: `${prefix}/rest/v1/popup-template`,
  getEditPopupData: `${prefix}/rest/v1/:url_key`,
  saveEditPopupData: `${prefix}/rest/v1/:url_key`,
  getApiForm: `${prefix}/rest/v1/api-form`,
  getApiJsonComponents: `${prefix}/rest/v1/api-json-components`,
  getRestockOrderCountIn: `${prefix}/rest/v1/stock/restock/order/count-in`,
  restockOrderCountInVerify: `${prefix}/rest/v1/stock/restock/order/count-in/verify`,
  restockOrderCountInConfirm: `${prefix}/rest/v1/stock/restock/order/count-in/confirm`,
  restockOrderItemPrintLabel: `${prefix}/rest/v1/stock/restock/order/item/print-label`,
  restockGetQcProductInfo: `${prefix}/rest/v1/stock/restock/order/quality-control/index`,
  restockQualityControlVerify: `${prefix}/rest/v1/stock/restock/order/quality-control/verify`,
  restockQualityControlConfirm: `${prefix}/rest/v1/stock/restock/order/quality-control/confirm`,
  getConfigurationList: `${prefix}/rest/v1/:url_key`,
  getShipOutOrderInfo: `${prefix}/rest/v1/ship/order/info`,
  getShipOutProductDetail: `${prefix}/rest/v1/ship/order/item/detail`,
  shipOrder: `${prefix}/rest/v1/ship/order/ship`,
  offlineShip: `${prefix}/rest/v1/ship/order/offline-ship`,
  shipOrderRepair: `${prefix}/rest/v1/ship/order/repair`,
  orderPrintLabel: `${prefix}/rest/v1/order/print-label`,
  orderPrintCard: `${prefix}/rest/v1/ship/order/item/print-card`,
  orderPrintGpsrLabel: `${prefix}/rest/v1/gpsr/order/label/print`,
  orderRestore: `${prefix}/rest/v1/ship/order/restore`,
  getOrderDetail: `${prefix}/rest/v1/order/detail`,
  getDownloadList: `${prefix}/rest/v1/global/download/list`,
  deleteDownloadFile: `${prefix}/rest/v1/global/download/delete/:id`,
  getGlobalMessageQuery: `${prefix}/rest/v1/global/message/query`,
  setGlobalMessageResolved: `${prefix}/rest/v1/global/message/resolved`,
  getPageInfo: `${prefix}/rest/v1/global/page-info`,
  getI18nResource: `${prefix}/rest/v1/global/i18n/:lang`,
  getTicketDetail: `${prefix}/rest/v1/ticket/ticket/play`,
  getIncrementDetail: `${prefix}/rest/v1/ticket/ticket/gupdate-increment`,
  getTicketUpdate: `${prefix}/rest/v1/ticket/ticket/update`,
  getPmsProductEdit: `${prefix}/rest/v1/pms/product/edit`,
  getCustomizeDictionaryOption: `${prefix}/rest/v1/customize/dictionary/option`,
  getDictionary: `${prefix}/rest/v1/dictionary`,
  getGlassesTemplate: `${prefix}/rest/v1/customize/advanced-option`,
  getOtherPenaltyDetail: `${prefix}/rest/v1/fine/log/other/edit`,
  getOtherPenaltyChangeDate: `${prefix}/rest/v1/fine/log/other/edit/change-date`,
  getIframePageData: `${prefix}/rest/v1/iframe/:url_key`,
  getTemplateProperty: `${prefix}/rest/v1/template/property`,

  getPodLibraries: `${prefix}/rest/v1/pod/libraries`,
  createPodLibrary: `${prefix}/rest/v1/pod/libraries`,
  deletePodLibrary: `${prefix}/rest/v1/pod/libraries/:id`,
  updatePodLibrary: `${prefix}/rest/v1/pod/libraries/:id`,
  getPodLibrary: `${prefix}/rest/v1/pod/libraries/:id`,
  addPodLibraryCategory: `${prefix}/rest/v1/pod/categories`,
  deletePodLibraryCategory: `${prefix}/rest/v1/pod/categories/:id`,
  getPodLibraryCategory: `${prefix}/rest/v1/pod/categories/:id`,
  getPodLibraryCategories: `${prefix}/rest/v1/pod/categories`,
  addPodLibraryItems: `${prefix}/rest/v1/pod/items`,
  deletePodLibraryItem: `${prefix}/rest/v1/pod/items/:id`,
  deletePodLibraryItems: `${prefix}/rest/v1/pod/items`,
  updatePodLibraryItem: `${prefix}/rest/v1/pod/items/:id`,
  sortPodLibraryItems: `${prefix}/rest/v1/pod/items`,
  getPodLibraryItems: `${prefix}/rest/v1/pod/items`,
  getPodTemplates: `${prefix}/rest/v1/pod/templates`,
  createPodTemplate: `${prefix}/rest/v1/pod/templates`,
  deletePodTemplate: `${prefix}/rest/v1/pod/templates/:id`,
  getPodTemplate: `${prefix}/rest/v1/pod/templates/:id`,
  updatePodTemplate: `${prefix}/rest/v1/pod/templates/:id`,
  addPodOptionSet: `${prefix}/rest/v1/pod/option-sets`,
  getPodOptionSets: `${prefix}/rest/v1/pod/option-sets`,
  getPodOptionSet: `${prefix}/rest/v1/pod/option-sets/:id`,
  updatePodOptionSet: `${prefix}/rest/v1/pod/option-sets/:id`,
  deletePodOptionSet: `${prefix}/rest/v1/pod/option-sets/:id`,
  getPodTemplateOptionSet: `${prefix}/rest/v1/pod/templates/:templateId/option-set`,
  searchPodI18n: `${prefix}/rest/v1/pod/i18n`,

  get3dModelData: `${prefix}/rest/v1/texture/type/:id`,
  save3dModelData: `${prefix}/rest/v1/texture/type/:id`,
  get3dProductData: `${prefix}/rest/v1/texture/draft/:data_id`,
  save3dProductImages: `${prefix}/rest/v1/texture/texture`,
};

module.exports = Api;
