import { useState, useEffect } from "react";

function usePageVisibility() {
  const isSupported = typeof document.visibilityState !== "undefined";
  const [isVisible, setIsVisible] = useState(document.visibilityState === "visible");

  useEffect(() => {
    if (!isSupported) {
      return;
    }

    const handleVisibilityChange = () => {
      setIsVisible(document.visibilityState === "visible");
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [isSupported]);

  return isVisible;
}

export default usePageVisibility;
