import { Table } from "antd";
import { useState, useEffect, useRef } from "react";
import { observer } from "mobx-react-lite";
import { reaction } from "mobx";
import debounce from "lodash.debounce";

import { Image, InputNumber, Input } from "antd";
import { EyeOutlined } from "@ant-design/icons";

import Enums from "enums";
import store from "pages/pms/product/edit/components/FormDesigner/store";

function OptionsSelector(props) {
  const { id, value, onChange, columns } = props;
  const [dataSource, setDataSource] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [tableColumns, setTableColumns] = useState([]);

  const isFirstRun = useRef(true);
  const paramsRef = useRef({});
  paramsRef.current = {
    columnRender,
    initColumnsValue,
    selectedRowKeys,
    value,
    dataSource,
    onChange,
  };

  const handleRowSelection = (selectedRowKeys, selectedRows) => {
    setSelectedRowKeys(selectedRowKeys);
    onChange?.(selectedRows);
  };

  const debouncedOnChange = debounce((selectedRows) => {
    onChange?.(selectedRows);
  }, 300);

  const handleCellEdit = (row, dataIndex, newValue) => {
    setDataSource((prevDataSource) => {
      const { selectedRowKeys } = paramsRef.current;
      const newDataSource = prevDataSource.map((item) => {
        if (item.value === row.value) {
          return { ...item, [dataIndex]: newValue };
        }
        return item;
      });

      if (selectedRowKeys.includes(row.value)) {
        const updatedValue = selectedRowKeys.map((key) => {
          const selectedRow = newDataSource.find((item) => item.value === key);
          return selectedRow;
        });

        debouncedOnChange(updatedValue);
      }

      return newDataSource;
    });
  };

  function editableRender({ column, row, value, index }) {
    const { editable } = column;
    const extraData = row[`${column?.dataIndex}_extra_data`];
    const component = editable.component;

    const commonProps = {
      ...editable.props,
      ...extraData?.props,
      value,
      onChange: (newValue) => handleCellEdit(row, column.dataIndex, newValue),
    };

    if (component === Enums.Components.InputNumber) {
      return <InputNumber {...commonProps} />;
    } else {
      return <Input {...commonProps} />;
    }
  }

  function columnRender({ column, row, value, index }) {
    if (column?.editable) {
      return editableRender({ column, row, value, index });
    } else if (column?.valueType === Enums.TableValueType.Image) {
      return <Image src={value} {...column.image} preview={{ mask: <EyeOutlined /> }} />;
    } else {
      return <span>{value}</span>;
    }
  }

  function initColumnsValue(columns) {
    return columns?.map((column) => ({
      ...column,
      render: (value, row, index) => columnRender({ column, row, value, index }),
    }));
  }

  useEffect(() => {
    const disposer = reaction(
      () => ({ id, options: store.optionsData[id] }),
      ({ options }) => {
        if (options) {
          setDataSource((prevDataSource) => {
            isFirstRun.current = false;
            return options.map((option) => {
              const existingItem = prevDataSource.find((item) => item.value === option.value);
              if (existingItem) {
                return existingItem;
              }
              const matchedItem = Array.isArray(paramsRef.current.value)
                ? paramsRef.current.value.find((item) => item.value === option.value)
                : {};
              return matchedItem ? { ...option, ...matchedItem } : option;
            });
          });

          // 每次选项数据改变，全选选项
          if (!isFirstRun.current || (!isFirstRun.current && !paramsRef.current.value?.length)) {
            const { onChange } = paramsRef.current;
            const selectedRowKeys = options.map((item) => item.value);
            setSelectedRowKeys(selectedRowKeys);
            onChange?.(options);
          }
        }
      },
      {
        fireImmediately: false,
      }
    );

    return () => {
      isFirstRun.current = true;
      disposer();
    };
  }, [id]);

  // 处理 value 回显
  useEffect(() => {
    if (value) {
      setDataSource((prevDataSource) => {
        if (!prevDataSource?.length) return [];
        return prevDataSource.map((row) => {
          const matchedItem = Array.isArray(value) ? value.find((item) => item.value === row.value) : {};
          return matchedItem ? { ...row, ...matchedItem } : row;
        });
      });
    }
  }, [value]);

  useEffect(() => {
    if (value?.length) {
      const selectedRowKeys = value.map((item) => item.value);
      setSelectedRowKeys(selectedRowKeys);
    }
  }, [value, dataSource]);

  useEffect(() => {
    if (columns) {
      const { initColumnsValue } = paramsRef.current;
      const newColumns = initColumnsValue([...columns]);
      setTableColumns(newColumns);
    }
  }, [columns]);

  return (
    <Table
      rowKey="value"
      size="small"
      {...props}
      columns={tableColumns}
      dataSource={dataSource}
      pagination={false}
      bordered
      rowSelection={{
        selectedRowKeys,
        columnWidth: 32,
        onChange: handleRowSelection,
      }}
    />
  );
}

OptionsSelector = observer(OptionsSelector);

export default OptionsSelector;
