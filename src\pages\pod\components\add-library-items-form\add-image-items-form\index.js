import Fetchers from "@/fetchers";
import { createImageLibraryItems, loadImage } from "@/pages/pod/common";
import { Button, Input, App } from "antd";
import { useRef, useState } from "react";
import PropTypes from "prop-types";
import { Form, FormControl, ControlWrapper } from "@/components/react-form-x";
import I18nSelect from "@/pages/pod/components/i18n-select";

function AddImageItemsForm(props) {
  const { libraryId, categoryId, onSubmit, onSuccess, onError, onFinally } = props;
  const [submitLoading, setSubmitLoading] = useState(false);
  const formRef = useRef();
  const { modal } = App.useApp();

  const initialValues = {
    // previewTemplate: `https://images.drawelry.com/pod/libraries/doormat-1/long_hair/{{index}}.png`,
    // prodTemplate: `https://images.drawelry.com/pod/libraries/doormat-1/long_hair/{{index}}.png`,
    // thumbTemplate: `https://images.drawelry.com/pod/libraries/doormat-1/long_hair/{{index}}-s.png`,
    // cover: "https://images.drawelry.com/pod/libraries/doormat-1/long-hair.jpg",
    // optionNameTemplate: `Option`,
  };

  return (
    <Form
      ref={formRef}
      onSubmit={async (event, values) => {
        try {
          setSubmitLoading(true);
          onSubmit?.(values);
          const payload = { library_id: libraryId, category_id: categoryId };
          const itemIds = await Fetchers.getPodLibraryItems({
            query: { library_id: libraryId, category_id: categoryId },
          }).then((res) => res.data?.data?.items?.map((item) => item.id));
          if (itemIds?.length > 0) {
            await Fetchers.deletePodLibraryItems({ ids: itemIds });
          }
          payload.items = await createImageLibraryItems({ ...values });
          if (values.cover) {
            payload.cover = await loadImage(values.cover).then((image) => ({
              src: image.src,
              width: image.width,
              height: image.height,
            }));
          }
          Fetchers.addPodLibraryItems(payload)
            .then(onSuccess)
            .catch(onError)
            .finally(() => {
              setSubmitLoading(false);
              onFinally?.();
            });
        } catch (error) {
          setSubmitLoading(false);
          modal.error({ title: "操作失败！", content: error.message || "添加图片时出现问题，请稍后重试" });
        }
      }}
    >
      <div style={{ display: "flex", flexDirection: "column", gap: 20 }}>
        <div>
          <FormControl
            name="previewTemplate"
            rule={{ required: true }}
            render={(props) => (
              <ControlWrapper
                {...props}
                defaultValue={initialValues.previewTemplate}
                render={(props) => (
                  <div>
                    <div>预览图：</div>
                    <div>
                      <Input
                        {...props}
                        placeholder="https://images.drawelry.com/pod/libraries/doormat-1/long_hair/{{index}}.png"
                      ></Input>
                    </div>
                  </div>
                )}
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
        <div>
          <FormControl
            name="prodTemplate"
            rule={{ required: true }}
            render={(props) => (
              <ControlWrapper
                {...props}
                defaultValue={initialValues.prodTemplate}
                render={(props) => (
                  <div>
                    <div>生产图：</div>
                    <div>
                      <Input
                        {...props}
                        placeholder="https://images.drawelry.com/pod/libraries/doormat-1/long_hair/{{index}}.png"
                      ></Input>
                    </div>
                  </div>
                )}
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
        <div>
          <FormControl
            name="thumbTemplate"
            rule={{ required: true }}
            render={(props) => (
              <ControlWrapper
                {...props}
                defaultValue={initialValues.thumbTemplate}
                render={(props) => (
                  <div>
                    <div>缩略图：</div>
                    <div>
                      <Input
                        {...props}
                        placeholder="https://images.drawelry.com/pod/libraries/doormat-1/long_hair/{{index}}.png"
                      ></Input>
                    </div>
                  </div>
                )}
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
        <div>
          <FormControl
            name="optionNameTemplate"
            rule={{ required: true }}
            render={(props) => (
              <ControlWrapper
                {...props}
                render={(props) => {
                  const { name, value, onChange } = props;
                  return (
                    <div>
                      <div>选项名：</div>
                      <div style={{ display: "flex", alignItems: "center", gap: 10 }}>
                        <I18nSelect
                          value={value}
                          onChange={(value, option) => {
                            onChange(null, { [name]: option.label });
                            formRef.current?.updateValue({ optionNameTemplateI18nKey: option.value });
                          }}
                          style={{ width: `100%` }}
                        ></I18nSelect>
                      </div>
                    </div>
                  );
                }}
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
        <div style={{ display: "none" }}>
          <FormControl
            name="optionNameTemplateI18nKey"
            rule={{ required: true }}
            render={(props) => (
              <ControlWrapper {...props} render={(props) => <Input {...props}></Input>}></ControlWrapper>
            )}
          ></FormControl>
        </div>
        <div style={{ textAlign: "end" }}>
          <Button type="primary" htmlType="submit" loading={submitLoading}>
            添加
          </Button>
        </div>
      </div>
    </Form>
  );
}

AddImageItemsForm.propTypes = {
  libraryId: PropTypes.any,
  libraryType: PropTypes.string,
  categoryId: PropTypes.any,
  onSubmit: PropTypes.func,
  onError: PropTypes.func,
  onSuccess: PropTypes.func,
  onFinally: PropTypes.func,
};

export default AddImageItemsForm;
