import styles from "./index.module.scss";
import { Collapse } from "antd";
import { useDraggable } from "@dnd-kit/core";
import { componentCategories } from "../../config/componentMap";

function DraggableComponent({ component }) {
  const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
    id: component.component,
    data: {
      fromSidebar: true,
      component,
    },
  });

  return (
    <div
      className={styles.componentItem}
      ref={setNodeRef}
      {...listeners}
      {...attributes}
      style={{ opacity: isDragging ? 0.5 : 1 }}
    >
      <div className={styles.componentCard}>
        <div className={styles.icon}>{component?.component_config?.icon}</div>
        <div className={styles.title}>{component.title}</div>
      </div>
    </div>
  );
}

function ComponentPanel() {
  const collapseItems = componentCategories.map((category) => {
    const children = (
      <div className={styles.componentList}>
        {category.children.map((component, index) =>
          component?.component_config?.hidden ? null : <DraggableComponent key={index} component={component} />
        )}
      </div>
    );

    return {
      key: category?.key,
      label: (
        <div className={styles.categoryHeader}>
          <span className={styles.categoryTitle}>{category.name}</span>
          <span className={styles.categoryCount}>{category.children.length}</span>
        </div>
      ),
      children: children,
    };
  });

  return (
    <div className={styles.componentPanel}>
      <Collapse
        defaultActiveKey={componentCategories.map((category) => category.key)}
        items={collapseItems}
        ghost
        size="small"
      ></Collapse>
    </div>
  );
}

export default ComponentPanel;
