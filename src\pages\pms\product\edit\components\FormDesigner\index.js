import styles from "./index.module.scss";
import { useState, useCallback } from "react";
import PropTypes from "prop-types";

import { DndContext, DragOverlay, PointerSensor, useSensors, useSensor, MeasuringStrategy } from "@dnd-kit/core";

import { App, Layout } from "antd";
import { ExclamationCircleFilled } from "@ant-design/icons";
import Loading from "@/components/common/Loading";
import Sidebar from "./components/Sidebar";
import Toolbar from "./components/Toolbar";
import FormCanvas from "./components/FormCanvas";
import ConfigDrawer from "./components/ConfigDrawer";
import DragPreview from "./components/DragPreview";

import useDragAndDrop from "./hooks/useDragAndDrop";
import customCollisionDetection from "./common/customStrategy";
import {
  removeComponent,
  formatOldData,
  sanitizeItems,
  isKeyUsedInConditions,
  cleanUpConditionsByDeletedWidgetKey,
  notifyConditionsUpdate,
} from "./common/utils";
import { afterRender } from "functions";
import Enums from "./common/enums";
import Utils from "@/utils";

const { Sider, Content } = Layout;

function FormDesigner(props) {
  const {
    value = {
      items: [],
    },
    onChange,
    style,
    master_property,
    second_property,
    save_template,
    template_select,
  } = props;
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [widgetForm, setWidgetForm] = useState(() => formatOldData(value));
  const [selectedComponent, setSelectedComponent] = useState(() => widgetForm.items[0] ?? null);
  const masterPropertyKey = Enums.PropertyType.master;
  const secondPropertyKey = Enums.PropertyType.second;

  const { modal, notification } = App.useApp();

  const {
    flattenedItems = [],
    activeId,
    dragData,
    isOverDropZone,
    handleDragStart,
    handleDragMove,
    handleDragOver,
    handleDragEnd,
  } = useDragAndDrop({
    widgetForm,
    setWidgetForm,
    setSelectedComponent,
    onChange,
  });

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
        tolerance: 100,
      },
    })
  );

  const handleWidgetDelete = useCallback(
    (key) => {
      const performDelete = (cleanUpConditions) => {
        let items = removeComponent({ items: widgetForm.items, key });

        if (cleanUpConditions) {
          items = cleanUpConditionsByDeletedWidgetKey({ items, key });
        }

        setWidgetForm((prev) => ({ ...prev, items }));
      };

      if (isKeyUsedInConditions({ items: widgetForm.items, key })) {
        modal.confirm({
          title: "确定要删除此项吗？",
          icon: <ExclamationCircleFilled />,
          content: "当前项有作为条件配置，同时会删除使用到当前项的条件配置",
          onOk() {
            return new Promise((resolve) => {
              performDelete(true);
              resolve();
            });
          },
        });
      } else {
        performDelete(false);
      }
    },
    [modal, widgetForm.items]
  );

  const handleChangeTemplate = useCallback(
    (template, options) => {
      if (!template) {
        const emptyData = {
          items: [],
          [masterPropertyKey]: null,
          [secondPropertyKey]: null,
        };
        setWidgetForm(emptyData);

        onChange(emptyData);
        return;
      }

      const selectedTemplate = options.find((item) => item.value === template);
      const items = Utils.JSON.parse(selectedTemplate?.data, selectedTemplate?.data) || [];
      const masterProperty = items.find((item) => item.key === masterPropertyKey);
      const secondProperty = items.find((item) => item.key === secondPropertyKey);

      setWidgetForm((prev) => {
        const data = formatOldData({
          ...prev,
          items,
          [masterPropertyKey]: masterProperty?.identifier || null,
          [secondPropertyKey]: secondProperty?.identifier || null,
        });

        onChange({ ...data, items: sanitizeItems(items) });
        return data;
      });
    },
    [masterPropertyKey, secondPropertyKey, onChange]
  );

  const handleSave = useCallback(
    (component) => {
      setSelectedComponent(component);
      setWidgetForm((prev) => {
        const updateComponentByKey = (items) => {
          return items.map((item) => {
            if (item.key === component.key) {
              return component;
            }

            const childrenName = item.component_config?.childrenName || "children";
            if (item[childrenName]?.length) {
              return {
                ...item,
                [childrenName]: updateComponentByKey(item[childrenName]),
              };
            }
            return item;
          });
        };

        const items = updateComponentByKey(prev.items);

        afterRender(() => {
          onChange?.({ ...prev, items: sanitizeItems(items) });
          notifyConditionsUpdate({ component, widgetForm: prev, notification });
        });

        return { ...prev, items: updateComponentByKey(prev.items) };
      });
    },
    [onChange, notification]
  );

  return (
    <DndContext
      // sensors={sensors}
      collisionDetection={customCollisionDetection}
      measuring={{
        droppable: {
          strategy: MeasuringStrategy.Always,
        },
      }}
      onDragStart={handleDragStart}
      onDragMove={handleDragMove}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
      autoScroll
    >
      <div className={styles.formDesigner} style={style}>
        <Loading loading={loading}>
          <Layout className={styles.layout}>
            <Sider width={200} className={styles.sidebar}>
              <Sidebar
                widgetForm={widgetForm}
                masterProperty={master_property}
                secondProperty={second_property}
                setWidgetForm={setWidgetForm}
                onSelect={setSelectedComponent}
                onChange={onChange}
                masterPropertyKey={masterPropertyKey}
                secondPropertyKey={secondPropertyKey}
              />
            </Sider>

            <Layout className={styles.formContainer}>
              <Toolbar
                saveTemplate={save_template}
                templateSelect={template_select}
                widgetForm={widgetForm}
                changeTemplate={handleChangeTemplate}
              />
              <Content className={styles.formCanvas}>
                <FormCanvas
                  widgetForm={widgetForm}
                  flattenedItems={flattenedItems}
                  selectedKey={selectedComponent?.key}
                  onSelect={setSelectedComponent}
                  isOverDropZone={isOverDropZone}
                  onWidgetDelete={handleWidgetDelete}
                  openConfigDrawer={() => {
                    setOpen(true);
                  }}
                />
              </Content>
            </Layout>

            <ConfigDrawer
              open={open}
              onClose={() => {
                setOpen(false);
              }}
              component={selectedComponent}
              onSave={handleSave}
              widgetForm={widgetForm}
            />

            <DragOverlay dropAnimation={null}>
              {activeId && (
                <DragPreview
                  className={`${!isOverDropZone ? styles.noDrop : ""}`}
                  activeId={activeId}
                  dragData={dragData}
                  isOverlay
                />
              )}
            </DragOverlay>
          </Layout>
        </Loading>
      </div>
    </DndContext>
  );
}

FormDesigner.propTypes = {
  value: PropTypes.object,
  style: PropTypes.object,
  onChange: PropTypes.func,
  master_property: PropTypes.object,
  second_property: PropTypes.object,
  save_template: PropTypes.object,
  template_select: PropTypes.object,
};

export default FormDesigner;
