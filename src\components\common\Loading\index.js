import { Spin } from "antd";
import styles from "./index.module.scss";
import PropTypes from "prop-types";
import classNames from "classnames";

function Loading(props) {
  const { children, loading, style, childrenStyle, className, containerProps, spinBoxProps, ...otherProps } = props;

  return (
    <div
      {...containerProps}
      className={classNames(styles.container, className, { [styles.loading]: loading })}
      style={style}
    >
      {loading ? (
        <div className={styles.spin} {...spinBoxProps}>
          <Spin></Spin>
        </div>
      ) : null}
      <div className={styles.children} style={childrenStyle}>
        {children}
      </div>
    </div>
  );
}

Loading.propTypes = {
  loading: PropTypes.bool,
  className: PropTypes.string,
  containerProps: PropTypes.object,
  spinBoxProps: PropTypes.object,
};

export default Loading;
