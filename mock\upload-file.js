const path = require("node:path");
const fs = require("node:fs");
const multer = require("multer");
const { v4: uuid } = require("uuid");
const sharp = require("sharp");
const common = require("./common");

const uploadDir = "./mock/upload/files";
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

const storage = multer.diskStorage({
  destination: uploadDir,
  filename(req, file, callback) {
    const arr = file.originalname.split(".");
    const fileExtName = arr[arr.length - 1];
    callback(null, `${req.query?.hash || uuid()}.${fileExtName}`);
  },
});

const uploader = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 默认最大10MB
  },
});

const getFileInfo = async (file, host) => {
  let fileInfo = {
    fid: common.uniqueId(),
    host: host || `http://localhost:8081`,
    originalName: file.originalname,
    filename: file.filename,
    size: file.size,
    mimetype: file.mimetype,
  };

  // 如果是图片，获取图片的宽高
  if (file.mimetype.startsWith("image/")) {
    try {
      const metadata = await sharp(file.path).metadata();
      fileInfo.file = {
        src: `/rest/v1/files/${file.filename}`,
        width: metadata.width,
        height: metadata.height,
      };
    } catch (error) {
      console.error("获取图片信息失败:", error);
      fileInfo.file = {
        src: `/rest/v1/files/${file.filename}`,
      };
    }
  } else {
    fileInfo.file = {
      src: `/rest/v1/files/${file.filename}`,
      size: file.size,
    };
  }

  return fileInfo;
};

module.exports = {
  "POST /rest/v1/upload/file": async (req, res) => {
    try {
      await new Promise((resolve, reject) => {
        uploader.single("file")(req, res, (err) => {
          err ? reject(err) : resolve();
        });
      });

      const fileInfo = await getFileInfo(req.file, req.body.host);
      res.status(200).json({
        success: true,
        data: fileInfo,
      });
    } catch (error) {
      console.error("上传文件失败:", error);
      res.status(500).json({
        success: false,
        message: "上传文件失败",
        command: {
          type: "message",
          config: {
            type: "error",
            content: "上传文件失败",
            duration: 3,
          },
        },
      });
    }
  },

  "DELETE /rest/v1/upload/file": (req, res) => {
    try {
      const { url, fid } = req.body;

      if (url) {
        const filename = url.split("/").pop();
        const filePath = path.resolve(`${uploadDir}/${filename}`);

        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      }

      res.status(200).json({
        success: true,
        command: {
          type: "message",
          config: {
            type: "success",
            content: "删除成功",
            duration: 3,
          },
        },
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message || "删除文件失败",
      });
    }
  },
};
