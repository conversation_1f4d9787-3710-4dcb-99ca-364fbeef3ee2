import styles from "./index.module.scss";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Config<PERSON><PERSON><PERSON>, InputN<PERSON>ber, Tooltip } from "antd";
import classNames from "classnames";
import Utils from "@/utils";
import SvgIcon from "@/components/common/SvgIcon";
import PropTypes from "prop-types";
import { fabric } from "fabric";
import { observer } from "mobx-react-lite";
import podStore from "@/pages/pod/stores";
import { useEffect, useMemo, useRef, useState } from "react";
import { reaction, toJS } from "mobx";
import { Form, FormControl } from "@/components/react-form-x";
import ControlWrapper from "@/components/react-form-x/control-wrapper";
import debounce from "lodash.debounce";
import Helper from "@/helpers";
import Fetchers from "@/fetchers";
import {
  FabricObjectType,
  defaultControlProps,
  defaultObjectProps,
  defaultTextProps,
} from "@/pages/pod/common/init-fabric-types";
import { useNavigate } from "react-router-dom";
import {
  editorHistory,
  forEachActiveObjects,
  getSelectedLayout,
  loadFromJSON,
  setActiveObjects,
  updateObjectExtraData,
} from "./common";
import FabricObjectList from "@/pages/pod/components/fabric-object-list";
import FormControlLabel from "@/pages/pod/components/form-control-label";
import {
  FabricEvent,
  getFontsFromFabricObjects,
  loadFabricObjectFonts,
  createObjectId as commonCreateObjectId,
  createExtraData as commonCreateExtraData,
  setObjectExtraData as commonSetObjectExtraData,
  getObjectScale as commonGetObjectScale,
  setObjectDefaultProps as commonSetObjectDefaultProps,
  createObjectFromJSON as commonCreateObjectFromJSON,
  config,
} from "@/pages/pod/common";
import FabricObjectEditor from "@/pages/pod/components/fabric-object-editor";
import LayerItems from "@/pages/pod/components/layer-items";

function FabricPropsEditor(props) {
  const {} = props;
  const canvas = toJS(podStore.canvas);
  const [objects, setObjects] = useState(toJS(podStore.objects));
  const [nextDisabled, setNextDisabled] = useState(true);
  const transformFormRef = useRef();
  const navigate = useNavigate();
  const { message } = App.useApp();

  const debounceSetRecovery = useMemo(() => {
    return debounce((value) => {
      paramsRef.current.isRecovery = value;
    }, 1000);
  }, []);

  const paramsRef = useRef({});
  paramsRef.current = {
    ...paramsRef.current,
    canvas,
    forEachActiveObjects,
    debounceSetRecovery,
    createObjectFromJSON,
    handleDeleteSelectedObjects,
    createLayout,
    removeLayout,
  };

  function createObjectId() {
    return commonCreateObjectId({ canvas });
    // const objects = canvas.getObjects();
    // if (objects?.length > 0) {
    //   layerId = Math.max(...objects.map((a) => a.extraData.elementId ?? 0)) + 1;
    // } else {
    //   layerId++;
    // }
    // return layerId;
  }

  function createExtraData({ object, layerType, uniqueId }) {
    return commonCreateExtraData({ canvas, object, layerType, uniqueId });
    // const id = createObjectId();
    // layerType = layerType || object?.extraData?.layerType;
    // const name = LayerName[layerType];
    // return {
    //   id: uniqueId ?? Helper.uniqueId,
    //   name: name.replace("{{index}}", id),
    //   name_i18n_key: name,
    //   name_i18n_params: { index: id },
    //   createTime: Date.now(),
    //   zIndex: id,
    //   elementId: id,
    //   optionId: id,
    //   layerType,
    // };
  }

  function setObjectExtraData({ object, layerType }) {
    commonSetObjectExtraData({ canvas, object, layerType });
    // object.extraData = createExtraData({ object, layerType });
    // object.zIndex = object.extraData.zIndex;
  }

  function getObjectScale({ object, key, factor = 0.4, max = 500 }) {
    return commonGetObjectScale({ canvas, object, key, factor, max });
    // const size = (canvas[key] / canvas.getZoom()) * factor;
    // return (size > max ? max : size) / object[key];
  }

  function setObjectDefaultProps({ object, data }) {
    return commonSetObjectDefaultProps({ object, data, canvas });
    // const activeObject = canvas.getActiveObject() || Utils.lastOne(canvas.getObjects());
    // object.set({ ...defaultObjectProps, ...data });
    // if (canvas.width > canvas.height) {
    //   object.scale(getObjectScale({ object, key: "height" }));
    // } else {
    //   object.scale(getObjectScale({ object, key: "width" }));
    // }
    // if (activeObject) {
    //   object.set({
    //     top: activeObject.top + config.newObjectGap.top,
    //     left: activeObject.left + config.newObjectGap.left,
    //   });
    // } else {
    //   object.center();
    // }
    // return object;
  }

  function createObjectFromJSON({ data, layerType }) {
    return commonCreateObjectFromJSON({ canvas, data, layerType });
    // let object;
    // if (data.type === FabricObjectType.CustomImageBox) {
    //   object = new fabric.CustomImageBox(data);
    // } else if (data.type === FabricObjectType.CustomTextBox) {
    //   object = new fabric.CustomTextBox("A", data);
    // }
    // setObjectExtraData({ object, layerType });
    // setObjectDefaultProps({ object, data });
    // return object;
  }

  function handleDeleteSelectedObjects() {
    const activeObject = canvas.getActiveObject();
    if (activeObject?.type === FabricObjectType.ActiveSelection) {
      const objects = activeObject.getObjects();
      canvas._discardActiveObject();
      canvas.remove(...objects);
    } else if (activeObject) {
      canvas.remove(activeObject);
    }
  }

  function createLayout() {
    const { canvas } = paramsRef.current;
    const layout = createNewLayoutData();
    canvas.getActiveObjects().forEach((object) => {
      object.extraData.layout = layout;
    });
    canvas.fire(FabricEvent.ObjectModified);
  }

  function removeLayout({ selectedLayout }) {
    const { canvas } = paramsRef.current;
    canvas.getObjects().forEach((object) => {
      if (object.extraData.layout?.id === selectedLayout.id) {
        delete object.extraData.layout;
      }
    });
    canvas.fire(FabricEvent.ObjectModified);
  }

  function createNewLayoutData() {
    const uniqueId = Helper.uniqueId;
    const nextId =
      Math.max(
        ...canvas.getObjects().map((obj) => obj.extraData.layout?.elementId ?? obj.extraData.layout?.optionId ?? 0)
      ) + 1;
    return {
      id: uniqueId,
      name: `Layout ${nextId}`,
      name_i18n_key: `Layout {{index}}`,
      name_i18n_params: { index: nextId },
      elementId: nextId,
      optionId: nextId,
    };
  }

  async function handleDuplicate() {
    const activeObject = canvas.getActiveObject();
    const setObjectProps = ({ object, clonedObject }) => {
      setObjectExtraData({ object: clonedObject });
      clonedObject.extraData.library = object.extraData.library;
      clonedObject.extraData.name = object.extraData.name;
      clonedObject.extraData.upload = object.extraData.upload;
      // clonedObject.extraData.layout = object.extraData.layout;
    };
    if (activeObject?.type === FabricObjectType.ActiveSelection) {
      const objects = activeObject.getObjects();
      const clonedObjects = [];
      objects.forEach((object) => {
        object.clone((clonedObject) => {
          setObjectProps({ object, clonedObject });
          canvas.add(clonedObject);
          clonedObjects.push(clonedObject);
        });
      });
      setActiveObjects({
        canvas,
        objects: clonedObjects,
        beforeRender(activeSelection) {
          activeSelection.set({
            top: activeObject.top,
            left: activeObject.left,
          });
        },
      });
    } else if (activeObject) {
      const clonedObject = await new Promise((resolve) => {
        activeObject.clone(resolve);
      });
      setObjectProps({ object: activeObject, clonedObject });
      clonedObject.set({
        top: activeObject.top,
        left: activeObject.left,
      });
      canvas.add(clonedObject);
      canvas.setActiveObject(clonedObject);
    }
  }

  useEffect(() => {
    function handleMouseMove() {
      paramsRef.current.isRecovery = false;
    }
    window.addEventListener("mousemove", handleMouseMove);

    return function () {
      window.removeEventListener("mousemove", handleMouseMove);
    };
  }, []);

  useEffect(() => {
    const debounceHistoryPush = debounce(() => {
      const { isRecovery } = paramsRef.current;
      if (podStore.initialized && !isRecovery) {
        const { canvas } = podStore;
        editorHistory.queue = editorHistory.queue.slice(0, editorHistory.index + 1);
        editorHistory.queue.push({
          activeObject: canvas.getActiveObject()?.toJSON() || null,
          objects: canvas.toJSON().objects,
        });
        while (editorHistory.queue.length > config.historyMaxLength) {
          editorHistory.queue.shift();
        }
        editorHistory.index = editorHistory.queue.length - 1;
        console.log(editorHistory);
      }
    }, 0);

    const disposeInitReaction = reaction(
      () => podStore.initialized,
      () => {
        debounceHistoryPush();
      }
    );

    const disposeObjectsReaction = reaction(
      () => podStore.objects,
      debounce((value) => {
        // 更新所有属性
        const objects = toJS(value).sort((a, b) => b.extraData.createTime - a.extraData.createTime);
        setObjects([...objects]);
        const activeObject = podStore.canvas?.getActiveObject();
        if (activeObject) {
          activeObject.scaledWidth = activeObject.width * activeObject.scaleX;
          activeObject.scaledHeight = activeObject.height * activeObject.scaleY;
          activeObject.angle = activeObject.angle || 0;
          updateObjectExtraData({ activeObject });
          podStore.setActiveObject({ ...activeObject });
        } else {
          debounceHistoryPush();
        }
      })
    );

    const debounceHandleActiveObjectReaction = debounce((activeObject) => {
      // 只更新extraData
      if (activeObject) {
        // activeObject = toJS(activeObject);
        updateObjectExtraData({ activeObject: activeObject });
        debounceHistoryPush();
        transformFormRef.current?.updateValue(activeObject);
      }
    }, 0);
    const disposeActiveObjectReaction = reaction(
      () => podStore.activeObject,
      (nextActiveObject, prevActiveObject) => {
        const isTypes = (types = []) => {
          return types.includes(nextActiveObject?.type) && types.includes(prevActiveObject?.type);
        };
        const isPropChange = (prop) => {
          const isSameObject = nextActiveObject?.extraData?.id === prevActiveObject?.extraData?.id;
          return isSameObject && nextActiveObject?.[prop] !== prevActiveObject?.[prop];
        };
        if (
          isTypes([FabricObjectType.CustomTextBox, FabricObjectType.CustomImageBox]) &&
          (isPropChange("scaleX") || isPropChange("scaleY"))
        ) {
          nextActiveObject.fitContent();
        }
        debounceHandleActiveObjectReaction(nextActiveObject, prevActiveObject);
      }
    );

    return function () {
      disposeInitReaction();
      disposeObjectsReaction();
      disposeActiveObjectReaction();
    };
  }, []);

  useEffect(() => {
    let loading = false;

    function updateActiveObject({ canvas, historyData }) {
      if (historyData.activeObject) {
        if (historyData.activeObject?.type === FabricObjectType.ActiveSelection) {
          const selectedMap = {};
          historyData.activeObject.objects.forEach((item) => {
            selectedMap[item.extraData.id] = true;
          });
          const objects = canvas.getObjects().filter((item) => selectedMap[item.extraData.id]);
          setActiveObjects({
            canvas,
            objects,
            beforeRender(activeSelection) {
              activeSelection.set({
                top: historyData.activeObject.top,
                left: historyData.activeObject.left,
                width: historyData.activeObject.width,
                height: historyData.activeObject.height,
              });
            },
          });
        } else {
          const activeObject = canvas
            .getObjects()
            .find((item) => item.extraData.id === historyData.activeObject.extraData.id);
          if (activeObject) {
            canvas.setActiveObject(activeObject);
          }
        }
      } else {
        canvas.discardActiveObject();
      }
    }

    async function goto(index) {
      if (loading) return;
      loading = true;
      paramsRef.current.isRecovery = true;
      const canvas = toJS(podStore.canvas);
      const historyData = editorHistory.queue[index];
      if (historyData) {
        await loadFromJSON({ canvas, objects: historyData.objects });
        updateActiveObject({ canvas, historyData });
        editorHistory.index = index;
      }
      loading = false;
    }

    async function handleKeyDown(event) {
      const { handleDeleteSelectedObjects } = paramsRef.current;
      const key = event.key.toLowerCase();
      if (key === "z" && event.ctrlKey && !event.shiftKey && !event.altKey) {
        // undo
        goto(editorHistory.index - 1);
      } else if ((key === "z" && event.ctrlKey && event.shiftKey) || (key === "y" && event.ctrlKey)) {
        // redo
        goto(editorHistory.index + 1);
      } else {
        paramsRef.current.isRecovery = false;
        if (key === "delete") {
          handleDeleteSelectedObjects();
        }
      }
    }
    window.addEventListener("keydown", handleKeyDown);

    return function () {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  useEffect(() => {
    document.body.classList.add("overflow-hidden");

    return function () {
      document.body.classList.remove("overflow-hidden");
    };
  }, []);

  useEffect(() => {
    const destroy = reaction(
      () => podStore.template,
      () => {
        if (podStore.template) {
          const { resizeForm } = paramsRef.current;
          resizeForm.updateValue({ width: podStore.template.initWidth, height: podStore.template.initHeight });
          setNextDisabled(!(podStore.template.canvas?.objects?.length > 0));
        }
      }
    );

    return function () {
      destroy?.();
    };
  }, []);

  useEffect(() => {
    const dispose = reaction(
      () => podStore.initialized,
      async () => {
        const { canvas } = paramsRef.current;
        const fonts = getFontsFromFabricObjects({ objects: canvas.getObjects() });
        podStore.setFontFamilyOptions([
          ...toJS(podStore.fontFamilyOptions),
          ...fonts.map((item) => ({ label: item.family, value: item.family, url: item.url })),
        ]);
        await loadFabricObjectFonts({ canvas, fonts });
      }
    );

    return () => {
      dispose();
    };
  }, []);

  return (
    <>
      <div className={styles.props}>
        <Form
          ref={(form) => {
            paramsRef.current.resizeForm = form;
          }}
          onSubmit={(event, values) => {
            const { width, height } = values;
            podStore.template.initWidth = width;
            podStore.template.initHeight = height;
            const canvasElement = canvas.getElement();
            const canvasBox = canvasElement.parentElement.parentElement.parentElement;
            const fitSize = Utils.getAspectRatioScaleMinSize({
              width,
              height,
              maxWidth: canvasBox.clientWidth,
              maxHeight: canvasBox.clientHeight,
            });
            canvas.setZoom(fitSize.width / canvas.extraData.originalWidth);
            canvas.setDimensions({ width: fitSize.width, height: fitSize.height });
          }}
          updateMode="mutation"
        >
          <div style={{ display: "flex", gap: 16 }}>
            <div className={styles.resizeControl}>
              <FormControl
                name="width"
                rule={{ required: true }}
                render={(props) => (
                  <ControlWrapper
                    {...props}
                    render={(props) => {
                      const { name, value, onChange } = props;
                      return (
                        <FormControlLabel label="Width:" layout="horizontal">
                          <InputNumber
                            value={value}
                            onChange={(value) => {
                              onChange(null, { [name]: value });
                            }}
                            style={{ width: `100%` }}
                          ></InputNumber>
                        </FormControlLabel>
                      );
                    }}
                  ></ControlWrapper>
                )}
              ></FormControl>
            </div>
            <div className={styles.resizeControl}>
              <FormControl
                name="height"
                rule={{ required: true }}
                render={(props) => (
                  <ControlWrapper
                    {...props}
                    render={(props) => {
                      const { name, value, onChange } = props;
                      return (
                        <FormControlLabel label="Height:" layout="horizontal">
                          <InputNumber
                            value={value}
                            onChange={(value) => {
                              onChange(null, { [name]: value });
                            }}
                            style={{ width: `100%` }}
                          ></InputNumber>
                        </FormControlLabel>
                      );
                    }}
                  ></ControlWrapper>
                )}
              ></FormControl>
            </div>
            <div>
              <Button type="primary" htmlType="submit">
                Resize
              </Button>
            </div>
          </div>
        </Form>
        <div>
          <ConfigProvider theme={{ components: { Collapse: { contentPadding: 0 } } }}>
            <Collapse
              defaultActiveKey={["1"]}
              items={[
                {
                  key: "1",
                  label: (
                    <div style={{ display: "flex", justifyContent: "space-between" }}>
                      <div>Layers</div>
                      {(() => {
                        const selectedLayout = getSelectedLayout({ canvas });
                        if (selectedLayout) {
                          return (
                            <div
                              onClick={(event) => {
                                event.stopPropagation();
                                removeLayout({ selectedLayout });
                              }}
                            >
                              Remove Layout
                            </div>
                          );
                        } else if (canvas?.getActiveObjects().length > 1) {
                          return (
                            <div
                              onClick={(event) => {
                                event.stopPropagation();
                                createLayout();
                              }}
                            >
                              Create Layout
                            </div>
                          );
                        }
                      })()}
                    </div>
                  ),
                  children: (
                    <div style={{ height: 280, overflow: "auto" }}>
                      <FabricObjectList canvas={canvas} layouts={toJS(podStore.layouts)}></FabricObjectList>
                    </div>
                  ),
                },
              ]}
            ></Collapse>
          </ConfigProvider>
        </div>
        <div>
          <LayerItems canvas={canvas}></LayerItems>
        </div>
        <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
          {podStore.activeObject ? (
            <FabricObjectEditor canvas={canvas} transformFormRef={transformFormRef}></FabricObjectEditor>
          ) : null}
          {podStore.activeObject ? (
            <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
              <div style={{ display: "flex", gap: 8 }}>
                <Tooltip title="Duplicate">
                  <Button className={styles.iconButton} onClick={handleDuplicate}>
                    <SvgIcon
                      src="https://cdn.customily.com/app/img/copy-item.80c2a0b0.svg"
                      style={{ width: "90%" }}
                    ></SvgIcon>
                  </Button>
                </Tooltip>
                <Tooltip title="Delete">
                  <Button className={styles.iconButton} onClick={handleDeleteSelectedObjects}>
                    <SvgIcon
                      src="https://cdn.customily.com/app/img/garbage.d7c42f9f.svg"
                      style={{ width: "90%" }}
                    ></SvgIcon>
                  </Button>
                </Tooltip>
              </div>
              <div style={{ flex: 1 }}>
                <Button
                  type="primary"
                  block
                  onClick={() => {
                    transformFormRef.current?.submit();
                  }}
                >
                  Done
                </Button>
              </div>
            </div>
          ) : (
            <div style={{ display: "flex", flexDirection: "column", gap: 8 }}>
              <Button
                type="primary"
                block
                disabled={podStore.objects.length === 0}
                onClick={async () => {
                  try {
                    Helper.pageLoading(true);
                    const { id, skipUploadCover } = Utils.getQueryParams(window.location.href);
                    const templateId = +id;
                    await Promise.all([
                      new Promise(async (resolve, reject) => {
                        let cover = null;
                        if (!skipUploadCover) {
                          try {
                            const coverFile = await new Promise((resolve) => {
                              canvas.toCanvasElement().toBlob((blob) => {
                                resolve(new File([blob], "cover.png", { type: "image/png" }));
                              });
                            });
                            cover = await Fetchers.uploadFile({
                              file: coverFile,
                              host: "http://*************:8081",
                            }).then((res) => {
                              const { host, image } = res.data.data;
                              return { ...image, src: host + image.src };
                            });
                          } catch (err) {
                            message.error(`模板封面保存失败！`);
                          }
                        }
                        try {
                          const canvasJsonData = canvas.toJSON();
                          canvasJsonData.objects.forEach((object) => {
                            if (object.extraData.layout) {
                              object.extraData.layout.elementId = object.extraData.layout.optionId;
                            }
                          });
                          if (templateId) {
                            await Fetchers.updatePodTemplate({
                              id: templateId,
                              data: {
                                name: podStore.template.name,
                                name_i18n_key: podStore.template.name_i18n_key,
                                name_i18n_params: podStore.template.name_i18n_params,
                                initWidth: podStore.template.initWidth,
                                initHeight: podStore.template.initHeight,
                                width: canvas.extraData?.originalWidth,
                                height: canvas.extraData?.originalHeight,
                                canvas: canvasJsonData,
                                cover,
                              },
                            });
                          } else {
                            await Fetchers.createPodTemplate({
                              name: podStore.template.name,
                              name_i18n_key: podStore.template.name_i18n_key,
                              name_i18n_params: podStore.template.name_i18n_params,
                              initWidth: podStore.template.initWidth,
                              initHeight: podStore.template.initHeight,
                              width: canvas.extraData?.originalWidth,
                              height: canvas.extraData?.originalHeight,
                              unit: "px",
                              canvas: canvasJsonData,
                              cover,
                            });
                          }
                          resolve();
                        } catch (err) {
                          reject();
                        }
                      }),
                    ]);
                    setNextDisabled(false);
                    message.success(`保存成功！`);
                  } finally {
                    Helper.pageLoading(false);
                  }
                }}
              >
                Save Template
              </Button>
              <Button
                type="primary"
                block
                className="ant-btn-success"
                onClick={() => {
                  const { id } = Utils.getQueryParams(window.location.href);
                  const templateId = +id;
                  navigate(Utils.setQueryParams(`/pod/edit-template-bg`, { id: templateId }));
                }}
                disabled={nextDisabled}
              >
                Next
              </Button>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

FabricPropsEditor = observer(FabricPropsEditor);

FabricPropsEditor.propTypes = {
  onChange: PropTypes.func,
  onSelect: PropTypes.func,
};

export default FabricPropsEditor;
export { defaultControlProps, defaultObjectProps, defaultTextProps, setActiveObjects };
