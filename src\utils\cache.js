let MemoryStorage = {};
const CacheLock = {};
const TimeoutIds = {};

const Events = [];
const Utils = Object.freeze({
  sleep(milliseconds) {
    return new Promise((resolve) => {
      setTimeout(resolve, milliseconds);
    });
  },

  addEventListener(name, handler, id = Math.random().toString().substring(2)) {
    Events.push({ name, handler, id });
  },

  removeEventListener(name, handler) {
    let arr;
    if (typeof handler === "string") {
      arr = Events.filter((item) => !(item.name === name && item.id === handler));
    } else {
      arr = Events.filter((item) => !(item.name === name && item.handler === handler));
    }
    Events.length = 0;
    Events.push(...arr);
  },

  dispatchEvent(name, ...args) {
    const events = Events.filter((item) => item.name === name);
    for (const event of events) {
      event.handler(...args);
    }
  },
});

const isExpire = (result) => {
  return Date.now() - result?.timestamp > result?.ttl * 1000;
};

class Cache {
  constructor(storage) {
    if (storage) {
      this.storage = storage;
    }
  }

  storage = Object.freeze({
    get: (key) => {
      return MemoryStorage[key];
    },
    set: (key, value) => {
      MemoryStorage[key] = value;
    },
    remove: (key) => {
      delete MemoryStorage[key];
    },
    clear: () => {
      MemoryStorage = {};
    },
  });

  // ttl单位：秒
  async get({ key, getValue, ttl = 600, delay = 0, onGetValue, onCacheHit }) {
    const responseEventName = `cache_${key}_on_response`;
    const result = await this.storage.get(key);
    if ((!result || isExpire(result)) && getValue && !CacheLock[key]) {
      try {
        CacheLock[key] = true;
        const value = await getValue();
        if (typeof onGetValue === "function") {
          onGetValue({ key, value });
        }
        if (value) {
          await this.set({ key, value, ttl, delay });
        }
        Utils.dispatchEvent(responseEventName, { value });
        return value;
      } finally {
        await Utils.sleep();
        delete CacheLock[key];
      }
    } else {
      let value = result?.value;
      if (CacheLock[key]) {
        value = await new Promise((resolve) => {
          const handleResponse = ({ value }) => {
            Utils.removeEventListener(responseEventName, handleResponse);
            resolve(value);
          };
          Utils.addEventListener(responseEventName, handleResponse);
        });
      }
      if (typeof onCacheHit === "function") {
        onCacheHit({ key, value });
      }
      return value;
    }
  }

  // ttl单位：秒
  async set({ key, value, ttl = 600, delay = 0 }) {
    await this.storage.set(key, {
      value,
      ttl,
      delay,
      timestamp: Date.now(),
    });
    clearTimeout(TimeoutIds[key]);
    TimeoutIds[key] = setTimeout(() => {
      this.storage.remove(key);
    }, (ttl + delay) * 1000);
  }

  async remove(key) {
    await this.storage.remove(key);
  }

  async clear() {
    await this.storage.clear();
  }
}

export default Cache;
