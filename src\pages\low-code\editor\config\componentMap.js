import {
  FontSizeOutlined,
  TableOutlined,
  EditOutlined,
  NumberOutlined,
  CheckSquareOutlined,
  SelectOutlined,
  CalendarOutlined,
  BarsOutlined,
  TagsOutlined,
  FileTextOutlined,
  RocketOutlined,
  AppstoreOutlined,
  LayoutOutlined,
  BorderOuterOutlined,
  SwitcherOutlined,
  FormOutlined,
} from "@ant-design/icons";
import {
  InputNumber,
  Select,
  Checkbox,
  Radio,
  DatePicker,
  Switch,
  Slider,
  Upload,
  Button,
  Table,
  Form,
  Card,
  Tabs,
  Tag,
  Divider,
  Typography,
  Row,
  Col,
  Space,
  Collapse,
} from "antd";
// import { generateUniqueId } from "../utils";

// 组件分类数组
export const componentCategories = [
  {
    name: "表单组件",
    key: "form",
    children: [
      {
        component: "Input",
        title: "输入框",
        component_config: {
          // icon: <EditOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            // dataTransform: {
            //   topLevelProps: ["key", "label"],
            // },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: ["props", "placeholder"],
                label: "Placeholder",
                component: "Input",
                props: { placeholder: "请输入" },
              },
            ],
          },
        },
      },
      {
        component: "InputNumber",
        title: "数字输入框",
        component_config: {
          // icon: <NumberOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: ["props", "placeholder"],
                label: "Placeholder",
                component: "Input",
                props: { placeholder: "请输入" },
              },
            ],
          },
        },
      },
      {
        component: "EditTable",
        title: "可编辑表格",
        // icon: <NumberOutlined />,
        component_config: {
          // icon: <EditOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: ["props", "columns"],
                label: "维护表头",
                component: "ColumnEditor",
                rules: [{ required: true }],
                props: {
                  scroll: { x: 900 },
                  operationsWidth: 60,
                  columns: [
                    // {
                    //   dataIndex: "sku",
                    //   title: "SKU",
                    //   width: 100,
                    //   ellipsis: true,
                    //   editable: {
                    //     type: "Input",
                    //     props: {
                    //       placeholder: "请输入",
                    //     },
                    //   },
                    // },
                    {
                      dataIndex: "dataIndex",
                      title: "表头Key",
                      width: "100",
                      editable: {
                        component: "Input",
                        props: {
                          placeholder: "请输入",
                        },
                      },
                    },
                    {
                      dataIndex: "title",
                      title: "表头名称",
                      width: "100",
                      editable: {
                        component: "Input",
                        props: {
                          placeholder: "请输入",
                        },
                      },
                    },
                    {
                      dataIndex: "valueType",
                      title: "展示类型",
                      width: "100",
                      editable: {
                        component: "Select",
                        props: {
                          style: { width: "100%" },
                          options: [
                            {
                              label: "图片",
                              value: "image",
                            },
                            {
                              label: "Html",
                              value: "html",
                            },
                          ],
                        },
                      },
                    },
                    {
                      dataIndex: "width",
                      title: "表头宽度",
                      width: "100",
                      editable: {
                        component: "InputNumber",
                        props: {
                          placeholder: "请输入",
                        },
                      },
                    },
                    {
                      dataIndex: "ellipsis",
                      title: "是否显示省略号",
                      width: "100",
                      editable: {
                        component: "Select",
                        props: {
                          style: { width: "100%" },
                          options: [
                            {
                              label: "是",
                              value: true,
                            },
                            {
                              label: "否",
                              value: false,
                            },
                          ],
                        },
                      },
                    },
                    {
                      dataIndex: ["editable", "type"],
                      title: "编辑组件类型",
                      width: "100",
                      editable: {
                        component: "Select",
                        props: {
                          style: { width: "100%" },
                          options: [
                            {
                              label: "输入框",
                              value: "Input",
                            },
                            {
                              label: "数字输入框",
                              value: "InputNumber",
                            },
                            {
                              label: "下拉选择",
                              value: "Select",
                            },
                            {
                              label: "上传图片",
                              value: "ImageUpload",
                            },
                          ],
                        },
                      },
                    },
                    {
                      dataIndex: ["editable", "searchApi"],
                      title: "Select远程搜索地址",
                      width: "100",
                      editable: {
                        component: "Input",
                        props: {
                          placeholder: "请输入",
                        },
                      },
                    },
                    {
                      dataIndex: ["editable", "props", "showSearch"],
                      title: "Select是否可以搜索",
                      width: "100",
                      editable: {
                        component: "Select",
                        props: {
                          style: { width: "100%" },
                          options: [
                            {
                              label: "是",
                              value: true,
                            },
                            {
                              label: "否",
                              value: false,
                            },
                          ],
                        },
                      },
                    },
                  ],
                },
              },
              {
                key: ["props", "isShowAddRow"],
                label: "是否显示添加行按钮",
                component: "CheckboxSingle",
                props: {
                  children: "显示",
                },
              },
              {
                key: ["props", "isShowSetDefault"],
                label: "是否显示设为默认按钮",
                component: "CheckboxSingle",
                props: {
                  children: "显示",
                },
              },
              {
                key: ["props", "rowSelection"],
                label: "是否显示行选择框",
                component: "CheckboxSingle",
                props: {
                  children: "显示",
                },
              },
            ],
          },
        },
      },
      {
        component: "Select",
        title: "下拉选择框",
        component_config: {
          // icon: <SelectOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: ["props", "placeholder"],
                label: "Placeholder",
                component: "Input",
                props: { placeholder: "请输入" },
              },
              {
                key: ["props", "options"],
                label: "选项数据",
                component: "EditTable",
                props: {
                  scroll: { x: 320 },
                  columns: [
                    {
                      dataIndex: "label",
                      title: "选项名称",
                      width: "80",
                      editable: {
                        type: "Input",
                        component: {
                          placeholder: "请输入",
                        },
                      },
                    },
                    {
                      dataIndex: "value",
                      title: "选项值",
                      width: "60",
                      editable: {
                        type: "Input",
                        component: {
                          placeholder: "请输入",
                        },
                      },
                    },
                  ],
                  operationsWidth: 60,
                },
              },
              {
                key: ["props", "showSearch"],
                label: "是否可以搜索",
                component: "CheckboxSingle",
                props: {
                  children: "显示",
                },
              },
              {
                key: ["props", "mode"],
                label: "多选或标签",
                component: "Select",
                props: {
                  options: [
                    {
                      label: "单选",
                      value: "",
                    },
                    {
                      label: "多选",
                      value: "multiple",
                    },
                    {
                      label: "tags",
                      value: "tags",
                    },
                  ],
                },
              },
              {
                key: "searchApi",
                label: "远程搜索api地址",
                component: "Input",
                props: { placeholder: "请输入" },
              },
            ],
          },
        },
      },
      {
        component: "Text",
        title: "Text组件",
        component_config: {
          // icon: <NumberOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "content",
                label: "显示内容",
                component: "Input",
                props: { placeholder: "请输入" },
              },
            ],
          },
        },
      },
      {
        component: "CheckboxSingle",
        title: "复选框",
        component_config: {
          // icon: <CheckSquareOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
            ],
          },
        },
      },
      {
        component: "Checkbox",
        title: "复选框组",
        component_config: {
          // icon: <CheckSquareOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
              },
              {
                key: ["props", "options"],
                label: "选项数据",
                component: "EditTable",
                props: {
                  scroll: { x: 320 },
                  operationsWidth: 60,
                  columns: [
                    {
                      dataIndex: "label",
                      title: "选项名称",
                      width: "80",
                      editable: {
                        type: "Input",
                        component: {
                          placeholder: "请输入",
                        },
                      },
                    },
                    {
                      dataIndex: "value",
                      title: "选项值",
                      width: "60",
                      editable: {
                        type: "Input",
                        component: {
                          placeholder: "请输入",
                        },
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
      },
      {
        component: "Radio",
        title: "单选框",
        component_config: {
          // icon: <CheckSquareOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: ["props", "options"],
                label: "选项数据",
                component: "EditTable",
                props: {
                  scroll: { x: 320 },
                  operationsWidth: 60,
                  columns: [
                    {
                      dataIndex: "label",
                      title: "选项名称",
                      width: "80",
                      editable: {
                        type: "Input",
                        component: {
                          placeholder: "请输入",
                        },
                      },
                    },
                    {
                      dataIndex: "value",
                      title: "选项值",
                      width: "60",
                      editable: {
                        type: "Input",
                        component: {
                          placeholder: "请输入",
                        },
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
      },
      {
        component: "Switch",
        title: "开关",
        component_config: {
          // icon: <CheckSquareOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
            ],
          },
        },
      },
      {
        component: "DatePicker",
        title: "日期选择器",
        component_config: {
          // icon: <CalendarOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
            ],
          },
        },
      },
      {
        component: "RangePicker",
        title: "日期范围选择",
        component_config: {
          // icon: <CalendarOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
            ],
          },
        },
      },
      {
        component: "Textarea",
        title: "多行文本框",
        component_config: {
          // icon: <CalendarOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: ["props", "rows"],
                label: "行数",
                component: "InputNumber",
                props: { placeholder: "请输入" },
              },
            ],
          },
        },
      },
      {
        component: "RichTextEditor",
        title: "富文本编辑器",
        component_config: {
          // icon: <CalendarOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: ["props", "placeholder"],
                label: "Placeholder",
                component: "Input",
                props: { placeholder: "请输入" },
              },
              {
                key: ["props", "minHeight"],
                label: "最小高度",
                component: "InputNumber",
                props: { placeholder: "请输入" },
              },
              {
                key: ["props", "uploadConfig", "uploadApi"],
                label: "图片上传地址",
                component: "Input",
                props: { placeholder: "请输入" },
              },
              {
                key: ["props", "uploadConfig", "data"],
                label: "额外携带参数(json)",
                component: "Input",
                props: { placeholder: "请输入" },
              },
            ],
          },
        },
      },
      {
        component: "ImageUpload",
        title: "图片上传",
        props: {
          data: { disk: "s3-static" },
        },
        component_config: {
          // icon: <CalendarOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: ["props", "action"],
                label: "上传api地址",
                component: "Input",
                props: { placeholder: "请输入" },
              },
              // {
              //   key: ["props", "listType"],
              //   label: "图片上传地址",
              //   component: "Select",
              //   props: {
              //     options: [
              //       { label: "图片", value: "picture" },
              //       { label: "图片卡片", value: "picture-card" },
              //     ],
              //   },
              // },
              {
                key: ["props", "data"],
                label: "额外携带参数(json)",
                component: "JSONEditorToParse",
              },
            ],
          },
        },
      },
      {
        component: "FileUpload",
        title: "文件上传",
        props: {
          data: { disk: "s3-static" },
        },
        component_config: {
          // icon: <CalendarOutlined />,
          isGroup: false,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: ["props", "action"],
                label: "上传api地址",
                component: "Input",
                props: { placeholder: "请输入" },
              },
              {
                key: ["props", "maxSize"],
                label: "文件大小",
                component: "Input",
                props: { placeholder: "例：5MB" },
              },
              {
                key: ["props", "maxCount"],
                label: "最大上传数",
                component: "InputNumber",
                props: { placeholder: "例：5" },
              },
              {
                key: ["props", "accept"],
                label: "文件类型",
                component: "Input",
                props: { placeholder: "例：.jpg, .jpeg, .png, .mp4, .pdf, .js" },
              },
              {
                key: ["props", "data"],
                label: "额外携带参数(json)",
                component: "JSONEditorToParse",
              },
            ],
          },
        },
      },
      {
        component: "CameraPhotoUpload",
        title: "拍照上传",
        props: {
          width: 200,
          height: 200,
          screenshot: {
            width: 1000,
            height: 1000,
          },
          action: "http://localhost:8081/rest/v1/upload",
          autoUpload: false,
          data: {
            disk: "s3-static",
          },
        },
        component_config: {
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "props",
                label: "Props",
                component: "JSONEditorToParse",
              },
            ],
          },
        },
      },
      {
        component: "Tree",
        title: "树组件",
        props: {
          treeData: [
            {
              key: "1",
              title: "节点1",
              children: [
                {
                  key: "1-1",
                  title: "节点1-1",
                },
                {
                  key: "1-2",
                  title: "节点1-2",
                  children: [
                    {
                      key: "1-2-1",
                      title: "节点1-2-1",
                    },
                  ],
                },
                {
                  key: "1-3",
                  title: "节点1-3",
                },
              ],
            },
          ],
        },
        component_config: {
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: ["props", "checkable"],
                label: "节点前添加 Checkbox 复选框",
                component: "CheckboxSingle",
              },
              {
                key: ["props", "defaultExpandAll"],
                label: "默认展开所有树节点",
                component: "CheckboxSingle",
              },
              {
                key: ["props", "defaultExpandedCheckedParents"],
                label: "默认展开选中节点父选项",
                component: "CheckboxSingle",
              },
              {
                key: ["props", "selectable	"],
                label: "是否可选中",
                component: "CheckboxSingle",
              },
              {
                key: ["props", "treeData"],
                label: "组件数据",
                component: "JSONEditorToParse",
              },
            ],
          },
        },
      },
      {
        component: "Transfer",
        title: "穿梭框",
        props: {
          dataSource: [
            {
              key: "1",
              title: "content1",
            },
            {
              key: "2",
              title: "content2",
            },
          ],
          listStyle: {
            width: 250,
            height: 300,
          },
        },
        component_config: {
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: ["props", "showSearch"],
                label: "是否可搜索",
                component: "CheckboxSingle",
              },
              {
                key: ["props", "dataSource"],
                label: "组件数据",
                component: "JSONEditorToParse",
              },
              {
                key: ["props", "listStyle"],
                label: "组件数据",
                component: "JSONEditorToParse",
              },
            ],
          },
        },
      },
      {
        component: "TreeSelect",
        title: "树形选择框",
        props: {
          treeData: [
            {
              value: "parent-1",
              title: "parent 1",
              children: [
                {
                  value: "parent-1-0",
                  title: "parent 1-0",
                  children: [
                    {
                      value: "leaf-1",
                      title: "leaf1",
                    },
                  ],
                },
              ],
            },
          ],
        },
        component_config: {
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "allowClear",
                label: "是否允许清除",
                component: "CheckboxSingle",
              },
              {
                key: "multiple",
                label: "是否支持多选",
                component: "CheckboxSingle",
              },
              {
                key: "showSearch",
                label: "是否支持搜索框",
                component: "CheckboxSingle",
              },
              {
                key: "treeDefaultExpandAll",
                label: "是否默认展开所有树节点",
                component: "CheckboxSingle",
              },
              {
                key: ["props", "treeData"],
                label: "Props",
                component: "JSONEditorToParse",
              },
            ],
          },
        },
      },
      {
        component: "Cascader",
        title: "级联选择器",
        props: {
          options: [
            {
              label: "陕西省",
              value: "shanxi",
              children: [
                {
                  label: "西安市",
                  value: "xian",
                  children: [
                    {
                      label: "雁塔区",
                      value: "yanta",
                    },
                    {
                      label: "未央区",
                      value: "weiyang",
                    },
                    {
                      label: "灞桥区",
                      value: "baqiao",
                    },
                    {
                      label: "莲湖区",
                      value: "lianhu",
                    },
                    {
                      label: "碑林区",
                      value: "beilin",
                    },
                  ],
                },
              ],
            },
          ],
        },
        component_config: {
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: "key",
                label: "Key",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: "label",
                label: "Label",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "请输入" }],
              },
              {
                key: ["props", "options"],
                label: "Props",
                component: "JSONEditorToParse",
              },
              {
                key: ["props", "placeholder"],
                label: "Placeholder",
                component: "Input",
              },
            ],
          },
        },
      },
    ],
  },
  {
    name: "容器组件",
    key: "container",
    children: [
      {
        component: "Row",
        title: "Row",
        props: {
          gutter: 16,
        },
        children: [
          // {
          //   component: "Col",
          //   props: {
          //     span: 12,
          //     offset: 0,
          //   },
          //   children: [],
          //   component_config: {
          //     hidden: true,
          //     isGroup: true,
          //   },
          // },
          // {
          //   component: "Col",
          //   props: {
          //     span: 12,
          //     offset: 0,
          //   },
          //   children: [],
          //   component_config: {
          //     hidden: true,
          //     isGroup: true,
          //   },
          // },
        ],
        component_config: {
          // icon: <LayoutOutlined />,
          isGroup: true,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: ["props", "gutter"],
                label: "栅格间隔",
                component: "InputNumber",
                props: { placeholder: "数字，如：16" },
              },
              {
                key: ["props", "justify"],
                label: "水平对齐方式",
                component: "Select",
                props: {
                  placeholder: "请选择",
                  options: [
                    { label: "起始对齐", value: "start" },
                    { label: "居中对齐", value: "center" },
                    { label: "结束对齐", value: "end" },
                    { label: "两端对齐", value: "space-between" },
                    { label: "环绕对齐", value: "space-around" },
                    { label: "均匀对齐", value: "space-evenly" },
                  ],
                },
              },
              {
                key: ["props", "align"],
                label: "垂直对齐方式",
                component: "Select",
                props: {
                  placeholder: "请选择",
                  options: [
                    { label: "顶部对齐", value: "top" },
                    { label: "居中对齐", value: "middle" },
                    { label: "底部对齐", value: "bottom" },
                    { label: "拉伸对齐", value: "stretch" },
                  ],
                },
              },
              {
                key: ["props", "wrap"],
                label: "是否自动换行",
                component: "CheckboxSingle",
              },
              {
                key: "children",
                label: "列配置",
                component: "RowColBuilder",
              },
            ],
          },
        },
      },
      {
        component: "Col",
        title: "Col",
        props: { span: 24, offset: 0 },
        children: [],
        component_config: {
          hidden: false,
          isGroup: true,
          props_config: {
            component: "Form",
            type: "json",
            props: {
              id: "propsForm",
              layout: "vertical",
            },
            formItems: [
              {
                key: ["props", "span"],
                label: "栅格占位格数(最多24份)",
                component: "InputNumber",
                props: { placeholder: "数字，如：8", max: 24, style: { width: "100%" } },
              },
              {
                key: ["props", "offset"],
                label: "栅格左侧的间隔格数，间隔内不可以有栅格",
                component: "InputNumber",
                props: { placeholder: "数字，如：8", style: { width: "100%" } },
              },
            ],
          },
        },
        // preview: (props) => (
        //   <div style={{ background: "#f0f0f0", padding: "10px", textAlign: "center" }}>
        //     列 (span: {props.span}, offset: {props.offset})
        //   </div>
        // ),
      },
      // {
      //   component: "form",
      //   title: "表单",
      //   // icon: <FormOutlined />,
      // },
      // {
      //   component: "card",
      //   title: "卡片",
      //   // icon: <FileTextOutlined />,
      //   preview: (props) => (
      //     <Card title={props.title} bordered={props.bordered} size={props.size}>
      //       <div style={{ padding: "20px 0", textAlign: "center", color: "#999" }}>卡片内容区域</div>
      //     </Card>
      //   ),
      // },
      // {
      //   component: "Row",
      //   title: "栅格布局",
      //   component_config: {
      //     // icon: <LayoutOutlined />,
      //     isGroup: true,
      //     props_config: {
      //       component: "Form",
      //       type: "json",
      //       props: {
      //         id: "propsForm",
      //         layout: "vertical",
      //       },
      //       formItems: [
      //         {
      //           key: ["props", "gutter"],
      //           label: "栅格间隔",
      //           component: "InputNumber",
      //           props: { placeholder: "数字，如：16" },
      //         },
      //         {
      //           key: ["props", "justify"],
      //           label: "水平对齐方式",
      //           component: "Select",
      //           props: {
      //             placeholder: "请选择",
      //             options: [
      //               { label: "起始对齐", value: "start" },
      //               { label: "居中对齐", value: "center" },
      //               { label: "结束对齐", value: "end" },
      //               { label: "两端对齐", value: "space-between" },
      //               { label: "环绕对齐", value: "space-around" },
      //               { label: "均匀对齐", value: "space-evenly" },
      //             ],
      //           },
      //         },
      //         {
      //           key: ["props", "align"],
      //           label: "垂直对齐方式",
      //           component: "Select",
      //           props: {
      //             placeholder: "请选择",
      //             options: [
      //               { label: "顶部对齐", value: "top" },
      //               { label: "居中对齐", value: "middle" },
      //               { label: "底部对齐", value: "bottom" },
      //               { label: "拉伸对齐", value: "stretch" },
      //             ],
      //           },
      //         },
      //         {
      //           key: ["props", "wrap"],
      //           label: "是否自动换行",
      //           component: "CheckboxSingle",
      //         },
      //         {
      //           key: "children",
      //           label: "列配置",
      //           component: "RowColBuilder",
      //         },
      //       ],
      //     },
      //   },
      // },

      // {
      //   component: "space",
      //   title: "间距",
      //   // icon: <AppstoreOutlined />,
      //   preview: (props) => (
      //     <Space direction={props.direction} size={props.size} align={props.align}>
      //       <Button>按钮 1</Button>
      //       <Button>按钮 2</Button>
      //       <Button>按钮 3</Button>
      //     </Space>
      //   ),
      // },
      // {
      //   component: "collapse",
      //   title: "折叠面板",
      //   // icon: <BarsOutlined />,
      //   preview: (props) => (
      //     <Collapse
      //       defaultActiveKey={props.defaultActiveKey}
      //       bordered={props.bordered}
      //       expandIconPosition={props.expandIconPosition}
      //     >
      //       {props.items.map((item) => (
      //         <Panel key={item.key} header={item.label}>
      //           {item.children}
      //         </Panel>
      //       ))}
      //     </Collapse>
      //   ),
      // },
      // {
      //   component: "tabs",
      //   title: "标签页",
      //   // icon: <AppstoreOutlined />,
      //   preview: (props) => (
      //     <Tabs defaultActiveKey={props.defaultActiveKey} type={props.type} tabPosition={props.tabPosition}>
      //       {props.items.map((item) => (
      //         <Tabs.TabPane key={item.key} tab={item.label}>
      //           {item.children}
      //         </Tabs.TabPane>
      //       ))}
      //     </Tabs>
      //   ),
      // },
    ],
  },
  // {
  //   name: "展示组件",
  //   key: "display",
  //   children: [
  //     {
  //       component: "table",
  //       title: "表格",
  //       // icon: <TableOutlined />,
  //       preview: (props) => (
  //         <div>
  //           <h3>{props.title}</h3>
  //           <Table columns={props.columns} dataSource={props.dataSource} pagination={false} size="small" />
  //         </div>
  //       ),
  //     },
  //   ],
  // },
];

export const componentMap = {};

// 将组件数组转换为映射对象
componentCategories.forEach((category) => {
  category.children.forEach((component) => {
    componentMap[component.component] = component;
  });
});
