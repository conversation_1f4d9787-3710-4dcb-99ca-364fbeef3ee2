import { fabric } from "fabric";
import { useEffect } from "react";
import debounce from "lodash.debounce";
import Utils from "@/utils";

const defaultObjectProps = {
  stroke: "#000",
  strokeWidth: 1,
  strokeDashArray: [30],
  borderScaleFactor: 2,
  borderColor: "rgba(79, 128, 255, 0.8)",
};

function useRenderFabricObjects({ canvas, objects, selectedIds }) {
  useEffect(() => {
    function setObjectProps({ object, data }) {
      let extraProps;
      if (data.extraData.lock) {
        canvas.discardActiveObject();
        extraProps = {
          stroke: "#f00",
          selectable: false,
          lockMovementX: true,
          lockMovementY: true,
          lockRotation: true,
          lockScalingFlip: true,
          lockScalingX: true,
          lockScalingY: true,
          lockUniScaling: true,
        };
      } else {
        extraProps = {
          stroke: defaultObjectProps.stroke,
          selectable: true,
          lockMovementX: false,
          lockMovementY: false,
          lockRotation: false,
          lockScalingFlip: false,
          lockScalingX: false,
          lockScalingY: false,
          lockUniScaling: false,
        };
      }
      object.set({ ...data.objectProps, ...extraProps });
      object.objectProps = data.objectProps;
      object.extraData = data.extraData;
    }

    const globalData = {};
    let selectedObjects = [];
    const debounceFireEvent = debounce((name, event) => {
      canvas.fire(name, { ...event, objects: selectedObjects });
    }, 0);

    if (canvas && objects.length > 0) {
      const fabricObjects = {};
      let discardActiveObjectId = false;
      canvas.getObjects().forEach((object) => {
        fabricObjects[object.extraData.id] = object;
      });
      objects.forEach((data) => {
        const objectId = data.extraData.id;
        const object = fabricObjects[objectId];
        if (!object) {
          const object = new fabric.Image();
          object.objectProps = data.objectProps;
          object.extraData = data.extraData;
          object.setSrc(data.objectProps.src, () => {
            canvas.renderAll();
          });
          setObjectProps({ object, data: { ...data, objectProps: { ...defaultObjectProps, ...data.objectProps } } });
          object.moveTo(data.objectProps.zIndex);
          canvas.add(object);
          if (data.extraData.select) {
            delete data.extraData.select;
            canvas._setActiveObject(object);
            canvas.renderAll();
          }
          const handleObjectSelected = function (event) {
            selectedObjects.push(object);
            Utils.removeDuplicates(selectedObjects, (item) => item.extraData.id);
            debounceFireEvent("object:selected", event);
          };
          globalData.handleObjectSelected = handleObjectSelected;
          object.on("selected", handleObjectSelected);
          const handleObjectDeselected = function (event) {
            selectedObjects = selectedObjects.filter((item) => item.extraData.id !== object.extraData.id);
            debounceFireEvent("object:selected", event);
          };
          globalData.handleObjectDeselected = handleObjectDeselected;
          object.on("deselected", handleObjectDeselected);
        } else {
          ["visible", "selectable"].forEach((key) => {
            if (object[key] && !data.objectProps[key]) {
              discardActiveObjectId = objectId;
            }
          });
          setObjectProps({ object, data });
        }
      });
      const activeObject = canvas.getActiveObject();
      if (activeObject && activeObject.extraData?.id === discardActiveObjectId) {
        canvas.discardActiveObject();
      }
      canvas.renderAll();
    }

    return function () {
      canvas?.off("selected", globalData.handleObjectSelected);
      canvas?.off("deselected", globalData.handleObjectDeselected);
    };
  }, [canvas, objects]);

  useEffect(() => {
    if (canvas) {
      const selectedObjects = canvas
        .getObjects()
        .filter((item) => selectedIds.includes(item.extraData.id) && !item.extraData.lock);
      if (selectedObjects.length > 0) {
        if (selectedObjects.length > 1) {
          canvas._discardActiveObject();
          const activeSelection = new fabric.ActiveSelection(selectedObjects, { canvas });
          canvas._setActiveObject(activeSelection);
        } else {
          canvas._setActiveObject(selectedObjects[0]);
        }
      } else {
        canvas._discardActiveObject();
      }
      canvas.renderAll();
    }
  }, [canvas, selectedIds]);
}

export default useRenderFabricObjects;
