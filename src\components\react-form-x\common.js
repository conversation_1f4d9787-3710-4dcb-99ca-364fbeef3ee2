export function getValueFromEvent({ event, name, value = {} }) {
  return typeof value === "object" && Object.hasOwnProperty.call(value, name) ? value[name] : event?.target?.value;
}

export function setFormFieldValue({ formValues, keyPath, name, value, setValue }) {
  if (formValues) {
    let cursor = formValues;
    for (let i = 0; i < keyPath?.length; i++) {
      const key = keyPath[i];
      const nextKey = keyPath[i + 1];
      if (!cursor[key]) {
        cursor[key] = typeof nextKey === "number" ? [] : {};
      }
      cursor = cursor[key];
    }
    if (cursor) {
      if (setValue) {
        setValue({ cursor });
      } else if (name) {
        cursor[name] = value;
      }
    }
  }
}

export function getObjectCursor({ object, keyPath }) {
  let cursor = object;
  if (object) {
    for (let i = 0; i < keyPath?.length; i++) {
      const key = keyPath[i];
      cursor = cursor?.[key];
    }
  }
  return cursor;
}

export function getFormFieldValue({ formValues, keyPath, name }) {
  const cursor = getObjectCursor({ object: formValues, keyPath });
  return cursor?.[name];
}

export const formInstances = {};

export function getFormRef({ formId }) {
  return { current: formInstances[formId] };
}

export const formControlsProps = {};

export function getFormControlProps({ formControlId }) {
  return formControlsProps[formControlId] || {};
}

export const updatedForms = {};

// export function debounce(callback, delay = 300) {
//   let timeoutId;
//   return (...args) => {
//     if (typeof timeoutId === "number") {
//       clearTimeout(timeoutId);
//     }
//     timeoutId = setTimeout(() => {
//       callback(...args);
//     }, delay);
//   };
// }

export function debounce(func, wait, options) {
  let lastArgs,
    lastThis,
    maxWait,
    result,
    timerId,
    lastCallTime,
    lastInvokeTime = 0,
    leading = false,
    maxing = false,
    trailing = true;

  if (typeof func != "function") {
    throw new TypeError("func is not a function");
  }
  if (typeof wait !== "number") {
    throw new TypeError("wait is not a function");
  }

  const isObject = (data) => typeof data === "object";
  const nativeMax = Math.max;
  const nativeMin = Math.min;
  const toNumber = (value) => +value;
  const now = () => Date.now();

  if (isObject(options)) {
    leading = !!options.leading;
    maxing = "maxWait" in options;
    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;
    trailing = "trailing" in options ? !!options.trailing : trailing;
  }

  function invokeFunc(time) {
    const args = lastArgs,
      thisArg = lastThis;

    lastArgs = lastThis = undefined;
    lastInvokeTime = time;
    result = func.apply(thisArg, args);
    return result;
  }

  function leadingEdge(time) {
    // Reset any `maxWait` timer.
    lastInvokeTime = time;
    // Start the timer for the trailing edge.
    timerId = setTimeout(timerExpired, wait);
    // Invoke the leading edge.
    return leading ? invokeFunc(time) : result;
  }

  function remainingWait(time) {
    const timeSinceLastCall = time - lastCallTime,
      timeSinceLastInvoke = time - lastInvokeTime,
      result = wait - timeSinceLastCall;

    return maxing ? nativeMin(result, maxWait - timeSinceLastInvoke) : result;
  }

  function shouldInvoke(time) {
    const timeSinceLastCall = time - lastCallTime,
      timeSinceLastInvoke = time - lastInvokeTime;

    // Either this is the first call, activity has stopped and we're at the
    // trailing edge, the system time has gone backwards and we're treating
    // it as the trailing edge, or we've hit the `maxWait` limit.
    return (
      lastCallTime === undefined ||
      timeSinceLastCall >= wait ||
      timeSinceLastCall < 0 ||
      (maxing && timeSinceLastInvoke >= maxWait)
    );
  }

  function timerExpired() {
    const time = now();
    if (shouldInvoke(time)) {
      return trailingEdge(time);
    }
    // Restart the timer.
    timerId = setTimeout(timerExpired, remainingWait(time));
  }

  function trailingEdge(time) {
    timerId = undefined;

    // Only invoke if we have `lastArgs` which means `func` has been
    // debounced at least once.
    if (trailing && lastArgs) {
      return invokeFunc(time);
    }
    lastArgs = lastThis = undefined;
    return result;
  }

  function cancel() {
    if (timerId !== undefined) {
      clearTimeout(timerId);
    }
    lastInvokeTime = 0;
    lastArgs = lastCallTime = lastThis = timerId = undefined;
  }

  function flush() {
    return timerId === undefined ? result : trailingEdge(now());
  }

  function debounced() {
    const time = now(),
      isInvoking = shouldInvoke(time);

    lastArgs = arguments;
    lastThis = this;
    lastCallTime = time;

    if (isInvoking) {
      if (timerId === undefined) {
        return leadingEdge(lastCallTime);
      }
      if (maxing) {
        // Handle invocations in a tight loop.
        timerId = setTimeout(timerExpired, wait);
        return invokeFunc(lastCallTime);
      }
    }
    if (timerId === undefined) {
      timerId = setTimeout(timerExpired, wait);
    }
    return result;
  }
  debounced.cancel = cancel;
  debounced.flush = flush;
  return debounced;
}

export function cloneDeep(data) {
  return JSON.parse(JSON.stringify(data));
}

export function validateRequired({ value, rule }) {
  return new Promise((resolve, reject) => {
    if (!value) {
      reject({ message: rule.message || "This field is required!" });
    } else {
      resolve();
    }
  });
}

export function findField({ fields, name, keyPath }) {
  return fields?.find((field) => {
    if (keyPath?.length > 0) {
      return field.name === name && JSON.stringify(field.keyPath) === JSON.stringify(keyPath);
    } else {
      return field.name === name;
    }
  });
}

export function classNames(...args) {
  const classList = [];
  args.forEach((arg) => {
    if (typeof arg === "string") {
      const key = arg.trim();
      if (key) {
        classList.push(key);
      }
    } else if (typeof arg === "object") {
      Object.entries(arg).forEach(([key, value]) => {
        key = key.trim();
        if (key && value) {
          classList.push(key);
        }
      });
    }
  });
  return classList.join(" ");
}

export function isServer() {
  return typeof window === "undefined";
}

export function uniqueId({ length = 40 } = {}) {
  let str = "";
  while (str.length < length) {
    str += (+Math.random().toString().substring(2)).toString(16);
  }
  return str.substring(0, length);
}
