import { Button, Modal } from "antd";
import styles from "./index.module.scss";

import { useState, useCallback, useEffect, useMemo } from "react";
import debounce from "lodash.debounce";
import cloneDeep from "lodash.clonedeep";

import Utils from "utils";

import Loading from "@/components/common/Loading";

function StarMapPreview(props) {
  const { formRef, mode = "preview" } = props;
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [celestialConfig, setCelestialConfig] = useState({});
  const [initializing, setInitializing] = useState(false);

  const containerId = useMemo(() => `celestial-map`, []);

  function handleCancel() {
    setModalVisible(false);
    window.Celestial.clear();
  }

  function handlePreview() {
    setModalVisible(true);
  }

  // 更新星图配置
  const updateCelestial = useCallback(
    ({ celestialConfig }) => {
      if (!window.Celestial) return;
      const celestial = celestialConfig?.celestial;
      const isProduction = mode === "production";
      const config = cloneDeep(isProduction ? celestial.production : celestial.preview);

      if (isProduction) {
        config.width = config.height = celestial.production.width;
      } else {
        config.width = config.height = 400;
      }

      config.stars.colors = celestial.preview.stars.colors;
      config.stars.style = celestial.preview.stars?.style || { fill: "#fff" };
      config.constellations.lineStyle.stroke = celestial.preview.constellations.lineStyle.stroke;
      config.background.stroke = celestial.preview.background.stroke;

      window.Celestial.display(config);
      setLoading(false);
      setInitializing(false);
    },
    [mode]
  );

  const debouncedUpdateCelestial = useMemo(
    () => debounce((celestialConfig) => updateCelestial({ celestialConfig }), 1000),
    [updateCelestial]
  );

  // 初始化星图
  const initCelestial = useCallback(async () => {
    const celestialConfig = formRef.current?.getFieldsValue();
    setCelestialConfig(celestialConfig);

    if (window.Celestial && document.querySelector(`#${containerId}`).innerHTML) {
      debouncedUpdateCelestial(celestialConfig);
      return;
    }

    setInitializing(true);
    setLoading(true);

    try {
      await Utils.appendScript("https://static.bizseas.com/celestial/js/lib/d3.min.js");
      await Promise.all([
        Utils.appendScript("https://static.bizseas.com/celestial/js/lib/d3.geo.projection.min.js"),
        Utils.appendScript("https://static.bizseas.com/celestial/js/lib/d3.geo.zoom.js"),
        Utils.appendScript("https://static.bizseas.com/celestial/js/lib/d3-queue.js"),
        Utils.appendScript("https://static.bizseas.com/celestial/js/lib/geolocator.js"),
        Utils.appendScript("https://static.bizseas.com/celestial/js/lib/topojson.js"),
      ]);
      await Promise.all([
        Utils.appendCss("https://static.bizseas.com/celestial/js/celestial.css"),
        Utils.appendScript("https://static.bizseas.com/celestial/js/celestial.js"),
      ]);

      // 初始化配置
      const config = cloneDeep(celestialConfig.celestial.preview);
      const size = 400;
      const defaultConfig = {
        width: size,
        height: size,
        projection: "airy",
        datapath: "https://static.bizseas.com/celestial/data",
        controls: false,
        interactive: false,
        form: false,
        // center: center,
        disableAnimations: true,
        // onAnimationStart: onAnimationStart,
        // onAnimationEnd: onAnimationEnd,
        container: containerId,
        stars: {
          show: true,
          limit: 4.6,
          colors: false,
          designation: false,
          size: 4,
          ...config.stars,
        },
        dsos: { show: false },
        constellations: {
          names: false,
          lineStyle: {
            stroke: "#fff",
            width: 2,
            opacity: 1,
          },
          ...config.constellations,
        },
        mw: { show: false },
        lines: {
          graticule: { show: false },
          equatorial: { show: false },
          ecliptic: { show: false },
          galactic: { show: false },
          supergalactic: { show: false },
        },
        background: { stroke: "#fff", width: 0.5, ...config.background },
        onReady: () => {
          setLoading(false);
          setInitializing(false);
        },
      };
      window.Celestial.display(defaultConfig);
      debouncedUpdateCelestial(celestialConfig);
    } catch (error) {
      console.error("Failed to initialize celestial map:", error);
      setLoading(false);
      setInitializing(false);
    }
  }, [formRef, debouncedUpdateCelestial, containerId]);

  useEffect(() => {
    if (modalVisible) {
      setTimeout(() => {
        initCelestial();
      });
    }
  }, [modalVisible, initCelestial]);

  return (
    <div className={styles.container}>
      <Button type="link" onClick={handlePreview}>
        预览
      </Button>
      <Modal
        className={styles.modal}
        title="星图预览"
        width={860}
        open={modalVisible}
        onCancel={handleCancel}
        maskClosable={false}
        destroyOnClose={true}
        closable={!initializing}
        keyboard={!initializing}
        footer={null}
      >
        <div
          className={styles.starMapPreview}
          style={{
            width: "100%",
            height: "100%",
            overflow: "auto",
            backgroundColor: celestialConfig?.container_style?.backgroundColor || "#000",
            display: "flex",
            alignItems: mode === "preview" ? "center" : "flex-start",
            justifyContent: mode === "preview" ? "center" : "flex-start",
          }}
        >
          <div id={containerId} className={styles.celestialMap} />
          {loading && <Loading className={styles.loading} loading={loading} />}
        </div>
      </Modal>
    </div>
  );
}

export default StarMapPreview;
