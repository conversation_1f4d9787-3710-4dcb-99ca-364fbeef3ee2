.container {
  width: 100%;
  height: 100%;
  padding: 10px;

  [class~="ant-table-content"] {
    overflow-x: auto !important;
    transform: scaleY(-1);

    table {
      transform: scaleY(-1);
    }
  }

  .panel {
    width: 100%;
    height: 100%;
    padding: 10px;
    border: 1px solid #cac2c2;
    background-color: #fff;

    .title {
      color: #949494;
      text-align: center;
      margin-bottom: 10px;
    }

    .actions {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 20px;
    }

    .scan {
      display: flex;
      align-items: center;
      gap: 20px;

      .scanNum {
        font-size: 30px;
        font-weight: 700;
        color: red;
      }
    }
  }
}
