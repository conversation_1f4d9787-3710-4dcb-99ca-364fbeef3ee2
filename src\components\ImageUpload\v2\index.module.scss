.imageUpload {
  position: relative;
  display: flex;

  [class~="ant-upload-list"] {
    > * {
      cursor: grab;
      height: 100%;

      &:active {
        cursor: grabbing;
      }
    }
  }

  [class~="ant-upload-list-picture-card"] {
    [class~="ant-upload-list-item"] {
      padding: 4px;
    }
  }

  .imageListItem {
    border: 1px solid;
  }

  [class~="ant-upload-list-item-container"],
  [class~="ant-upload-list-item"],
  [class~="ant-upload-select"],
  .uploadButton {
    width: var(--upload-image-v2-width) !important;
    height: var(--upload-image-v2-height) !important;
  }

  img[class~="ant-upload-list-item-image"] {
    object-fit: contain;
  }

  .uploadButton {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  &.disabled {
    [class~="ant-upload-list-item"] {
      color: rgba(0, 0, 0, 0.25);
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}
