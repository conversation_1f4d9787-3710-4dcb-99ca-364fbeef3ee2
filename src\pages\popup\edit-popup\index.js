import styles from "./index.module.scss";
import Loading from "components/common/Loading";
import useSWR from "swr";
import Fetchers from "fetchers";
import Helpers from "helpers";
import { useLocation, useParams, useNavigate } from "react-router-dom";
import Breadcrumbs from "components/common/Breadcrumbs";
import { Button, Card, Steps, Col, Row, Tabs, Form } from "antd";
import JSONForm from "components/common/JSONForm";
import { useRef, useState, useEffect } from "react";
import classNames from "classnames";
import Utils from "utils";
import TemplatePreview from "./components/template-preview";
import hooks from "./hooks";

function EditPopup(props) {
  const location = useLocation();
  const params = useParams();
  const navigate = useNavigate();
  const url_key = Helpers.getUrlKey({ location, params });
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [device, setDevice] = useState("pc");
  const [saveLoading, setSaveLoading] = useState(false);
  const [formValues, setFormValues] = useState({ content: {}, template_info: {} });
  const paramsRef = useRef({ isSubmit: false, formValues });

  paramsRef.current = {
    ...paramsRef.current,
  };

  const { data } = useSWR(url_key, async () => {
    try {
      setLoading(true);
      const result = await Fetchers.getEditPopupData({ url_key }).then((res) => res.data?.data);
      initialFormValues(result);
      return result;
    } finally {
      setLoading(false);
    }
  });

  const deviceItems = [
    {
      label: "PC",
      key: "pc",
      children: renderTemplatePreview("pc"),
    },
    {
      label: "M",
      key: "mobile",
      children: renderTemplatePreview("mobile"),
    },
  ];

  function handleBackClick() {
    navigate(-1);
  }

  function initialFormValues(data) {
    if (data?.steps?.length > 0) {
      let newFormValues = { content: {}, template_info: {} };
      data?.steps?.forEach((item) => {
        if (item?.forms) {
          item?.forms?.forEach((formItem) => {
            if (formItem?.name) {
              newFormValues = getFormValues({ item, formItem, values: formItem?.props?.initialValues });
              paramsRef.current.formValues = newFormValues;
            }
          });
        }
      });
      setFormValues(newFormValues);
    }
  }

  async function handleSubmit(values) {
    try {
      setSaveLoading(true);
      await Fetchers.saveEditPopupData({ url_key, data: values }).then((res) => res.data?.data);
      setSaveLoading(false);
    } catch (error) {
      setSaveLoading(false);
    }
  }

  function getFormValues({ item, formItem, values }) {
    const { formValues } = paramsRef.current;
    let newFormValues = { ...formValues };
    const { name: itemName } = item;
    const { name: formItemName } = formItem;
    if (Object.hasOwnProperty.call(formValues, itemName)) {
      const itemValues = formValues[itemName];
      const mergedFormItemValues = { ...itemValues[formItemName], ...values };

      newFormValues = {
        ...formValues,
        [itemName]: {
          ...itemValues,
          [formItemName]: mergedFormItemValues,
        },
      };
    }

    return newFormValues;
  }

  function handleFinish({ item, formItem, values }) {
    if (values) {
      const newFormValues = getFormValues({ item, formItem, values });
      paramsRef.current.formValues = newFormValues;
      if (paramsRef.current.isSubmit) {
        handleSubmit(newFormValues);
        paramsRef.current.isSubmit = false;
      }
      setFormValues(newFormValues);
    }
  }

  async function validateFormsFields({ items }) {
    try {
      return await Promise.all(items?.map((form) => form?.ref?.validateFields()));
    } catch (error) {
      return false;
    }
  }

  async function submitForms({ items }) {
    try {
      return await Promise.all(items?.map((form) => form?.ref?.submit()));
    } catch (error) {
      return false;
    }
  }

  async function handleSubmitForms({ items }) {
    const validateResult = await validateFormsFields({ items });
    if (validateResult) {
      return await submitForms({ items });
    }
    return false;
  }

  function filterDeviceData(obj, device) {
    for (let key in obj) {
      const value = obj[key];
      if (!value) continue;
      const keys = Object.keys(obj[key]);
      if (keys && keys.length === 0) continue;

      if (keys.includes(device)) {
        if (typeof value?.[device] === "string") {
          obj[key] = Utils.JSON.parse(value?.[device]);
        }
      } else if (Object.prototype.toString.call(value) === "[object Object]") {
        filterDeviceData(value, device);
      }
    }
  }

  function filterAndReturnDeviceData(data) {
    const newData = Utils.cloneDeep(data);
    filterDeviceData(newData.content, device);
    return newData;
  }

  function onTabsChange(value) {
    setDevice(value);
  }

  function renderTemplatePreview(device) {
    if (!data) return null;
    return (
      <div className={styles.preview}>
        <TemplatePreview
          type={data?.type}
          current={currentStep}
          device={device}
          data={filterAndReturnDeviceData(formValues)}
          {...hooks}
        />
      </div>
    );
  }

  function renderForm(formItem, item, j) {
    return (
      <JSONForm
        key={`${formItem.name}-${JSON.stringify(formItem?.props?.initialValues)}`}
        ref={(e) => {
          item.forms[j].ref = e;
        }}
        data={{
          ...formItem,
          props: {
            ...formItem.props,
            initialValues: formValues[item.name]?.[formItem.name] || formItem?.props?.initialValues,
          },
        }}
        onFinish={(values) => {
          handleFinish({ item, formItem, values });
        }}
      />
    );
  }

  return (
    <div className={styles.container}>
      <div className="page-header">
        <div>
          <Breadcrumbs data={data?.breadcrumbs}></Breadcrumbs>
        </div>
        <div className="page-header-actions">
          <Button type="default" onClick={handleBackClick}>
            返回
          </Button>
        </div>
      </div>
      <div className={styles.contentWrapper}>
        <Loading loading={loading}>
          <Steps
            type="navigation"
            size="small"
            current={currentStep}
            className={classNames("site-navigation-steps", styles.steps)}
            items={data?.steps}
          />
          <div className={styles.content}>
            <Row gutter={26}>
              <Col span={10}>
                <div className={styles.stepsWrapper}>
                  {data?.steps?.map((item, index) => (
                    <div
                      className={classNames(styles.contentItem, { [styles.active]: index === currentStep })}
                      key={index}
                    >
                      <Card className={styles.formCard}>
                        {item?.forms?.map((formItem, j) => (
                          <div className={styles.formItem} key={j}>
                            <div className={styles.formItemTitle}>{formItem?.title}</div>
                            {renderForm(formItem, item, j)}
                          </div>
                        ))}
                        <div className={styles.actions}>
                          {currentStep > 0 && (
                            <Button
                              onClick={() => {
                                setCurrentStep((currentStep) => currentStep - 1);
                              }}
                            >
                              上一步
                            </Button>
                          )}

                          {currentStep > 0 && (
                            <Button onClick={() => handleSubmitForms({ items: item?.forms })}>预览</Button>
                          )}

                          {currentStep < data?.steps?.length - 1 && (
                            <Button
                              type="primary"
                              onClick={async () => {
                                try {
                                  const result = await handleSubmitForms({ items: item?.forms });
                                  result && setCurrentStep((currentStep) => currentStep + 1);
                                } catch (error) {}
                              }}
                            >
                              下一步
                            </Button>
                          )}

                          {currentStep === data?.steps?.length - 1 && (
                            <Button
                              type="primary"
                              onClick={async () => {
                                paramsRef.current.isSubmit = true;
                                await handleSubmitForms({ items: item?.forms });
                              }}
                              loading={saveLoading}
                            >
                              保存
                            </Button>
                          )}
                        </div>
                      </Card>
                    </div>
                  ))}
                </div>
              </Col>
              <Col span={14}>
                <Card className={styles.previewCard}>
                  <Tabs defaultActiveKey={device} type="card" onChange={onTabsChange} items={deviceItems} />
                </Card>
              </Col>
            </Row>
          </div>
        </Loading>
      </div>
    </div>
  );
}

export default EditPopup;
