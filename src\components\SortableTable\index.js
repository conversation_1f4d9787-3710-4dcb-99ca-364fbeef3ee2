import React from "react";
import { Dnd<PERSON>ontext, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Table } from "antd";
import PropTypes from "prop-types";

const Row = (props) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: props["data-row-key"],
  });
  const style = {
    ...props.style,
    transform: CSS.Transform.toString(
      transform && {
        ...transform,
        scaleY: 1,
      }
    ),
    transition,
    cursor: "grab",
    ...(isDragging
      ? {
          position: "relative",
          zIndex: 10,
        }
      : {}),
  };
  return <tr {...props} ref={setNodeRef} style={style} {...attributes} {...listeners} />;
};

function SortableTable(props) {
  const { dataSource, setDataSource, onSortChange, ...otherProps } = props;

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        // https://docs.dndkit.com/api-documentation/sensors/pointer#activation-constraints
        distance: 1,
      },
    })
  );

  const onDragEnd = ({ active, over }) => {
    if (active.id !== over?.id) {
      setDataSource((prevDataSource) => {
        const activeIndex = prevDataSource.findIndex((item) => item.id === active?.id);
        const overIndex = prevDataSource.findIndex((item) => item.id === over?.id);
        const dataSource = arrayMove(prevDataSource, activeIndex, overIndex);
        onSortChange?.({ active, over, dataSource });
        return dataSource;
      });
    }
  };

  return (
    <DndContext sensors={sensors} modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
      <SortableContext items={dataSource?.map((item) => item.id) || []} strategy={verticalListSortingStrategy}>
        <Table
          {...otherProps}
          components={{
            body: {
              row: Row,
            },
          }}
          dataSource={dataSource}
        />
      </SortableContext>
    </DndContext>
  );
}

SortableTable.propTypes = {
  columns: PropTypes.array,
  dataSource: PropTypes.array,
  setDataSource: PropTypes.func,
  rowKey: PropTypes.string,
  bordered: PropTypes.bool,
  onChange: PropTypes.func,
  onSortChange: PropTypes.func,
};

export default SortableTable;
