import styles from "./index.module.scss";
import { Input, Divider, Upload } from "antd";
import JSONForm from "components/common/JSONForm";
import { useEffect, useRef, useState } from "react";
import Enums from "enums";
import Helper from "helpers";
import SelectWithPreview from "components/business/ticket/components/SelectWithPreview";
import RichTextEditor from "components/common/RichTextEditor";
import FileUpload from "components/common/FileUpload";
import { renderActions } from "../../../vars";
import request from "@/fetchers/request";

function Form(props) {
  const { data, ticketId, ...otherProps } = props;
  const [selectWithPreviewOptions, setSelectWithPreviewOptions] = useState([]);
  const formRef = useRef();
  const richTextEditorRef = useRef();
  const paramsRef = useRef();

  paramsRef.current = {
    ...paramsRef.current,
    data,
  };

  function handleBeforeUpload(file) {
    const isLt5M = file.size / 1024 / 1024 < 5;
    // 只能上传图片、PDF文件或视频
    const isImageOrPdfOrVideo =
      file.type.startsWith("image/") || file.type === "application/pdf" || file.type.startsWith("video/");
    if (!isLt5M) {
      Helper.openMessage({ type: "error", content: "文件大小不能超过5M" });
      return Upload.LIST_IGNORE;
    }
    if (!isImageOrPdfOrVideo) {
      Helper.openMessage({ type: "error", content: "只能上传图片、PDF文件或视频" });
      return Upload.LIST_IGNORE;
    }

    return true;
  }

  async function getSelectTemplateInitialOptions(item) {
    const { data } = paramsRef.current;
    const { initialValues } = data.props;
    if (initialValues?.target_lang) {
      const result = await request(item?.cascader?.searchApi, {
        method: "GET",
        params: { query: initialValues.target_lang },
      }).then((res) => res.data);
      return result?.data;
    }
    return [];
  }

  function renderExtraComponent({ item, cascaderOptions }) {
    const { component, key: name } = item;
    const validate = () => formRef.current?.validateFields([name]);
    const setFieldValue = (value) => formRef.current?.setFieldValue(name, value);

    if (component === "SelectTemplate") {
      if (cascaderOptions?.[name]) {
        item.props.options = cascaderOptions[name];
      }
      return (
        <SelectWithPreview
          richTextEditorRef={richTextEditorRef}
          item={item}
          ticketId={ticketId}
          form={formRef.current}
          options={item.props.options?.length ? item.props.options : selectWithPreviewOptions}
        />
      );
    } else if (component === "RichTextEditorTemplate") {
      return <RichTextEditor ref={richTextEditorRef} {...item.props} validate={validate} />;
    } else if (component === Enums.Components.FileUpload) {
      return <FileUpload {...item.props} beforeUpload={handleBeforeUpload} setFieldValue={setFieldValue} />;
    }

    return null;
  }

  useEffect(() => {
    (async () => {
      const { data } = paramsRef.current;
      const item = data.formItems.filter((item) => item.key === "target_lang")[0];
      if (item) {
        const options = await getSelectTemplateInitialOptions(item);
        setSelectWithPreviewOptions(options);
      }
    })();
  }, []);

  return (
    <>
      <JSONForm ref={formRef} {...otherProps} data={data} renderExtraComponent={renderExtraComponent}></JSONForm>
      {data?.actions?.length && (
        <>
          <Divider />
          <div className={styles.actionsWrapper}>
            {renderActions({
              actions: data?.actions,
              onClick: async (item) => {
                let values = {};
                if (item.type === "submit") {
                  values = await formRef.current.validateFields();
                  if (values.errorFields) return;
                }
                await Helper.commandHandler({
                  command: {
                    ...item.command,
                    request: { ...item?.command?.request, data: { ...item?.command?.request?.data, ...values } },
                  },
                });
              },
            })}
          </div>
        </>
      )}
    </>
  );
}

export default Form;
