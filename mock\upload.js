const path = require("node:path");
const multer = require("multer");
const { v4: uuid } = require("uuid");
const sharp = require("sharp");
const common = require("./common");

const uploader = multer({
  storage: multer.diskStorage({
    destination: `./mock/upload/files`,
    filename(req, file, callback) {
      const arr = file.originalname.split(".");
      const fileExtName = arr[arr.length - 1];
      callback(null, `${req.query?.hash || uuid()}.${fileExtName}`);
    },
  }),
});

module.exports = {
  "POST /rest/v1/upload": async (req, res, next) => {
    const { "do-not-save": doNotSave } = req.headers;
    let metadata = { width: 800, height: 800 };
    if (!doNotSave) {
      await new Promise((resolve, reject) => {
        uploader.single("file")(req, res, (err) => {
          err ? reject(err) : resolve();
        });
      });
      metadata = await sharp(req.file.path).metadata();
    }
    res.status(200).json({
      success: true,
      data: {
        fid: common.uniqueId(),
        host: req.body?.host || `http://localhost:8081`,
        image: {
          src: `/rest/v1/files/${req.file?.filename || "image.png"}`,
          width: metadata.width,
          height: metadata.height,
        },
      },
      command: {
        type: "message",
        config: {
          type: "success",
          content: "上传成功",
          duration: 3,
        },
      },
    });
  },

  "DELETE /rest/v1/upload": (req, res) => {
    res.status(200).json({
      success: true,
      command: {
        type: "message",
        config: {
          type: "success",
          content: "删除成功",
          duration: 3,
        },
      },
    });
  },

  "GET /rest/v1/files/:fileId": async (req, res) => {
    const { fileId } = req.pathParams;
    const filePath = path.resolve(`./mock/upload/files/${fileId}`);
    res.sendFile(filePath, (err) => {
      if (err) {
        res.status(err.status).json({ success: false, message: "error" });
      }
    });
  },
};
