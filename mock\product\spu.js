module.exports = {
  "GET /rest/v1/product/spu": async (req, res) => {
    const { query } = req.query;
    const source = [
      "DRN0392",
      "DRN0720",
      "DRN0642",
      "DRN0670",
      "DRN0874",
      "DRN0867",
      "DRB0275",
      "DRB0273",
      "DRH0128",
      "DRH0130",
      "DRDK008",
      "DRDK010",
      "DRCS144",
      "DRCS159",
    ];
    let spus = source.slice(0, 10);
    if (query) {
      const queryString = query.split(",");
      // spus = source.filter((spu) => spu.toLowerCase().indexOf(query.toLowerCase()) > -1);
      spus = source.filter((spu) => queryString.some((query) => spu.toLowerCase().indexOf(query.toLowerCase()) > -1));
    }
    const data = spus.map((spu, index) => ({
      label: `label${spu}`,
      value: spu,
    }));
    res.status(200).json({
      success: true,
      data: data,
      command: [],
    });
  },
};
