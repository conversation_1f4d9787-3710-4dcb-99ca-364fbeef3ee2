const webpackPart = require("./webpack.part");

module.exports = function override(config, env) {
  config.resolve.alias = {
    ...config.resolve.alias,
    ...webpackPart.resolve.alias,
  };

  config.ignoreWarnings = [
    {
      // module: /node_modules\/@module-federation\/utilities/,
      message: /Critical dependency: the request of a dependency is an expression/,
    },
  ];

  if (process.env.NODE_ENV === "production") {
    if (!config.optimization.splitChunks) {
      config.optimization.splitChunks = { cacheGroups: {} };
    }
    config.optimization.splitChunks.cacheGroups.framework = {
      test: /[\\/]node_modules[\\/](react|react-dom|react-router-dom)/,
      name: "framework",
      chunks: "initial",
      usedExports: true,
      enforce: true,
    };
    ["@dnd-kit", "react-ace", "react-rnd", "cropperjs", "fabric", "sanitize-html", "three"].forEach((name) => {
      config.optimization.splitChunks.cacheGroups[name] = {
        test: new RegExp(`[\\\\/]node_modules[\\\\/]${name}`, "i"),
        name: name,
        chunks: "all",
        usedExports: true,
        enforce: true,
      };
    });
  }

  return config;
};
