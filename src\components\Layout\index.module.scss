.main {
  //min-height: 100vh;
  --aside-width: 220px;
  --header-height: 50px;

  &.asideFold {
    --aside-width: 80px;

    // .asideHeader {
    //   opacity: 0;
    // }

    .asideBody {
      [class~="ant-menu-title-content"],
      [class~="ant-menu-submenu-arrow"] {
        display: none;
      }
    }
  }
}

.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  min-height: 100vh;
}

.header {
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 0 #ddd;
  padding: 0 10px;
  position: fixed;
  top: 0;
  left: var(--aside-width);
  right: 0;
  background: #fff;
  z-index: 1000;
  transition: all ease 0.3s;
}

.body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.footer {
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: center;
}

.aside {
  width: var(--aside-width);
  background: #3975c6;
  color: #eee;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 10;
  transition: width ease 0.3s;
  overflow: hidden auto;

  // 左侧菜单
  [class~="ant-menu-dark"] {
    background: #3975c6;
  }

  [class~="ant-menu"] [class~="ant-menu-sub"][class~="ant-menu-inline"] {
    background: #3975c6;
  }

  [class~="ant-menu-item-selected"],
  [class~="ant-menu-item-active"],
  [class~="ant-menu-submenu-selected"] {
    color: #fff !important;
  }

  // [class~="ant-menu-dark"] [class~="ant-menu-item"],
  // [class~="ant-menu-dark"] [class~="ant-menu-submenu-title"] {
  //   color: #fff;
  // }
}

// [class~="ant-menu-dark"]>[class~="ant-menu"] [class~="ant-menu-item"] {
//   color: #7199d4;
// }

[class~="ant-menu-dark"] [class~="ant-menu-item-selected"],
[class~="ant-menu-dark"] [class~="ant-menu-item-active"] {
  color: #fff !important;
  background: #3975c6 !important;
}

.asideHeader {
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  font-size: 17px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  white-space: nowrap;

  &,
  a {
    color: #fff;
  }
}

.asideBody {
  white-space: nowrap;
}

.content {
  max-width: 100vw;
  height: 100%;
  padding-top: var(--header-height);
  padding-left: var(--aside-width);
  //transition: all ease 0.3s;
  background-color: #f3f3f3;
}
