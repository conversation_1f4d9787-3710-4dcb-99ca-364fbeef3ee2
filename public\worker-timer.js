/* eslint-disable no-restricted-globals */

const Action = {
  Start: "start",
  Stop: "stop",
};

const Status = {
  Running: "running",
  Paused: "paused",
  Stopped: "stopped",
};

let status = Status.Stopped;
let intervalId = 0;

self.onmessage = function (e) {
  const { action, interval = 1000 } = e.data;
  if (action === Action.Start) {
    if (status !== Status.Running) {
      status = Status.Running;
      clearInterval(intervalId);
      intervalId = setInterval(() => {
        self.postMessage({ event: "tick" });
      }, interval);
    }
  } else if (action === Action.Stop) {
    clearInterval(intervalId);
    status = Status.Stopped;
  }
};
