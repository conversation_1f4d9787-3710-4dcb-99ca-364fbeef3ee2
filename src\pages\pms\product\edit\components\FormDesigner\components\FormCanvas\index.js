import styles from "./index.module.scss";
import classNames from "classnames";
import { useMemo } from "react";

import { useDroppable } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

import Enums from "../../common/enums";

import { Empty } from "antd";
import SortableItem from "./components/SortableItem";

function FormCanvas({
  widgetForm = { items: [] },
  flattenedItems,
  selectedKey,
  onSelect,
  isOverDropZone,
  ...restProps
}) {
  const { attributes, listeners, setNodeRef, transform, transition } = useDroppable({
    id: Enums.DroppableIds.FormCanvas,
    data: {
      isContainer: true,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const { items } = widgetForm;
  const sortedKeys = useMemo(
    () => flattenedItems.filter((widget) => !widget?.dragDisabled).map((widget) => widget.key),
    [flattenedItems]
  );

  return (
    <SortableContext items={sortedKeys} strategy={verticalListSortingStrategy}>
      <div
        ref={setNodeRef}
        className={classNames(styles.formCanvas, { [styles.isOver]: isOverDropZone })}
        style={style}
        {...attributes}
        {...listeners}
      >
        {items.length === 0 ? (
          <div className={styles.empty}>
            <Empty description="拖拽组件到此处" />
          </div>
        ) : (
          <div className={styles.formCanvasContent}>
            {items.map((widget, index) => (
              <SortableItem
                key={widget.key}
                widget={widget}
                index={index}
                selectedKey={selectedKey}
                onSelect={onSelect}
                {...restProps}
              />
            ))}
          </div>
        )}
      </div>
    </SortableContext>
  );
}

export default FormCanvas;
