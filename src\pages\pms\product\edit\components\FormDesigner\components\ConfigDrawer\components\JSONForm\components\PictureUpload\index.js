import styles from "./index.module.scss";
import { UploadOutlined } from "@ant-design/icons";
import { Input, Upload, Button } from "antd";
import Cookies from "js-cookie";
import Helper from "helpers";

import Enums from "enums";

function PictureUpload(props) {
  const { value, onChange } = props;
  const token = Cookies.get()[Enums.CookieName.Token];

  const handleChange = ({ file, fileList }) => {
    if (!fileList?.length) {
      onChange?.("");
      return;
    }

    if (file.status === Enums.UploadFileStatus.Error) {
      Helper.commandHandler({ command: file?.response?.command });
      onChange?.("");
      return;
    }

    if (file.status !== Enums.UploadFileStatus.Done) {
      onChange?.(fileList);
      return;
    }
    onChange?.(file?.response?.data?.image?.src || "");
  };

  function getFileList(value) {
    if (!value) {
      return [];
    }

    if (typeof value === "string") {
      return [
        {
          uid: "-1",
          url: value,
          status: "done",
        },
      ];
    }

    return value;
  }

  const fileList = getFileList(value);

  return (
    <div className={styles.pictureUpload}>
      <Upload
        {...props}
        headers={{
          authorization: `Bearer ${token}`,
        }}
        fileList={fileList}
        onChange={handleChange}
      >
        <Button icon={<UploadOutlined />}>Upload</Button>
      </Upload>
      <Input value={value ?? ""} disabled />
    </div>
  );
}

export default PictureUpload;
