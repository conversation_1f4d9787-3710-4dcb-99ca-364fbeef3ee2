.groupContainer {
  margin-bottom: 20px;

  .groupWrapper {
    cursor: pointer;
    position: relative;
    border: 1px solid #d9d9d9;
    background: rgb(253 246 236);
    padding: 25px 25px 30px;

    &:hover {
      border: 1px dashed #e6a23c;
    }

    &.selected {
      border: 2px solid #e6a23c;
      background-color: rgb(255 244 228);
    }

    .groupContent {
      display: inline-block;
      width: 100%;
      height: auto;
      min-height: 80px;
      // padding: 20px;
      background-color: #fff;
    }

    &.isOverlay {
      border: 2px solid #e6a23c;
      border-bottom: none;
      background: linear-gradient(to bottom, rgba(253, 246, 236, 0.3), rgba(236, 245, 255, 0));
      opacity: 0.6;
    }

    &.ghost {
      padding: 0;
    }

    .dragHandle,
    .configButton,
    .deleteButton {
      position: absolute;
      z-index: 1;
      width: 28px;
      height: 28px;
      color: #fff;
      line-height: 28px;
      text-align: center;
      background: #e6a23c;
    }

    .dragHandle {
      top: 0;
      left: 0;
      cursor: move;
    }

    .configButton {
      top: 0;
      right: 0;
      cursor: pointer;
    }

    .deleteButton {
      bottom: 0;
      right: 0;
      cursor: pointer;
    }
  }
}

.groupGhostLine {
  width: 100%;
  height: 80px;
  background: #2389ff;
  opacity: 0.25;
  margin-bottom: 20px;
}
