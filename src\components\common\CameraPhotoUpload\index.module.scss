.container {
  width: 100%;
  height: 100%;

  .content {
    display: inline-block;
    border: 1px solid #d9d9d9;
    padding: 8px;

    .previewWrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      margin-bottom: 8px;
      overflow: hidden;

      .photoPreview {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 2;
      }

      canvas {
        display: none;
      }
    }

    .preview {
      object-fit: cover;
      display: none;
    }

    .screenshot {
      position: absolute;
      z-index: -1;
      top: 0;
      left: 0;
      opacity: 0;
    }

    .activePreview {
      display: inline-block;
    }

    .actionsWrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: row;
      gap: 8px;
    }
  }

  .imageList {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;

    .imageItem {
      padding: 8px;
      border: 1px solid #d9d9d9;
      border-radius: 2px;
    }
  }
}
