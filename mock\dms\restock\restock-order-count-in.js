module.exports = async (req, res) => {
  res.status(200).json({
    status: "00",
    success: true,
    data: {
      columns: [
        {
          dataIndex: "image",
          title: "图片",
          width: 100,
          ellipsis: true,
          valueType: "image",
          image: { width: 45 },
        },
        {
          dataIndex: "spu",
          title: "SPU",
          width: 120,
          ellipsis: true,
        },
        {
          dataIndex: "sku",
          title: "SKU",
          width: 120,
          ellipsis: true,
        },
        {
          dataIndex: "primary_props",
          title: "主属性",
          width: 120,
          ellipsis: true,
        },
        {
          dataIndex: "second_props",
          title: "次属性",
          width: 120,
          ellipsis: true,
        },
        {
          dataIndex: "custom_props",
          title: "定制属性",
          width: 250,
          ellipsis: true,
        },
        {
          dataIndex: "qty",
          title: "数量",
          width: 120,
          editable: {
            component: "InputNumber",
            props: {
              min: 1,
              max: 10,
            },
          },
        },
        {
          dataIndex: "freight_space",
          title: "仓位",
          width: 120,
          ellipsis: true,
          valueType: "html",
        },
        {
          dataIndex: "stock_out_qty",
          title: "缺货数",
          width: 100,
          ellipsis: true,
        },
        {
          dataIndex: "in_qty",
          title: "进仓库存",
          width: 100,
          ellipsis: true,
        },
      ],
      items: Array(20)
        .fill(null)
        .map((_, i) => ({
          id: i,
          image: "https://img.yzcdn.cn/vant/leaf.jpg",
          spu: `SPU${i + 1}`,
          sku: `SKU${i + 1}`,
          primary_props: "primary_props",
          second_props: "second_props",
          custom_props: "custom_props",
          qty: i + 1,
          qty_extra_data: {
            props: {
              max: 15,
            },
          },
          freight_space: `<div style="color:red;">仓位</div>`,
          stock_out_qty: 123,
          in_qty: 321,
        })),
    },
  });
};
