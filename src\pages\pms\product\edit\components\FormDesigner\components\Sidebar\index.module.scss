$hover-color: #409eff;

.container {
  height: 100%;
  overflow-y: auto;

  .sidebar {
    height: 100%;
  }

  .componentList {
    height: 100%;

    .componentItem {
      position: relative;

      &:active {
        transform: scale(0.98);
      }

      &:hover,
      &:active {
        .originalComponent {
          color: $hover-color;
          border: 1px dashed $hover-color;
        }
      }
    }
  }
}
