import styles from "./index.module.scss";
import { UploadOutlined } from "@ant-design/icons";
import { Input, Upload, Button } from "antd";
import Cookies from "js-cookie";
import Enums from "enums";

function GlassesPictureUpload(props) {
  const { value, onChange } = props;
  const token = Cookies.get()[Enums.CookieName.Token];
  const fileList = getFileList(value);

  const handleChange = ({ file, fileList }) => {
    if (!fileList?.length) {
      onChange?.({});
      return;
    }

    if (file.status !== Enums.UploadFileStatus.Done) {
      onChange?.(fileList);
      return;
    }

    const image = file?.response?.data?.image;

    onChange?.({ src: image?.src, width: image?.width, height: image?.height });
  };

  function getFileList(value) {
    if (!value) {
      return [];
    }

    if (Object.prototype.toString.call(value) === "[object Object]") {
      return [
        {
          uid: "-1",
          url: value.src,
          status: "done",
        },
      ];
    }

    return value;
  }

  return (
    <div className={styles.pictureUpload}>
      <Upload
        {...props}
        headers={{
          authorization: `Bearer ${token}`,
        }}
        fileList={fileList}
        onChange={handleChange}
      >
        <Button icon={<UploadOutlined />}>Upload</Button>
      </Upload>
      <Input value={value?.src ?? ""} disabled />
    </div>
  );
}

export default GlassesPictureUpload;
