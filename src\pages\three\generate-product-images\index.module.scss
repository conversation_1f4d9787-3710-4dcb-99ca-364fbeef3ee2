.container {
  padding: 16px;
}

.wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.main {
  padding: 16px;
  background-color: #fff;
}

.preview {
  display: flex;
  gap: 16px;

  .left {
    flex: 0.6;
    max-width: 735px;
  }

  .right {
    flex: 0.4;
    max-width: 500px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
}

.large {
  border: 1px solid #eee;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  aspect-ratio: 1/1;

  img {
    width: 100%;
  }
}

.images {
  display: grid;
  grid-template-columns: repeat(4, minmax(0px, 1fr));
  gap: 10px;

  > div {
    aspect-ratio: 1/1;
    border: 1px solid #eee;
    cursor: pointer;

    img {
      width: 100%;
      display: block;
    }
  }

  .checked {
    border-color: var(--ant-primary-color);
  }
}

.offscreen {
  position: fixed;
  top: 0;
  left: -200vw;
}

.debugOffscreen {
  left: 0 !important;
  z-index: 9999;
}
