import { rectIntersection, pointerWithin } from "@dnd-kit/core";

import { DroppableIds } from "../enums";

function isHovered(pointer, clientRect) {
  if (!pointer || !clientRect) {
    return false;
  }

  const marginBottom = 20;
  const isInMarginArea = pointer.y > clientRect.bottom - marginBottom && pointer.y <= clientRect.bottom;

  // 如果在 margin 区域，返回 false
  if (isInMarginArea) {
    return false;
  }

  return (
    pointer.x >= clientRect.left &&
    pointer.x <= clientRect.right &&
    pointer.y >= clientRect.top &&
    pointer.y <= clientRect.bottom - marginBottom
  );
}
function sortCollisionsAsc({ data: { value: a } }, { data: { value: b } }) {
  return a - b;
}

function getRelativePosition(pointer, dropRect) {
  const distanceToTop = Math.abs(pointer.y - dropRect.top);
  const distanceToBottom = Math.abs(pointer.y - dropRect.bottom);

  const verticalDistance = Math.min(distanceToTop, distanceToBottom);

  return verticalDistance;
}

let previousValue = [];

export function customCollisionDetection({
  collisionRect,
  droppableRects,
  droppableContainers,
  active,
  pointerCoordinates,
}) {
  // 所有碰撞的组容器
  const groupContainers = rectIntersection({
    collisionRect, // 碰撞矩形
    droppableRects, // 所有容器矩形
    active, // 当前拖拽的组件
    pointerCoordinates, // 当前拖拽的坐标
    // droppableContainers: droppableContainers.filter((item) => !item.data.current.isGhost),
    droppableContainers: droppableContainers.filter((item) => item.data.current?.isGroup && !item.data.current.isGhost),
  });

  // 拖拽元素所在的group容器
  const activeElementGroup = droppableContainers.find((container) => {
    if (active.data.current?.fromSidebar) {
      return container.data.current?.component?.relKey === active.id;
    } else {
      return container.id === active.id;
    }
  });
  const activeParentGroupKey = activeElementGroup?.data.current?.parentKey;
  const hasHoveredContainer = groupContainers.some((container) => {
    const rect = container.data.droppableContainer.node.current?.getBoundingClientRect();
    return container?.id !== active?.id && isHovered(pointerCoordinates, rect);
  });

  if (groupContainers.length === 0 || !hasHoveredContainer) {
    return pointerWithin({
      collisionRect,
      droppableRects,
      droppableContainers: droppableContainers.filter(
        (item) => item.id === DroppableIds.FormCanvas || !item.data.current?.isGroup
      ),
      active,
      pointerCoordinates,
    });
  }

  if (groupContainers.length > 0) {
    // 找到鼠标当前悬停的容器
    const hoveredContainers = groupContainers.filter((container) => {
      const rect = container.data.droppableContainer.node.current?.getBoundingClientRect();
      return container?.id !== active?.id && isHovered(pointerCoordinates, rect);
    });

    // 按照容器面积从小到大排序，最小的就是最内层的容器
    const hoveredContainer =
      hoveredContainers.length > 0
        ? hoveredContainers.sort((a, b) => {
            const rectA = a.data.droppableContainer.node.current?.getBoundingClientRect();
            const rectB = b.data.droppableContainer.node.current?.getBoundingClientRect();
            const areaA = rectA ? rectA.width * rectA.height : Infinity;
            const areaB = rectB ? rectB.width * rectB.height : Infinity;
            return areaA - areaB;
          })[0]
        : null;

    if (activeParentGroupKey) {
      let collisions = [];

      for (const droppableContainer of droppableContainers) {
        const { id } = droppableContainer;
        const rect = droppableRects.get(id);

        if (rect && id !== DroppableIds.FormCanvas) {
          const relativePosition = getRelativePosition(pointerCoordinates, rect);

          collisions.push({
            id,
            data: {
              droppableContainer,
              value: relativePosition,
              hovered: hoveredContainer?.id === id,
              parentKey: activeParentGroupKey,
              isGroup: droppableContainer.data.current?.isGroup,
            },
          });
        }
      }

      previousValue = collisions.sort(sortCollisionsAsc).map((collision) => ({
        ...collision,
        data: {
          ...collision.data,
          isActiveContainer: collision.id === hoveredContainer?.id,
        },
      }));

      return previousValue;
    }
  }

  return pointerWithin({ collisionRect, droppableRects, droppableContainers, active, pointerCoordinates });
}

export default customCollisionDetection;
