import styles from "./index.module.scss";
import { Empty, Select, Spin, Button } from "antd";
import { useEffect, useMemo, useState, useRef } from "react";
import debounce from "lodash.debounce";
import PropTypes from "prop-types";
import request from "@/fetchers/request";
import useSWR from "swr";
import Helper from "helpers";

function RemoteSearchSelect(props) {
  const {
    api,
    fetchDelay = 400,
    value,
    defaultValue,
    onChange,
    addOptions = null,
    defaultOptions = [],
    isGetDefaultOptions = false,
    onOptionsChange,
    ...otherProps
  } = props;
  const [options, setOptions] = useState(defaultOptions);
  const [loading, setLoading] = useState(false);

  const instanceId = useRef(value);

  let { data: currentOptions } = useSWR([api, isGetDefaultOptions, defaultValue, instanceId], async () => {
    try {
      // !options.length && isGetDefaultOptions && (value || defaultValue)
      if (isGetDefaultOptions || (!options.length && !isGetDefaultOptions && (value || defaultValue))) {
        setLoading(true);
        const queryValue = value || defaultValue;
        const result = await request(api, {
          method: "GET",
          params: { query: Array.isArray(queryValue) ? queryValue.join(",") : queryValue, type: "value" },
        }).then((res) => res.data);
        const options = result?.data || [];
        setOptions(options);
        onOptionsChange?.(options);
        return options;
      }
      return [];
    } finally {
      setLoading(false);
    }
  });

  const debounceFetcher = useMemo(() => {
    return debounce(async (keywords) => {
      if (keywords) {
        try {
          setOptions([]);
          setLoading(true);
          const result = await request(api, { method: "GET", params: { query: keywords } }).then((res) => res.data);
          setOptions(result?.data || []);
        } catch (e) {
          setOptions([]);
        } finally {
          setLoading(false);
        }
      }
    }, fetchDelay);
  }, [api, fetchDelay]);

  useEffect(() => {
    if (currentOptions?.length) {
      setOptions(currentOptions);
    }
  }, [currentOptions]);

  return (
    <div className={styles.container}>
      <Select
        showSearch
        onSearch={debounceFetcher}
        filterOption={false}
        defaultActiveFirstOption={false}
        notFoundContent={
          <div style={{ display: "flex", justifyContent: "center" }}>
            {loading ? <Spin /> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
          </div>
        }
        // onDropdownVisibleChange={(open) => {
        // 单个选项模式下，editable
        // if (open && isGetDefaultOptions && otherProps?.mode !== "multiple") {
        //   setOptions(currentOptions);
        // }
        // if (currentOptions?.length) {
        //   setOptions(currentOptions);
        // }
        // }}
        {...otherProps}
        value={value}
        defaultValue={defaultValue}
        options={options}
        onChange={(val, option) => {
          if (value === undefined) {
            currentOptions = options;
          }
          onChange?.(val, option);
        }}
      />
      {addOptions?.command ? (
        <Button
          type="link"
          className={styles.addButton}
          onClick={() => {
            Helper.commandHandler({ command: addOptions?.command });
          }}
        >
          {addOptions?.title || "维护"}
        </Button>
      ) : null}
    </div>
  );
}

RemoteSearchSelect.propTypes = {
  api: PropTypes.string,
  fetchDelay: PropTypes.number,
};

export default RemoteSearchSelect;
