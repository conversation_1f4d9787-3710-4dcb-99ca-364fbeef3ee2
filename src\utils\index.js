const Events = [];
const ByteUnits = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB", "BB", "NB", "DB", "CB", "XB"];

const Utils = Object.freeze({
  sleep(millisecond = 0) {
    return new Promise((resolve) => {
      setTimeout(resolve, millisecond);
    });
  },

  createURL(url = "") {
    let fullUrl;
    if (this.isFullUrl(url)) {
      fullUrl = url;
    } else if (this.isAbsolutePath(url)) {
      fullUrl = `http://localhost${url}`;
    } else {
      fullUrl = `http://localhost/${url || ""}`;
    }
    return new URL(fullUrl);
  },

  getPathname(url) {
    return this.createURL(url).pathname;
  },

  isFullUrl(url) {
    return /^http(s)?:\/\//i.test(url);
  },

  isAbsolutePath(url) {
    return url?.[0] === "/";
  },

  getQueryParams(url = "") {
    return this.toQueryParams(url);
  },

  setQueryParams(url, params) {
    const urlObj = this.createURL(url);
    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        urlObj.searchParams.set(key, value.toString());
      } else {
        urlObj.searchParams.delete(key);
      }
    });
    let newUrl;
    if (this.isFullUrl(url)) {
      newUrl = urlObj.toString();
    } else if (this.isAbsolutePath(url)) {
      newUrl = urlObj.toString().replace(urlObj.origin, "");
    } else if (typeof url === "string") {
      newUrl = `${url.split("?")[0] || ""}?${urlObj.searchParams.toString()}`;
    } else {
      newUrl = url;
    }
    return newUrl;
  },

  encodeURI(str) {
    try {
      return encodeURI(str);
    } catch (e) {
      return str;
    }
  },

  decodeURI(str) {
    try {
      return decodeURI(str);
    } catch (e) {
      return str;
    }
  },

  encodeURIComponent(str) {
    try {
      return encodeURIComponent(str);
    } catch (e) {
      return str;
    }
  },

  decodeURIComponent(str) {
    try {
      return decodeURIComponent(str);
    } catch (e) {
      return str;
    }
  },

  toQueryParams(url) {
    const result = {};
    const urlObj = this.createURL(url);
    for (const key of urlObj.searchParams.keys()) {
      const value = urlObj.searchParams.get(key);
      if (value) {
        result[key] = Utils.decodeURI(value);
      }
    }
    return result;
  },

  toQueryString(queryParams) {
    const result = Object.keys(queryParams || {})
      .map((key) => (queryParams[key] ? `${key}=${queryParams[key]}` : ""))
      .filter((str) => str);
    return result.join("&");
  },

  toNumber(str, defaultValue = null) {
    const result = +str;
    return isNaN(result) ? defaultValue : result;
  },

  numberToFixed(value, fractionDigits) {
    const str = value.toFixed(fractionDigits + 1);
    return str.substring(0, str.length - 1);
  },

  padNumberFractionDigitsEnd(number, length, char = "0") {
    const str = number?.toString() || "";
    if (str.indexOf(".") > -1) {
      const arr = str.split(".");
      return `${arr[0]}.${arr[1].padEnd(length, char)}`;
    } else {
      return `${number}.${new Array(length).fill(char).join("")}`;
    }
  },

  toOrdinal(number) {
    let str = "";
    if (number < 4 || number > 20) {
      const tail = this.toNumber(this.lastOne(number.toString()));
      if (tail === 1) {
        str = `${number}st`;
      } else if (tail === 2) {
        str = `${number}nd`;
      } else if (tail === 3) {
        str = `${number}rd`;
      }
    } else {
      str = `${number}th`;
    }
    return str;
  },

  // 包含min，max
  random(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },

  randomFloat(min, max, fixed = 2) {
    return (Math.random() * (max - min) + min).toFixed(fixed);
  },

  randomArray(max, min, count) {
    const list = new Set();
    while (list.size < count) {
      const num = this.random(max, min);
      list.add(num);
    }
    return Array.from(list);
  },

  merge() {
    const target = arguments[0];
    for (let i = 1; i < arguments.length; i++) {
      const option = arguments[i];
      for (const key of Object.keys(option)) {
        if (option[key]) {
          target[key] = option[key];
        }
      }
    }
    return target;
  },

  buildObjectTree(list, { childrenKey = "children", idKey = "id", parentIdKey = "parentId" } = {}) {
    const temp = {};
    const tree = {};
    for (let i = 0; i < list.length; i++) {
      temp[list[i][idKey]] = list[i];
    }
    for (const i of Object.keys(temp)) {
      if (temp[i][parentIdKey]) {
        if (!temp[temp[i][parentIdKey]][childrenKey]) {
          temp[temp[i][parentIdKey]][childrenKey] = {};
        }
        temp[temp[i][parentIdKey]][childrenKey][temp[i][idKey]] = temp[i];
      } else {
        tree[temp[i][idKey]] = temp[i];
      }
    }
    return tree;
  },

  buildListTree(list, { childrenKey = "children", idKey = "id", parentIdKey = "parentId" } = {}) {
    const parseObjectTree = (objectTree) => {
      const arr = [];
      for (const key of Object.keys(objectTree)) {
        const node = objectTree[key];
        arr.push(node);
        if (node.children) {
          node.children = parseObjectTree(node.children);
        }
      }
      return arr;
    };
    const objectTree = this.buildObjectTree(list, { childrenKey, idKey, parentIdKey });
    return parseObjectTree(objectTree);
  },

  parseObjectTree(objectTree) {
    const arr = [];
    for (const key of Object.keys(objectTree)) {
      const node = objectTree[key];
      arr.push(node);
      if (node.children) {
        node.children = this.parseObjectTree(node.children);
      }
    }
    return arr;
  },

  forEachTree({ treeData, forEach, childrenKey = "children" }) {
    const handleForEach = (nodes) => {
      for (let i = 0; i < nodes?.length; i++) {
        const item = nodes[i];
        forEach?.(item, i);
        const children = item[childrenKey];
        if (children?.length > 0) {
          handleForEach(children);
        }
      }
    };
    handleForEach(treeData);
  },

  flatTree({ treeData, childrenKey }) {
    const list = [];
    this.forEachTree({
      treeData,
      childrenKey,
      forEach(item) {
        list.push(item);
      },
    });
    return list;
  },

  buildTreeId({ treeData, onEachRoot, onEachLeaf, childrenKey = "children" }) {
    const addTreeId = (nodes, prefix = "") => {
      nodes?.forEach((parent, i) => {
        const children = parent[childrenKey];
        if (!prefix) {
          parent.__id = `${prefix}${i + 1}`;
          onEachRoot?.(parent);
        }
        if (children?.length > 0) {
          children?.forEach((item, j) => {
            item.__id = `${parent.__id}-${j + 1}`;
            item.__parentId = parent.__id;
            onEachLeaf?.(item);
          });
          addTreeId(children, `${parent.__id}-`);
        }
      });
    };
    addTreeId(treeData);
    return treeData;
  },

  fillTreeKeysParents({ treeData, flatTreeData, keys }) {
    if (!flatTreeData) {
      flatTreeData = Utils.flatTree({ treeData });
    }
    const treeNodes = flatTreeData.filter((item) => keys.includes(item.key));
    return Utils.fillTreeNodesParents({ flatTreeData, treeNodes });
  },

  fillTreeNodesParents({ treeNodes, flatTreeData }) {
    const result = [];
    treeNodes?.forEach((item) => {
      const arr = item.__id.split("-");
      let ids = [];
      arr.forEach((id, index) => {
        ids.push(id);
        const node = flatTreeData.find((a) => a.__id === ids.join("-"));
        if (result.findIndex((a) => a.__id === node.__id) === -1) {
          result.push(node);
        }
      });
    });
    return result;
  },

  searchTree({ flatTreeData, keywords = "", childrenKey = "children" }) {
    const searchedNodes = flatTreeData?.filter((item) => Utils.hasKeywords(item.title, keywords));
    const searchedTreeData = Utils.fillTreeNodesParents({ treeNodes: searchedNodes, flatTreeData });
    return Utils.buildListTree(searchedTreeData, { idKey: "__id", parentIdKey: "__parentId" });
  },

  hasKeywords(text, keywords) {
    return (text || "").toLowerCase().indexOf((keywords || "").toLowerCase()) > -1;
  },

  lastOne(arr) {
    return Array.isArray(arr) ? arr[arr.length - 1] : undefined;
  },

  cloneDeep(obj) {
    return this.JSON.parse(JSON.stringify(obj));
  },

  addEventListener(name, handler, id = Math.random().toString().substring(2)) {
    Events.push({ name, handler, id });
  },

  removeEventListener(name, handler) {
    let arr;
    if (typeof handler === "string") {
      arr = Events.filter((item) => !(item.name === name && item.id === handler));
    } else {
      arr = Events.filter((item) => !(item.name === name && item.handler === handler));
    }
    Events.length = 0;
    Events.push(...arr);
  },

  dispatchEvent(name, ...args) {
    const events = Events.filter((item) => item.name === name);
    for (const event of events) {
      event.handler(...args);
    }
  },

  async executeEvent(name, ...args) {
    const events = Events.filter((item) => item.name === name);
    return await Promise.all([...events.map((event) => event?.handler(...args))]);
  },

  removeDuplicates(arr, key) {
    const temp = {};
    const result = [];
    arr.forEach((item) => {
      const value = typeof key === "function" ? key(item) : item[key];
      if (!temp[value]) {
        temp[value] = true;
        result.push(item);
      }
    });
    return result;
  },

  humanize: {
    bytes: {
      stringify: (number, fractionDigits = 2) => {
        let num = number;
        let i = 0;
        while (num > 1) {
          if (num / 1024 < 1 || i >= ByteUnits.length - 1) break;
          num = +(num / 1024).toFixed(fractionDigits);
          i++;
        }
        return num + ByteUnits[i];
      },
      parse: (humanizeString) => {
        const matches = humanizeString.match(/^(\d+(\.\d+)?)\s*([a-zA-Z]+)$/) || [];
        const value = Utils.toNumber(matches[1], 0);
        const unit = matches[3]?.toUpperCase();
        if (!value || !unit) throw new Error("[Humanize Bytes] parse error: invalid string");
        const index = ByteUnits.findIndex((item) => item === unit);
        if (index === -1) throw new Error("[Humanize Bytes] parse error: invalid unit");
        return Math.pow(1024, index) * value;
      },
    },
  },

  type: {
    isBlob(value) {
      return Object.prototype.toString.call(value) === "[object Blob]";
    },
    isFile(value) {
      return Object.prototype.toString.call(value) === "[object File]";
    },
    isFileLike(value) {
      return Utils.type.isBlob(value) || Utils.type.isFile(value);
    },
    isPromise(value) {
      return Object.prototype.toString.call(value) === "[object Promise]";
    },
    isAsyncFunction(value) {
      return Object.prototype.toString.call(value) === "[object AsyncFunction]";
    },
    isPromiseLike(value) {
      return Utils.type.isPromise(value) || Utils.type.isAsyncFunction(value);
    },
    isEmpty(value) {
      return typeof value === "undefined" || [null, ""].includes(value);
    },
  },

  async readAsDataUrl(file) {
    if (Utils.type.isFile(file)) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = function (e) {
          resolve(e.target.result);
        };
        reader.onerror = function (e) {
          reject(e);
        };
        reader.readAsDataURL(file);
      });
    }
  },

  getAspectScaleSize({ width, height, targetSize, isWidth = true }) {
    const size = { width, height };
    if (width > 0 && height > 0) {
      if (isWidth) {
        size.width = targetSize;
        size.height = (targetSize / width) * height;
      } else {
        size.width = (targetSize / height) * width;
        size.height = targetSize;
      }
    }
    return size;
  },

  getAspectRatioScaleSize({ width, height, targetSize, isWidth = true, fractionDigits }) {
    const originalSize = isWidth ? width : height;
    const scale = 1 + (targetSize - originalSize) / originalSize;
    const hasFractionDigits = typeof fractionDigits === "number";
    const targetWidth = hasFractionDigits ? +Utils.numberToFixed(width * scale, fractionDigits) : width * scale;
    const targetHeight = hasFractionDigits ? +Utils.numberToFixed(height * scale, fractionDigits) : height * scale;
    return { width: targetWidth, height: targetHeight };
  },

  getAspectRatioScaleMinSize({ width, height, maxWidth, maxHeight }) {
    const scaledWidthSize = Utils.getAspectScaleSize({
      width,
      height,
      targetSize: maxWidth,
      isWidth: true,
    });
    const scaledHeightSize = Utils.getAspectScaleSize({
      width,
      height,
      targetSize: maxHeight,
      isWidth: false,
    });
    return scaledWidthSize.width <= maxWidth && scaledWidthSize.height <= maxHeight
      ? scaledWidthSize
      : scaledHeightSize;
  },

  textCapitalize(text = "") {
    return (text || "")
      .toLowerCase()
      .split(" ")
      .map((word) => word.replace(/^./, (char) => char.toUpperCase()))
      .join(" ");
  },

  base64ToBytes(base64) {
    const binString = atob(base64);
    return Uint8Array.from(binString, (m) => m.codePointAt(0));
  },

  bytesToBase64(bytes) {
    const binString = Array.from(bytes, (x) => String.fromCodePoint(x)).join("");
    return btoa(binString);
  },

  base64ToFile({ base64, filename }) {
    const arr = base64.split(",");
    const mimeType = arr[0].match(/:(.*?);/)[1];
    const buffer = this.base64ToBytes(arr[1]);
    return new File([buffer], filename, { type: mimeType });
  },

  base64: {
    encode(str) {
      try {
        if (typeof btoa === "function") {
          return Utils.bytesToBase64(new TextEncoder().encode(str));
        }
        return Buffer.from(str, "utf8").toString("base64");
      } catch (e) {
        // console.error(`base64 encode error: ${str}`)
        return "";
      }
    },
    decode(str) {
      try {
        if (typeof atob === "function") {
          return new TextDecoder().decode(Utils.base64ToBytes(str));
        }
        return Buffer.from(str, "base64").toString("utf8");
      } catch (e) {
        // console.error(`base64 decode error: ${str}`)
        return "";
      }
    },
  },

  JSON: {
    parse(str, defaultValue = {}) {
      try {
        return JSON.parse(str) ?? defaultValue;
      } catch (e) {
        return defaultValue;
      }
    },
    stringify(data, defaultValue = "") {
      try {
        return JSON.stringify(data);
      } catch (e) {
        return defaultValue;
      }
    },
  },

  styleStringToObject(str) {
    const style = {};
    const attrs = str.split(";").filter((item) => item);
    attrs.forEach((attr) => {
      const arr = attr.split(":");
      style[arr[0].trim()] = (arr[1] || "").replace(/[\s;]*/g, "");
    });
    return style;
  },

  objectToStyleString(obj) {
    let str = "";
    Object.keys(obj || {}).forEach((key) => {
      const value = obj[key];
      if (value) {
        str += `${key}: ${value};`;
      }
    });
    return str;
  },

  pxToNumber(str) {
    return this.toNumber((str || "").replace("px", ""), 0);
  },

  scrollPagination({ scrollingElement = window, distance = 50, callback } = {}) {
    let loading = false;
    let scrollHandler = async function () {
      const scrollHeight =
        scrollingElement === window ? document.scrollingElement.scrollHeight : scrollingElement.scrollHeight;
      if (!loading && scrollHeight - (window.scrollY + window.innerHeight) < distance) {
        try {
          loading = true;
          await callback?.();
        } finally {
          loading = false;
        }
      }
    };
    if (scrollingElement) {
      scrollingElement.removeEventListener("scroll", scrollHandler);
      scrollingElement.addEventListener("scroll", scrollHandler);
    }
    return scrollHandler;
  },

  removeScrollPagination({ scrollingElement = window, scrollHandler } = {}) {
    if (scrollingElement) {
      scrollingElement.removeEventListener("scroll", scrollHandler);
    }
  },

  imageSrcToCanvas(src) {
    return new Promise((resolve, reject) => {
      const image = new Image();
      image.crossOrigin = "anonymous";
      image.onload = function () {
        const canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        const context = canvas.getContext("2d");
        context.drawImage(image, 0, 0);
        resolve(canvas);
      };
      image.onerror = reject;
      image.src = src;
    });
  },

  async imageSrcToDataUrl(src) {
    const canvas = await this.imageSrcToCanvas(src);
    return canvas.toDataURL();
  },

  async svgToBlob(svg) {
    return new Promise(async (resolve, reject) => {
      try {
        const box = document.createElement("div");
        box.innerHTML = svg;
        const svgNode = box.children[0];
        const serializer = new XMLSerializer();
        const str = serializer.serializeToString(svgNode);
        const src = `data:image/svg+xml;base64,${btoa(str)}`;
        const canvas = await this.imageSrcToCanvas(src);
        canvas.toBlob(resolve);
      } catch (e) {
        reject(e);
      }
    });
  },

  async dataUrlToBlob(dataUrl) {
    const mimeString = dataUrl.split(",")[0].split(":")[1].split(";")[0];
    const byteString = atob(dataUrl.split(",")[1]);
    const arrayBuffer = new ArrayBuffer(byteString.length);
    const intArray = new Uint8Array(arrayBuffer);
    for (let i = 0; i < byteString.length; i++) {
      intArray[i] = byteString.charCodeAt(i);
    }
    return new Blob([intArray], { type: mimeString });
  },

  closest(node, selector) {
    let parentNode = node?.parentElement;
    let selectNode = parentNode?.parentElement.querySelector(selector);
    while (parentNode && parentNode?.parentElement && parentNode !== selectNode) {
      if (Array.from(parentNode?.parentElement?.querySelectorAll(selector)).some((item) => item === parentNode)) {
        break;
      }
      parentNode = parentNode?.parentElement;
      selectNode = parentNode?.parentElement?.querySelector(selector);
    }
    return parentNode;
  },

  appendScript(src, attrs = {}) {
    return new Promise((resolve, reject) => {
      let script = document.querySelector(`script[src="${src}"]`);
      if (!script) {
        script = document.createElement("script");
        script.onload = () => {
          resolve(script);
        };
        script.onerror = reject;
        script.src = src;
        Object.keys(attrs).forEach((key) => {
          script[key] = attrs[key];
        });
        document.body.appendChild(script);
      } else {
        resolve(script);
      }
    });
  },

  appendCss(href, attrs) {
    return new Promise((resolve, reject) => {
      let link = document.querySelector(`link[href="${href}"]`);
      if (!link) {
        link = document.createElement("link");
        link.rel = "stylesheet";
        link.type = "text/css";
        link.href = href;
        link.onload = () => {
          resolve(link);
        };
        link.onerror = reject;
        Object.keys(attrs || {}).forEach((key) => {
          link[key] = attrs[key];
        });
        document.head.appendChild(link);
      } else {
        resolve(link);
      }
    });
  },

  hasOwnProperty(obj, key) {
    return this.hasOneOfOwnProperties(obj, [key]);
  },

  hasOwnProperties(obj, keys) {
    return keys.every((key) => Object.hasOwnProperty.call(obj, key));
  },

  hasOneOfOwnProperties(obj, keys) {
    return keys.some((key) => Object.hasOwnProperty.call(obj, key));
  },

  flat({ array, forEach }) {
    const result = [];
    const flat = (arr) => {
      arr.forEach((item, index) => {
        if (forEach) {
          forEach({ result, item, index });
        } else {
          Array.isArray(item) ? flat(item) : result.push(item);
        }
      });
    };
    flat(array);
    return result;
  },

  loadScript(src, attrs) {
    return new Promise((resolve, reject) => {
      let script = document.querySelector(`script[src="${src}"]`);

      if (script && script.dataset.loaded) {
        return resolve(script);
      }

      if (script) {
        const originalOnload = script.onload;
        script.onload = (e) => {
          originalOnload?.(e);
          script.dataset.loaded = true;
          resolve(script);
        };
        script.onerror = reject;
        return;
      }

      script = document.createElement("script");
      script.src = src;
      script.onload = (e) => {
        script.dataset.loaded = true;
        resolve(script);
      };
      script.onerror = reject;

      Object.entries(attrs || {}).forEach(([key, value]) => {
        script[key] = value;
      });

      document.body.appendChild(script);
    });
  },
});

export default Utils;
