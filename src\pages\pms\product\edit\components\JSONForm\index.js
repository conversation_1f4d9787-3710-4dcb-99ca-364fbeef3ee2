import CommonJSONForm from "components/common/JSONForm";

import Enums from "@/enums";
import ImageUpload from "../ImageUpload";
import FormDesigner from "../FormDesigner";

const extendComponents = {
  [Enums.Components.ImageUpload]: ({ item, ...props }) => <ImageUpload item={item} {...props} />,
  [Enums.Components.FormDesigner]: ({ item }) => <FormDesigner {...item?.props} />,
};

function JSONForm(props) {
  const { data, ...otherProps } = props;

  return <CommonJSONForm data={data} {...otherProps} extendComponents={extendComponents} />;
}

export default JSONForm;
