import { Card, Row, Col, <PERSON>ton, Typography } from "antd";
import { useNavigate } from "react-router-dom";
import { EditOutlined, TableOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";

const { Title, Paragraph } = Typography;

const LowCodeEntry = () => {
  const navigate = useNavigate();

  const lowCodeOptions = [
    {
      title: "编辑页编辑器",
      description: "用于创建和编辑表单页面的低代码编辑器，支持面包屑、操作按钮和内容区域的配置。",
      icon: <EditOutlined style={{ fontSize: 48 }} />,
      path: "/low-code/edit-page",
      color: "#1890ff",
    },
    {
      title: "列表页编辑器",
      description: "用于创建和编辑数据列表页面的低代码编辑器，支持表格列配置、搜索条件和操作按钮的配置。",
      icon: <TableOutlined style={{ fontSize: 48 }} />,
      path: "/low-code/list-page",
      color: "#52c41a",
    },
  ];

  return (
    <div className={styles.lowCodeEntry}>
      <div className={styles.header}>
        <Title level={2}>低代码平台</Title>
      </div>

      <Row gutter={[24, 24]}>
        {lowCodeOptions.map((option, index) => (
          <Col xs={24} sm={24} md={12} key={index}>
            <Card
              hoverable
              className={styles.card}
              onClick={() => navigate(option.path)}
              style={{ borderTop: `4px solid ${option.color}` }}
            >
              <div className={styles.cardContent}>
                <div className={styles.iconWrapper} style={{ color: option.color }}>
                  {option.icon}
                </div>
                <div className={styles.cardInfo}>
                  <Title level={4}>{option.title}</Title>
                  <Paragraph className={styles.description}>{option.description}</Paragraph>
                  <Button type="primary" onClick={() => navigate(option.path)}>
                    进入编辑器
                  </Button>
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default LowCodeEntry;
