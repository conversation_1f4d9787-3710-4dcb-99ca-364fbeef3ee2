import { arrayMove } from "@dnd-kit/sortable";
import { componentCategories } from "../config/componentMap";

const components = [];
componentCategories.forEach((category) =>
  category.children.forEach((component) => {
    components.push(component);
  })
);

export function generateUniqueId(prefix = "") {
  let guid = Date.now();
  return `${prefix}${(guid++).toString(36).toLowerCase()}`;
}

function flatten(items = [], parentKey = null) {
  return items.reduce((acc, item, index) => {
    return [...acc, { ...item, parentKey, index }, ...flatten(item.children || item.items, item.id)];
  }, []);
}

export function flattenTree(items) {
  return flatten(items);
}

// 根据ID查找组件
export function findComponentById(components, id) {
  if (!components || !id) return null;

  for (const component of components) {
    if (component.id === id) {
      return component;
    }

    // 如果组件有子组件，递归查找
    if (component.children) {
      const found = findComponentById(component.children, id);
      if (found) return found;
    }
  }

  return null;
}

// 根据ID删除组件
export function removeComponentById(components, id) {
  if (!components || !id) return components;

  return components.filter((component) => {
    if (component.id === id) {
      return false;
    }

    // 如果组件有子组件，递归删除
    if (component.children) {
      component.children = removeComponentById(component.children, id);
    }

    return true;
  });
}

// 清理组件数据，移除不需要的属性
export function sanitizeItems(items) {
  const newItems = [...items];
  return newItems.map((item) => {
    const { component_config, dragDisabled, title, id, ...rest } = item;
    const childrenName = component_config?.childrenName || "children";
    if (item[childrenName]?.length) {
      rest[childrenName] = sanitizeItems(item[childrenName]);
    }
    return rest;
  });
}

export function cloneComponent(component) {
  const { component_config, ...rest } = component;

  const cloned = { ...rest };

  if (component_config) {
    cloned.component_config = { ...component_config };
    if (component_config.preview_component) {
      cloned.component_config.preview_component = component_config.preview_component;
    }
  }

  const childrenName = component_config?.childrenName || "children";
  if (Array.isArray(component[childrenName])) {
    cloned[childrenName] = [];
  }

  Object.keys(rest).forEach((key) => {
    if (Array.isArray(rest[key])) {
      cloned[key] = [...rest[key]];
    } else if (typeof rest[key] === "object" && rest[key] !== null) {
      cloned[key] = { ...rest[key] };
    }
  });

  return cloned;
}

export function removeComponent({ items, id, component }) {
  if (!Array.isArray(items)) return items;

  return items
    .filter((item) => {
      if (component) {
        return item.component !== component;
      }
      return item.id !== id;
    })
    .map((item) => removeComponent({ items: (item.items || item.children) ?? [], id, component }));
}

export function moveComponent({ items, activeId, overId }) {
  if (!items || !activeId || !overId) {
    return items;
  }

  const newItems = [...items];

  function findAndMove(components) {
    if (!Array.isArray(components)) return false;

    const activeIndex = components.findIndex((item) => item.id === activeId);
    const overIndex = components.findIndex((item) => item.id === overId);

    if (activeIndex !== -1 && overIndex !== -1) {
      if (activeIndex === overIndex) {
        return true;
      }

      const overComponent = components[overIndex];
      if (overComponent.component === "ghost") {
        return true;
      }

      const reordered = arrayMove(components, activeIndex, overIndex);
      components.splice(0, components.length, ...reordered);
      return true;
    }

    for (const component of components) {
      if (findAndMove(component.children) || findAndMove(component.items)) {
        return true;
      }
    }

    return false;
  }

  findAndMove(newItems);
  return newItems;
}

export function isInContainer(items, targetId) {
  for (const item of items) {
    if (item.component_config?.isGroup) {
      const childrenName = item.component_config?.childrenName || "children";
      const children = item[childrenName] || [];

      if (children.some((child) => child.id === targetId)) {
        return true;
      }

      if (isInContainer(children, targetId)) {
        return true;
      }
    }
  }
  return false;
}

export function initializeComponents(components) {
  return formatComponents(components);
}

/**
 * @param {*} items
 * @returns
 */
function formatComponents(items) {
  if (!items?.length) return [];
  return items.map((item, index) => {
    const { icon, ...rest } = item;
    const targetComponent = components.find((component) => component.component === item.component);
    const id = generateUniqueId(`${targetComponent?.component}${index}`);
    const title = targetComponent.title;
    const { component_config } = targetComponent ?? {};
    const childrenName = component_config?.childrenName ?? "children";
    const newItem = { ...rest, id, title, component_config: { ...component_config } };

    if (item[childrenName]?.length) {
      newItem[childrenName] = formatComponents(item[childrenName]);
    }

    return newItem;
  });
}
