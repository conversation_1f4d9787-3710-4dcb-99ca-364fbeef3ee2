import { useEffect, useState } from "react";
import useS<PERSON> from "swr";
import Fetchers from "@/fetchers";
import { CloudDownloadOutlined, DeleteOutlined } from "@ant-design/icons";
import { Toolt<PERSON>, Button, Drawer, Typography, Divider, Flex, Badge, Empty, Spin } from "antd";
import Loading from "components/common/Loading";
import Helper from "helpers";
import styles from "./index.module.scss";
import useWebSocketReceiver from "@/hooks/useWebSocketReceiver";
import Enums from "@/enums";
import { useTranslation } from "react-i18next";

const { Text } = Typography;

function DownloadList() {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [hasNew, setHasNew] = useState(false);
  const [downloadingIds, setDownloadingIds] = useState(new Set());
  const { data: wsData } = useWebSocketReceiver({
    filter: { type: Enums.WebSocketType.DownloadNotification },
  });
  const { t } = useTranslation();

  const { data, mutate } = useSWR([open, Fetchers.Api.getDownloadList], async () => {
    try {
      if (!open) return null;
      setLoading(true);
      setHasNew(false);
      return await Fetchers.getDownloadList().then((res) => res?.data?.data);
    } finally {
      setLoading(false);
    }
  });

  const openDrawer = () => {
    setOpen(true);
  };
  const onClose = () => {
    setOpen(false);
  };

  const handleDownload = async (file) => {
    setDownloadingIds((prev) => new Set([...prev, file.id]));
    try {
      await Helper.commandHandler({
        command: {
          type: "direct_download",
          downloadType: file?.fileType === "zip" ? "window" : "default",
          url: file?.downloadUrl,
        },
      });
    } finally {
      setDownloadingIds((prev) => {
        const next = new Set(prev);
        next.delete(file.id);
        return next;
      });
    }
  };

  const onHandleDelete = async (file) => {
    const { id } = file;

    if (id) {
      await new Promise((resolve) => {
        Helper.modal.confirm({
          title: t("AreYouSureYouWantToDeleteThisFile"),
          onOk: async () => {
            try {
              Helper.pageLoading(true);
              const res = await Fetchers.deleteDownloadFile({ id }).then((res) => res?.data);
              if (res?.success) {
                Helper.pageLoading(false);
                mutate();
              }
            } finally {
              Helper.pageLoading(false);
              resolve();
            }
          },
          afterClose: resolve,
        });
      });
    }
  };

  const handleGetGlobalMessageQuery = async () => {
    const res = await Fetchers.getGlobalMessageQuery({
      params: { type: Enums.WebSocketType.DownloadNotification },
    }).then((res) => res?.data);
    if (res.success) {
      setHasNew(res?.data?.has_new ?? false);
    }
  };

  useEffect(() => {
    if (wsData) {
      const { type, command } = wsData.data;

      if (type === Enums.WebSocketType.DownloadNotification) {
        handleGetGlobalMessageQuery();
        command && Helper.commandHandler({ command });
      }
    }
  }, [wsData]);

  useEffect(() => {
    if (open && hasNew) {
      Fetchers.setGlobalMessageResolved({ data: { type: Enums.WebSocketType.DownloadNotification } });
    }
  }, [open, hasNew]);

  useEffect(() => {
    handleGetGlobalMessageQuery();
  }, []);

  return (
    <>
      <Tooltip title={t("ExportList")}>
        <Flex align="center">
          <Button className={styles.button} type="text" shape="circle" onClick={openDrawer}>
            <Badge dot={hasNew} size="small">
              <CloudDownloadOutlined />
            </Badge>
          </Button>
        </Flex>
      </Tooltip>

      <Drawer title={t("FileExportList")} onClose={onClose} open={open}>
        <Loading loading={loading}>
          <div className={styles.container}>
            <ul className={styles.list}>
              {data?.files?.length > 0 ? (
                data.files?.map((file) => (
                  <li key={file.id}>
                    <Flex justify="space-between" align="center">
                      <Tooltip title={t("ClickToDownload")}>
                        <Text
                          className={styles.name}
                          onClick={() => !downloadingIds.has(file.id) && handleDownload(file)}
                          style={{
                            cursor: downloadingIds.has(file.id) ? "not-allowed" : "pointer",
                            opacity: downloadingIds.has(file.id) ? 0.5 : 1,
                          }}
                        >
                          {file?.name}
                          {downloadingIds.has(file.id) && <Spin size="small" style={{ marginLeft: 8 }} />}
                        </Text>
                      </Tooltip>

                      <Flex className={styles.actions} gap={5} align="center">
                        <Tooltip title={t("Delete")}>
                          <Button
                            type="text"
                            shape="circle"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => onHandleDelete(file)}
                          />
                        </Tooltip>
                      </Flex>
                    </Flex>
                    <Divider className={styles.divider} />
                  </li>
                ))
              ) : (
                <Empty />
              )}
            </ul>
          </div>
        </Loading>
      </Drawer>
    </>
  );
}

export default DownloadList;
