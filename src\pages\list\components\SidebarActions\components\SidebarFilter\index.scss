.sidebarFilter {
  height: 100%;

  .batchFilters {
    background-color: red;

    .text {
      width: 100px;
      margin: -5px -11px;
      text-align: left;
      padding: 0 11px;
    }
  }
}

body {
  [class~="ant-form-item"] {
    margin-bottom: 5px;
  }

  [class~="addon-after-text"] {
    width: 100px;
    margin: -5px -11px;
    text-align: left;
    padding: 0 11px;
  }

  [class~="ant-input-group-addon"] {
    .ant-form-item {
      margin-bottom: 0;

      [class~="ant-select"] {
        width: 100px;
      }

      [class~="ant-form-item-control-input"] {
        min-height: auto;
      }
    }
  }
}
