import { Menu as AntdMenu } from "antd";
import PropTypes from "prop-types";
import styles from "./index.module.scss";
import Utils from "utils";
import SvgIcon from "components/common/SvgIcon";
import { Link } from "react-router-dom";

function Menu(props) {
  const { items, ...otherProps } = props;

  function processItems(items) {
    Utils.forEachTree({
      treeData: items,
      forEach(item, index) {
        const { url, label, icon_url, children } = item;
        item.icon = url ? (
          <Link to={url}>
            <SvgIcon src={icon_url} />
          </Link>
        ) : (
          <SvgIcon src={icon_url} />
        );
        item.title = label || "";
        // item.label = !children?.length ? (
        //   <Link className="ant-menu-title-content" to={url}>
        //     {label}
        //   </Link>
        // ) : (
        //   <span className="ant-menu-title-content">{label}</span>
        // );
      },
    });
    return items;
  }

  return (
    <div className={styles.menu}>
      <AntdMenu items={processItems(items)} theme="dark" mode="inline" inlineIndent={15} {...otherProps}></AntdMenu>
    </div>
  );
}

Menu.propTypes = {
  ...AntdMenu.propTypes,
  items: PropTypes.array,
  onSelect: PropTypes.func,
  onClick: PropTypes.func,
};

export default Menu;
