import { Image } from "antd";
import CommonJSONForm from "components/common/JSONForm";
import SelectStone from "../SelectStone";

function JSONForm(props) {
  const extendComponents = {
    Image: ({ item }) => <Image {...item?.props} />,
    SelectStone: ({ item, form }) => <SelectStone {...item.props} form={form} />,
  };

  return <CommonJSONForm {...props} extendComponents={extendComponents} />;
}

export default JSONForm;
