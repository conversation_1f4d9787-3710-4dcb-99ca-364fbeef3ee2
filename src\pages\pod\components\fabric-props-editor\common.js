import { defaultTextProps, FabricObjectType } from "@/pages/pod/common/init-fabric-types";
import Utils from "@/utils";
import podStore from "@/pages/pod/stores";
import styles from "@/pages/pod/components/fabric-props-editor/index.module.scss";
import { fabric } from "fabric";
import { FabricEvent, LayerType } from "@/pages/pod/common";

export const layerItems = [
  { id: "textBox", icon: "https://cdn.customily.com/app/img/text.d5015b66.svg", label: "Text Box" },
  // { id: "curvedBox", icon: "https://cdn.customily.com/app/img/curved-text.63704adf.svg", label: "Curved Box" },
  {
    id: "imagePlaceholder",
    icon: "https://cdn.customily.com/app/img/image-placeholder.8b050070.svg",
    label: "Image Placeholder",
    className: styles.icon1,
  },
  // {
  //   id: "vectorPlaceholder",
  //   icon: "https://cdn.customily.com/app/img/vector-placeholder.aee53d17.svg",
  //   label: "Vector Placeholder",
  //   className: styles.icon1,
  // },
  // { id: "map", icon: "https://cdn.customily.com/app/img/map.0d2e5da9.svg", label: "Map" },
  {
    id: "dynamicImage",
    icon: "https://cdn.customily.com/app/img/dynamic-image.5a1beed2.svg",
    label: "Dynamic Image",
    className: styles.icon1,
  },
  // {
  //   id: "dynamicVector",
  //   icon: "https://cdn.customily.com/app/img/dynamic-vector.cd43c24c.svg",
  //   label: "Dynamic Vector",
  //   className: styles.icon1,
  // },
  // { id: "starMap", icon: "https://cdn.customily.com/app/img/star-map.99613b7a.svg", label: "Star Map" },
];

export const editorHistory = {
  queue: [],
  index: -1,
  reset() {
    this.queue = [];
    this.index = -1;
  },
};

export const forEachActiveObjects = (activeObject, callback) => {
  if (activeObject.type === FabricObjectType.ActiveSelection) {
    activeObject.getObjects?.().forEach(callback);
  } else {
    callback(activeObject, 0);
  }
};

export function getCanvasAllObjects({ canvas }) {
  const objects = [];
  canvas.getObjects().forEach((object) => {
    if (object.type === FabricObjectType.Group) {
      objects.push(object);
      objects.push(...object.getObjects());
    } else {
      objects.push(object);
    }
  });
  return objects;
}

export const setActiveObjects = ({ canvas, objects, beforeRender, render = true }) => {
  if (objects.length > 0) {
    canvas._discardActiveObject();
    if (objects.length > 1) {
      const activeSelection = new fabric.ActiveSelection(objects, { canvas });
      activeSelection.extraData = { id: "activeSelection" };
      beforeRender?.(activeSelection);
      canvas._setActiveObject(activeSelection);
    } else {
      canvas._setActiveObject(objects[0]);
    }
    if (render) {
      canvas.renderAll();
    }
  }
};

export const updateObjectExtraData = ({ activeObject }) => {
  forEachActiveObjects(activeObject, (object) => {
    object.name = object.extraData.name;
    object.zIndex = object.extraData.zIndex = object.canvas
      .getObjects()
      .findIndex((item) => item.extraData.id === object.extraData.id);
    object.elementId = object.extraData.elementId;
    object.optionId = object.extraData.optionId;
  });
};

export const historySaveFields = {
  basic: [
    "top",
    "left",
    "width",
    "height",
    "angle",
    "scaleX",
    "scaleY",
    "skewX",
    "skewY",
    "originX",
    "originY",
    "cropX",
    "cropY",
    "flipX",
    "flipY",
    "selectable",
    "visible",
    "extraData",
  ],
};
historySaveFields[FabricObjectType.CustomImageBox] = [...historySaveFields.basic];
historySaveFields[FabricObjectType.ActiveSelection] = ["top", "left", "width", "height", "angle", "extraData"];
historySaveFields[FabricObjectType.CustomTextBox] = Array.from(
  new Set([...historySaveFields.basic, ...Object.keys(defaultTextProps)])
);

export const updateObjectAndFireEvent = ({ object, setter }) => {
  if (!object) return;
  const oldData = object.toJSON(["extraData", "zIndex"]);
  setter();
  const newData = object.toJSON(["extraData", "zIndex"]);
  if (Utils.JSON.stringify(oldData) !== Utils.JSON.stringify(newData)) {
    podStore.canvas.fire(FabricEvent.ObjectModified, { target: object });
  }
};

export const buttons = [
  {
    id: "moveX",
    title: "move element forward",
    icon: "https://cdn.customily.com/app/img/move-front.04592178.svg",
    onClick: ({ canvas }) => {
      const activeObject = canvas.getActiveObject();
      if (activeObject) {
        updateObjectAndFireEvent({
          object: activeObject,
          setter() {
            activeObject.bringToFront();
            updateObjectExtraData({ activeObject });
          },
        });
      }
    },
  },
  {
    id: "moveY",
    title: "move element backward",
    icon: "https://cdn.customily.com/app/img/move-back.ec1bfe9d.svg",
    onClick: ({ canvas }) => {
      const activeObject = canvas.getActiveObject();
      if (activeObject) {
        updateObjectAndFireEvent({
          object: activeObject,
          setter() {
            activeObject.sendToBack();
            updateObjectExtraData({ activeObject });
          },
        });
      }
    },
  },
  {
    id: "flipX",
    title: "flip horizontal",
    icon: "https://cdn.customily.com/app/img/flip-horizontal.a88e2370.svg",
    onClick: ({ canvas }) => {
      const activeObject = canvas.getActiveObject();
      if (activeObject) {
        updateObjectAndFireEvent({
          object: activeObject,
          setter() {
            forEachActiveObjects(activeObject, (object) => {
              object.set({ flipX: !object.flipX });
            });
            canvas.renderAll();
          },
        });
      }
    },
  },
  {
    id: "flipY",
    title: "flip vertical",
    icon: "https://cdn.customily.com/app/img/flip-vertical.060355d7.svg",
    onClick: ({ canvas }) => {
      const activeObject = canvas.getActiveObject();
      if (activeObject) {
        updateObjectAndFireEvent({
          object: activeObject,
          setter() {
            forEachActiveObjects(activeObject, (object) => {
              object.set({ flipY: !object.flipY });
            });
            canvas.renderAll();
          },
        });
      }
    },
  },
  {
    id: "fitToCanvas",
    title: "fit to canvas",
    icon: "https://cdn.customily.com/app/img/fit-canvas.bc488b69.svg",
    hide({ canvas }) {
      return canvas?.getActiveObject()?.type === FabricObjectType.ActiveSelection;
    },
    onClick({ canvas }) {
      const activeObject = canvas.getActiveObject();
      if (activeObject?.type !== FabricObjectType.ActiveSelection) {
        updateObjectAndFireEvent({
          object: activeObject,
          setter() {
            const canvasWidth = canvas.getWidth() / canvas.getZoom();
            const objectWidth = activeObject.width;
            const scaleX = canvasWidth / objectWidth;
            const canvasHeight = canvas.getHeight() / canvas.getZoom();
            const objectHeight = activeObject.height;
            const scaleY = canvasHeight / objectHeight;
            activeObject.set({ top: 0, left: 0, scaleX, scaleY });
            canvas.renderAll();
          },
        });
      }
    },
  },
  {
    id: "alignTop",
    title: "align top",
    icon: "https://cdn.customily.com/app/img/align-top.c5f1ea49.svg",
    hide: () => {},
    onClick({ canvas }) {
      const activeObject = canvas.getActiveObject();
      if (activeObject) {
        updateObjectAndFireEvent({
          object: activeObject,
          setter() {
            activeObject.set({ top: 0 });
            canvas.renderAll();
          },
        });
      }
    },
  },
  {
    id: "alignVerticalCenter",
    title: "align vertical center",
    icon: "https://cdn.customily.com/app/img/align-center.e0578fad.svg",
    hide: () => {},
    onClick({ canvas }) {
      const activeObject = canvas.getActiveObject();
      if (activeObject) {
        updateObjectAndFireEvent({
          object: activeObject,
          setter() {
            activeObject.viewportCenterV();
            canvas.renderAll();
          },
        });
      }
    },
  },
  {
    id: "alignBottom",
    title: "align bottom",
    icon: "https://cdn.customily.com/app/img/align-bottom.73e8f7fb.svg",
    hide: () => {},
    onClick({ canvas }) {
      const activeObject = canvas.getActiveObject();
      if (activeObject) {
        updateObjectAndFireEvent({
          object: activeObject,
          setter() {
            const canvasHeight = canvas.height / canvas.getZoom();
            const top = canvasHeight - activeObject.getScaledSize().height;
            activeObject.set({ top });
            canvas.renderAll();
          },
        });
      }
    },
  },
  {
    id: "alignLeft",
    title: "align left",
    icon: "https://cdn.customily.com/app/img/align-left.aa83d09c.svg",
    hide: () => {},
    onClick({ canvas }) {
      const activeObject = canvas.getActiveObject();
      if (activeObject) {
        updateObjectAndFireEvent({
          object: activeObject,
          setter() {
            activeObject.set({ left: 0 });
            canvas.renderAll();
          },
        });
      }
    },
  },
  {
    id: "alignHorizontalCenter",
    title: "align horizontal center",
    icon: "https://cdn.customily.com/app/img/align-middle.565291ee.svg",
    hide: () => {},
    onClick({ canvas }) {
      const activeObject = canvas.getActiveObject();
      if (activeObject) {
        updateObjectAndFireEvent({
          object: activeObject,
          setter() {
            activeObject.viewportCenterH();
            canvas.renderAll();
          },
        });
      }
    },
  },
  {
    id: "alignRight",
    title: "align right",
    icon: "https://cdn.customily.com/app/img/align-right.b713d52f.svg",
    hide: () => {},
    onClick({ canvas }) {
      const activeObject = canvas.getActiveObject();
      if (activeObject) {
        updateObjectAndFireEvent({
          object: activeObject,
          setter() {
            const canvasWidth = canvas.width / canvas.getZoom();
            const left = canvasWidth - activeObject.getScaledSize().width;
            activeObject.set({ left });
            canvas.renderAll();
          },
        });
      }
    },
  },
  {
    id: "alignCenter",
    title: "align center",
    icon: "https://images.drawelry.com/pod/manage/align-center-md.svg",
    hide: () => {},
    onClick({ canvas }) {
      const activeObject = canvas.getActiveObject();
      if (activeObject) {
        updateObjectAndFireEvent({
          object: activeObject,
          setter() {
            // activeObject.viewportCenter();
            canvas.viewportCenterObject(activeObject);
            canvas.renderAll();
          },
        });
      }
    },
  },
];

export function createLayouts({ objects }) {
  const layouts = [];
  objects.forEach((object) => {
    if (object.extraData.layout) {
      const layout = layouts.find((item) => item.id === object.extraData.layout.id);
      const item = { type: "object", id: object.extraData.id, name: object.extraData.name };
      if (layout) {
        layout.children.push(item);
      } else {
        layouts.push({
          type: "layout",
          id: object.extraData.layout.id,
          name: object.extraData.layout.name,
          elementId: object.extraData.layout.elementId ?? object.extraData.layout.optionId,
          optionId: object.extraData.layout.optionId,
          children: [item],
        });
      }
    } else {
      layouts.push({ type: "object", id: object.extraData.id });
    }
  });
  return layouts.sort((a, b) => b.elementId - a.elementId);
}

export function loadFromJSON({ canvas, objects }) {
  return new Promise((resolve) => {
    canvas.loadFromJSON(
      { objects },
      () => {
        // canvas.renderAll();
        resolve();
      },
      (json, object) => {
        if (json.type === FabricObjectType.CustomImageBox) {
          const cache = canvas.getObjects().find((item) => item.extraData.id === json.extraData.id);
          if (cache) {
            cache.set({ ...json });
            return cache;
          }
        }
        return object;
      }
    );
  });
}

export function getSelectedLayout({ canvas }) {
  const activeObjects = canvas?.getActiveObjects() || [];
  if (activeObjects.length > 0) {
    const layout = activeObjects[0].extraData.layout;
    if (layout) {
      const layoutObjects = canvas.getObjects().filter((item) => item.extraData.layout?.id === layout.id);
      if (
        activeObjects.length === layoutObjects.length &&
        layoutObjects.every((x) => activeObjects.some((y) => x.extraData.layout.id === y.extraData.layout?.id))
      ) {
        return layout;
      }
    }
  }
}

export function getCurrentLayout({ canvas }) {
  const object = canvas.getActiveObjects()[0];
  if (object) {
    return object.extraData.layout;
  }
}

export const DefaultPlaceholderImage = Object.freeze({
  [LayerType.DynamicImage]: `https://cdn.customily.com/app/img/dynamic_image.6cac0749.png`,
  [LayerType.ImagePlaceholder]: `https://cdn.customily.com/app/img/photo-example.10387cd6.png`,
});
