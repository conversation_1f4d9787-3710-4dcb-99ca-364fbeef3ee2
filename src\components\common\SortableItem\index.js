import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import PropTypes from "prop-types";
import { useEffect, useRef } from "react";
import styles from "./index.module.scss";

function SortableItem(props) {
  const { id, children } = props;
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });
  const paramsRef = useRef({});

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  function handlePointerDown(e) {
    if (e.target.closest(".ant-upload-list-item-actions")) {
      e.preventDefault();
    }
    paramsRef.current.container.classList.add(styles.dragging);
    listeners?.onPointerDown?.(e);
  }

  useEffect(() => {
    function handlePointerUp() {
      paramsRef.current.container.classList.remove(styles.dragging);
    }
    document.body.addEventListener("pointerup", handlePointerUp);

    return function () {
      document.body.removeEventListener("pointerup", handlePointerUp);
    };
  }, []);

  return (
    <div
      ref={(node) => {
        paramsRef.current.container = node;
        setNodeRef?.(node);
      }}
      {...attributes}
      {...listeners}
      style={{ ...style, position: "relative" }}
      onPointerDown={handlePointerDown}
    >
      {children}
    </div>
  );
}

SortableItem.propTypes = {
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  handle: PropTypes.bool,
};

export default SortableItem;
