.swatch {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;

  .option {
    width: 50px;
    height: 50px;
    box-sizing: border-box;
    border: 2px solid #00000029;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .checked {
    border-color: var(--ant-primary-color);
  }

  .label {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    user-select: none;
  }

  img {
    vertical-align: middle;
  }

  &.disabled {
    opacity: 0.5;

    .option {
      cursor: not-allowed;
    }
  }
}
