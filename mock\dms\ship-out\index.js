const orderInfo = require("./order-info");
const productDetail = require("./product-detail");
const repair = require("./repair");
const printCard = require("./print-card");
const orderRestore = require("./order-restore");

module.exports = {
  "GET /rest/v1/ship/order/info": async (req, res) => orderInfo(req, res),
  "GET /rest/v1/ship/order/item/detail": async (req, res) => productDetail(req, res),
  "POST /rest/v1/ship/order/repair": async (req, res) => repair(req, res),
  "GET /rest/v1/ship/order/item/print-card": async (req, res) => printCard(req, res),
  "GET /rest/v1/ship/order/restore": async (req, res) => orderRestore(req, res),
};
