const tree = require("./tree");
const productSort = require("./products");
const sortSetting = require("./sort-setting");

async function handler(req, res) {
  res.status(200).json({
    success: true,
    data: {},
    message: "操作成功",
  });
}

module.exports = {
  "GET /rest/v1/category/product-sort/category-tree": async (req, res) => tree(req, res),
  "GET /rest/v1/category/product-sort/positions": async (req, res) => productSort(req, res),
  "POST /rest/v1/category/product-sort/setting": async (req, res) => sortSetting(req, res),
  "POST /rest/v1/category/product-sort/positions": async (req, res) => handler(req, res),
  "POST /rest/v1/category/product-sort/move": async (req, res) => handler(req, res),
  "POST /rest/v1/category/product-sort/move-top": async (req, res) => handler(req, res),
};
