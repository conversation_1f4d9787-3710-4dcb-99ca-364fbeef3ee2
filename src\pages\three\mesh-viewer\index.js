import styles from "./index.module.scss";
import * as THREE from "three";
import { useEffect, useRef, useState } from "react";
import {
  DRACOLoader,
  DragControls,
  EffectComposer,
  GLTFLoader,
  OrbitControls,
  OutlinePass,
  RenderPass,
} from "three/addons";
import { App, Radio } from "antd";

function MeshViewer() {
  const [meshes, setMeshes] = useState([]);
  const mountRef = useRef();
  const { message: toast } = App.useApp();

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current };

  async function loadModel({ modelLoader, url, arrayBuffer }) {
    const gltf = await new Promise((resolve, reject) => {
      if (url) {
        modelLoader.load(url, resolve, null, reject);
      } else if (arrayBuffer) {
        modelLoader.parse(arrayBuffer, "", resolve, reject);
      }
    });

    const model = gltf.scene;
    function normalizeModel(model, targetSize = 1) {
      // 1. 计算包围盒
      const box = new THREE.Box3().setFromObject(model);
      const size = new THREE.Vector3();
      box.getSize(size);
      const center = new THREE.Vector3();
      box.getCenter(center);

      // 2. 居中
      model.position.sub(center);

      // 3. 缩放
      const maxAxis = Math.max(size.x, size.y, size.z);
      model.scale.multiplyScalar(targetSize / maxAxis);
    }
    normalizeModel(model, 1);
    model.rotation.y = THREE.MathUtils.degToRad(180);

    const meshes = [];
    paramsRef.current.defaultMaterials = {};
    model.traverse((child) => {
      if (child.isMesh) {
        child.material.metalness = 0;
        child.material.roughness = 1;
        meshes.push(child);
        paramsRef.current.defaultMaterials[child.uuid] = child.material.clone();
      }
    });

    return { model, meshes };
  }

  useEffect(() => {
    (async () => {
      const width = mountRef.current.clientWidth;
      const height = mountRef.current.clientHeight;

      // 场景
      const scene = new THREE.Scene();
      scene.background = new THREE.Color("#fff");
      paramsRef.current.scene = scene;

      // 相机
      const camera = new THREE.PerspectiveCamera(60, width / height, 0.1, 1000);
      camera.position.set(0, 0, -1.3);
      camera.lookAt(0, 0, 0);
      paramsRef.current.camera = camera;

      // 渲染器
      const renderer = new THREE.WebGLRenderer({ antialias: true, preserveDrawingBuffer: true });
      renderer.outputColorSpace = THREE.SRGBColorSpace;
      renderer.setSize(width, height);
      renderer.setPixelRatio(window.devicePixelRatio);
      mountRef.current?.appendChild(renderer.domElement);
      paramsRef.current.renderer = renderer;

      // 环境光
      const ambientLight = new THREE.AmbientLight("#fff", 1);
      scene.add(ambientLight);
      paramsRef.current.ambientLight = ambientLight;

      // 平行光
      const directionalLight = new THREE.DirectionalLight("#fff", 2.5);
      directionalLight.position.copy(camera.position);
      directionalLight.castShadow = true;
      scene.add(directionalLight);
      paramsRef.current.directionalLight = directionalLight;

      // 轨道控制器
      const orbitControls = new OrbitControls(camera, renderer.domElement);
      orbitControls.minDistance = 0;
      orbitControls.maxDistance = 3;
      orbitControls.enablePan = false; // 禁止鼠标右键平移

      // 拖拽控制器
      function handleDragStart() {
        orbitControls.enabled = false;
      }
      function handleDrag(event) {}
      function handleDragEnd() {
        orbitControls.enabled = true;
      }
      const dragControls = new DragControls([], camera, renderer.domElement);
      dragControls.transformGroup = true;
      dragControls.addEventListener("dragstart", handleDragStart);
      dragControls.addEventListener("drag", handleDrag);
      dragControls.addEventListener("dragend", handleDragEnd);
      paramsRef.current.dragControls = dragControls;

      // 创建 DRACOLoader 实例
      const dracoLoader = new DRACOLoader();

      // 设置 DRACO 解码器文件的路径
      dracoLoader.setDecoderPath("https://www.gstatic.com/draco/v1/decoders/");

      // 模型加载器
      const modelLoader = new GLTFLoader();
      modelLoader.setDRACOLoader(dracoLoader);
      paramsRef.current.modelLoader = modelLoader;

      // 创建后期处理效果合成器
      const composer = new EffectComposer(renderer);
      composer.setPixelRatio(window.devicePixelRatio);

      // 添加渲染通道
      const renderPass = new RenderPass(scene, camera);
      composer.addPass(renderPass);

      // 创建轮廓效果通道
      const outlinePass = new OutlinePass(new THREE.Vector2(window.innerWidth, window.innerHeight), scene, camera);
      // 配置轮廓效果参数 (匹配Blender风格)
      outlinePass.visibleEdgeColor.set(0xff6600); // 可见边缘颜色 (黄色)
      outlinePass.hiddenEdgeColor.set(0xff6600); // 隐藏边缘颜色 (黄色)
      outlinePass.edgeStrength = 3.0; // 边缘强度
      outlinePass.edgeThickness = 1.0; // 边缘厚度
      outlinePass.edgeGlow = 0.8; // 边缘发光效果
      // outlinePass.usePatternTexture = false; // 不使用纹理图案
      // outlinePass.pulsePeriod = 0; // 无脉动效果
      paramsRef.current.outlinePass = outlinePass;

      // 添加到后期处理
      composer.addPass(outlinePass);

      // 处理窗口大小变化
      const handleWindowResize = async () => {
        const width = mountRef.current.clientWidth;
        const height = mountRef.current.clientWidth;
        camera.aspect = 1;
        camera.updateProjectionMatrix();
        renderer.setSize(width, height);
        composer.setSize(width, height);
      };
      paramsRef.current.handleWindowResize = handleWindowResize;
      window.addEventListener("resize", handleWindowResize);

      // 动画循环
      const animate = () => {
        requestAnimationFrame(animate);
        // renderer.render(scene, camera);
        orbitControls.update();
        composer.render();
      };
      animate();
    })();

    return () => {
      if (mountRef.current) {
        // eslint-disable-next-line react-hooks/exhaustive-deps
        mountRef.current.innerHTML = "";
      }
      window.removeEventListener("resize", paramsRef.current.handleWindowResize);
    };
  }, []);

  return (
    <div className={styles.page}>
      <div className={styles.wrapper}>
        <div className={styles.left}>
          <div ref={mountRef} className={styles.canvasBox}></div>
        </div>
        <div className={styles.right}>
          <div className={styles.propsPanel}>
            <div>
              <input
                type="file"
                onChange={async (event) => {
                  try {
                    const file = event.target.files[0];
                    if (!file) return;
                    const arrayBuffer = await file.arrayBuffer();
                    const { model, meshes } = await loadModel({
                      modelLoader: paramsRef.current.modelLoader,
                      arrayBuffer,
                    });
                    const scene = paramsRef.current.scene;
                    const dragControls = paramsRef.current.dragControls;
                    scene.clear();
                    scene.add(paramsRef.current.ambientLight, paramsRef.current.directionalLight, model);
                    dragControls.objects = [model];
                    setMeshes(meshes);
                  } catch (err) {
                    toast.error(`glb模型文件错误`);
                  }
                }}
              />
            </div>
            <div style={{ display: "flex", flexDirection: "column", gap: 10 }}>
              <div style={{ paddingTop: 10 }}>Mesh列表：</div>
              <Radio.Group
                options={meshes.map((mesh) => ({ label: mesh.name, value: mesh.uuid }))}
                style={{ display: "flex", flexDirection: "column", gap: 5 }}
                size="large"
                onChange={(event) => {
                  const uuid = event.target.value;
                  meshes.forEach((mesh) => {
                    if (mesh.uuid === uuid) {
                      mesh.material = new THREE.MeshStandardMaterial({
                        color: "#c60",
                        emissive: "#888",
                        emissiveIntensity: 1,
                      });
                    } else {
                      mesh.material = paramsRef.current.defaultMaterials[mesh.uuid];
                    }
                  });
                }}
              ></Radio.Group>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default MeshViewer;
