const Api = require("../../../src/fetchers/api");

module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      content: {
        component: "JSONComponents",
        type: "json",
        children: [
          {
            component: "Form",
            submit: {
              request: {
                url: Api.customer,
                data: {},
              },
            },
            props: {
              id: "form",
              initialValues: {},
              labelCol: {
                span: 4,
              },
              wrapperCol: {
                span: 20,
              },
            },
            formItems: [
              {
                key: "brand_ids",
                label: "品牌",
                component: "Select",
                props: {
                  showSearch: true,
                  options: [
                    { value: "ssy", label: "SSY" },
                    { value: "jeulia", label: "JUELIA" },
                  ],
                },
                rules: [{ required: true, message: "This is a required field" }],
              },
              {
                key: "overdue_rule_id",
                label: "规则类型",
                component: "Select",
                props: {
                  showSearch: true,
                  options: [
                    { value: "ssy", label: "SSY" },
                    { value: "jeulia", label: "JUELIA" },
                  ],
                },
                rules: [{ required: true, message: "This is a required field" }],
              },
              {
                key: "penalty_factory",
                label: "罚款工厂",
                component: "Select",
                props: {
                  showSearch: true,
                  options: [
                    { value: "ssy", label: "SSY" },
                    { value: "jeulia", label: "JUELIA" },
                  ],
                },
                rules: [{ required: true, message: "This is a required field" }],
              },
              {
                key: "fine_num",
                label: "罚款件数",
                component: "Input",
                props: { placeholder: "请输入" },
                rules: [{ required: true, message: "This is a required field" }],
              },
              {
                key: "penalty_time",
                label: "罚款时间",
                component: "PenaltyTimePicker",
                rules: [{ required: true, message: "This is a required field" }],
              },
              {
                key: "overdue_day",
                label: "超期天数",
                component: "Input",
                rules: [{ required: true, message: "This is a required field" }],
              },
              {
                key: "pause_date",
                label: "暂停统计天数",
                component: "Input",
                props: {
                  disabled: true,
                },
              },
              {
                key: "fine_amount",
                label: "罚款金额",
                component: "Input",
                rules: [{ required: true, message: "This is a required field" }],
              },
              {
                key: "penalty_content",
                label: "罚款内容",
                component: "Textarea",
              },
            ],
          },
        ],
      },
    },
  });
};
