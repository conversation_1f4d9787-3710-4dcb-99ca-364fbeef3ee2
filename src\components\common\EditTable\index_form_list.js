import { Form, Input, Select, Table, Button, Dropdown } from "antd";
import { useEffect, useRef, useState } from "react";
import { CaretDownOutlined } from "@ant-design/icons";
import Enums from "enums";

function EditTable(props) {
  const { form, value, rowSelection, columns, dataSource, operations = [], isShowAddRow = true, onChange } = props;
  const [tableColumns, setTableColumns] = useState([]);
  const [tableDataSource, setTableDataSource] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const paramsRef = useRef({});
  console.log(props);
  paramsRef.current = {
    ...paramsRef.current,
    initColumnsValue,
    operations,
  };

  function handleAdd() {
    setTableDataSource((data) => {
      const newData = [...data];
      newData.push({ ...newData[0], key: newData[newData.length - 1].key + 1 });
      onChange?.(newData);
      return newData;
    });
  }
  function handleDeleteRow({ row, index }) {
    setTableDataSource((data) => {
      const newData = [...data];
      newData.splice(index, 1);
      onChange?.(newData);
      return newData;
    });
  }

  function createOperations({ operations, row, index }) {
    return operations.map((item, i) => {
      const menu = { ...item?.props };
      if (item?.key === "delete") {
        menu.label = <div onClick={() => handleDeleteRow({ row, index })}>删除</div>;
      } else {
        menu.label = <div onClick={() => {}}>{item.title}</div>;
      }

      return menu;
    });
  }

  function updateValue({ column, value, index }) {
    setTableDataSource((data) => {
      const newData = [...data];
      if (newData[index][column?.dataIndex] === value) return;
      newData[index][column?.dataIndex] = value;
      onChange?.(newData);
      return newData;
    });
  }

  function editableRender({ column, row, value, index }) {
    const { editable } = column;
    const component = editable.component;
    // <Form.Item name={[column.dataIndex, index]} key={[column.dataIndex, index]}></Form.Item>;
    if (component === Enums.Components.Select) {
      return <Select {...editable.props} defaultValue={value} />;
    } else {
      return (
        <Input
          {...editable.props}
          defaultValue={value}
          onChange={(e) => {
            updateValue({ column, value: e.target.value, index });
          }}
        />
      );
    }
  }

  // function columnRender({ column, row, value, index, action }) {
  //   if (column?.valueType === Enums.TableValueType.Operation) {
  //     const items = createOperations({ operations: column?.operations, row, index });
  //     return (
  //       <div>
  //         <Dropdown menu={{ items }} trigger={["click"]} placement="bottomLeft">
  //           <Button type="primary" size="small">
  //             <span>操作</span>
  //             <CaretDownOutlined style={{ margin: 0, fontSize: 10 }} />
  //           </Button>
  //         </Dropdown>
  //       </div>
  //     );
  //   } else if (column?.editable) {
  //     return editableRender({ column, row, value, index });
  //   } else {
  //     return <span>{value}</span>;
  //   }
  // }

  function columnRender({ column, text, field, index }) {
    return (
      <Form.Item name={[field.name, column.dataIndex]} style={{ marginBottom: "0px" }} shouldUpdate>
        <Input />
      </Form.Item>
    );
  }

  function initColumnsValue(columns) {
    columns?.forEach((column, index) => {
      // column.render = (value, row, index) => columnRender({ column, row, value, index });
      column.render = (text, field, index) => columnRender({ column, text, field, index });
    });
    return columns;
  }

  useEffect(() => {
    const { initColumnsValue, operations } = paramsRef.current;

    columns.unshift({ title: "序号", valueType: "key", dataIndex: "key", width: 50 });
    columns.push({
      dataIndex: "operations",
      valueType: "operation",
      title: "操作",
      width: 80,
      fixed: "right",
      align: "center",
      operations: [{ key: "delete" }, ...operations],
    });
    initColumnsValue(columns);
    setTableColumns(columns);
  }, [columns]);

  useEffect(() => {
    if (dataSource) {
      dataSource.forEach((item, index) => {
        item.key = index + 1;
      });
      setTableDataSource(dataSource);
    }
  }, [dataSource]);

  return (
    <>
      <Table
        bordered
        rowKey="key"
        size="small"
        pagination={false}
        rowSelection={
          rowSelection
            ? {
                selectedRowKeys,
                columnWidth: 32,
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRowKeys(selectedRowKeys);
                },
              }
            : false
        }
        {...props}
        columns={tableColumns}
        dataSource={tableDataSource}
      />
      {isShowAddRow ? (
        <Button
          onClick={handleAdd}
          type="primary"
          style={{
            marginTop: 16,
          }}
        >
          添加一行
        </Button>
      ) : null}
    </>
  );
}

export default EditTable;
