import styles from "./index.module.scss";
import useS<PERSON> from "swr";
import Fetchers from "@/fetchers";
import Helper from "@/helpers";
import { useLocation } from "react-router-dom";
import { useState } from "react";
import { Divider, Collapse } from "antd";
import Loading from "components/common/Loading";
import PanelEditor from "components/business/PanelEditor";
import JSONComponents from "components/common/JSONComponent";
import CollapseLabel from "components/common/JSONComponent/components/Collapse/components/label";
import classNames from "classnames";
import Utils from "utils";
import Tags from "components/business/Tags";

function OrderEditor(props) {
  const location = useLocation();
  const params = Utils.getQueryParams(decodeURIComponent(location.search));
  const [loading, setLoading] = useState(false);
  const isInsideIframe = Helper.isInsideIframe();

  const { data } = useSWR(params, async () => {
    try {
      setLoading(true);
      const result = await Fetchers.getOrderDetail({ params }).then((res) => res?.data?.data);
      return result;
    } finally {
      setLoading(false);
    }
  });

  const items = [
    { key: "basic_info", label: "基础信息", data: { children: [{ ...data?.basic_info }] }, component: "PanelEditor" },
    { key: "address", label: "收货地址", data: data?.address, component: "JSONComponents" },
    { key: "customs_declaration", label: "报关信息", data: data?.customs_declaration },
    { key: "product_info", label: "商品信息", data: data?.product_info },
    { key: "remark", label: "备注信息", data: data?.remark },
    { key: "log", label: "操作日志", data: data?.log },
    { key: "shipping_log", label: "发货记录", data: data?.shipping_log },
  ];

  const defaultActiveKey = items.map((item) => item.key);
  const collapseItems = items.map((item) => {
    const children = (
      <div className={styles.panel}>
        {item?.component === "PanelEditor" ? <PanelEditor data={item?.data} /> : <JSONComponents data={item?.data} />}
      </div>
    );

    return {
      key: item?.key,
      label: (
        <span id={item?.key}>
          <CollapseLabel data={{ label: item?.label, actions: item?.data?.actions }} />
        </span>
      ),
      children: children,
    };
  });

  async function handleCommand({ command, ...others }) {
    Helper.commandHandler({ command, ...others });
  }

  return (
    <div className={classNames(styles.container, { [styles.insideIframe]: isInsideIframe })}>
      <div className={styles.navBar}>
        {items?.map((item) => (
          <div key={item.key} className={styles.navBarItem}>
            <a href={`#${item.key}`}>{item?.label}</a>
          </div>
        ))}
        <Divider />
        <div className={styles.navBarItem}>
          <a href={`#${items[0].key}`}>回顶部</a>
        </div>
      </div>
      <div className={styles.content}>
        <Loading loading={loading}>
          <Collapse defaultActiveKey={defaultActiveKey} items={collapseItems} />
        </Loading>
      </div>

      <div className={styles.actions}>
        <div className={styles.actionsWrapper}>
          {data?.actions?.map((item, index) => {
            return (
              <div
                className={styles.operation}
                key={index}
                onClick={(e) => {
                  e.stopPropagation();
                  handleCommand({ command: item?.command });
                }}
              >
                {item?.label}
              </div>
            );
          })}
        </div>
        {data?.tags && (
          <div className={styles.tags}>
            <Tags tags={data?.tags} />
          </div>
        )}
      </div>
    </div>
  );
}

export default OrderEditor;
