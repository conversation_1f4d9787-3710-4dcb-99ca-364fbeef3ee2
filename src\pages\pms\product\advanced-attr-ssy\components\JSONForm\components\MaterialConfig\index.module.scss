.container {
  width: 100%;

  [class~="ant-card"] {
    margin-bottom: 15px;
  }

  table {
    table-layout: fixed;
    border-collapse: collapse;

    thead th {
      border-bottom: 2px solid #dee2e6;
      border-bottom-width: 1px;
      text-align: center;
      line-height: 35px;
      background-color: #f5f4f4;
    }

    th,
    td {
      padding: 2px;
      border: 1px solid #dee2e6;
    }

    td {
      vertical-align: top;
    }

    .materialHeader {
      display: flex;
      align-items: center;
      justify-content: center;

      .materialImage {
        max-height: 32px;
      }

      [class~="ant-radio-wrapper"] {
        height: 32px;
        align-items: center;
        margin: 0;

        span {
          display: flex;
        }
      }
    }
  }
}
