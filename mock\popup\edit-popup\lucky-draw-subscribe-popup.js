const Api = require("../../../src/fetchers/api");

const contentInitialValues = {
  main_bg_img: [
    {
      uid: 1,
      fid: 1,
      name: "photo",
      status: "done",
      url: "https://static.bizseas.com/static/popup/lucky-draw-subscribe-popup/main_bg_img.png",
      // url: "https://static.bizseas.com/static/popup/lucky-draw-subscribe-popup/main_bg_img_horizontal.png",
    },
  ],
  title: "You're one spin away from winning a free order",
  title_color: "#ff0000",
  title_size: 24,
  sub_title: "Enjoy the discount and happy shopping",
  sub_title_color: "#000000",
  sub_title_size: 18,
  wheel_position: "center",
  wheel_bg_img: [
    {
      uid: 1,
      fid: 1,
      name: "photo",
      status: "done",
      url: "https://static.bizseas.com/static/popup/lucky-draw-subscribe-popup/wheel_bg_img.png",
    },
  ],
  wheel_point_img: [
    {
      uid: 1,
      fid: 1,
      name: "photo",
      status: "done",
      url: "https://static.bizseas.com/static/popup/lucky-draw-subscribe-popup/wheel_point_img.png",
    },
  ],
  wheel_point_degrees_1: 60,
  coupon_code_1: "NEW",
  coupon_message_1: "10% OFF FOR ANY ORDER",
  wheel_point_degrees_2: 120,
  coupon_code_2: "NEW22",
  coupon_message_2: "10% OFF FOR ANY ORDER222",
  count_down: true,
  count_down_time: 60,
  count_down_bg_color: "#323232",
  count_down_text_color: "#ffffff",
  email_collection: true,
  email_collection_placeholder: "Just leave your email",
  email_collection_required: true,
  phone_collection: true,
  phone_collection_placeholder: "Enter a valid phone number",
  privacy_policy_checkbox: "hidden",
  privacy_policy_checkbox_error_message: "Please check this box.",
  privacy_terms:
    "I agree to receive recurring automated marketing text messages at the phone number provided. Message frequency varies. Please view our Terms of Service and ",
  privacy_policy: "Privacy Policy.",
  privacy_terms_link: "https://www.baidu.com",
  button_name: "SPIN IT",
  button_bg_color: "#323232",
  button_text_color: "#ffffff",
  button_click_action: "open_result_popup",
  open_link: "https://www.baidu.com",
};

const successInitialValues = {
  success_bg_img: [
    {
      uid: 1,
      fid: 1,
      name: "photo",
      status: "done",
      url: "https://static.bizseas.com/static/popup/lucky-draw-subscribe-popup/success_bg_img.png",
    },
  ],
  success_title: "It's your lucky day!",
  success_title_color: "#0F0F0F",
  success_title_font_size: 30,
  success_coupon_title_color: "#0F0F0F",
  success_coupon_title_font_size: 18,
  success_desc: "Don't forget to use your coupon at checkout!",
  success_desc_color: "#0F0F0F",
  success_desc_font_size: 20,
  copy_success_message: "Copied!",
  success_button_name: "SHOP NOW",
  success_button_bg_color: "#323232",
  success_button_text_color: "#ffffff",
  success_button_click_action: "open_link",
  success_open_link: "https://www.baidu.com",
};

const collapseInitialValues = {
  icon: [
    {
      uid: 1,
      fid: 1,
      name: "photo",
      status: "done",
      url: "https://static.bizseas.com/static/popup/lucky-draw-subscribe-popup/open.gif",
    },
  ],
  width: 80,
  height: 80,
  bottom: 145,
  position: "right",
  z_index: 1000,
};

const steps = [
  {
    title: "基础设置",
    name: "template_info",
    forms: [
      {
        title: "模板设置",
        name: "template_config",
        formItems: [
          {
            key: "type",
            label: "弹窗类型",
            component: "Select",
            props: {
              options: [{ label: "订阅电话弹窗", value: "sms" }],
              disabled: true,
            },
          },
          {
            key: "admin_name",
            component: "Input",
            label: "后台名称",
            rules: [
              {
                message: "后台名称是必填项！",
                required: true,
              },
            ],
          },
          {
            key: "images",
            label: "弹窗说明图",
            component: "ImageUpload",
            props: {
              listType: "picture-card",
              action: Api.uploadFile,
              multiple: true,
              maxCount: 1,
            },
          },
        ],
        props: {
          layout: "vertical",
          initialValues: {
            type: 1,
            admin_name: "黑五转盘订阅弹窗",
            images: [
              {
                uid: 1,
                fid: 1,
                name: "photo",
                status: "done",
                url: "https://res2022.jeulia.com/product/2/1/1000x1000/62fde0f091112.jpg",
              },
            ],
          },
        },
      },
      {
        title: "展示条件",
        name: "condition",
        formItems: [
          {
            key: "user_types",
            label: "人群",
            component: "Select",
            props: {
              mode: "multiple",
              options: [
                { label: "选项1", value: "1" },
                { label: "选项2", value: "2" },
                { label: "选项3", value: "3" },
                { label: "选项4", value: "4" },
                { label: "选项5", value: "5" },
              ],
            },
          },
          {
            key: "device",
            label: "设备",
            component: "Select",
            props: {
              mode: "multiple",
              options: [
                { label: "PC", value: "pc" },
                { label: "M", value: "mobile" },
              ],
            },
          },
          {
            key: "popup_pages",
            label: "展示页面",
            component: "Select",
            props: {
              mode: "multiple",
              options: [
                {
                  value: "home",
                  label: "首页",
                },
                {
                  value: "product",
                  label: "产品页",
                },
                {
                  value: "category",
                  label: "类目页",
                },
                {
                  value: "cart",
                  label: "购物车页",
                },
                {
                  value: "checkout",
                  label: "结算页",
                },
                {
                  value: "success",
                  label: "支付完成页",
                },
                {
                  value: "others",
                  label: "其他页面",
                },
              ],
            },
          },
          {
            key: "exclude_page",
            label: "排除页面",
            component: "Select",
            props: {
              mode: "multiple",
              options: [
                { label: "选项1", value: "1" },
                { label: "选项2", value: "2" },
                { label: "选项3", value: "3" },
                { label: "选项4", value: "4" },
                { label: "选项5", value: "5" },
              ],
            },
          },
          {
            key: "timing",
            label: "弹出时机",
            component: "Select",
            props: {
              options: [
                { label: "即时弹出", value: "immediately" },
                { label: "延时弹出", value: "delayed" },
                { label: "滚动弹出", value: "scroll" },
                { label: "不自动弹出", value: "none" },
              ],
            },
          },
          {
            key: "delay",
            label: "延时秒数",
            component: "Input",
          },
          {
            key: "scroll_distance",
            label: "滚动距离",
            component: "Input",
          },
          {
            key: "interval",
            label: "弹出频率",
            component: "Input",
            rules: [{ required: true, message: "弹出频率是必填项！" }],
            props: {
              extra: "单位：小时，填写24，则表示弹窗关闭24小时后再弹",
            },
          },
        ],
        props: {
          layout: "vertical",
          initialValues: {
            // user_types: ["1", "2", "3"],
            device: ["pc", "mobile"],
            interval: 24,
            popup_pages: ["home", "category"],
            timing: "immediately",
            delay: 0,
            scroll_distance: 100,
          },
        },
      },
    ],
  },
  {
    title: "弹窗主体",
    name: "content",
    forms: [
      {
        name: "main",
        formItems: [
          {
            key: "main_bg_img",
            label: "背景图",
            component: "ImageUpload",
            props: {
              listType: "picture-card",
              action: Api.uploadFile,
              multiple: true,
              maxCount: 1,
              accept: ".png,.jpg,.jpeg,.webp",
              extra: "*仅限上传大小不超过500kb，格式为：png、jpg、jpeg、webp的图片",
            },
          },
          {
            key: "title",
            label: "标题文案",
            component: "Input",
            props: { placeholder: "Please input your value" },
            rules: [{ required: true, message: "This is a required field" }],
          },
          {
            key: "title_color",
            label: "标题文案颜色",
            component: "ColorPicker",
            rules: [{ required: true, message: "This is a required field" }],
          },
          {
            key: "title_size",
            label: "标题文案字号",
            component: "InputNumber",
            props: { placeholder: "Please input your value" },
            rules: [{ required: true, message: "This is a required field" }],
          },
          {
            key: "sub_title",
            label: "副标题文案",
            component: "Input",
            props: { placeholder: "Please input your value" },
            rules: [{ required: true, message: "This is a required field" }],
          },
          {
            key: "sub_title_color",
            label: "副标题文案颜色",
            component: "ColorPicker",
            props: { placeholder: "Please input your value" },
            rules: [{ required: true, message: "This is a required field" }],
          },
          {
            key: "sub_title_size",
            label: "副标题文案字号",
            component: "InputNumber",
            props: { placeholder: "Please input your value" },
            rules: [{ required: true, message: "This is a required field" }],
          },
          {
            key: "wheel_position",
            label: "转盘位置",
            component: "Select",
            props: {
              options: [
                { label: "中图", value: "center" },
                { label: "左图", value: "left" },
              ],
            },
          },
          {
            key: "wheel_bg_img",
            label: "转盘底图",
            component: "ImageUpload",
            props: {
              listType: "picture-card",
              action: Api.uploadFile,
              multiple: true,
              maxCount: 1,
              accept: ".png,.jpg,.jpeg,.webp",
              extra: "*仅限上传大小不超过500kb，格式为：png、jpg、jpeg、webp的图片",
            },
            rules: [{ required: true, message: "This is a required field" }],
          },
          {
            key: "wheel_point_img",
            label: "转盘指针图",
            component: "ImageUpload",
            props: {
              listType: "picture-card",
              action: Api.uploadFile,
              multiple: true,
              maxCount: 1,
              accept: ".png,.jpg,.jpeg,.webp",
              extra: "*仅限上传大小不超过500kb，格式为：png、jpg、jpeg、webp的图片",
            },
            rules: [{ required: true, message: "This is a required field" }],
          },
          {
            key: "wheel_point_degrees_1",
            label: "指针最终指向区域1",
            component: "InputNumber",
            props: { placeholder: "Please input your value", max: 360 },
            rules: [{ required: true, message: "This is a required field" }],
          },
          {
            key: "coupon_code_1",
            label: "区域1的优惠券码",
            component: "Input",
            props: { placeholder: "Please input your value" },
            rules: [{ required: true, message: "This is a required field" }],
          },
          {
            key: "coupon_message_1",
            label: "区域1的优惠券文案",
            component: "Input",
            props: { placeholder: "Please input your value" },
            rules: [{ required: true, message: "This is a required field" }],
          },
          {
            key: "wheel_point_degrees_2",
            label: "指针最终指向区域2",
            component: "Input",
            props: { placeholder: "Please input your value" },
          },
          {
            key: "coupon_code_2",
            label: "区域2的优惠券码",
            component: "Input",
            props: { placeholder: "Please input your value" },
          },
          {
            key: "coupon_message_2",
            label: "区域2的优惠券文案",
            component: "Input",
            props: { placeholder: "Please input your value" },
          },
          {
            key: "wheel_point_degrees_3",
            label: "指针最终指向区域3",
            component: "Input",
            props: { placeholder: "Please input your value" },
          },
          {
            key: "coupon_code_3",
            label: "区域3的优惠券码",
            component: "Input",
            props: { placeholder: "Please input your value" },
          },
          {
            key: "coupon_message_3",
            label: "区域3的优惠券文案",
            component: "Input",
            props: { placeholder: "Please input your value" },
          },
          {
            key: "wheel_duration",
            label: "转盘转动时长",
            component: "InputNumber",
          },
          {
            key: "wheel_circle",
            label: "转盘转动圈数",
            component: "InputNumber",
          },
          {
            key: "wheel_result_duration",
            label: "转盘转动结果展示时长",
            component: "InputNumber",
          },
          {
            key: "count_down",
            label: "倒计时",
            component: "Switch",
          },
          {
            key: "count_down_time",
            label: "时长（单位：分钟）",
            component: "InputNumber",
            props: {
              max: 60,
            },
          },
          {
            key: "count_down_bg_color",
            label: "倒计时背景色",
            component: "ColorPicker",
          },
          {
            key: "count_down_text_color",
            label: "数字颜色",
            component: "ColorPicker",
          },
          {
            key: "email_collection",
            label: "收集邮箱",
            component: "Switch",
          },
          {
            key: "email_collection_placeholder",
            label: "邮箱输入框提示文案",
            component: "Input",
          },
          {
            key: "email_collection_required",
            label: "是否必填",
            component: "Switch",
          },
          {
            key: "phone_collection",
            label: "收集电话",
            component: "Switch",
          },
          {
            key: "phone_collection_placeholder",
            label: "收集电话提示文案",
            component: "Input",
          },
          {
            key: "privacy_policy_checkbox",
            label: "隐私政策复选框状态",
            component: "Select",
            props: {
              options: [
                { label: "隐藏", value: "hidden" },
                { label: "显示默认勾选", value: "checked" },
                { label: "显示默认不勾选", value: "unchecked" },
              ],
            },
            rules: [{ required: true, message: "This is a required field" }],
          },
          {
            key: "privacy_policy_checkbox_error_message",
            label: "隐私政策复选框必选提示文案",
            component: "Input",
          },
          {
            key: "privacy_terms",
            label: "隐私条款内容",
            component: "Textarea",
          },
          {
            key: "privacy_policy",
            label: "隐私政策内容",
            component: "Input",
          },
          {
            key: "privacy_terms_link",
            label: "隐私条款链接",
            component: "Input",
          },
          {
            key: "phone_collection_required",
            label: "是否必填",
            component: "Switch",
          },
          {
            key: "is_step_show",
            label: "是否分步骤展示",
            component: "Switch",
          },
          {
            key: "button_name",
            label: "按钮名称",
            component: "Input",
          },
          {
            key: "button_bg_color",
            label: "按钮背景色",
            component: "ColorPicker",
          },
          {
            key: "button_text_color",
            label: "按钮文案颜色",
            component: "ColorPicker",
          },
          {
            key: "button_click_action",
            label: "点击按钮",
            component: "Select",
            props: {
              options: [
                {
                  label: "打开结果弹窗",
                  value: "open_result_popup",
                },
                {
                  label: "关闭弹窗",
                  value: "close_popup",
                },
                {
                  label: "打开指定链接",
                  value: "open_link",
                },
              ],
            },
          },
          {
            key: "open_link",
            label: "指定链接",
            component: "Input",
            props: { placeholder: "Please input your value" },
          },
        ],
        props: {
          layout: "vertical",
          initialValues: contentInitialValues,
        },
      },
    ],
  },
  {
    title: "结果弹窗",
    name: "content",
    forms: [
      {
        name: "success",
        formItems: [
          {
            key: "success_bg_img",
            label: "背景图",
            component: "ImageUpload",
            props: {
              listType: "picture-card",
              action: Api.uploadFile,
              multiple: true,
              maxCount: 1,
            },
          },
          {
            key: "success_title",
            label: "标题文案",
            component: "Input",
          },
          {
            key: "success_title_color",
            label: "标题文案颜色",
            component: "ColorPicker",
          },
          {
            key: "success_title_font_size",
            label: "标题文案字号",
            component: "InputNumber",
          },
          {
            key: "success_coupon_title_color",
            label: "Coupon提示文案颜色",
            component: "ColorPicker",
          },
          {
            key: "success_coupon_title_font_size",
            label: "Coupon提示文案字号",
            component: "InputNumber",
          },
          {
            key: "success_desc",
            label: "描述文案",
            component: "Input",
          },
          {
            key: "success_desc_color",
            label: "描述文案颜色",
            component: "ColorPicker",
          },
          {
            key: "success_desc_font_size",
            label: "描述文案字号",
            component: "InputNumber",
          },
          {
            key: "copy_success_message",
            label: "复制成功提示文案",
            component: "Input",
            rules: [
              {
                required: true,
                message: "请输入复制成功提示文案",
              },
            ],
          },
          {
            key: "success_button_name",
            label: "按钮文案",
            component: "Input",
          },
          {
            key: "success_button_bg_color",
            label: "按钮背景色",
            component: "ColorPicker",
          },
          {
            key: "success_button_text_color",
            label: "按钮文案颜色",
            component: "ColorPicker",
          },
          {
            key: "success_button_click_action",
            label: "点击按钮",
            component: "Select",
            props: {
              options: [
                {
                  key: "open_link",
                  label: "打开指定链接",
                },
                {
                  key: "close_popup",
                  label: "关闭弹窗",
                },
              ],
            },
          },
          {
            key: "success_open_link",
            label: "指定链接",
            component: "Input",
          },
        ],
        props: {
          layout: "vertical",
          initialValues: successInitialValues,
        },
      },
    ],
  },
  {
    title: "收起样式",
    name: "content",
    forms: [
      {
        name: "collapse_style",
        formItems: [
          {
            key: "icon",
            label: "收起icon",
            component: "ImageUpload",
            props: {
              listType: "picture-card",
              action: Api.uploadFile,
              multiple: true,
              maxCount: 1,
            },
          },
          {
            key: "width",
            label: "宽",
            component: "InputNumber",
          },
          {
            key: "height",
            label: "高",
            component: "InputNumber",
          },
          {
            key: "position",
            label: "位置",
            component: "Select",
            props: {
              options: [
                {
                  value: "left",
                  label: "左",
                },
                {
                  value: "right",
                  label: "右",
                },
              ],
            },
          },
          {
            key: "bottom",
            label: "距离底部高度",
            component: "InputNumber",
          },
          {
            key: "bg_color",
            label: "背景色",
            component: "ColorPicker",
          },
          {
            key: "z_index",
            label: "层级",
            component: "InputNumber",
          },
        ],
        props: {
          layout: "vertical",
          initialValues: collapseInitialValues,
        },
      },
    ],
  },
];

module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      breadcrumbs: [],
      type: "LuckyDrawSubscribePopup",
      steps,
    },
    command: [],
  });
};
