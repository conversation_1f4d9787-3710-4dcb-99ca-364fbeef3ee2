import { App, Button, Form, Input, InputNumber } from "antd";
import styles from "./index.module.scss";
import { CloseOutlined } from "@ant-design/icons";
import { useEffect, useRef, useState } from "react";
import Axios from "axios";
import EnvHelper from "@/helpers/env-helper";
import Utils from "@/utils";
import { createCustomToken } from "@/fetchers/custom-token";
import { ApiTools } from "@/fetchers";
import Helper from "@/helpers";

const axios = Axios.create({});

axios.interceptors.request.use(async (config) => {
  const token = await createCustomToken();
  config.headers.Authorization = `Bearer ${token}`;
  return config;
});

const PrivateFetchers = Object.freeze({
  get id() {
    return "face-swap";
  },

  get origin() {
    const protocol = window.location.protocol;
    const host = EnvHelper.isDevelopment ? "localhost:8085" : "pwa.bizseas.com";
    // const protocol = "https:";
    // const host = "pwa.bizseas.com";
    // https://open.feishu.cn/open-apis/bot/v2/hook/51637950-e750-4104-9ff3-d94d4862b242
    return `${protocol}//${host}`;
  },

  getConfig({ id = this.id } = {}) {
    const url = ApiTools.setPathParams(`${this.origin}/fdc/rest/v1/config/:id`, { id });
    return axios.get(url);
  },

  saveConfig({ id = this.id, data } = {}) {
    return axios.post(`${this.origin}/fdc/rest/v1/config`, { id, data });
  },

  sendNotification({ id = this.id, data } = {}) {
    const url = ApiTools.setPathParams(`${this.origin}/fdc/rest/v1/config/notification/:id`, { id });
    return axios.post(url, data);
  },

  clearNotificationLastTime({ id = this.id } = {}) {
    const url = ApiTools.setPathParams(`${this.origin}/fdc/rest/v1/config/notification/:id`, { id });
    return axios.patch(url, { jsonPatch: [{ op: "updateOne", path: "notification.lastTime", value: null }] });
  },
});

function Crossword() {
  const [initialValues, setInitialValues] = useState();
  const formRef = useRef();
  const { message } = App.useApp();

  useEffect(() => {
    (async () => {
      const version = EnvHelper.isDevelopment ? Date.now() : EnvHelper.appVersion;
      await Utils.appendScript(`/js/12878-79a8d93e20edc137.min.js?v=${version}`, { async: true });
      await Utils.appendScript(`/js/16518-55214b11a211d391.min.js?v=${version}`, { defer: true });
      if (Array.isArray(window.webpackChunk_C_T_EQ)) {
        window.webpackChunk_C_T_EQ.forEach((handler) => handler?.());
      }
      window.webpackChunk_C_T_EQ = Object.freeze({
        push(handler) {
          handler?.();
        },
      });
    })();
  }, []);

  useEffect(() => {
    Helper.pageLoading(true);
    PrivateFetchers.getConfig()
      .then((res) => {
        const values = res.data?.data?.data || {
          balanceAlertThreshold: 1000,
          alertInterval: 60,
          messageTemplate: "piapi换脸接口余额剩余$ {{balance}} ，请及时充值！",
          at: [
            {
              user_id: "7432633666796421148",
              user_name: "刘笑",
            },
          ],
        };
        setInitialValues(values);
        formRef.current?.setFieldsValue(values);
      })
      .finally(() => {
        Helper.pageLoading(false);
      });
  }, []);

  return (
    <div
      style={{
        height: "100vh",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      <div style={{ width: 800 }}>
        <h1 style={{ textAlign: "center" }}>piapi换脸配置</h1>
        <div>
          <Form
            ref={formRef}
            initialValues={initialValues}
            layout="vertical"
            onFinish={(values) => {
              Helper.pageLoading(true);
              PrivateFetchers.saveConfig({ data: values })
                .then(() => {
                  message.success(`保存成功`);
                })
                .finally(() => {
                  Helper.pageLoading(false);
                });
            }}
          >
            <div>
              <Form.Item name="balanceAlertThreshold" label="余额警报阈值" rules={[{ required: true }]}>
                <InputNumber style={{ width: `100%` }} addonAfter="$"></InputNumber>
              </Form.Item>
            </div>
            <div className={styles.row}>
              <Form.Item label="通知间隔" required>
                <Form.Item name="alertInterval" noStyle rules={[{ required: true }]}>
                  <InputNumber style={{ width: `100%` }} addonAfter="分钟"></InputNumber>
                </Form.Item>
                <Button
                  type="primary"
                  onClick={() => {
                    PrivateFetchers.clearNotificationLastTime()
                      .then(() => {
                        message.success(`重置成功`);
                      })
                      .catch((err) => {
                        message.error(`重置失败: ${err.message}`);
                      });
                  }}
                >
                  重置
                </Button>
              </Form.Item>
            </div>
            <div className={styles.row}>
              <Form.Item label="消息模板" required>
                <Form.Item name="messageTemplate" label="消息模板" noStyle rules={[{ required: true }]}>
                  <Input style={{ width: `100%` }}></Input>
                </Form.Item>
                <Button
                  type="primary"
                  onClick={async () => {
                    Helper.pageLoading(true);
                    await PrivateFetchers.clearNotificationLastTime();
                    PrivateFetchers.sendNotification({
                      data: {
                        balance: 6666,
                      },
                    })
                      .then((res) => {
                        message.success("发送成功");
                      })
                      .catch((err) => {
                        message.error(`发送失败: ${err.message}`);
                      })
                      .finally(() => {
                        Helper.pageLoading(false);
                      });
                  }}
                >
                  测试
                </Button>
              </Form.Item>
            </div>
            <div>
              <Form.Item label="艾特">
                <Form.List name="at">
                  {(fields, { remove }) => {
                    return fields.map(({ key, name, ...others }) => (
                      <div key={key} style={{ display: "flex", gap: 8 }}>
                        <Form.Item
                          {...others}
                          name={[name, "user_id"]}
                          rules={[{ required: true, message: "请输入用户ID" }]}
                          style={{ flex: 0.7 }}
                        >
                          <Input placeholder="用户ID"></Input>
                        </Form.Item>
                        <Form.Item
                          {...others}
                          name={[name, "user_name"]}
                          rules={[{ required: true, message: "请输入用户名" }]}
                          style={{ flex: 0.3 }}
                        >
                          <Input placeholder="用户名"></Input>
                        </Form.Item>
                        <Button
                          type="text"
                          style={{
                            width: 32,
                            padding: 0,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <CloseOutlined
                            style={{ fontSize: 16, color: "#666" }}
                            className="dynamic-delete-button"
                            onClick={() => remove(name)}
                          />
                        </Button>
                      </div>
                    ));
                  }}
                </Form.List>
                <Button
                  type="dashed"
                  style={{ width: `100%` }}
                  onClick={() => {
                    const value = formRef.current?.getFieldValue("at") || [];
                    formRef.current?.setFieldValue("at", [...value, { userId: "", name: "" }]);
                  }}
                >
                  添加@人
                </Button>
              </Form.Item>
            </div>
            <div style={{ display: "flex", justifyContent: "center", gap: 20 }}>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
              <Button type="default" htmlType="reset">
                重置
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
}

export default Crossword;
