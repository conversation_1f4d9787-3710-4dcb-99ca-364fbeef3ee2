import { useEffect } from "react";
import { importRemote } from "@module-federation/utilities";
import Axios from "axios";

const axios = Axios.create();

function Test(props) {
  const {} = props;

  useEffect(() => {
    (async () => {
      const version = await axios
        .get(`https://pwa.bizseas.com/app-store/api/version`)
        .then((res) => res.data.data.version);
      const remoteEntryFileName = `app-store/_next/static/chunks/remoteEntry.js?v=${version}`;
      const [dependencies, popups] = await Promise.all([
        importRemote({
          url: "https://pwa.bizseas.com",
          // url: "http://localhost:3000",
          remoteEntryFileName,
          scope: "appStore",
          module: "./dependencies",
          bustRemoteEntryCache: false,
        }),
        importRemote({
          url: "https://pwa.bizseas.com",
          // url: "http://localhost:3000",
          remoteEntryFileName,
          scope: "appStore",
          module: "./popups",
          bustRemoteEntryCache: false,
        }),
      ]);
      const { React, ReactDOM } = dependencies;
      const { PopupTest1: PopupTest } = popups;
      let root = document.getElementById("app1");
      if (!root) {
        root = document.createElement("div");
        root.id = "app1";
        document.body.appendChild(root);
      }
      ReactDOM.render(<PopupTest></PopupTest>, root);
    })();
  }, []);

  return <div style={{ padding: 20 }}></div>;
}

export default Test;
