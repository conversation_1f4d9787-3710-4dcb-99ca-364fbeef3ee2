import { Input, Select } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import Api from "@/fetchers/api";

// 删除了drakey(老项目拼写错误),icon字段,title_label,title_value,placeholder_label
export const widgets = addConditionsForWidgets([
  {
    type: "text",
    required: false,
    title: "",
    placeholder: "",
    max_length: 12,
    key: "Engraving",
    isDisabled: false,
    uppercase: false,
    uppercase_first: false,
    min_length: 1,
    price: {
      base_price: 0,
    },
    font: {
      family: "",
      url: "",
      size: 60,
    },
    name: "",
    widget_name: "单行刻字",
    enable_font_options: false,
    emoji_list: [],
    ban_emoji: false,
    component_config: {
      icon: "EditOutlined",
      // 显示的组件
      preview_component: (placeholder) => <Input type="text" placeholder={placeholder} readOnly />,
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
        font: "product_custom_option_wordart_font",
        emoji_list: "product_custom_option_emoji",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "提示文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: ["price", "base_price"],
                      label: "刻字价格USD",
                      component: "InputNumber",
                      props: {
                        min: 0,
                        placeholder: "请输入",
                      },
                    },
                    {
                      key: "min_length",
                      label: "最小刻字数",
                      component: "InputNumber",
                      props: {
                        min: 1,
                        placeholder: "请输入",
                      },
                    },
                    {
                      key: "max_length",
                      label: "最大刻字数",
                      component: "InputNumber",
                      props: {
                        min: 1,
                        max: 1000,
                        placeholder: "请输入",
                      },
                    },
                    {
                      key: "font",
                      label: "默认字体",
                      component: "FontSelectorWithPreview",
                      props: {
                        placeholder: "请选择",
                      },
                    },
                    {
                      key: "uppercase_first",
                      label: "首字母大写",
                      component: "CheckboxSingle",
                      props: {
                        children: "首字母大写",
                      },
                    },
                    {
                      key: "uppercase",
                      label: "字母自动大写",
                      component: "CheckboxSingle",
                      props: {
                        children: "字母自动大写",
                      },
                    },
                    {
                      key: "ban_emoji",
                      label: "禁止输入emoji，以下除外",
                      component: "CheckboxSingle",
                      props: {
                        children: "禁止输入emoji，以下除外",
                      },
                    },
                    {
                      key: "emoji_list",
                      label: "全选",
                      component: "CheckboxGroup",
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "text_with_font",
    required: false,
    title: "",
    placeholder: "",
    max_length: 12,
    key: "text_with_font",
    isDisabled: false,
    uppercase: false,
    uppercase_first: false,
    min_length: 1,
    price: {
      base_price: 0,
    },
    font: {
      family: "",
      url: "",
      size: 60,
    },
    name: "",
    widget_name: "单行刻字(带字体)",
    enable_font_options: false,
    emoji_list: [],
    ban_emoji: false,
    component_config: {
      icon: "EditOutlined",
      preview_component: (placeholder) => <Input type="text" placeholder={placeholder} readOnly />,
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
        emoji_list: "product_custom_option_emoji",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "提示文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: ["price", "base_price"],
                      label: "刻字价格USD",
                      component: "InputNumber",
                      props: {
                        min: 0,
                        placeholder: "请输入",
                      },
                    },
                    {
                      key: "min_length",
                      label: "最小刻字数",
                      component: "InputNumber",
                      props: {
                        min: 1,
                        placeholder: "请输入",
                      },
                    },
                    {
                      key: "max_length",
                      label: "最大刻字数",
                      component: "InputNumber",
                      props: {
                        min: 1,
                        max: 1000,
                        placeholder: "请输入",
                      },
                    },
                    {
                      key: "uppercase_first",
                      label: "首字母大写",
                      component: "CheckboxSingle",
                      props: {
                        children: "首字母大写",
                      },
                    },
                    {
                      key: "uppercase",
                      label: "字母自动大写",
                      component: "CheckboxSingle",
                      props: {
                        children: "字母自动大写",
                      },
                    },
                    {
                      key: "ban_emoji",
                      label: "禁止输入emoji，以下除外",
                      component: "CheckboxSingle",
                      props: {
                        children: "禁止输入emoji，以下除外",
                      },
                    },
                    {
                      key: "emoji_list",
                      label: "全选",
                      component: "CheckboxGroup",
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "textarea",
    required: false,
    rows: 2,
    title: "",
    key: "textarea",
    placeholder: "",
    max_length: 200,
    price: {
      base_price: 0,
    },
    font: {
      family: "",
      url: "",
      size: 60,
    },
    isDisabled: false,
    name: "",
    widget_name: "多行刻字",
    enable_font_options: false,
    emoji_list: [],
    ban_emoji: false,
    component_config: {
      icon: "CopyOutlined",
      preview_component: (placeholder) => (
        <Input.TextArea placeholder={placeholder} readOnly autoSize={{ minRows: 2 }} />
      ),
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
        font: "product_custom_option_wordart_font",
        emoji_list: "product_custom_option_emoji",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "提示文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: ["price", "base_price"],
                      label: "刻字价格USD",
                      component: "InputNumber",
                      props: {
                        min: 0,
                        placeholder: "请输入",
                      },
                    },
                    {
                      key: "max_length",
                      label: "最大刻字数",
                      component: "InputNumber",
                      props: {
                        min: 1,
                        max: 1000,
                        placeholder: "请输入",
                      },
                    },
                    {
                      key: "rows",
                      label: "最大行数",
                      component: "InputNumber",
                      props: {
                        min: 1,
                        max: 20,
                        placeholder: "请输入",
                      },
                    },
                    {
                      key: "font",
                      label: "默认字体",
                      component: "FontSelectorWithPreview",
                      props: {
                        placeholder: "请选择",
                      },
                    },
                    {
                      key: "ban_emoji",
                      label: "禁止输入emoji，以下除外",
                      component: "CheckboxSingle",
                      props: {
                        children: "禁止输入emoji，以下除外",
                      },
                    },
                    {
                      key: "emoji_list",
                      label: "全选",
                      component: "CheckboxGroup",
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "select",
    placeholder: "-- Please select --",
    required: false,
    title: "",
    key: "select",
    isDisabled: false,
    identifier: "",
    options: [],
    expand: false,
    name: "",
    widget_name: "下拉选项",
    component_config: {
      icon: "DownOutlined",
      preview_component: (placeholder) => (
        <Select placeholder={placeholder} style={{ width: "100%", pointerEvents: "none" }} />
      ),
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
        identifier: "product_custom_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "提示文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: "identifier",
                      label: "选项数据",
                      component: "DropdownOptionsSelector",
                      props: {
                        placeholder: "请选择",
                        linkageKey: "options",
                      },
                    },
                    {
                      key: "options",
                      label: "选项",
                      component: "OptionsSelector",
                      props: {
                        columns: [
                          {
                            dataIndex: "admin_name",
                            title: "选项",
                            width: 150,
                            ellipsis: true,
                            fixed: "left",
                          },
                          {
                            dataIndex: "res_url",
                            title: "图片",
                            width: 65,
                            ellipsis: true,
                            valueType: "image",
                            image: { width: 45 },
                          },
                          {
                            dataIndex: "price",
                            title: "加价USD",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                          {
                            dataIndex: "cost",
                            title: "成本RMB",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                        ],
                        rowSelection: true,
                        isShowAddRow: false,
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "select_icon",
    placeholder: "-- Please select --",
    required: false,
    title: "",
    key: "select",
    isDisabled: false,
    identifier: "",
    options: [],
    expand: false,
    name: "",
    widget_name: "下拉选项（带图标）",
    // placeholder: '',
    component_config: {
      icon: "DownOutlined",
      preview_component: (placeholder) => (
        <Select placeholder={placeholder} style={{ width: "100%", pointerEvents: "none" }} />
      ),
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
        identifier: "product_custom_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "提示文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: "identifier",
                      label: "选项数据",
                      component: "DropdownOptionsSelector",
                      props: {
                        placeholder: "请选择",
                        linkageKey: "options",
                      },
                    },
                    {
                      key: "options",
                      label: "选项",
                      component: "OptionsSelector",
                      props: {
                        columns: [
                          {
                            dataIndex: "admin_name",
                            title: "选项",
                            width: 150,
                            ellipsis: true,
                            fixed: "left",
                          },
                          {
                            dataIndex: "res_url",
                            title: "图片",
                            width: 65,
                            ellipsis: true,
                            valueType: "image",
                            image: { width: 45 },
                          },
                          {
                            dataIndex: "price",
                            title: "加价USD",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                          {
                            dataIndex: "cost",
                            title: "成本RMB",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                        ],
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "radio_list",
    placeholder: "-- Please select --",
    required: false,
    title: "",
    key: "select",
    isDisabled: false,
    identifier: "",
    options: [],
    expand: false,
    name: "",
    widget_name: "平铺选项",
    style: "thumbnail",
    change_title: true,
    component_config: {
      icon: "UnorderedListOutlined",
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
        identifier: "product_custom_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: "identifier",
                      label: "选项数据",
                      component: "DropdownOptionsSelector",
                      props: {
                        placeholder: "请选择",
                        linkageKey: "options",
                      },
                    },
                    {
                      key: "options",
                      label: "选项",
                      component: "OptionsSelector",
                      props: {
                        columns: [
                          {
                            dataIndex: "admin_name",
                            title: "选项",
                            width: 150,
                            ellipsis: true,
                            fixed: "left",
                          },
                          {
                            dataIndex: "res_url",
                            title: "图片",
                            width: 65,
                            ellipsis: true,
                            valueType: "image",
                            image: { width: 45 },
                          },
                          {
                            dataIndex: "price",
                            title: "加价USD",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                          {
                            dataIndex: "cost",
                            title: "成本RMB",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                        ],
                      },
                    },
                    {
                      key: "change_title",
                      label: "是否改变标题",
                      component: "CheckboxSingle",
                      props: {
                        children: "改变标题",
                      },
                    },
                    {
                      key: "style",
                      label: "展示方式",
                      component: "Radio",
                      props: {
                        options: [
                          {
                            label: "默认",
                            value: "default",
                          },
                          {
                            label: "缩略图",
                            value: "thumbnail",
                          },
                        ],
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "upload",
    title: "",
    style: "drag",
    key: "upload",
    required: false,
    shadow: false,
    isDisabled: false,
    name: "",
    widget_name: "上传图片",
    mask_info: {
      image: "",
      // size: {
      //   width: 800,
      //   height: 800,
      // },
    },
    produce_mask_info: {
      image: "",
    },
    component_config: {
      icon: "PictureOutlined",
      preview_component: () => <CustomUpload />,
      identifiers: {
        title: "product_custom_name_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: "style",
                      label: "图片类型",
                      component: "Select",
                      props: {
                        options: [
                          { label: "拖拽", value: "drag" },
                          { label: "自由裁剪", value: "crop-free-size" },
                          {
                            label: "固定裁剪",
                            value: "crop-fixed-size",
                          },
                          {
                            label: "常规(不处理)",
                            value: "normal",
                          },
                        ],
                        showSearch: true,
                        optionFilterProp: "label",
                        placeholder: "请选择",
                      },
                      showFieldsWhen: {
                        face_swap: ["normal"],
                        face_swap_image: ["normal"],
                        [["mask_info", "image"]]: ["drag"],
                        [["produce_mask_info", "image"]]: ["drag"],
                      },
                    },
                    {
                      key: ["mask_info", "image"],
                      label: "遮罩",
                      component: "PictureUpload",
                      props: {
                        // listType: "picture-card",
                        action: Api.uploadFile,
                        data: { disk: "s3-pms" },
                        multiple: false,
                        maxCount: 1,
                        showUploadList: false,
                        listType: "picture",
                      },
                    },
                    {
                      key: ["produce_mask_info", "image"],
                      label: "生产图",
                      component: "PictureUpload",
                      props: {
                        // listType: "picture-card",
                        action: Api.uploadFile,
                        data: { disk: "s3-pms" },
                        multiple: false,
                        maxCount: 1,
                        showUploadList: false,
                      },
                    },
                    {
                      key: "shadow",
                      label: "是否影刻",
                      component: "CheckboxSingle",
                      props: {
                        children: "影刻",
                      },
                    },
                    {
                      key: "face_swap",
                      label: "是否启用换脸",
                      component: "CheckboxSingle",
                      props: {
                        children: "换脸",
                      },
                    },
                    {
                      key: "face_swap_image",
                      label: "换脸背景图",
                      component: "Input",
                      props: {
                        placeholder: "请输入换脸背景图链接",
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "group",
    key: "group",
    required: false,
    title: "",
    placeholder: "",
    groupOption: "",
    isDisabled: false,
    name: "",
    widget_name: "组",
    options: [],
    items: [],
    component_config: {
      icon: "GroupOutlined",
      // 是否是组组件
      isGroup: true,
      // 拖拽后放置容器字段名，默认children
      childrenName: "items",
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
        groupOption: "product_custom_group_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "提示文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: "groupOption",
                      label: "选项数据",
                      component: "DropdownOptionsSelector",
                      props: {
                        placeholder: "请选择",
                        linkageKey: "options",
                      },
                    },
                    {
                      key: "options",
                      label: "选项",
                      component: "OptionsSelector",
                      props: {
                        columns: [
                          {
                            dataIndex: "admin_name",
                            title: "选项",
                            width: 150,
                            ellipsis: true,
                            fixed: "left",
                          },
                          {
                            dataIndex: "res_url",
                            title: "图片",
                            width: 65,
                            ellipsis: true,
                            valueType: "image",
                            image: { width: 45 },
                          },
                          {
                            dataIndex: "price",
                            title: "加价USD",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                          {
                            dataIndex: "cost",
                            title: "成本RMB",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                        ],
                        rowSelection: true,
                        isShowAddRow: false,
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "popup",
    key: "popup",
    button_text: "",
    isDisabled: false,
    image: "",
    name: "",
    widget_name: "弹窗组",
    children: [],
    component_config: {
      icon: "UpSquareOutlined",
      isGroup: true,
      identifiers: {
        button_text: "product_custom_tips_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "button_text",
                      label: "按钮文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "image",
                      label: "图片地址",
                      component: "PictureUpload",
                      props: {
                        action: Api.uploadFile,
                        data: { disk: "s3-pms" },
                        multiple: false,
                        maxCount: 1,
                        showUploadList: false,
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "stepper",
    key: "stepper",
    isDisabled: false,
    name: "",
    widget_name: "步骤分组",
    children: [],
    component_config: {
      icon: "ApartmentOutlined",
      isGroup: true,
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "text_group",
    title: "",
    key: "text_group",
    svg: "",
    button_text: "",
    name: "",
    widget_name: "多行刻字组",
    children: [],
    style: "",
    isDisabled: false,
    component_config: {
      icon: "SnippetsOutlined",
      isGroup: true,
      identifiers: {
        title: "product_custom_name_option",
        button_text: "product_custom_tips_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "button_text",
                      label: "按钮文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "svg",
                      label: "按钮图标",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "style",
                      label: "是否展开",
                      component: "Select",
                      props: {
                        options: [
                          { label: "隐藏", value: "" },
                          { label: "展开", value: "fixed" },
                        ],
                        showSearch: true,
                        optionFilterProp: "label",
                        placeholder: "请选择",
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "composite_group",
    key: "engraving",
    title: "",
    placeholder: "",
    image: "",
    background_image: "",
    required: false,
    name: "",
    widget_name: "组合组件",
    children: [],
    isDisabled: false,
    component_config: {
      icon: "DiffOutlined",
      isGroup: true,
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "按钮文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: "image",
                      label: "遮罩",
                      component: "PictureUpload",
                      props: {
                        action: Api.uploadFile,
                        data: { disk: "s3-pms" },
                        multiple: false,
                        maxCount: 1,
                        showUploadList: false,
                      },
                    },
                    {
                      key: "background_image",
                      label: "底图",
                      component: "PictureUpload",
                      props: {
                        action: Api.uploadFile,
                        data: { disk: "s3-pms" },
                        multiple: false,
                        maxCount: 1,
                        showUploadList: false,
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "write",
    key: "write",
    title: "",
    placeholder: "请选择",
    fontSize: "",
    fontFamily: "",
    isDisabled: false,
    name: "",
    widget_name: "自定义文案",
    component_config: {
      icon: "FileAddOutlined",
      identifiers: {
        title: "product_custom_name_option",
        fontFamily: "product_custom_option_font_family",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "fontFamily",
                      label: "默认字体",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "fontSize",
                      label: "字号",
                      component: "Select",
                      props: {
                        options: [
                          { label: "Small", value: "small" },
                          { label: "Medium", value: "medium" },
                          { label: "Large", value: "large" },
                        ],
                        showSearch: true,
                        optionFilterProp: "label",
                        placeholder: "请选择",
                      },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "preview_text",
    key: "preview_text",
    title: "",
    placeholder: "",
    maxlength: 12,
    uppercase_first: false,
    uppercase: false,
    min_length: 1,
    required: false,
    name: "",
    widget_name: "艺术字",
    font: {
      family: "",
      url: "",
      size: 60,
    },
    color_type: "flex", // fixed/flex
    color: "",
    identifier: "", // product_custom_option_wordart_color
    options: [],
    isDisabled: false,
    component_config: {
      icon: "FontSizeOutlined",
      preview_component: (placeholder) => <Input type="text" placeholder={placeholder} readOnly />,
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
        identifier: "product_custom_option",
        font: "product_custom_option_wordart_font",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "提示文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: "identifier",
                      label: "选项数据",
                      component: "DropdownOptionsSelector",
                      props: {
                        placeholder: "请选择",
                        linkageKey: "options",
                      },
                    },
                    {
                      key: "options",
                      label: "选项",
                      component: "OptionsSelector",
                      props: {
                        columns: [
                          {
                            dataIndex: "admin_name",
                            title: "选项",
                            width: 150,
                            ellipsis: true,
                            fixed: "left",
                          },
                          {
                            dataIndex: "res_url",
                            title: "图片",
                            width: 65,
                            ellipsis: true,
                            valueType: "image",
                            image: { width: 45 },
                          },
                          {
                            dataIndex: "price",
                            title: "加价USD",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                          {
                            dataIndex: "cost",
                            title: "成本RMB",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                        ],
                        rowSelection: true,
                        isShowAddRow: false,
                      },
                    },
                    {
                      key: "min_length",
                      label: "最小刻字数",
                      component: "InputNumber",
                      props: {
                        min: 1,
                        placeholder: "请输入",
                      },
                    },
                    {
                      key: "maxlength",
                      label: "最大刻字数",
                      component: "InputNumber",
                      props: {
                        min: 1,
                        max: 1000,
                        placeholder: "请输入",
                      },
                    },
                    {
                      key: ["font", "size"],
                      label: "字号",
                      component: "InputNumber",
                      props: {
                        min: 1,
                        max: 1000,
                        placeholder: "请输入",
                      },
                    },
                    {
                      key: "color",
                      label: "默认颜色",
                      component: "Select",
                      props: {
                        options: [
                          { label: "Silver", value: "silver" },
                          { label: "Yellow Gold", value: "yellow_gold" },
                          { label: "Rose Gold", value: "rose_gold" },
                          { label: "Black Gold", value: "black_gold" },
                        ],
                        placeholder: "请选择",
                      },
                    },
                    {
                      key: "font",
                      label: "默认字体",
                      component: "FontSelectorWithPreview",
                      props: {
                        placeholder: "请选择",
                      },
                    },
                    {
                      key: "uppercase_first",
                      label: "首字母大写",
                      component: "CheckboxSingle",
                      props: {
                        children: "首字母大写",
                      },
                    },
                    {
                      key: "uppercase",
                      label: "字母自动大写",
                      component: "CheckboxSingle",
                      props: {
                        children: "字母自动大写",
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "spotify",
    title: "",
    key: "spotify",
    required: false,
    placeholder: "",
    mask_info: {
      src: "",
      width: 560,
      height: 560,
    },
    style: "",
    cover: {
      width: null,
      top: null,
      left: null,
      default_photo: "https://images.drawelry.com/assets/product/custom-options/spotify-default-cover.jpg",
      shadow: false,
    },
    barcode: {
      width: null,
      height: null,
      top: null,
      left: null,
      player: false,
      rotate: 0,
    },
    player: {
      width: null,
      height: null,
      top: null,
      left: null,
      rotate: 0,
    },
    colors: [],
    default_color: "",
    isDisabled: false,
    name: "",
    widget_name: "Spotify",
    component_config: {
      icon: "HeatMapOutlined",
      preview_component: () => <CustomUpload />,
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "提示文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "mask_info",
                      label: "图片地址",
                      component: "SpotifyImageUpload",
                      props: {
                        action: Api.uploadFile,
                        // action: "https://test-res.drawelry.com/rest/v1/upload",
                        data: { disk: "s3-pms" },
                        multiple: false,
                        maxCount: 1,
                        showUploadList: false,
                      },
                    },
                    {
                      key: ["cover", "default_photo"],
                      label: "专辑图片地址",
                      component: "Input",
                      props: {
                        disabled: true,
                      },
                    },
                    {
                      key: "default_color",
                      label: "默认颜色",
                      component: "Select",
                      props: {
                        options: [
                          { label: "Black", value: "_000000" },
                          { label: "White", value: "_FFFFFF" },
                        ],
                        placeholder: "请选择",
                      },
                    },
                    {
                      key: "colors",
                      label: "颜色选项",
                      component: "Select",
                      props: {
                        options: [
                          { label: "Black", value: "_000000" },
                          { label: "White", value: "_FFFFFF" },
                        ],
                        placeholder: "请选择",
                        mode: "multiple",
                        labelInValue: true,
                      },
                    },
                    {
                      key: ["barcode", "width"],
                      label: "条码宽度",
                      component: "InputNumber",
                      props: {
                        placeholder: "请输入",
                        disabled: true,
                        style: {
                          width: "100%",
                        },
                      },
                    },
                    {
                      key: ["barcode", "height"],
                      label: "条码高度",
                      component: "InputNumber",
                      props: {
                        placeholder: "请输入",
                        disabled: true,
                        style: {
                          width: "100%",
                        },
                      },
                    },
                    {
                      key: ["barcode", "left"],
                      label: "条码横轴坐标",
                      component: "InputNumber",
                      props: {
                        placeholder: "请输入",
                        disabled: true,
                        style: {
                          width: "100%",
                        },
                      },
                    },
                    {
                      key: ["barcode", "top"],
                      label: "条码纵轴坐标",
                      component: "InputNumber",
                      props: {
                        placeholder: "请输入",
                        disabled: true,
                        style: {
                          width: "100%",
                        },
                      },
                    },
                    {
                      key: ["cover", "width"],
                      label: "专辑宽度",
                      component: "InputNumber",
                      props: {
                        placeholder: "请输入",
                        disabled: true,
                        style: {
                          width: "100%",
                        },
                      },
                    },
                    {
                      key: ["cover", "left"],
                      label: "专辑横轴坐标",
                      component: "InputNumber",
                      props: {
                        placeholder: "请输入",
                        disabled: true,
                        style: {
                          width: "100%",
                        },
                      },
                    },
                    {
                      key: ["cover", "top"],
                      label: "专辑纵轴坐标",
                      component: "InputNumber",
                      props: {
                        placeholder: "请输入",
                        disabled: true,
                        style: {
                          width: "100%",
                        },
                      },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: ["cover", "shadow"],
                      label: "是否影刻",
                      component: "CheckboxSingle",
                      props: {
                        children: "影刻",
                      },
                    },
                    {
                      key: ["barcode", "player"],
                      label: " ",
                      component: "Input",
                      props: {
                        style: { display: "none" },
                      },
                    },
                    {
                      key: ["barcode", "rotate"],
                      label: " ",
                      component: "Input",
                      props: {
                        style: { display: "none" },
                      },
                    },
                    {
                      key: "player",
                      label: " ",
                      component: "Input",
                      props: {
                        style: { display: "none" },
                      },
                    },
                    {
                      key: "style",
                      label: " ",
                      component: "Input",
                      props: {
                        style: { display: "none" },
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "swatch",
    title: "",
    key: "color",
    change_title: true,
    required: false,
    options: [],
    isDisabled: false,
    identifier: "",
    style: "color",
    name: "",
    widget_name: "色卡",
    component_config: {
      icon: "FileTextOutlined",
      identifiers: {
        title: "product_custom_name_option",
        identifier: "product_custom_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: "identifier",
                      label: "选项数据",
                      component: "DropdownOptionsSelector",
                      props: {
                        placeholder: "请选择",
                        linkageKey: "options",
                      },
                    },
                    {
                      key: "options",
                      label: "选项",
                      component: "OptionsSelector",
                      props: {
                        columns: [
                          {
                            dataIndex: "admin_name",
                            title: "选项",
                            width: 150,
                            ellipsis: true,
                            fixed: "left",
                          },
                          {
                            dataIndex: "res_url",
                            title: "图片",
                            width: 65,
                            ellipsis: true,
                            valueType: "image",
                            image: { width: 45 },
                          },
                          {
                            dataIndex: "price",
                            title: "加价USD",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                          {
                            dataIndex: "cost",
                            title: "成本RMB",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                        ],
                        rowSelection: true,
                        isShowAddRow: false,
                      },
                    },
                    {
                      key: "change_title",
                      label: "是否改变标题",
                      component: "CheckboxSingle",
                      props: {
                        children: "改变标题",
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "date_picker",
    title: "",
    key: "date_picker",
    required: false,
    placeholder: "",
    isDisabled: false,
    name: "",
    widget_name: "日期",
    disable_past: false,
    format: "",
    number_type: "arab",
    upload_calendar_image: false,
    component_config: {
      icon: "InsertRowAboveOutlined",
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "提示文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "name",
                      label: "name(选填)",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: "format",
                      label: "日期格式化",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "number_type",
                      label: "数字类型",
                      component: "Select",
                      props: {
                        options: [
                          { label: "阿拉伯数字", value: "arab" },
                          { label: "罗马数字", value: "roman" },
                        ],
                        showSearch: true,
                        optionFilterProp: "label",
                        placeholder: "请选择",
                      },
                    },
                    {
                      key: "disable_past",
                      label: "是否禁用当天之前的日期",
                      component: "CheckboxSingle",
                      props: {
                        children: "禁用当天之前的日期",
                      },
                    },
                    {
                      key: "upload_calendar_image",
                      label: "是否上传日历图片",
                      component: "CheckboxSingle",
                      props: {
                        children: "上传日历图片",
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "glasses_select",
    key: "glasses_select",
    isDisabled: false,
    template_id: null,
    title: "",
    name: "",
    widget_name: "眼镜",
    glasses_front_image: {
      src: "",
      width: null,
      height: null,
    },
    glasses_back_image: {
      src: "",
      width: null,
      height: null,
    },
    hide_frame_only: false,
    component_config: {
      icon: "EyeOutlined",
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "template_id",
                      label: "眼镜模版",
                      component: "GlassesTemplateSelect",
                      props: {
                        optionsApi: Api.getGlassesTemplate,
                        placeholder: "请选择眼镜模版",
                      },
                    },
                    {
                      key: "glasses_front_image",
                      label: "正面图片",
                      component: "GlassesPictureUpload",
                      props: {
                        action: Api.uploadFile,
                        data: { disk: "s3-pms" },
                        multiple: false,
                        maxCount: 1,
                        showUploadList: false,
                      },
                    },
                    {
                      key: "glasses_back_image",
                      label: "反面图片",
                      component: "GlassesPictureUpload",
                      props: {
                        action: Api.uploadFile,
                        data: { disk: "s3-pms" },
                        multiple: false,
                        maxCount: 1,
                        showUploadList: false,
                      },
                    },
                    {
                      key: "hide_frame_only",
                      label: "是否隐藏Frame Only按钮",
                      component: "CheckboxSingle",
                      props: {
                        children: "隐藏Frame Only按钮",
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "dialog_checkbox",
    key: "dialog_checkbox",
    options: [],
    placeholder: "",
    isDisabled: false,
    min_count: 1,
    max_count: 5,
    title: "",
    name: "",
    widget_name: "弹窗多选",
    required: false,
    component_config: {
      icon: "FileDoneOutlined",
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
        identifier: "product_custom_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "提示文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: "identifier",
                      label: "选项数据",
                      component: "DropdownOptionsSelector",
                      props: {
                        placeholder: "请选择",
                        linkageKey: "options",
                      },
                    },
                    {
                      key: "options",
                      label: "选项",
                      component: "OptionsSelector",
                      props: {
                        columns: [
                          {
                            dataIndex: "admin_name",
                            title: "选项",
                            width: 150,
                            ellipsis: true,
                            fixed: "left",
                          },
                          {
                            dataIndex: "res_url",
                            title: "图片",
                            width: 65,
                            ellipsis: true,
                            valueType: "image",
                            image: { width: 45 },
                          },
                          {
                            dataIndex: "price",
                            title: "加价USD",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                          {
                            dataIndex: "cost",
                            title: "成本RMB",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                        ],
                        rowSelection: true,
                        isShowAddRow: false,
                      },
                    },
                    {
                      key: "min_count",
                      label: "最少可选",
                      component: "InputNumber",
                      props: {
                        min: 1,
                        max: 1000,
                        placeholder: "请输入",
                      },
                    },
                    {
                      key: "max_count",
                      label: "最大可选",
                      component: "InputNumber",
                      props: {
                        min: 1,
                        max: 1000,
                        placeholder: "请输入",
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "star_map",
    key: "star_map",
    placeholder: "",
    isDisabled: false,
    title: "",
    name: "",
    widget_name: "星图",
    celestial: {
      preview: {
        stars: {
          // 星星密度
          limit: 4.6,
          // 星星大小
          size: 4,
          // 彩色星星，为true时星星颜色无效
          colors: true,
          // 星星颜色
          style: {
            fill: "#fff",
          },
        },
        constellations: {
          // 星座线条
          lineStyle: {
            stroke: "#fff",
            width: 1,
            opacity: 1,
          },
        },
        background: { fill: "transparent", stroke: "#fff", width: 0.5 },
      },
      production: {
        width: 2000,
        height: 2000,
        stars: { limit: 4.6, size: 16, colors: true },
        constellations: { lineStyle: { stroke: "#fff", width: 2 } },
        background: { fill: "transparent", width: 1 },
      },
    },
    container_style: {
      backgroundColor: "#000",
    },
    component_config: {
      icon: "FileImageOutlined",
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
          watch: {
            // 监听生产图宽度变化
            [["celestial", "production", "width"]]: {
              target: ["celestial", "production", "height"],
              type: "sync",
            },
            [["celestial", "preview", "stars", "colors"]]: {
              target: ["celestial", "production", "stars", "colors"],
              type: "sync",
            },
            [["celestial", "preview", "stars", "style", "fill"]]: {
              target: ["celestial", "production", "stars", "style", "fill"],
              type: "sync",
            },
            [["celestial", "preview", "constellations", "lineStyle", "stroke"]]: {
              target: ["celestial", "production", "constellations", "lineStyle", "stroke"],
              type: "sync",
            },
            [["celestial", "preview", "background", "stroke"]]: {
              target: ["celestial", "production", "background", "stroke"],
              type: "sync",
            },
          },
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "提示文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: ["celestial", "preview", "constellations", "lineStyle", "opacity"],
                      label: "预览图配置",
                      component: "StarMapPreview",
                      props: {
                        mode: "preview",
                      },
                    },
                    {
                      key: ["celestial", "preview", "stars", "limit"],
                      label: "星星密度",
                      component: "InputNumber",
                      props: { placeholder: "请输入", step: 0.1 },
                    },
                    {
                      key: ["celestial", "preview", "stars", "size"],
                      label: "星星大小",
                      component: "InputNumber",
                      props: { placeholder: "请输入", step: 0.1 },
                    },
                    {
                      key: ["celestial", "preview", "stars", "colors"],
                      label: "是否彩色",
                      component: "CheckboxSingle",
                      props: {
                        children: "彩色星星",
                      },
                      showFieldsWhen: {
                        [["celestial", "preview", "stars", "style", "fill"]]: [false],
                      },
                    },
                    {
                      key: ["celestial", "preview", "stars", "style", "fill"],
                      label: "星星颜色",
                      component: "ColorPicker",
                    },
                    {
                      key: ["celestial", "preview", "constellations", "lineStyle", "width"],
                      label: "星座线条",
                      component: "InputNumber",
                      props: { placeholder: "请输入", step: 0.1 },
                    },
                    {
                      key: ["celestial", "preview", "constellations", "lineStyle", "stroke"],
                      label: "星座线条颜色",
                      component: "ColorPicker",
                    },
                    {
                      key: ["celestial", "preview", "background", "width"],
                      label: "边框线条",
                      component: "InputNumber",
                      props: { placeholder: "请输入", step: 0.1 },
                    },
                    {
                      key: ["celestial", "preview", "background", "stroke"],
                      label: "边框线条颜色",
                      component: "ColorPicker",
                    },
                    {
                      key: ["container_style", "backgroundColor"],
                      label: "预览背景色",
                      component: "ColorPicker",
                    },
                    {
                      key: ["celestial", "production", "stars", "style"],
                      label: "生产图配置",
                      component: "StarMapPreview",
                      props: {
                        mode: "production",
                      },
                    },

                    {
                      key: ["celestial", "production", "width"],
                      label: "生产图尺寸",
                      component: "InputNumber",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: ["celestial", "production", "stars", "limit"],
                      label: "星星密度",
                      component: "InputNumber",
                      props: { placeholder: "请输入", step: 0.1 },
                    },
                    {
                      key: ["celestial", "production", "stars", "size"],
                      label: "星星大小",
                      component: "InputNumber",
                      props: { placeholder: "请输入", step: 0.1 },
                    },
                    {
                      key: ["celestial", "production", "constellations", "lineStyle", "width"],
                      label: "星座线条",
                      component: "InputNumber",
                      props: { placeholder: "请输入", step: 0.1 },
                    },
                    {
                      key: ["celestial", "production", "background"],
                      label: " ",
                      component: "Input",
                      props: {
                        style: { display: "none" },
                      },
                    },
                    {
                      key: ["celestial", "preview", "background", "fill"],
                      label: " ",
                      component: "Input",
                      props: { style: { display: "none" } },
                    },
                    // 生产图尺寸
                    {
                      key: ["celestial", "production", "height"],
                      label: " ",
                      component: "InputNumber",
                      props: { style: { display: "none" } },
                    },
                    {
                      key: ["celestial", "production", "stars", "colors"],
                      label: " ",
                      component: "Input",
                      props: { style: { display: "none" } },
                    },
                    {
                      key: ["celestial", "production", "stars", "style", "fill"],
                      label: " ",
                      component: "ColorPicker",
                      props: { style: { display: "none" } },
                    },
                    {
                      key: ["celestial", "production", "constellations", "lineStyle", "stroke"],
                      label: " ",
                      component: "ColorPicker",
                      props: { style: { display: "none" } },
                    },
                    {
                      key: ["celestial", "production", "background", "stroke"],
                      label: " ",
                      component: "ColorPicker",
                      props: { style: { display: "none" } },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "map",
    key: "map",
    placeholder: "",
    isDisabled: false,
    title: "",
    name: "",
    widget_name: "地图",
    map_options: {
      style: "https://api.maptiler.com/maps/toner-v2/style.json?key=k5gxrPavubPDki0zQ6U0",
    },
    marker_options: {
      scale: 1,
    },
    marker_icon: {
      src: "https://static.bizseas.com/static/images/heart.png",
      width: 30,
    },
    mask_image: "",
    component_config: {
      icon: "GlobalOutlined",
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "提示文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: ["map_options", "style"],
                      label: "地图样式",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: ["marker_icon", "src"],
                      label: "图标",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: ["marker_icon", "width"],
                      label: "图标大小",
                      component: "InputNumber",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: "mask_image",
                      label: "遮罩图",
                      component: "Input",
                      props: { placeholder: "请输入" },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "pod",
    key: "pod",
    placeholder: "",
    isDisabled: false,
    title: "",
    name: "",
    widget_name: "POD",
    option_sets: null,
    template: null,
    component_config: {
      icon: "RadiusSettingOutlined",
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "option_set",
                      label: "POD配置",
                      component: "PodEdit",
                    },
                    {
                      key: "template",
                      label: " ",
                      component: "Input",
                      props: {
                        style: { display: "none" }, // 隐藏输入框，保留字段
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "crossword",
    key: "crossword",
    placeholder: "",
    isDisabled: false,
    title: "",
    name: "",
    widget_name: "填字游戏",
    options: {
      cellBgImg: null,
      width: 800,
      height: 800,
      enableScore: true,
      textColor: null,
    },
    component_config: {
      icon: "AreaChartOutlined",
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "提示文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                    {
                      key: ["options", "cellBgImg"],
                      label: "格子背景图",
                      component: "Input",
                      props: {
                        placeholder: "请输入",
                      },
                    },
                    {
                      key: ["options", "width"],
                      label: "宽度",
                      component: "InputNumber",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: ["options", "height"],
                      label: "高度",
                      component: "InputNumber",
                      props: { placeholder: "请输入" },
                    },
                    {
                      key: ["options", "enableScore"],
                      label: "是否开启分数",
                      component: "CheckboxSingle",
                      props: {
                        children: "分数",
                      },
                    },
                    {
                      key: ["options", "textColor"],
                      label: "文案颜色",
                      component: "ColorPicker",
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "moon_phase",
    key: "moon_phase",
    placeholder: "",
    title: "",
    name: "",
    widget_name: "月相",
    component_config: {
      icon: "AreaChartOutlined",
      identifiers: {
        title: "product_custom_name_option",
        placeholder: "product_custom_tips_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "placeholder",
                      label: "提示文案",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "必填",
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "yes_no_toggle",
    key: "yes_no_toggle",
    widget_name: "Yes|No 复选项",
    title: "",
    block_id: "",
    display_type: "switch",
    value: "yes",
    component_config: {
      icon: "CheckSquareOutlined",
      identifiers: {
        title: "product_custom_name_option",
        identifier: "product_custom_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "identifier",
                      label: "选项数据",
                      component: "DropdownOptionsSelector",
                      props: {
                        placeholder: "请选择",
                        linkageKey: "options",
                      },
                    },
                    {
                      key: "options",
                      label: "选项",
                      component: "OptionsSelector",
                      props: {
                        columns: [
                          {
                            dataIndex: "admin_name",
                            title: "选项",
                            width: 150,
                            ellipsis: true,
                            fixed: "left",
                          },
                          {
                            dataIndex: "price",
                            title: "加价USD",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                          {
                            dataIndex: "cost",
                            title: "成本RMB",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                        ],
                      },
                    },
                    {
                      key: "display_type",
                      label: "样式",
                      component: "Select",
                      props: {
                        options: [
                          {
                            label: "开关",
                            value: "switch",
                          },
                          {
                            label: "复选框",
                            value: "checkbox",
                          },
                        ],
                      },
                    },
                    {
                      key: "value",
                      label: "默认开启",
                      component: "CheckboxSingle",
                      props: {
                        children: "默认开启",
                      },
                    },
                    {
                      key: "block_id",
                      label: "说明 Block",
                      component: "Input",
                      props: {
                        placeholder: "请输入说明 Block",
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
  {
    type: "multi_style_image",
    key: "multi_style_image",
    widget_name: "多风格图片",
    change_title: true,
    component_config: {
      icon: "PictureOutlined",
      identifiers: {
        title: "product_custom_name_option",
        identifier: "product_custom_option",
      },
      widget_config: {
        component: "Form",
        type: "json",
        props: {
          id: "configDrawer",
          layout: "vertical",
        },
        formItems: [
          {
            component: "Collapse",
            props: {
              defaultActiveKey: ["1"],
              items: [
                {
                  key: "1",
                  label: "选项设置",
                  children: [
                    {
                      key: "title",
                      label: "选项名称",
                      component: "DictionarySelect",
                      props: { placeholder: "请选择" },
                    },
                    {
                      key: "identifier",
                      label: "选项数据",
                      component: "DropdownOptionsSelector",
                      props: {
                        placeholder: "请选择",
                        linkageKey: "options",
                      },
                    },
                    {
                      key: "options",
                      label: "选项",
                      component: "OptionsSelector",
                      props: {
                        columns: [
                          {
                            dataIndex: "admin_name",
                            title: "选项",
                            width: 150,
                            ellipsis: true,
                            fixed: "left",
                          },
                          {
                            dataIndex: "price",
                            title: "加价USD",
                            width: 100,
                            editable: {
                              component: "InputNumber",
                              props: {
                                placeholder: "请输入",
                              },
                            },
                          },
                        ],
                      },
                    },
                    {
                      key: "required",
                      label: "是否必填",
                      component: "CheckboxSingle",
                      props: {
                        children: "是否必填",
                      },
                    },
                    {
                      key: "change_title",
                      label: "改变标题",
                      component: "Radio",
                      props: {
                        options: [
                          {
                            label: "是",
                            value: true,
                          },
                          {
                            label: "否",
                            value: false,
                          },
                        ],
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  },
]);

function CustomUpload() {
  return (
    <button
      style={{
        border: "1px solid #d9d9d9",
        background: "#fff",
        padding: "26px",
        borderRadius: "4px",
      }}
      type="button"
    >
      <PlusOutlined />
      <div
        style={{
          marginTop: 8,
        }}
      >
        Upload
      </div>
    </button>
  );
}

function addConditionsForWidgets(list = []) {
  if (!Array.isArray(list) || list.length === 0) {
    return list;
  }

  const defaultTemp = {
    key: "1",
    label: "条件设置",
    children: [
      {
        key: "conditions",
        label: "",
        component: "Conditions",
        props: {},
      },
    ],
  };

  const excludeWidgets = [];
  let result = list;

  if (excludeWidgets.length > 0) {
    result = result.filter((widget) => !excludeWidgets.includes(widget.type));
  }

  result = result.map((widget) => {
    const widgetConfig = widget?.component_config?.widget_config;
    if (widgetConfig) {
      const formItems = widgetConfig?.formItems;

      if (formItems?.length) {
        const foundCollapseItem = formItems.find((item) => item.component === "Collapse");
        const items = foundCollapseItem?.props?.items;

        if (Array.isArray(items) && items.length > 0) {
          const latestKey = items[items.length - 1].key;
          items.push({
            ...defaultTemp,
            key: `${Number(latestKey) + 1}`,
          });
        } else {
          items.push(defaultTemp);
        }
      }
    }

    return widget;
  });

  return result;
}
