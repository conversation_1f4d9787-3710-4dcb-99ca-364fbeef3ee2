import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

import Item from "../Item";

function SortableItem(props) {
  const { id, item } = props;

  const { isDragging, attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id,
    data: item,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: transition || undefined,
  };

  return <Item ref={setNodeRef} style={style} isDragging={isDragging} {...attributes} {...listeners} {...props} />;
}

export default SortableItem;
