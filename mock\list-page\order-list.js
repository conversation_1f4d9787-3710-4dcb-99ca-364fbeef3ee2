const Api = require("../../src/fetchers/api");

const operations = [
  {
    key: "edit",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    title: "编辑",
    command: {
      type: "redirect",
      url: "/edit/order?a=1",
      openInNewTab: true,
    },
  },
  {
    key: "modal_edit",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    title: "弹窗编辑",
    command: {
      type: "modal",
      closable: true,
      title: "编辑",
      footer: [
        {
          title: "确认",
          props: { type: "primary" },
          command: {
            type: "request",
            request: {
              url: Api.customer,
              data: { action: "contact" },
            },
            confirm: "确定要联系客户吗？",
            command: {
              type: "reload_table",
            },
          },
        },
        {
          title: "保存",
          props: { type: "primary" },
          command: {
            type: "submit",
            id: "form2",
          },
        },
      ],
      content: {
        component: "JSONComponents",
        type: "json",
        props: {},
        children: [
          {
            component: "Form",
            type: "json",
            props: {
              layout: "vertical",
              id: "form2",
              initialValues: { field5: "field5", field6: "field6" },
              labelCol: {
                span: 8,
              },
              wrapperCol: {
                span: 16,
              },
            },
            formItems: [
              {
                component: "Row",
                props: { gutter: 16 },
                children: [
                  {
                    component: "Col",
                    props: { span: 12 },
                    children: [
                      {
                        key: "field1",
                        label: "Field1",
                        component: "Input",
                        props: {},
                        rules: [{ required: true, message: "This is a required field" }],
                      },
                    ],
                  },
                  {
                    component: "Col",
                    props: { span: 12 },
                    children: [
                      {
                        key: "field2",
                        label: "Field2",
                        component: "Input",
                        props: {},
                        rules: [{ required: true, message: "This is a required field" }],
                      },
                    ],
                  },
                  {
                    component: "Col",
                    props: { span: 12 },
                    children: [
                      {
                        key: "field3",
                        label: "Field3",
                        component: "Input",
                        props: {},
                        rules: [{ required: true, message: "This is a required field" }],
                      },
                    ],
                  },
                  {
                    component: "Col",
                    props: { span: 12 },
                    children: [
                      {
                        key: "field4",
                        label: "Field4",
                        component: "Textarea",
                        props: { rows: 5 },
                        rules: [],
                      },
                    ],
                  },
                ],
              },
              {
                key: "field5",
                label: "Field5",
                component: "Input",
                props: {},
                rules: [{ required: true, message: "This is a required field" }],
              },
              {
                key: "field6",
                label: "Field6",
                component: "Input",
                props: {},
                rules: [{ required: true, message: "This is a required field" }],
              },
            ],
            fetcher: {},
            submit: {
              request: {
                url: Api.customer,
                data: {},
              },
            },
          },
        ],
      },
    },
  },
  {
    key: "modal_gallery",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    title: "弹窗gallery",
    command: {
      type: "modal",
      closable: true,
      title: "查看面单",
      props: {
        width: 1500,
        style: {
          maxHeight: "90vh",
        },
      },
      content: {
        component: "JSONComponents",
        type: "json",
        props: {},
        children: [
          {
            component: "Gallery",
            props: {
              centered: true,
            },
            items: [
              {
                image: {
                  src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/27/171682111566549c7babddb0.png",
                },
                style: {},
              },
              {
                image: {
                  src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/30/1717080509665891bd60f400.png",
                },
              },
              {
                image: {
                  src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/29/17169541166656a404adbca0.png",
                },
              },
              {
                image: {
                  src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/28/1716848967665509476374f0.png",
                },
              },
              {
                image: {
                  src: "https://erp5-dms.cnzlerp.com/uploads/logistics/hsd/waybill/2024/05/27/171678882566541e59bc80b0.png?1=%2Fuploads%2Flogistics%2Fhsd%2Fwaybill%2F2024%2F05%2F27%2F171678882566541e59bc80b1.png",
                },
              },
            ],
          },
        ],
      },
    },
  },
  {
    key: "view",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    title: "查看",
    command: {
      type: "redirect",
      url: "/detail/order",
    },
  },
  {
    key: "contact",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    title: "联系客户",
    command: {
      type: "request",
      request: {
        url: Api.customer,
        data: { action: "contact" },
      },
      confirm: "确定要联系客户吗？",
      command: {
        type: "reload_table",
      },
    },
  },
  {
    key: "reload_table",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    title: "刷新table",
    command: {
      type: "reload_table",
    },
  },
  {
    key: "reload",
    title: "刷新页面",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    command: {
      type: "reload",
    },
  },
  {
    key: "expedite",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    title: "加急",
    command: {
      type: "request",
      request: {
        url: Api.customer,
        data: { action: "expedite" },
      },
      confirm: "我是二次确认弹窗title",
    },
  },
  {
    key: "remark",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    title: "备注",
    command: {
      type: "modal",
      title: "备注",
      footer: [
        {
          title: "保存",
          props: { type: "primary" },
          command: {
            type: "submit",
            id: "form3",
          },
        },
      ],
      content: {
        component: "JSONComponents",
        type: "json",
        props: {},
        children: [
          {
            component: "Form",
            props: {
              id: "form3",
              initialValues: { type: "order_remark", content: "hhhh", a: 1, b: 2, c: 3 },
            },
            formItems: [
              {
                key: "type",
                label: "备注类型",
                component: "Select",
                props: {
                  options: [
                    { label: "订单备注", value: "order_remark" },
                    { label: "状态备注", value: "status_remark" },
                  ],
                },
                rules: [{ required: true, message: "This is a required field" }],
              },
              {
                key: "content",
                label: "备注内容",
                component: "Textarea",
                props: { rows: 5 },
                rules: [{ required: true, message: "This is a required field" }],
              },
            ],
            submit: {
              preserveInitialValues: true,
              request: {
                url: Api.customer,
                data: {},
              },
            },
          },
        ],
      },
    },
  },
  {
    key: "查看table",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    title: "弹窗table",
    // command: {
    //   type: "modal",
    //   closable: true,
    //   title: "Api Components",
    //   props: {
    //     width: "90vw",
    //     height: "95vh",
    //   },
    //   content: {
    //     component: "JSONComponents",
    //     type: "json",
    //     props: {},
    //     children: [
    //       {
    //         component: "Iframe",
    //         props: {
    //           src: "/list/order-list",
    //           height: "80vh",
    //         },
    //       },
    //     ],
    //   },
    // },
    command: {
      type: "drawer",
      closable: true,
      title: "新建订单",
      props: {
        closeIcon: null,
        placement: "bottom",
        height: "70vh",
      },
      extra: [
        {
          title: "保存",
          props: { type: "primary" },
          command: {
            type: "close_drawer",
          },
        },
      ],
      content: {
        component: "JSONComponents",
        type: "json",
        props: {},
        children: [
          {
            component: "Iframe",
            props: {
              src: "/list/order-list",
            },
          },
        ],
      },
    },
  },
  {
    key: "delete",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    title: "删除",
    command: {
      type: "request",
      request: {
        url: Api.order,
        data: { action: "delete" },
      },
      confirm: "确定删除吗？",
    },
    props: { danger: true },
  },
];

const columns = [
  {
    dataIndex: "order_number",
    title: "订单号",
    width: 190,
    ellipsis: true,
    copyable: true,
    fixed: "left",
    filter: { component: "Search" },
    sorter: {
      // 优先级
      multiple: 3,
    },
    // valueType: "link",
    valueType: "command",
  },
  {
    dataIndex: "site_name",
    title: "站点",
    width: 150,
    filter: {
      // 组件类型
      component: "Select",
      // 选项是否可搜索
      searchable: true,
      searchApi: Api.searchProductSpu,
      // 筛选选项
      props: {
        options: [
          { label: "DM美国站1", value: "dm-us-1" },
          { label: "DR法国站2", value: "dr-fr-2" },
          { label: "DR英国站3", value: "dr-uk-3" },
          { label: "DM美国站4", value: "dm-us-4" },
          { label: "DR法国站5", value: "dr-fr-5" },
          { label: "DR英国站6", value: "dr-uk-6" },
          { label: "DM美国站7", value: "dm-us-7" },
          { label: "DR法国站8", value: "dr-fr-8" },
          { label: "DR英国站9", value: "dr-uk-9" },
          { label: "DM美国站10", value: "dm-us-10" },
          { label: "DR法国站11", value: "dr-fr-11" },
          { label: "DR英国站12", value: "dr-uk-12" },
          { label: "DM美国站13", value: "dm-us-13" },
          { label: "DR法国站14", value: "dr-fr-14" },
          { label: "DR英国站15", value: "dr-uk-15" },
          { label: "DM美国站16", value: "dm-us-16" },
          { label: "DR法国站17", value: "dr-fr-17" },
          { label: "DR英国站18", value: "dr-uk-18" },
        ],
      },
    },
  },
  {
    dataIndex: "language",
    title: "语言",
    width: 150,
    filter: {
      // 组件类型
      component: "Checkbox",
      // 选项是否可搜索
      searchable: true,
      // 筛选选项
      props: {
        options: [
          { label: "英语", value: "en" },
          { label: "法语", value: "fr" },
          { label: "日语", value: "jp" },
          { label: "德语", value: "de" },
          { label: "意大利语", value: "it" },
        ],
      },
    },
    // editable: {
    //   component: "Select",
    //   props: {
    //     // mode: "multiple", // 多选
    //     options: [
    //       { label: "英语", value: "en" },
    //       { label: "日语", value: "jp" },
    //       { label: "法语", value: "fr" },
    //       { label: "德语", value: "de" },
    //       { label: "意大利语", value: "it" },
    //     ],
    //   },
    //   // 支持远程搜索
    //   searchApi: Api.searchProductSpu,
    //   request: {
    //     url: Api.order,
    //     data: { action: "edit" },
    //   },
    // },
  },
  {
    dataIndex: "email",
    title: "用户邮箱",
    width: 200,
    copyable: true,
    filter: {
      // 组件类型
      component: "Tree",
      // 选项是否可搜索
      searchable: true,
      props: {
        // 是否默认展开所有节点
        defaultExpandAll: false,
        // 树组件数据
        treeData: [
          {
            key: "1",
            title: "节点1",
            children: [
              { key: "1-1", title: "节点1-1" },
              {
                key: "1-2",
                title: "节点1-2",
                children: [
                  { key: "1-2-1", title: "节点1-2-1" },
                  { key: "1-2-2", title: "节点1-2-2" },
                ],
              },
              { key: "1-3", title: "节点1-3" },
            ],
          },
          {
            key: "2",
            title: "节点2",
            children: [
              { key: "2-1", title: "节点2-1" },
              { key: "2-2", title: "节点2-2" },
              { key: "2-3", title: "节点2-3" },
            ],
          },
          {
            key: "3",
            title: "节点3",
            children: [
              { key: "3-1", title: "节点3-1" },
              { key: "3-2", title: "节点3-2" },
              { key: "3-3", title: "节点3-3" },
            ],
          },
        ],
      },
    },
    editable: {
      component: "Input",
      props: {},
      rules: [{ required: false }],
      request: { url: Api.order, data: { action: "edit" } },
      // 指定需要从行数据中提取的额外字段
      extraFields: ["order_number", "email", "currency"],
    },
  },
  {
    dataIndex: "product_image",
    title: "产品图片",
    width: 130,
    valueType: "image",
    image: { width: 45 },
    filter: { component: "Search" },
  },
  { dataIndex: "grand_total", title: "订单金额", width: 120, sorter: { multiple: 1 }, filter: { component: "Search" } },
  {
    dataIndex: "currency",
    title: "货币",
    width: 120,
    filter: {
      component: "Checkbox",
      props: {
        options: [
          { label: "USD", value: "USD" },
          { label: "CAD", value: "CAD" },
          { label: "JPY", value: "JPY" },
          { label: "EUR", value: "EUR" },
          { label: "AUD", value: "AUD" },
        ],
      },
    },
  },
  {
    dataIndex: "order_status",
    title: "订单状态",
    width: 150,
    valueType: "command",
    // modal: {
    //   title: "订单状态变更历史",
    //   request: {
    //     url: Api.orderModifyHistory,
    //     method: "GET",
    //     params: { field: "order_status" },
    //   },
    // },
  },
  {
    dataIndex: "payment_status",
    title: "支付状态",
    width: 150,
    copyable: true,
    valueEnum: { pending: { text: "未支付", status: "error" }, success: { text: "已支付", status: "success" } },
  },
  { dataIndex: "payment_method", title: "支付方式", width: 150 },
  { dataIndex: "risk", title: "是否风控", width: 150 },
  {
    dataIndex: "operation_record",
    title: "出库记录",
    width: 200,
    valueType: "html",
    rowSpan: { 0: 2, 1: 0 },
    copyable: true,
  },
  {
    dataIndex: "created_at",
    title: "创建时间",
    width: 200,
    sorter: { multiple: 2 },
    filter: { component: "RangePicker" },
  },
  {
    dataIndex: "operations",
    valueType: "operation",
    title: "操作",
    width: 80,
    fixed: "right",
    align: "center",
  },
];

const rows = [];

const headerActions = [
  {
    title: "下拉菜单",
    component: "Dropdown",
    props: {},
    dropdownProps: {
      menu: {
        items: [1, 2, 3, 4, 5].map((i) => ({
          key: `menu${i}`,
          label: `菜单选项${i}`,
          // command: { type: "request", request: { url: Api.commandEnd } },
          command: {
            type: "code_execution",
            code_string: `
              console.log("菜单选项${i}");
              alert("菜单选项${i}");
            `,
          },
        })),
      },
    },
    enableByRowSelection: true,
  },
  {
    title: "上传文件",
    icon: "/icons/upload.svg",
    component: "Upload",
    props: {},
    uploadProps: { action: Api.commandEnd },
    // command: {
    //   type: "request",
    //   request: {
    //     url: Api.commandEnd,
    //   },
    // },
    enableByRowSelection: false,
  },
  {
    key: "66a1a845153ec",
    title: "批量导出",
    icon: "/icons/download.svg",
    label: "批量导出",
    enableByRowSelection: true,
    command: {
      type: "download",
      confirm: "你确认要进行此操作吗？",
      url: Api.download,
    },
    props: [],
  },
  {
    title: "连续弹窗",
    props: {},
    command: {
      type: "reload_table",
      command: {
        type: "request",
        request: {
          url: Api.serialRequest,
        },
      },
    },
  },
  {
    title: "新建订单",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    props: { type: "primary", style: { background: "green" } },
    command: {
      type: "modal",
      closable: true,
      title: "新建订单",
      props: {
        // closeIcon: null,
        width: 1000,
        placement: "right",
      },
      footer: [
        {
          title: "确定",
          props: { type: "primary" },
          command: {
            type: "submit",
            id: "form2",
          },
        },
      ],
      content: {
        component: "JSONComponents",
        type: "api",
        props: {},
        fetcher: {
          request: {
            url: "http://192.168.2.110:8081/rest/v1/list/order-list/create-order",
            data: {},
          },
        },
      },
    },
  },
  {
    title: "Api Components",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    props: { type: "primary" },
    command: {
      type: "modal",
      closable: true,
      title: "Api Components",
      props: {
        width: 1000,
      },
      footer: [
        {
          title: "确定",
          props: { type: "primary" },
          command: {
            type: "submit",
            id: "form1",
          },
        },
      ],
      content: {
        component: "JSONComponents",
        type: "api",
        props: {},
        fetcher: {
          request: {
            url: Api.getApiJsonComponents,
            data: {},
          },
        },
        // form: {
        //   type: "api",
        //   fetcher: {
        //     request: {
        //       url: Api.getApiForm,
        //       method: "GET",
        //       data: {},
        //     },
        //   },
        //   submit: {
        //     request: {
        //       url: Api.customer,
        //       data: {},
        //     },
        //   },
        // },
      },
    },
  },
  {
    title: "新增罚款",
    props: { type: "primary" },
    command: {
      type: "modal",
      closable: true,
      title: "新增罚款",
      props: {
        width: 800,
      },
      content: {
        component: "JSONComponents",
        type: "json",
        props: {},
        children: [
          {
            component: "Iframe",
            props: {
              src: "/dms/other-penalty/edit",
            },
          },
        ],
      },
    },
  },
  {
    title: "提示信息",
    props: {},
    command: {
      type: "message",
      config: {
        type: "success",
        content: "提示文案",
        duration: 3,
      },
      command: {
        type: "reload_table",
      },
    },
  },
  {
    title: "跳转页面",
    props: { type: "primary" },
    command: { type: "redirect", url: "/edit/order?id=1" },
  },
  {
    title: "批量删除",
    props: { danger: true },
    command: {
      type: "request",
      request: { url: Api.order, data: { action: "batchDelete" } },
      confirm: "确定删除吗？",
    },
    enableByRowSelection: true,
  },
];

const toolbarActions = [
  {
    component: "Search",
    key: "keywords",
    title: "关键字",
    props: {
      placeholder: "请输入关键字",
    },
  },
  {
    title: "下拉菜单",
    component: "Dropdown",
    icon: "/icons/download.svg",
    tooltip: "更多操作",
    props: {},
    dropdownProps: {
      menu: {
        items: [
          {
            key: "export1",
            label: "批量导出1",
            icon: "/icons/download.svg",
            command: { type: "download", url: Api.download },
          },
          {
            key: "export2",
            label: "批量导出2",
            icon: "/icons/download.svg",
            command: { type: "download", url: Api.download },
          },
          {
            key: "export3",
            label: "批量导出3",
            icon: "/icons/download.svg",
            command: { type: "download", url: Api.download },
          },
        ],
      },
    },
  },
  {
    title: "设置备货周期",
    icon: "https://images.aoolia.com/static/icons/edit.svg",
    props: {},
    command: {
      type: "modal",
      closable: true,
      title: "设置备货周期",
      props: {
        width: 1000,
      },
      footer: [
        {
          title: "确定",
          props: { type: "primary" },
          command: {
            type: "submit",
            id: "form1",
          },
        },
      ],
      content: {
        component: "JSONComponents",
        type: "api",
        props: {},
        fetcher: {
          request: {
            url: Api.getApiJsonComponents,
            data: {},
          },
        },
      },
    },
  },
];

const sidebarActions = {
  props: {
    initialValues: {
      order_type: "so",
      receiver_type: "phone",
      spaceWrapper1: "1",
    },
  },
  batchFilters: [
    {
      key: "field1",
      component: "Input",
      props: {
        placeholder: "回车批量搜索",
      },
      addonAfter: {
        component: "Text",
        content: "内部订单号",
        props: {
          style: { width: 110 },
        },
      },
    },
    {
      key: "order",
      component: "Input",
      props: {
        placeholder: "回车批量搜索",
      },
      addonAfter: {
        key: "order_type",
        component: "Select",
        props: {
          style: { width: 110 },
          options: [
            { label: "线上订单号", value: "so" },
            { label: "支付单号", value: "outer_pay" },
          ],
        },
      },
    },
    {
      key: "receiver",
      component: "Input",
      props: {
        placeholder: "回车批量搜索",
      },
      addonAfter: {
        key: "receiver_type",
        component: "Select",
        props: {
          style: { width: 110, background: "#ccc" },
          options: [
            { label: "收货手机", value: "phone" },
            { label: "收货城市", value: "city" },
          ],
        },
      },
    },
    {
      component: "SpaceWrapper",
      props: { style: { width: "100%" } },
      children: [
        {
          key: "spaceWrapper1",
          component: "Select",
          props: {
            style: { width: "110px", background: "red" },
            allowClear: true,
            options: [
              { value: "1", label: "不包含全部" },
              { value: "2", label: "满足任意" },
            ],
          },
        },
        {
          key: "spaceWrapper2",
          component: "Select",
          props: {
            style: { width: "135px" },
            showSearch: true,
            mode: "multiple",
            maxTagCount: 1,
            options: [
              { value: "select_1", label: "选项1" },
              { value: "select_2", label: "选项2" },
            ],
          },
        },
      ],
    },
  ],
  groupFilters: {
    props: {},
    items: [
      {
        key: "1",
        label: "订单状态",
        children: [
          {
            key: "field3",
            component: "Checkbox",
            props: {
              options: [
                { label: "英语", value: "en" },
                { label: "法语", value: "fr" },
                { label: "意大利语", value: "it" },
                { label: "日语", value: "jp" },
                { label: "德语", value: "de" },
              ],
            },
          },
        ],
        // style: { marginBottom: 24, background: "#fff", border: "none" },
      },
      {
        key: "2",
        label: "买家留言",
        children: [
          {
            key: "field4",
            component: "Radio",
            props: {
              options: [
                { label: "英语", value: "en" },
                { label: "法语", value: "fr" },
                { label: "日语", value: "jp" },
                { label: "德语", value: "de" },
                { label: "意大利语", value: "it" },
              ],
            },
          },
        ],
      },
      {
        key: "3",
        label: "订单时间",
        children: [
          {
            key: "field5",
            component: "RangePicker",
            props: {},
          },
        ],
      },
    ],
  },
};

const sidebarList = {
  type: "sidebarList",
  content: {
    children: [
      {
        component: "Card",
        props: {
          title: "客服列表",
          bordered: true,
          hoverable: false,
          size: "small",
          styles: { body: { padding: 5 } },
        },
        children: [
          {
            component: "Collapse",
            props: {
              items: [
                {
                  key: 1,
                  label: {
                    icon: {
                      src: "https://static.bizseas.com/static/icon/file.svg",
                      style: {
                        color: "blue",
                      },
                    },
                    text: "合计",
                    check_icon: {
                      src: "https://s3.ap-east-1.amazonaws.com/assets.cnzlerp.com/erp/icon/zaiy.svg",
                      style: {
                        color: "yellow",
                      },
                    },
                  },
                  children: [
                    {
                      url: "",
                      text: "New",
                      count: 42,
                    },
                    {
                      url: "",
                      text: "羊羊",
                      count: 8,
                    },
                  ],
                },
                {
                  key: 2,
                  label: {
                    icon: {
                      src: "https://static.bizseas.com/static/icon/file.svg",
                      style: {
                        color: "blue",
                      },
                    },
                    text: "合计",
                    check_icon: {
                      src: "https://s3.ap-east-1.amazonaws.com/assets.cnzlerp.com/erp/icon/zaiy.svg",
                      style: {
                        color: "yellow",
                      },
                    },
                  },
                  children: [
                    {
                      url: "",
                      text: "New",
                      count: 42,
                    },
                    {
                      url: "",
                      text: "羊羊",
                      count: 8,
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    ],
  },
};

module.exports = async (req, res) => {
  const query = req.query;
  const page = +(query.page || 1);
  const pageSize = +(query.pageSize || 20);
  const total = 500;
  const start = (page - 1) * pageSize;
  const end = page * pageSize >= total ? total : page * pageSize;
  const getTag = (i) => {
    if (i === 0) {
      return [
        { text: "待审核", color: "warning" },
        { text: "风控", style: { color: "#fff", backgroundColor: "#f00", borderColor: "#f00" } },
        { text: "默认", color: "success" },
      ];
    } else if (i === 1) {
      return [{ text: "风控", style: { color: "#fff", backgroundColor: "#f00", borderColor: "#f00" } }];
    }
    return [{ text: "默认", color: "success" }];
  };

  rows.length = 0;
  const rowSpanColumn = columns.find((item) => item.dataIndex === "operation_record");
  for (let i = start; i < end; i++) {
    const id = i + 1;
    rowSpanColumn.rowSpan[i] = i % 2 === 0 ? 2 : 0;
    rows.push({
      id: id,
      order_number: `DMUS202203627400-${id}`,

      // todo 改为右侧抽屉，修复底部空白问题
      order_number_extra_data: {
        // link: `/list/popup-management`,
        // link: `/details/order?id=${id}`,
        command: {
          type: "drawer",
          closable: true,
          title: "新建订单",
          props: {
            width: 1200,
            placement: "right",
          },
          content: {
            component: "JSONComponents",
            type: "json",
            props: {},
            children: [
              {
                component: "Iframe",
                props: {
                  // style: { height: "calc(100vh - 50px)" },
                  src: `/dms/order/detail?id=${i}`,
                },
              },
            ],
          },
        },
        // command: {
        //   type: "modal",
        //   closable: true,
        //   title: "订单详情",
        //   props: {
        //     width: 1500,
        //   },
        //   content: {
        //     component: "JSONComponents",
        //     type: "json",
        //     props: {},
        //     children: [
        //       {
        //         component: "Iframe",
        //         props: {
        //           src: `/dms/order/detail?id=${i}`,
        //         },
        //       },
        //     ],
        //   },
        // },
      },
      site_name: "DM-美国站",
      language: "英语",
      language_extra_data:
        i % 2 === 0
          ? {
              editable: {
                component: "Select",
                isGetDefaultOptions: true,
                props: {
                  // mode: "multiple", // 多选
                  options: [
                    { label: "英语", value: "en" },
                    { label: "日语", value: "jp" },
                    { label: "法语", value: "fr" },
                    { label: "德语", value: "de" },
                    { label: "意大利语", value: "it" },
                  ],
                },
                // 支持远程搜索
                searchApi: Api.searchProductSpu,
                request: {
                  url: Api.order,
                  data: { action: "edit" },
                },
              },
            }
          : null,
      email: "<EMAIL>",
      email_extra_data: {
        style: {
          backgroundColor: "#ccc",
          color: "#fff",
        },
      },
      product_image: [
        {
          src: "https://test-res.jeulia.com/product/0/7/800x800/6571369a28570.jpg",
        },
        {
          src: "https://test-je5-oms.cnzlerp.com/uploads/product/e/9/6015357862b9e.jpg",
        },
      ],
      product_image_extra_data: {
        preview_group: [
          {
            items: [
              {
                src: "https://test-res.jeulia.com/product/0/7/800x800/6571369a28570.jpg",
              },
              { src: "https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg" },
              { src: "https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg" },
              { src: "https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg" },
              { src: "https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg" },
            ],
            fallback: "https://www.google.com.hk/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png",
          },
        ],
        badges: [
          {
            count: 5,
            color: "red",
            size: "small",
          },
          {
            count: 1,
            color: "red",
            size: "small",
          },
        ],
      },
      grand_total: "107.85",
      grand_total_extra_data: {
        tags: [
          {
            text: "金额",
            style: {
              color: "#fff",
              backgroundColor: "#f00",
              borderColor: "#f00",
            },
          },
        ],
      },
      currency: "USD",
      order_status: "Processing",
      order_status_extra_data: {
        tags: getTag(i),
        command: {
          type: "modal",
          closable: true,
          title: "订单状态变更历史",
          footer: null,
          props: {
            width: 800,
          },
          content: {
            component: "JSONComponents",
            type: "api",
            props: {},
            fetcher: {
              request: {
                url: Api.orderModifyHistory,
                data: {},
              },
            },
          },
        },
      },
      payment_status: i % 2 === 0 ? "success" : "pending",
      payment_method: "PayPal Express",
      risk: i % 2 === 0 ? "是" : "否",
      risk_extra_data: {
        style: i % 2 === 0 ? { color: "#f00" } : {},
        icon:
          i % 2 === 0
            ? {
                src: "https://images.aoolia.com/static/images/time_fill.svg",
                style: { width: 16, height: 16, color: "#f00" },
              }
            : null,
      },
      operation_record: `
        <div data-clipboard="https://detail.1688.com/offer/654423996015.html?spm=a26352.b28411319.offerlist.20.60321e62PVWVtT">
          <div><strong>订单出库</strong></div>
          <div>打印：<span style="color: var(--ant-error-color)">2023-02-01 15:00:00</span></div>
          <div>打印：<span style="color: var(--ant-success-color)">2023-02-01 15:00:00</span></div>
          <div>出库：<span style="color: var(--ant-primary-color)">2023-02-01 15:00:00</span></div>
        </div>
      `,
      created_at: "2022-12-15 04:15:15",
      updated_at: "2022-12-15 04:16:02",
      operations,
    });
  }

  const calculateSummary = () => {
    let totalAmount = 0;

    rows.forEach((row) => {
      totalAmount += parseFloat(row.grand_total);
    });

    return {
      grand_total: totalAmount.toFixed(2),
      currency: "USD",
    };
  };

  const footerData = {
    type: "json",
    children: [
      {
        component: "Row",
        props: {
          gutter: 16,
          align: "middle",
          style: { margin: 0, padding: "2px 4px" },
        },
        children: [
          {
            component: "Col",
            props: { span: 4 },
            children: [
              {
                component: "CommandTrigger",
                props: { children: "设置补货任务规则" },
                command: {
                  type: "drawer",
                  closable: true,
                  title: "新建订单",
                  props: {
                    placement: "bottom",
                    height: "70vh",
                  },
                  extra: [
                    {
                      title: "保存",
                      props: { type: "primary" },
                      command: {
                        type: "close_drawer",
                      },
                    },
                  ],
                  content: {
                    component: "JSONComponents",
                    type: "json",
                    props: {},
                    children: [
                      {
                        component: "Iframe",
                        props: {
                          src: "/list/order-list",
                        },
                      },
                    ],
                  },
                },
              },
            ],
          },
          {
            component: "Col",
            props: { span: 12 },
            children: [
              {
                component: "Text",
                props: { type: "secondary" },
                content: "当前商品数 9527",
              },
            ],
          },
        ],
      },
    ],
  };

  res.status(200).json({
    status: "00",
    success: true,
    data: {
      breadcrumbs: [
        { title: "商品管理", url: "/" },
        { title: "商品列表", url: "/" },
      ],
      headerActions: headerActions,
      toolbarActions: toolbarActions,
      sidebarActions,
      polling: 1000,
      tableProps: {
        rowKey: "id",
        dataSource: rows,
        columns: columns,
        virtual: true,
        pagination: {
          current: +page,
          pageSize: +pageSize,
          total: total,
          pageSizeOptions: [20, 50, 100, 500, 1000, 2000],
          showSizeChanger: true,
          position: ["bottomRight"],
          size: "small",
          showQuickJumper: false,
          showTotal: `{range} of {total} items`,
        },
        rowSelection: true,
        size: "small",
        bordered: true,
        summary_data: calculateSummary(),
        footerData,
      },
    },
  });
};
