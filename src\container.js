import { useEffect, useRef, useState } from "react";
import Cookies from "js-cookie";
import Enums from "enums";
import { Mo<PERSON>, Drawer, FloatButton, App } from "antd";
import Helper from "helpers";
import SubmitButton from "components/common/Button";
import JSONComponents from "components/common/JSONComponent";
import HTMLBlock from "components/common/HtmlBlock";
import Utils from "utils";
import EnvHelper from "@/helpers/env-helper";

const modalStyle = { overflowY: "auto" };
const drawerStyle = { overflowY: "auto" };

function Container(props) {
  const { children } = props;
  const { message, modal, notification } = App.useApp();
  const [commandModal, setCommandModal] = useState({ open: false, data: {} });
  const [commandExtraData, setCommandExtraData] = useState({});
  const [commandDrawer, setCommandDrawer] = useState({ open: false, data: {} });
  const [commandLoading, setCommandLoading] = useState({ loading: false, childrenStyle: { opacity: 1 } });

  const paramsRef = useRef({});
  paramsRef.current = {
    ...paramsRef.current,
    message,
    modal,
    notification,
  };

  function closeCommandModal() {
    setCommandModal((commandModal) => ({ ...commandModal, open: false }));
  }

  function closeCommandDrawer() {
    setCommandDrawer((commandDrawer) => ({ ...commandDrawer, open: false }));
  }

  function ActionsButton(props) {
    const { data } = props;
    return data
      ? data?.map((item) => {
          return (
            <SubmitButton
              key="confirm"
              type="primary"
              {...item.props}
              command={item.command}
              onClick={async () => {
                await Helper.commandHandler({ command: item.command, ...commandExtraData });
              }}
            >
              {item.title}
            </SubmitButton>
          );
        })
      : null;
  }

  useEffect(() => {
    function handleSetCommandModal(values) {
      const { open, data, ...others } = values;
      // 带参数给JSONComponents的请求URL
      if (data?.content?.fetcher?.request?.url) {
        const url = data.content.fetcher.request.url;
        data.content.fetcher.request.url = Utils.setQueryParams(url, others);
      }
      setCommandModal({ open, data });
      setCommandExtraData(others);
    }

    Utils.addEventListener(Enums.EventName.SetCommandModal, handleSetCommandModal);

    return function () {
      Utils.removeEventListener(Enums.EventName.SetCommandModal, handleSetCommandModal);
    };
  }, []);

  useEffect(() => {
    function handleSetCommandDrawer(values) {
      const { open, data, ...others } = values;
      // 带参数给JSONComponents的请求URL
      if (data?.content?.fetcher?.request?.url) {
        const url = data.content.fetcher.request.url;
        data.content.fetcher.request.url = Utils.setQueryParams(url, others);
      }
      setCommandDrawer((commandDrawer) => ({ ...commandDrawer, open, data }));
      setCommandExtraData(others);
    }

    Utils.addEventListener(Enums.EventName.SetCommandDrawer, handleSetCommandDrawer);

    return function () {
      Utils.removeEventListener(Enums.EventName.SetCommandDrawer, handleSetCommandDrawer);
    };
  }, []);

  useEffect(() => {
    function handleAntdMessage(config) {
      const { message } = paramsRef.current;
      message.open(config);
    }

    Utils.addEventListener(Enums.EventName.AntdMessage, handleAntdMessage);

    return function () {
      Utils.removeEventListener(Enums.EventName.AntdMessage, handleAntdMessage);
    };
  }, []);

  useEffect(() => {
    function handleAntdModal(type, content, duration, close) {
      const { modal } = paramsRef.current;
      modal[type](content, duration, close);
    }

    Utils.addEventListener(Enums.EventName.AntdModal, handleAntdModal);

    return function () {
      Utils.removeEventListener(Enums.EventName.AntdModal, handleAntdModal);
    };
  }, []);

  useEffect(() => {
    function handleAntdNotification(config) {
      const { notification } = paramsRef.current;
      notification.open(config);
    }

    Utils.addEventListener(Enums.EventName.AntdNotification, handleAntdNotification);

    return function () {
      Utils.removeEventListener(Enums.EventName.AntdNotification, handleAntdNotification);
    };
  }, []);

  useEffect(() => {
    const isInsideIframe = Helper.isInsideIframe();
    if (isInsideIframe) {
      const body = document.getElementsByTagName("body")[0];
      body.classList.add("inside-iframe-body");
    }
  }, []);

  useEffect(() => {
    // 监听iframe message
    function handleMessage(event) {
      const { data } = event;
      if (data.action === "command") {
        Helper.commandHandler({ command: data.data });
      }
    }

    window.addEventListener("message", handleMessage);

    return function () {
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  useEffect(() => {
    window.history.scrollRestoration = "manual";
  }, []);

  useEffect(() => {
    console.log(`runtime: ${EnvHelper.RuntimeEnv}`);
    console.log(`version: ${EnvHelper.version}`);
  }, []);

  useEffect(() => {
    function handleGlobalMessage({ type, content }) {
      const { message } = paramsRef.current;
      message.open({ type, content });
    }
    Utils.addEventListener(Enums.EventName.GlobalMessage, handleGlobalMessage);

    return function () {
      Utils.removeEventListener(Enums.EventName.GlobalMessage, handleGlobalMessage);
    };
  }, []);

  return (
    <>
      <div>{children}</div>
      <Modal
        open={commandModal.open}
        title={commandModal.data?.title || <div>&nbsp;</div>}
        onOk={closeCommandModal}
        onCancel={closeCommandModal}
        destroyOnClose
        closable={commandModal.data?.closable}
        maskClosable={false}
        centered
        style={modalStyle}
        zIndex={1200}
        {...commandModal.data?.props}
        footer={<ActionsButton data={commandModal.data?.footer} />}
      >
        {typeof commandModal?.data?.content === "string" ? (
          <HTMLBlock html={commandModal.data.content} />
        ) : (
          commandModal?.data?.content && (
            <JSONComponents data={commandModal.data.content} extraData={commandExtraData} />
          )
        )}
      </Modal>
      <Drawer
        open={commandDrawer.open}
        title={commandDrawer.data?.title}
        destroyOnClose
        maskClosable={false}
        onClose={closeCommandDrawer}
        height="auto"
        style={drawerStyle}
        styles={{
          body: commandDrawer.data?.props?.placement === "bottom" ? { padding: 4 } : {},
          header: { padding: 8 },
        }}
        {...commandDrawer.data?.props}
        extra={commandDrawer.data?.extra ? <ActionsButton data={commandDrawer.data?.extra} /> : null}
        footer={commandDrawer.data?.footer ? <ActionsButton data={commandDrawer.data?.footer} /> : null}
      >
        {typeof commandDrawer?.data?.content === "string" ? (
          <HTMLBlock html={commandModal.data.content} />
        ) : (
          commandDrawer?.data?.content && <JSONComponents data={commandDrawer.data.content} />
        )}
      </Drawer>
      <FloatButton.BackTop />
    </>
  );
}

export default Container;
