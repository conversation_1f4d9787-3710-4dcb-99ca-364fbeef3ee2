import styles from "./index.module.scss";
import { useEffect, useState, useRef } from "react";
import { createPortal } from "react-dom";

import {
  closestCenter,
  DndContext,
  PointerSensor,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from "@dnd-kit/core";

import Utils from "utils";

import { Empty, Button } from "antd";
import SortableList from "./components/SortableList";
import Item from "./components/Item";

function ProductPanel({ data = [], onChange, loadMore, selectedIds, setSelectedIds }) {
  const [items, setItems] = useState([]);
  const [activeItem, setActiveItem] = useState(null);
  const [loading, setLoading] = useState(false);
  const paramsRef = useRef({});

  paramsRef.current = {
    movingItems: [],
  };

  const sensors = useSensors(
    useSensor(MouseSensor),
    useSensor(TouchSensor),
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 10,
      },
    })
  );

  async function handleLoadMore() {
    try {
      setLoading(true);
      loadMore();
    } finally {
      setLoading(false);
    }
  }

  function handleSelect(event, item) {
    const itemId = item?.id;

    if (event.shiftKey && items.length > 0) {
      const lastSelectedId = selectedIds[selectedIds.length - 1];
      // 最后一个被选中项的索引
      const lastIndex = items.findIndex((i) => i.id === lastSelectedId);
      const currentIndex = items.findIndex((i) => i.id === itemId);

      if (lastIndex !== -1 && lastIndex !== currentIndex) {
        const [start, end] = [lastIndex, currentIndex].sort((a, b) => a - b);
        const rangeIds = items.slice(start + 1, end).map((i) => i.id);
        setSelectedIds((selectedIds) => [...selectedIds, ...rangeIds]);
      }
    }

    setSelectedIds((selectedIds) => {
      if (selectedIds.includes(itemId)) {
        return selectedIds.filter((value) => value !== itemId);
      } else {
        return [...selectedIds, itemId];
      }
    });
  }

  function handleDragStart(event) {
    const itemId = event.active.id;
    const isItemSelected = selectedIds.includes(itemId);

    if (!isItemSelected) {
      setSelectedIds([itemId]);
    }

    setActiveItem(event.active.data.current);

    setItems((prevItems) =>
      prevItems.map((item) => {
        // 隐藏其他已选的项
        if (selectedIds.length > 1 && selectedIds.includes(item.id) && item.id !== event.active.id) {
          return { ...item, hidden: true };
        } else {
          return item;
        }
      })
    );
  }

  function handleDragOver(event) {
    const { active, over } = event;
    if (!over) return;

    const overId = over.id;
    const activeId = active.id;

    if (activeId === overId) return;

    paramsRef.current.movingItems = items.filter((item) => selectedIds.includes(item.id));

    setItems((items) => {
      let updatedItems = [...items];
      const movingItems = updatedItems.filter((item) => selectedIds.includes(item.id));

      // 去除移动的项
      updatedItems = updatedItems.filter((item) => !selectedIds.includes(item.id));

      const overIndex = items.findIndex((item) => item.id === overId);

      updatedItems.splice(overIndex, 0, ...movingItems);

      return updatedItems;
    });
  }

  function handleDragEnd(event) {
    const { active, over } = event;

    if (!over) return;

    const overId = over.id;

    setSelectedIds([]);

    let updatedItems = Utils.cloneDeep(items);
    const movingItems = updatedItems.filter((item) => selectedIds.includes(item.id));

    // 去除移动的项
    updatedItems = updatedItems.filter((item) => !selectedIds.includes(item.id));

    const overIndex = items.findIndex((item) => item.id === overId);

    updatedItems.splice(overIndex, 0, ...movingItems);
    const newItems = updatedItems.map(({ hidden, ...item }) => item);
    setItems(newItems);
    onChange?.((data) => ({ ...data, items: newItems }));
  }

  useEffect(() => {
    setItems(Utils.cloneDeep(data));
  }, [data]);

  return (
    <div className={styles.container}>
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        // onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
        collisionDetection={closestCenter}
      >
        {items?.length > 0 ? (
          <>
            <SortableList items={items} onSelect={handleSelect} selectedIds={selectedIds} count={selectedIds.length} />

            <div className={styles.loadMore}>
              <Button type="primary" onClick={handleLoadMore} loading={loading}>
                加载下一页
              </Button>
            </div>
          </>
        ) : (
          <Empty />
        )}

        {createPortal(
          <DragOverlay>
            {activeItem ? (
              <div>
                <Item selected={selectedIds.includes(activeItem.id)} item={activeItem} count={selectedIds.length} />
              </div>
            ) : null}
          </DragOverlay>,
          document.body
        )}
      </DndContext>
    </div>
  );
}

export default ProductPanel;
