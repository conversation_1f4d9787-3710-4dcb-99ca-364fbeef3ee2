const play = require("./play");
const template = require("./ajax-template");
const increment = require("./gupdate-increment");
const update = require("./update");

module.exports = {
  "GET /rest/v1/ticket/ticket/play": async (req, res) => play(req, res),
  "GET /rest/v1/ticket/ticket/ajax-template": async (req, res) => template(req, res),
  "GET /rest/v1/ticket/ticket/gupdate-increment": async (req, res) => increment(req, res),
  "POST /rest/v1/ticket/ticket/gupdate-increment": async (req, res) =>
    res.status(200).json({
      success: true,
      command: {
        type: "close_modal",
        command: { type: "update_page_data" },
      },
    }),
  "GET /rest/v1/ticket/ticket/update": async (req, res) => update(req, res),
};
