import { useRef, useState } from "react";
import styles from "./index.module.scss";
import { Row, Col, Tabs } from "antd";
import QualityControlForm from "./components/quality-control-form";
import JSONComponents from "components/common/JSONComponent";

function QualityControl(props) {
  const [autoPrint, setAutoPrint] = useState(false);
  const [data, setData] = useState();
  const [activeKey, setActiveKey] = useState("batch_control");
  const formRef = useRef();

  const renderTabsChildren = () => {
    return <QualityControlForm ref={formRef} type={activeKey} updateData={setData} />;
  };

  const tabItems = [
    {
      key: "batch_control",
      label: "按批次质检",
      children: renderTabsChildren(),
    },
    {
      key: "quality_control",
      label: "快速质检",
      children: renderTabsChildren(),
    },
  ];

  const handleAutoPrintChange = (e) => {
    setAutoPrint(e.target.checked);
  };

  const handleTabsChange = (key) => {
    setActiveKey(key);
    setData(null);
  };

  return (
    <div className={styles.container}>
      <Row gutter={16}>
        <Col span={6}>
          <div className={styles.panel}>
            <Tabs defaultActiveKey={activeKey} items={tabItems} onChange={handleTabsChange} />
            {/* <Checkbox onChange={handleAutoPrintChange} checked={autoPrint}>
              次品自动打印次品标签
            </Checkbox> */}
          </div>
        </Col>
        <Col span={18}>
          {data?.content ? (
            <div className={styles.content}>
              <JSONComponents data={data?.content} />
            </div>
          ) : null}
        </Col>
      </Row>
    </div>
  );
}

export default QualityControl;
