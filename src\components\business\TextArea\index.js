import { useCallback } from "react";
import { Input } from "antd";

const AntdTextArea = Input.TextArea;

function TextArea({ autoFocus, isSelected, ...restProps }) {
  const ref = useCallback(
    (node) => {
      if (!node) return;

      if (autoFocus) {
        node?.focus();
      }
      if (isSelected) {
        node?.select();
      }
    },
    [autoFocus, isSelected]
  );

  return <AntdTextArea ref={ref} {...restProps} />;
}

export default TextArea;
