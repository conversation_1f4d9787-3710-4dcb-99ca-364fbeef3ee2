body {
  .jsonEditor {
    padding: 0;

    &.jsonEditorFocus {
      border-color: var(--ant-primary-color-hover);
      border-right-width: 1px;
      outline: 0;
      box-shadow: 0 0 0 2px var(--ant-primary-color-outline);
    }

    &.disabled {
      cursor: not-allowed;

      * {
        pointer-events: none;
      }

      [class~="ace_scroller"] {
        color: rgba(0, 0, 0, 0.25);
        background-color: #f5f5f5;
        border-color: #d9d9d9;
        box-shadow: none;
        cursor: not-allowed;
        opacity: 1;
      }

      [class~="ace_marker-layer"] {
        display: none;
      }

      [class="ace_cursor"] {
        display: none !important;
      }
    }
  }

  [class~="ant-form-item-has-error"] {
    .jsonEditor {
      border-color: var(--ant-error-color);

      &.jsonEditorFocus {
        border-color: var(--ant-error-color-hover);
        border-right-width: 1px;
        outline: 0;
        box-shadow: 0 0 0 2px var(--ant-error-color-outline);
      }
    }
  }
}
