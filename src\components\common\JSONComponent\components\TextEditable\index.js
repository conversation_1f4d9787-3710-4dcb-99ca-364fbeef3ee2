import { useState, useRef } from "react";
import HTMLBlock from "components/common/HtmlBlock";
import axios from "@/fetchers/request";
import EditablePopover from "components/common/EditablePopover";

function TextEditable(props) {
  const { data } = props;
  const { content } = data;
  const [loading, setLoading] = useState(false);
  const popoverRef = useRef();

  async function commandRequest({ request, values }) {
    const { url, ...options } = request;
    return await axios({
      url,
      method: "POST",
      ...options,
      data: { ...options.data, ...values },
    });
  }

  function renderEditable(item, index) {
    if (item?.editable) {
      const { editable } = item;
      async function onFinish(values, options) {
        try {
          popoverRef.current.hidden();
          setLoading(true);
          await commandRequest({
            request: editable?.request,
            values: { field: item?.key, value: values[item?.key] },
          });
          if (options?.length > 0) {
            item.text = options?.find((a) => a.value === values[item?.key])?.label;
          } else {
            item.text = values[item?.key];
          }
          setLoading(false);
        } catch (e) {
          setLoading(false);
        }
      }

      return (
        <EditablePopover
          key={index}
          ref={popoverRef}
          onFinish={onFinish}
          defaultValue={item?.text}
          field={item?.key}
          editable={editable}
        >
          <HTMLBlock key={index} html={item.text} tag="span" {...item.props} />
        </EditablePopover>
      );
    } else {
      return <HTMLBlock key={index} html={item.text} tag="span" {...item.props} />;
    }
  }

  function renderContent() {
    if (typeof content === "string") {
      return <HTMLBlock html={content} />;
    } else if (Array.isArray(content)) {
      return content?.map((item, index) => {
        return renderEditable(item, index);
      });
    }
    return null;
  }

  return renderContent();
}

export default TextEditable;
