import useSWR from "swr";
import HtmlBlock from "components/common/HtmlBlock";
import PropTypes from "prop-types";
import axios from "axios";
import styles from "./index.module.scss";
import classNames from "classnames";

const CacheValues = {};

function SvgIcon(props) {
  const { src, className } = props;
  const { data } = useSWR(
    src,
    (src) => {
      if (!CacheValues[src]) {
        return axios
          .get(src)
          .then((res) => {
            let str = /<svg.*svg>/i.exec(res.data)?.[0];
            str = str.replace(/(width|height)=".*?"/gi, `$1="1em"`);
            str = str.replace(/fill=".*?"/gi, "");
            str = str.replace(/<svg\s/i, `<svg fill="currentColor" `);
            CacheValues[src] = str;
            return str;
          })
          .catch(() => "");
      } else {
        return CacheValues[src];
      }
    },
    {
      dedupingInterval: 2000,
    }
  );

  return (
    <HtmlBlock
      {...props}
      tag="span"
      html={data}
      className={classNames(styles.svgIcon, "svg-icon", className)}
    ></HtmlBlock>
  );
}

SvgIcon.propTypes = {
  src: PropTypes.string,
  className: PropTypes.string,
};

export default SvgIcon;
