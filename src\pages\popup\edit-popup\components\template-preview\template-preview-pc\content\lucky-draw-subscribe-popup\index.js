import React, { useEffect, useRef, useState, Fragment } from "react";
import styles from "./index.module.scss";

const ROTATING_CONFIG = {
  wheel_duration: 3,
  wheel_circle: 9,
  wheel_result_duration: 2,
};

const timerArr = ["minutes", "seconds", "milliseconds"];
const eventSource = "turntable_subscribe_popup";
const temporarilyClosedKey = "lucky_draw_subscribe_popup_temporarily_closed";

function LuckyDrawSubscribePopupPc(props) {
  const {
    data,
    name,
    popup_id,
    device,
    countryId = "US",
    getCountryList,
    fetchBlock,
    trackViewPopup,
    trackSubscribe,
    CountdownConstructor,
    validator,
    submitFetcher,
    usePopupControl,
    countries = [],
    preview,
    isSubscribed,
    current,
  } = props;
  const [blockData, setBlockData] = useState(data);
  const [countryList, setCountryList] = useState(countries);
  const [selectedCountryId, setSelectedCountryId] = useState(countryId);
  const [emailErrorMessage, setEmailErrorMessage] = useState(null);
  const [phoneErrorMessage, setPhoneErrorMessage] = useState(null);
  const [step, setStep] = useState(1);
  const [subscribed, setSubscribed] = useState(JSON.parse(isSubscribed ?? false));
  const [couponIndex, setCouponIndex] = useState(1);
  const [copied, setCopied] = useState(false);
  const [isRotating, setIsRotating] = useState(false);
  const [privacyPolicyError, setPrivacyPolicyError] = useState(null);
  const { open, setOpen, handleTemporarilyClose } = usePopupControl({
    React,
    data: blockData,
    subscribed,
    temporarilyClosedKey,
  });

  const formRef = useRef();
  const wheelImageRef = useRef();
  const timerRefs = useRef({});
  const emailRef = useRef();
  const phoneRef = useRef();
  const couponCodeRef = useRef();
  const privacyPolicyCheckboxRef = useRef();
  const paramsRef = useRef({});
  const isWheelLeft = blockData?.content?.main?.wheel_position === "left";

  paramsRef.current = {
    name,
    popup_id,
    device,
    fetchBlock,
    getCountryList,
    subscribed,
    luckyDrawSubscribePopupTemporarilyClosed: JSON.parse(localStorage.getItem(temporarilyClosedKey) || null),
    CountdownConstructor,
    trackViewPopup,
    preview,
  };

  function generateRandomNumber({ min, max }) {
    return Math.floor(Math.random() * (max - min + 1) + min);
  }

  function getRotateDegrees() {
    const max = blockData?.content?.main?.wheel_point_degrees_3 ? 3 : 2;
    const randomCouponIndex = generateRandomNumber({ min: 1, max });
    setCouponIndex(randomCouponIndex);
    return (
      blockData?.content?.main?.[`wheel_point_degrees_${randomCouponIndex}`] ??
      blockData?.content?.main?.wheel_point_degrees_1
    );
  }

  function handleResult() {
    const { button_click_action, open_link } = blockData?.content?.main || {};
    if (button_click_action === "open_link" && open_link) {
      window.location.href = open_link;
      return;
    }

    if (button_click_action === "close_popup") {
      handleClose();
      return;
    }

    setSubscribed(true);
    formRef.current?.classList.remove("has-error");
  }

  function handleRotating({ isSubmit = false } = {}) {
    // 防重复点击
    if (isRotating) {
      return false;
    }

    const wheelElement = wheelImageRef.current;
    if (!wheelElement || !blockData) {
      return false;
    }

    setIsRotating(true);

    const { wheel_duration, wheel_circle, wheel_result_duration } = { ...ROTATING_CONFIG, ...blockData?.content?.main };
    const degrees = getRotateDegrees();

    try {
      onHandleSubmit();
    } catch (error) {
      setIsRotating(false);
      console.error("Submit failed:", error);
      return false;
    }

    const animate = () => {
      return new Promise((resolve) => {
        // 重置动画
        wheelElement.style.animation = "none";
        wheelElement.style.transition = "none";
        wheelElement.style.transform = "rotate(0deg)";

        // 强制重排
        void wheelElement.offsetHeight;

        wheelElement.style.transition = `transform ${wheel_duration}s ease-in-out`;
        wheelElement.style.transform = `rotate(${360 * wheel_circle + (360 - degrees)}deg)`;

        wheelElement.addEventListener("transitionend", resolve, { once: true });
      });
    };

    animate()
      .then(() => {
        if (isSubmit) {
          setTimeout(() => {
            handleResult();
          }, wheel_result_duration * 1000);
        }
      })
      .catch((error) => {
        console.error("Animation failed:", error);
      })
      .finally(() => {
        setIsRotating(false);
      });
  }

  async function onHandleSubmit() {
    try {
      const dial = countryList?.filter((item) => item?.country_id === selectedCountryId)?.[0]?.dia;
      const email = emailRef.current?.value || "";
      const phone = phoneRef.current?.value || "";

      if (email || phone) {
        const data = {
          email,
          country_id: selectedCountryId,
          phone,
          dial,
        };

        const result = await submitFetcher?.(data);
        if (result?.status === "00") {
          trackSubscribe?.({ eventSource, data });
        }
      }
    } catch (error) {
      console.log("[lucky-draw-subscribe-popup-pc] handleSubmit error", error);
    }
  }

  async function validateFields(fields) {
    const errors = await validator?.(fields);
    if (!errors?.length) return null;

    const errorMessage = {};
    errors.forEach((error) => {
      errorMessage[error.field] = error.message;
    });

    setEmailErrorMessage(errorMessage.email ?? null);
    setPhoneErrorMessage(errorMessage.phone ?? null);

    formRef.current?.classList.add("has-error");

    return errorMessage;
  }

  function validatePrivacyPolicyCheckbox() {
    const { privacy_policy_checkbox, privacy_policy_checkbox_error_message } = blockData?.content?.main;
    const show = privacy_policy_checkbox && privacy_policy_checkbox !== "hidden";
    const errorMessage = privacy_policy_checkbox_error_message ?? "";
    if (show && !privacyPolicyCheckboxRef.current?.checked) {
      formRef.current.classList.add("has-error");
      setPrivacyPolicyError(errorMessage);
      return false;
    }
    formRef.current?.classList.remove("has-error");
    setPrivacyPolicyError(null);
    return true;
  }

  async function handleFinish() {
    const email = emailRef.current?.value;
    const { is_step_show, email_collection, email_collection_required, phone_collection, phone_collection_required } =
      blockData?.content?.main || {};

    if (is_step_show && step === 1) {
      if (email_collection && email_collection_required) {
        const errors = await validateFields({ email });
        if (errors) return;
      }

      setStep(2);
      return;
    }

    if (!validatePrivacyPolicyCheckbox()) return;

    if (email_collection && email_collection_required) {
      const errors = await validateFields({ email });
      if (errors) return;
    }

    if (phone_collection && phone_collection_required) {
      const phone = phoneRef.current?.value;
      const errors = await validateFields({ phone });
      if (errors) return;
    }

    formRef.current?.classList.remove("has-error");
    handleRotating({ isSubmit: true });
  }

  function handleInputFocus(e) {
    e.stopPropagation();
    setTimeout(() => {
      e.target.focus();
    }, 0);
  }

  function handleSuccessButtonClick() {
    const { success_button_click_action, success_open_link } = blockData?.content?.success || {};
    handleTemporarilyClose();
    if (success_button_click_action === "open_link" && success_open_link) {
      window.location.href = success_open_link;
    }
  }

  function renderWheelImage() {
    return (
      <div className={styles.wheelImageWrapper}>
        {blockData?.content?.main?.wheel_bg_img && (
          <img
            ref={wheelImageRef}
            className={styles.wheelImage}
            src={blockData?.content?.main?.wheel_bg_img[0]?.url || blockData?.content?.main?.wheel_bg_img}
            draggable={false}
            alt=""
          />
        )}
        {blockData?.content?.main?.wheel_point_img && (
          <img
            className={styles.point}
            src={blockData?.content?.main?.wheel_point_img[0]?.url || blockData?.content?.main?.wheel_point_img}
            draggable={false}
            alt=""
          />
        )}
      </div>
    );
  }

  function renderCountDown() {
    const style = {
      color: blockData?.content?.main?.count_down_text_color,
      backgroundColor: blockData?.content?.main?.count_down_bg_color,
    };

    return (
      blockData?.content?.main?.count_down && (
        <div className={styles.countDown}>
          {timerArr.map((unit, index) => (
            <Fragment key={unit}>
              <div ref={(el) => (timerRefs.current[unit] = el)} style={style}>
                0
              </div>
              {index !== timerArr.length - 1 && ":"}
            </Fragment>
          ))}
        </div>
      )
    );
  }

  async function copyToClipboard(text) {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      handleTemporarilyClose();
      setTimeout(() => {
        setCopied(false);
      }, 1000);
    } catch (error) {
      console.error("复制失败:", error);
    }
  }

  function onHandleCopy() {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      copyToClipboard(couponCodeRef.current.textContent);
    }
  }

  function handleOpen() {
    setOpen(true);
  }

  function handleClose() {
    setOpen(false);
  }

  useEffect(() => {
    (async () => {
      const { getCountryList } = paramsRef.current;
      if (getCountryList) {
        setCountryList(await getCountryList());
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      const { fetchBlock, name, popup_id, device } = paramsRef.current;
      if (fetchBlock) {
        const block = await fetchBlock({ name, id: popup_id, device });
        setBlockData(block);
      }
    })();
  }, []);

  useEffect(() => {
    if (preview && data) {
      setBlockData(data);
    }
  }, [preview, data]);

  useEffect(() => {
    if (open) {
      const { trackViewPopup } = paramsRef.current;
      trackViewPopup?.({ eventSource });
    }
  }, [open]);

  useEffect(() => {
    const { count_down, count_down_time } = blockData?.content?.main || {};
    if (!count_down || !count_down_time) return;

    const { CountdownConstructor } = paramsRef.current;
    if (!CountdownConstructor) return;

    const startTime = Date.now();
    const endTime = startTime + count_down_time * 60 * 1000;

    const countDown = new CountdownConstructor({
      startTime,
      endTime,
      interval: 100,
      loop: true,
      max_unit: "hour",
      onTick: (duration) => {
        timerArr.forEach((unit) => {
          timerRefs.current[unit].textContent = duration[unit]().toString().padStart(2, "0");
        });
      },
    });
    countDown.start();

    return () => countDown.stop();
  }, [blockData]);

  if (!blockData?.content) return null;

  return (
    <div className={`${styles.container} ${preview ? styles.preview : ""}`}>
      {!open && blockData?.content?.collapse_style?.icon && (
        <div
          className={styles.open}
          style={{
            width: blockData?.content?.collapse_style?.width,
            height: blockData?.content?.collapse_style?.height,
            [blockData?.content?.collapse_style?.position]: 0,
            bottom: blockData?.content?.collapse_style?.bottom,
            backgroundColor: blockData?.content?.collapse_style?.bg_color,
            zIndex: blockData?.content?.collapse_style?.z_index,
          }}
          onClick={() => {
            if (open) {
              handleTemporarilyClose();
            } else {
              handleOpen?.();
              localStorage.removeItem(temporarilyClosedKey);
            }
          }}
        >
          <img
            style={{
              maxWidth: blockData?.content?.collapse_style?.width,
              maxHeight: blockData?.content?.collapse_style?.height,
            }}
            src={blockData?.content?.collapse_style?.icon[0]?.url || blockData?.content?.collapse_style?.icon}
            alt=""
          />
        </div>
      )}

      {copied && <div className={styles.messageCopy}>{blockData?.content?.success?.copy_success_message}</div>}
      <div className={`${styles.overlay} ${open ? styles.opened : ""}`}></div>
      <div className={`${styles.contentWrapper} ${open ? styles.opened : ""}`}>
        <div
          className={`${styles.wheelContainer}  ${
            (!preview && isWheelLeft && !subscribed) || (preview && isWheelLeft && current !== 2)
              ? styles.wheelLeft
              : ""
          }`}
        >
          <div className={styles.wheelContent}>
            <div className={styles.close} onClick={handleTemporarilyClose}>
              <img src="https://static.bizseas.com/static/popup/lucky-draw-subscribe-popup/close.png" alt="" />
            </div>

            {subscribed || current === 2 ? (
              <>
                {/* 订阅成功 */}
                {blockData?.content?.success?.success_bg_img && (
                  <div className={styles.successBackgroundImage}>
                    <img
                      src={
                        blockData?.content?.success?.success_bg_img[0]?.url ||
                        blockData?.content?.success?.success_bg_img
                      }
                      alt=""
                    />
                  </div>
                )}
                <div className={styles.success}>
                  <div className={styles.successContentInner}>
                    {blockData?.content?.success?.success_title && (
                      <div
                        className={styles.title}
                        style={{
                          fontSize: blockData?.content?.success?.success_title_font_size,
                          color: blockData?.content?.success?.success_title_color,
                        }}
                      >
                        {blockData?.content?.success?.success_title}
                      </div>
                    )}

                    {blockData?.content?.main?.[`coupon_message_${couponIndex}`] && (
                      <div
                        className={styles.couponMessage}
                        style={{
                          fontSize: blockData?.content?.success?.success_coupon_title_font_size,
                          color: blockData?.content?.success?.success_coupon_title_color,
                        }}
                      >
                        {blockData?.content?.main?.[`coupon_message_${couponIndex}`]}
                      </div>
                    )}

                    {blockData?.content?.success?.success_desc && (
                      <div
                        className={styles.successDesc}
                        style={{
                          fontSize: blockData?.content?.success?.success_desc_font_size,
                          color: blockData?.content?.success?.success_desc_color,
                        }}
                      >
                        {blockData?.content?.success?.success_desc}
                      </div>
                    )}

                    {renderCountDown()}

                    <div className={styles.couponCodeWrapper}>
                      <div className={styles.couponCodeContent}>
                        <div ref={couponCodeRef} className={styles.couponCode}>
                          {blockData?.content?.main?.[`coupon_code_${couponIndex}`]}
                        </div>
                        <div className={styles.copy} onClick={onHandleCopy}>
                          <img
                            src="https://static.bizseas.com/static/popup/lucky-draw-subscribe-popup/copy.png"
                            alt=""
                          />
                        </div>
                      </div>
                    </div>

                    {blockData?.content?.success?.success_button_name && (
                      <div
                        className={styles.shopNow}
                        style={{
                          background: blockData?.content?.success?.success_button_bg_color,
                          color: blockData?.content?.success?.success_button_text_color,
                        }}
                        onClick={handleSuccessButtonClick}
                      >
                        {blockData?.content?.success?.success_button_name}
                      </div>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* 抽奖 */}
                {blockData?.content?.main?.main_bg_img && (
                  <div className={styles.backgroundImage}>
                    <img
                      src={blockData?.content?.main?.main_bg_img[0]?.url || blockData?.content?.main?.main_bg_img}
                      alt=""
                    />
                  </div>
                )}
                <div className={`${styles.content} ${isWheelLeft ? styles.left : ""}`}>
                  {isWheelLeft && renderWheelImage()}
                  <div className={styles.contentInner}>
                    {blockData?.content?.main?.title && (
                      <div
                        className={styles.title}
                        style={{
                          fontSize: blockData?.content?.main?.title_size,
                          color: blockData?.content?.main?.title_color,
                        }}
                      >
                        {blockData?.content?.main?.title}
                      </div>
                    )}

                    {blockData?.content?.main?.sub_title && (
                      <div
                        className={styles.subTitle}
                        style={{
                          color: blockData?.content?.main?.sub_title_color,
                          fontSize: blockData?.content?.main?.sub_title_size,
                        }}
                      >
                        {blockData?.content?.main?.sub_title}
                      </div>
                    )}

                    {!isWheelLeft && renderWheelImage()}

                    {renderCountDown()}

                    <form ref={formRef} className={styles.form}>
                      {blockData?.content?.main?.email_collection && (
                        <div
                          className={`${
                            blockData?.content?.main?.is_step_show && step === 2 ? styles.displayNone : ""
                          }`}
                        >
                          <div className={`${styles.formItem} ${emailErrorMessage ? "has-error" : ""}`}>
                            <input
                              type="text"
                              ref={emailRef}
                              onFocus={handleInputFocus}
                              placeholder={blockData?.content?.main?.email_collection_placeholder}
                            />
                          </div>
                          <div className={styles.errorMessage}>{emailErrorMessage}</div>
                        </div>
                      )}
                      {blockData?.content?.main?.phone_collection && (
                        <div
                          className={`${
                            blockData?.content?.main?.is_step_show && step === 1 ? styles.displayNone : ""
                          }`}
                        >
                          <div className={styles.formItemWrapper}>
                            <div className={styles.formItem}>
                              <select
                                className={styles.countryArea}
                                value={selectedCountryId}
                                onChange={(e) => setSelectedCountryId(e.target.value)}
                              >
                                {countryList.map((option) => (
                                  <option key={option.country_id} value={option.country_id}>
                                    {`${option?.country_id}${option?.dia ? ` (${option?.dia})` : ""}`}
                                  </option>
                                ))}
                              </select>
                            </div>
                            <div
                              className={`${styles.formItem} ${phoneErrorMessage ? "has-error" : ""}`}
                              style={{ flex: 1, flexDirection: "column" }}
                            >
                              <input
                                type="text"
                                ref={phoneRef}
                                onFocus={handleInputFocus}
                                placeholder={blockData?.content?.main?.phone_collection_placeholder}
                                style={{ width: "auto" }}
                              />
                              <div className={styles.errorMessage}>{phoneErrorMessage}</div>
                            </div>
                          </div>
                        </div>
                      )}
                      <label className={styles.privacyPolicyLabel}>
                        <input
                          ref={privacyPolicyCheckboxRef}
                          className={`${styles.privacyPolicyCheckbox} ${
                            blockData?.content?.main?.privacy_policy_checkbox &&
                            blockData?.content?.main?.privacy_policy_checkbox !== "hidden"
                              ? styles.show
                              : ""
                          }`}
                          type="checkbox"
                          defaultChecked={blockData?.content?.main?.privacy_policy_checkbox === "checked"}
                        />
                        {blockData?.content?.main?.phone_collection && blockData?.content?.main?.privacy_terms && (
                          <span className={styles.privacy}>
                            {blockData?.content?.main.privacy_policy_checkbox === "hidden" && (
                              <span style={{ color: "red" }}>*</span>
                            )}
                            {blockData?.content?.main?.privacy_terms}
                            <a
                              href={blockData?.content?.main?.privacy_terms_link}
                              style={{ color: "#000", textDecoration: "underline" }}
                              target="_blank"
                              rel="noreferrer"
                            >
                              {blockData?.content?.main?.privacy_policy}
                            </a>
                          </span>
                        )}
                      </label>
                      {privacyPolicyError && <div className={styles.errorMessage}>{privacyPolicyError}</div>}
                    </form>

                    {blockData?.content?.main?.button_name && (
                      <div
                        className={styles.submitButton}
                        style={{
                          color: blockData?.content?.main?.button_text_color,
                          background: blockData?.content?.main?.button_bg_color,
                        }}
                        onClick={handleFinish}
                      >
                        {blockData?.content?.main?.button_name}
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default LuckyDrawSubscribePopupPc;
