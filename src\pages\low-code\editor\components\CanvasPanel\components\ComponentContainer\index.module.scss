.container {
  // padding: 20px;
  // background-color: rgba(0, 0, 0, 0.05);
  // border: 1px solid #e6a23c;

  .containerWrapper {
    cursor: pointer;
    position: relative;
    // border: 1px solid #d9d9d9;
    // background: rgb(236, 243, 253);

    // &:hover {
    //   border: 1px dashed #2389ff;
    // }

    // &.selected {
    //   border: 1px solid #2389ff;
    //   background-color: rgb(228, 247, 255);
    // }

    .containerContent {
      display: flex;
      flex-direction: column;
      gap: 16px;
      width: 100%;
      height: auto;
      min-height: 160px;
      padding: 30px 0;
    }
  }
}

.ghostLine {
  width: 100%;
  height: 100px;
  background: #2389ff;
  opacity: 0.25;
  margin-bottom: 20px;
}
