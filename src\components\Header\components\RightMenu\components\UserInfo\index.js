import styles from "./index.module.scss";
import { Button, Dropdown } from "antd";
import { SmileOutlined, UserOutlined } from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import dayjs from "dayjs";
import store from "@/stores";
import Helper from "helpers";
import { useI18n } from "@/context/I18nContext";

function UserInfo() {
  const { systemInfo } = store;
  const { t } = useI18n();

  const items = [
    {
      key: "1",
      label: (
        <div className={styles.userInfo}>
          <div className={styles.avatar}>
            <SmileOutlined
              style={{
                fontSize: "42px",
              }}
            />
          </div>
          <div className={styles.info}>
            <span>{systemInfo?.user?.name}</span>
            <span>{dayjs(new Date()).format("YYYY-MM-DD")}</span>
          </div>
        </div>
      ),
    },
    {
      type: "divider",
    },
    {
      key: "2",
      label: <div>{t({ key: "LogOut", defaultText: "退出登录" })}</div>,
    },
  ];

  function handleItemClick({ key, keyPath, domEvent }) {
    if (key === "2") {
      Helper.logout();
    }
  }

  return (
    <Dropdown
      menu={{
        items,
        onClick: handleItemClick,
      }}
      trigger={["click"]}
    >
      <Button type="text" shape="circle">
        <UserOutlined />
      </Button>
    </Dropdown>
  );
}

UserInfo = observer(UserInfo);

export default UserInfo;
