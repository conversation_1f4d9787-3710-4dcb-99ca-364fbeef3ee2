import styles from "./index.module.scss";
import { Button, Dropdown } from "antd";
import { SmileOutlined, UserOutlined } from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import dayjs from "dayjs";
import store from "@/stores";
import Helper from "helpers";
import { useTranslation } from "react-i18next";

function UserInfo() {
  const { systemInfo } = store;
  const { t } = useTranslation();

  const items = [
    {
      key: "user",
      label: (
        <div className={styles.userInfo}>
          <div className={styles.avatar}>
            <SmileOutlined
              style={{
                fontSize: "42px",
              }}
            />
          </div>
          <div className={styles.info}>
            <span>{systemInfo?.user?.name}</span>
            <span>{dayjs(new Date()).format("YYYY-MM-DD")}</span>
          </div>
        </div>
      ),
    },
    {
      type: "divider",
    },
    {
      key: "clearCache",
      label: <div>{t("ClearCache")}</div>,
    },
    {
      type: "divider",
    },
    {
      key: "logout",
      label: <div>{t("LogOut")}</div>,
    },
  ];

  function handleItemClick({ key, keyPath, domEvent }) {
    if (key === "logout") {
      Helper.logout();
    }

    if (key === "clearCache") {
      Helper.clearSystemInfoCache();
      window.location.reload();
    }
  }

  return (
    <Dropdown
      menu={{
        items,
        onClick: handleItemClick,
      }}
      trigger={["click"]}
    >
      <Button type="text" shape="circle">
        <UserOutlined />
      </Button>
    </Dropdown>
  );
}

UserInfo = observer(UserInfo);

export default UserInfo;
