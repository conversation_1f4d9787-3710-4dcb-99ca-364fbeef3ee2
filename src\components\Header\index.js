import styles from "./index.module.scss";
import { useLocation } from "react-router-dom";
import { observer } from "mobx-react-lite";
import { MenuFoldOutlined, MenuUnfoldOutlined } from "@ant-design/icons";

import Enums from "@/enums";
import Helper from "helpers";
import store from "@/stores";

import { Button, Select, Space } from "antd";
import RightMenu from "./components/RightMenu";
import SiteSwitcher from "@/components/business/SiteSwitcher";

// const Brands = [
//   { label: "Jeulia", value: "jeulia" },
//   { label: "Drawelry", value: "drawelry" },
//   { label: "Shesaidyes", value: "shesaidyes" },
// ];

// const WebsitesMap = {
//   jeulia: [
//     { label: "-- All Website --", value: "" },
//     { label: "JE-美国站", value: "jeulia-us" },
//     { label: "JE-英国站", value: "jeulia-uk" },
//     { label: "JE-法国站", value: "jeulia-fr" },
//   ],
//   drawelry: [
//     { label: "-- All Website --", value: "" },
//     { label: "DR-美国站", value: "drawelry-us" },
//     { label: "DR-英国站", value: "drawelry-uk" },
//     { label: "DR-法国站", value: "drawelry-fr" },
//   ],
//   shesaidyes: [
//     { label: "-- All Website --", value: "" },
//     { label: "SSY-美国站", value: "shesaidyes-us" },
//   ],
// };

function Header(props) {
  // const brand = store.brand || Brands?.[0]?.value;
  // const websites = WebsitesMap[brand] || [];
  // const website = store.website || websites?.[1]?.value;
  const location = useLocation();
  const pageInfo = store.pathPageConfigs?.[location.pathname] ?? null;

  return (
    <div className={styles.header}>
      <Space>
        <div>
          <Button
            type="text"
            shape="circle"
            onClick={() => {
              const nextValue = !store.asideFold;
              localStorage.setItem(Enums.LocalStorageKey.AsideFold, nextValue.toString());
              store.setAsideFold(nextValue);
            }}
          >
            {store.asideFold ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </Button>
        </div>
        {pageInfo?.site_switcher?.enable && <SiteSwitcher siteSwitcher={pageInfo?.site_switcher} />}

        {/* <div>
          <Select
            value={brand}
            options={Brands}
            onChange={(value) => {
              Helper.setCurrentBrand(value);
              Helper.setCurrentWebsite(WebsitesMap?.[value]?.[1]?.value);
              store.setBrand(value);
              store.setWebsite(WebsitesMap?.[value]?.[1]?.value);
            }}
            style={{ width: 120 }}
          ></Select>
        </div>
        <div>
          <Select
            value={website}
            options={websites}
            onChange={(value) => {
              Helper.setCurrentWebsite(value);
              store.setWebsite(value);
            }}
            style={{ width: 150 }}
          ></Select>
        </div> */}
      </Space>
      <RightMenu />
    </div>
  );
}

Header = observer(Header);

export default Header;
