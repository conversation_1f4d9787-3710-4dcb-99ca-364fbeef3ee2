import styles from "./index.module.scss";
import classNames from "classnames";
import { Button, Form, Input, Modal, Popconfirm, Table, Tabs } from "antd";
import React, { useEffect, useRef, useState } from "react";
import Fetchers from "@/fetchers";
import PropTypes from "prop-types";
import Link from "@/components/Link";
import Utils from "@/utils";
import { useLocation, useNavigate } from "react-router-dom";
import TableHelper from "@/helpers/table-helper";
import I18nSelect from "@/pages/pod/components/i18n-select";

function LibraryTable(props) {
  const { type, active } = props;
  const [tableData, setTableData] = useState({});
  const [tableLoading, setTableLoading] = useState(false);
  const [addLibraryModalOpen, setAddLibraryModalOpen] = useState(false);
  const [addLibraryLoading, setAddLibraryLoading] = useState(false);
  const addLibraryFormRef = useRef();
  const location = useLocation();

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current, loadTableData };

  async function loadTableData() {
    setTableLoading(true);
    const { page, pageSize } = Utils.getQueryParams(window.location.href);
    const result = await Fetchers.getPodLibraries({ type: active, page, pageSize }).then((res) => res.data);
    if (result.success) {
      TableHelper.createColumnsRender({ columns: result.data.tableProps.columns, reloadData: loadTableData });
      result.data.tableProps.columns.push({
        width: 1,
        render(text, item, index) {
          return (
            <div style={{ display: "flex", gap: 8 }}>
              <Link to={`/pod/edit-library`} query={{ id: item.id }}>
                <Button size="small" type="primary">
                  编辑
                </Button>
              </Link>
              <Popconfirm
                title="确定删除吗？"
                placement="topRight"
                onConfirm={async () => {
                  await Fetchers.deletePodLibrary({ id: item.id });
                  await loadTableData();
                }}
              >
                <Button size="small" type="primary">
                  删除
                </Button>
              </Popconfirm>
            </div>
          );
        },
      });
      setTableData(result.data);
      setTableLoading(false);
    }
  }

  useEffect(() => {
    (async () => {
      if (type === active) {
        const { loadTableData } = paramsRef.current;
        loadTableData();
        window.scrollTo({ top: 0, left: 0 });
      }
    })();
  }, [type, active, location.search]);

  return (
    <>
      <div>
        <div style={{ padding: `20px 0` }}>
          <Button
            type="primary"
            onClick={async () => {
              setAddLibraryModalOpen(true);
              // await loadTableData();
            }}
          >
            添加素材库
          </Button>
        </div>
        <Table rowKey="id" {...tableData.tableProps} loading={tableLoading} onChange={TableHelper.onChange}></Table>
      </div>
      <Modal
        open={addLibraryModalOpen}
        onOk={() => {
          addLibraryFormRef.current?.submit();
          return new Promise((resolve) => {
            paramsRef.current.submitResolve = resolve;
          });
        }}
        onCancel={() => {
          setAddLibraryModalOpen(false);
        }}
        okButtonProps={{ loading: addLibraryLoading }}
        destroyOnClose
      >
        <Form
          ref={addLibraryFormRef}
          layout="vertical"
          onFinish={(values) => {
            setAddLibraryLoading(true);
            Fetchers.addPodLibrary({ data: { ...values, type: active } })
              .then(() => {
                setAddLibraryModalOpen(false);
                loadTableData();
              })
              .finally(() => {
                paramsRef.current.submitResolve?.();
                setAddLibraryLoading(false);
              });
          }}
        >
          <Form.Item
            name="name_i18n_key"
            label="Library Name"
            rules={[{ required: true, message: "This field is required" }]}
          >
            <I18nSelect
              onSelect={(value, option) => {
                addLibraryFormRef.current?.setFieldValue("name", option.label);
              }}
            ></I18nSelect>
          </Form.Item>
          <Form.Item name="name" style={{ display: "none" }}>
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
LibraryTable.propTypes = {
  key: PropTypes.string,
  active: PropTypes.string,
};

function Libraries() {
  const { type = "image" } = Utils.getQueryParams(window.location.href);
  const navigate = useNavigate();

  return (
    <>
      <div className={classNames("page", styles.page)}>
        <div>
          <Tabs
            items={[
              {
                key: "image",
                label: "图片素材库",
                children: (
                  <div>
                    <LibraryTable type="image" active={type}></LibraryTable>
                  </div>
                ),
              },
              {
                key: "font",
                label: "字体素材库",
                children: (
                  <div>
                    <LibraryTable type="font" active={type}></LibraryTable>
                  </div>
                ),
              },
              {
                key: "color",
                label: "颜色素材库",
                children: (
                  <div>
                    <LibraryTable type="color" active={type}></LibraryTable>
                  </div>
                ),
              },
            ]}
            className={styles.tabs}
            activeKey={type}
            onChange={(value) => {
              navigate(Utils.setQueryParams(`${window.location.pathname}`, { type: value }));
            }}
          ></Tabs>
        </div>
      </div>
    </>
  );
}

export default Libraries;
