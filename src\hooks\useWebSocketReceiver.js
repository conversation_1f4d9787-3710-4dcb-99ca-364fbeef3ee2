import { useEffect, useState } from "react";
import MyWebSocket from "@/websocket";
import Utils from "@/utils";

function useWebSocketReceiver({ filter } = {}) {
  const [status, setStatus] = useState(MyWebSocket.status);
  const [data, setData] = useState();
  const filterStr = Utils.JSON.stringify(filter);

  useEffect(() => {
    const { ws_debug } = Utils.getQueryParams(window.location.href);

    function handleStatusChange(status) {
      setStatus(status);
    }
    MyWebSocket.addEventListener(MyWebSocket.Event.StatusChange, handleStatusChange);

    function handleMessage(event) {
      const data = Utils.JSON.parse(event.data);
      if (ws_debug) {
        console.log(data);
      }
      const filter = Utils.JSON.parse(filterStr);
      if (Object.keys(filter).length > 0 && Object.keys(filter).every((key) => data?.data?.[key] === filter[key])) {
        setData(data);
      }
    }
    MyWebSocket.addEventListener(MyWebSocket.Event.Message, handleMessage);

    return () => {
      MyWebSocket.removeEventListener(MyWebSocket.Event.StatusChange, handleStatusChange);
      MyWebSocket.removeEventListener(MyWebSocket.Event.Message, handleMessage);
    };
  }, [filterStr]);

  return { status, data };
}

export default useWebSocketReceiver;
