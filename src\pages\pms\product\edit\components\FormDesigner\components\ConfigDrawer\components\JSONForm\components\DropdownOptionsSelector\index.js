import { Select } from "antd";
import useSWR from "swr";
import { useEffect } from "react";

import Fetchers from "@/fetchers";
import store from "@/pages/pms/product/edit/components/FormDesigner/store";
import Enums from "../../../../../../common/enums";

function DropdownOptionsSelector(props) {
  const { id, value, onChange, identifiers, linkageKey, componentKey, disabled, ...restProps } = props;
  const identifier = identifiers[id];
  const isMasterOrSecond = componentKey === Enums.PropertyType.master || componentKey === Enums.PropertyType.second;

  const { data: options = [] } = useSWR([identifier, Fetchers.Api.getDictionary], async () => {
    try {
      if (!identifier) return;

      if (store.optionsData[id]) {
        return store.optionsData[id];
      }

      const result = await Fetchers.getDictionary({ params: { prefix: identifier } }).then((res) => res?.data?.data);

      store.setOptionsData({ id, options: result });
      return result;
    } finally {
    }
  });

  async function getDictionaryOptions(id, identifier) {
    if (!id) {
      return store.setOptionsData({ id, options: [] });
    }

    const result = await Fetchers.getCustomizeDictionaryOption({ params: { dict: [identifier] } }).then(
      (res) => res?.data?.data[identifier] || []
    );

    store.setOptionsData({ id, options: result });
    return result;
  }

  useEffect(() => {
    if (value) {
      // value改变，获取选项数据
      getDictionaryOptions(linkageKey, value);
    }
  }, [linkageKey, value]);

  return (
    <Select
      options={options}
      value={value}
      onChange={onChange}
      showSearch
      placeholder="请选择"
      optionFilterProp="label"
      disabled={disabled || isMasterOrSecond}
      {...restProps}
    />
  );
}

export default DropdownOptionsSelector;
