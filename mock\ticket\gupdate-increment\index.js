const Api = require("../../../src/fetchers/api");

module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      content: {
        children: [
          {
            component: "Row",
            props: {
              gutter: [0, 10],
            },
            children: [
              {
                component: "Col",
                props: { span: 24 },
                children: [
                  {
                    component: "Card",
                    props: {
                      bordered: true,
                      title: "Order Info:",
                      // icon: "https://static.bizseas.com/static/icon/file.svg",
                    },
                    children: [
                      {
                        component: "Row",
                        props: {},
                        children: [
                          {
                            component: "Col",
                            props: { xs: { span: 24 }, lg: { span: 24 } },
                            children: [
                              {
                                component: "NativeTable",
                                props: {},
                                children: [
                                  [
                                    {
                                      tag: "th",
                                      valueType: "text",
                                      value: "Order ID",
                                      props: {
                                        style: {
                                          width: "50px",
                                        },
                                      },
                                    },
                                    {
                                      tag: "td",
                                      valueType: "input",
                                      props: {
                                        style: {
                                          width: "150px",
                                        },
                                      },
                                      view: {
                                        src: "",
                                      },
                                      command: {
                                        type: "request",
                                        request: {
                                          url: Api.order,
                                          data: {
                                            action: "batchDelete",
                                          },
                                          method: "POST",
                                        },
                                      },
                                    },
                                  ],
                                ],
                              },
                            ],
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                component: "Col",
                props: { span: 24 },
                children: [
                  {
                    component: "Card",
                    props: {
                      bordered: true,
                      title: "Customer Info:",
                      extra: [
                        {
                          title: "删除",
                          props: {
                            type: "primary",
                            danger: true,
                          },
                          command: {
                            type: "request",
                            request: {
                              url: Api.order,
                              data: {
                                action: "batchDelete",
                              },
                              method: "POST",
                            },
                          },
                        },
                      ],
                    },
                    children: [
                      {
                        component: "NativeTable",
                        children: [
                          [
                            {
                              tag: "th",
                              valueType: "text",
                              value: "Email",
                              props: {
                                style: {
                                  width: "50px",
                                },
                              },
                            },
                            {
                              tag: "td",
                              valueType: "command",
                              command: {
                                type: "message",
                                config: {
                                  type: "success",
                                  content: "提示文案",
                                  duration: 3,
                                },
                              },
                              props: {
                                style: {
                                  width: "150px",
                                },
                              },
                              value: "<EMAIL>",
                            },
                          ],
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                component: "Col",
                props: { span: 24 },
                children: [
                  {
                    component: "Card",
                    props: {
                      bordered: true,
                      title: "标签",
                      extra: [
                        {
                          title: "新增",
                          props: {
                            type: "primary",
                          },
                          command: {
                            command: {
                              type: "modal",
                              closable: true,
                              title: "",
                              props: {
                                width: "800px",
                                maskClosable: true,
                                closeIcon: null,
                              },
                              footer: [
                                {
                                  title: "确定",
                                  props: { type: "primary" },
                                  command: {
                                    type: "submit",
                                    id: "remark-form",
                                  },
                                },
                              ],
                              content: {
                                type: "json",
                                component: "JSONComponents",
                                children: [
                                  {
                                    component: "Form",
                                    props: {
                                      id: "remark-form",
                                      layout: "horizontal",
                                      labelCol: {
                                        style: {
                                          width: 120,
                                        },
                                      },
                                    },
                                    submit: {
                                      request: {
                                        data: {
                                          ticket_id: "1",
                                          action: "add_remark",
                                        },
                                        method: "POST",
                                        url: "http://192.168.2.110:8081/rest/v1/ticket/ticket/gupdate-increment",
                                      },
                                    },
                                    formItems: [
                                      {
                                        component: "Input",
                                        key: "remark_content",
                                        rules: [
                                          {
                                            required: true,
                                          },
                                        ],
                                        label: "备注内容",
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          },
                        },
                      ],
                    },
                    children: [
                      {
                        component: "Tag",
                        children: [
                          {
                            text: "加急",
                            color: "error",
                          },
                          {
                            text: "风控",
                            style: {
                              color: "#fff",
                              backgroundColor: "#f00",
                              borderColor: "#f00",
                            },
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    },
    command: {
      type: "close_modal",
    },
  });
};
