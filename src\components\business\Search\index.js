import React, { useRef, useEffect } from "react";
import { Input } from "antd";

const { Search: AntdSearch } = Input;

function Search({ autoFocus, isSelected, ...restProps }) {
  const inputRef = useRef(null);

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  useEffect(() => {
    if (isSelected && inputRef.current) {
      inputRef.current.select();
    }
  }, [isSelected]);

  return <AntdSearch ref={inputRef} {...restProps} />;
}

export default Search;
