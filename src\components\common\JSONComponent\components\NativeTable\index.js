import styles from "./index.module.scss";
import HTMLBlock from "components/common/HtmlBlock";
import { Image, Typography } from "antd";
import EditablePopover from "components/common/EditablePopover";
import { EyeOutlined } from "@ant-design/icons";
import Enums from "@/enums";
import Helper from "@/helpers";
import { useState, useRef } from "react";
import axios from "fetchers/request";
import Tags from "components/business/Tags";
import ExternalPageEditor from "@/components/business/ExternalPageEditor";

function NativeTable(props) {
  const { data, renderExtraComponent } = props;
  const { children, props: tableProps = {} } = data;
  const { header, footer, ...restProps } = tableProps;
  const [loading, setLoading] = useState(false);
  const popoverRef = useRef();

  async function commandRequest({ request, values }) {
    const { url, ...options } = request;
    return await axios({
      url,
      method: "POST",
      ...options,
      data: { ...options.data, ...values },
    });
  }
  const renderContent = (cell) => {
    const { valueType, value } = cell;

    if (valueType === Enums.TableValueType.Html) {
      return <HTMLBlock html={value} />;
    } else if (valueType === Enums.TableValueType.NativeTable) {
      return <NativeTable data={value} />;
    } else if (valueType === Enums.TableValueType.Image) {
      return (
        <div className={styles.imageGroupWrapper}>
          {value?.map((item, index) => (
            <Image.PreviewGroup key={index} {...item?.props}>
              <div className={styles.imageWrapper}>
                <Image
                  src={item?.src}
                  width={item?.width}
                  height={item?.height}
                  preview={{ mask: <EyeOutlined /> }}
                  {...item?.props}
                />
                {item?.text ? <div className={styles.imageText}>{item?.text}</div> : null}
              </div>
            </Image.PreviewGroup>
          ))}
        </div>
      );
    } else if (valueType === Enums.TableValueType.Tag) {
      return <Tags tags={value} />;
    } else if (valueType === Enums.TableValueType.Command) {
      return (
        <span
          className={styles.command}
          onClick={async () => {
            await Helper.commandHandler({ command: cell?.command });
          }}
        >
          {value}
        </span>
      );
    } else if (renderExtraComponent) {
      return renderExtraComponent(cell);
    }

    return (
      <>
        {value}
        {cell?.copyable && typeof value === "string" && (
          <Typography.Text
            className={styles.copyableText}
            copyable={{
              text: value,
            }}
          ></Typography.Text>
        )}
      </>
    );
  };

  function renderEditable(item, index) {
    const { editable } = item;

    if (!editable) {
      return null;
    }

    const { component } = editable;

    if (component === Enums.Components.ExternalPageEditor) {
      return <ExternalPageEditor {...editable?.props} />;
    }

    return (
      <EditablePopover
        ref={popoverRef}
        defaultValue={item?.value}
        field={item?.key}
        editable={editable}
        onFinish={async (values, options) => {
          if (item?.key) {
            try {
              popoverRef.current.hidden();
              setLoading(true);
              await commandRequest({
                request: editable?.request,
                values: { field: item.key, value: values[item.key] },
              });
              setLoading(false);
              if (options?.length > 0) {
                item.value = options?.find((a) => a.value === values[item.key])?.label;
              } else {
                item.value = values[item.key];
              }
            } catch (e) {
              setLoading(false);
            }
          }
        }}
      />
    );
  }

  function renderComponent(componentName) {
    const componentData = componentName === "header" ? header : footer;
    if (!componentData) return null;

    const { content, ...otherProps } = componentData;
    if (!content) return null;

    const className = componentName === "header" ? styles.header : styles.footer;
    return <HTMLBlock className={className} {...otherProps} html={content} />;
  }

  return (
    <div className={styles.container}>
      {renderComponent("header")}
      <table {...restProps}>
        <tbody>
          {children?.map((tr, index) => {
            return (
              <tr key={index}>
                {tr?.map((cell, cellIndex) => {
                  const Tag = cell?.tag || "td";

                  return (
                    <Tag {...cell?.props} key={cellIndex}>
                      {renderContent(cell)}
                      {renderEditable(cell, cellIndex)}
                    </Tag>
                  );
                })}
              </tr>
            );
          })}
        </tbody>
      </table>
      {renderComponent("footer")}
    </div>
  );
}

export default NativeTable;
