import styles from "./index.module.scss";

import { useLocation } from "react-router-dom";

import useS<PERSON> from "swr";
import Fetchers from "@/fetchers";
import Helpers from "@/helpers";
import Utils from "utils";

import { Button, Flex } from "antd";
import J<PERSON>NComponents from "components/common/JSONComponent";
import JSONForm from "./components/JSONForm";

function OtherPenaltyEditor(props) {
  const location = useLocation();
  const params = Utils.getQueryParams(decodeURIComponent(location.search));

  const { data } = useSWR(params, async () => {
    try {
      Helpers.pageLoading(true);
      const result = await Fetchers.getOtherPenaltyDetail({ params }).then((res) => res?.data?.data);
      return result;
    } finally {
      Helpers.pageLoading(false);
    }
  });

  const extendComponents = {
    Form: JSONForm,
  };

  async function handleCommand({ command, ...others }) {
    Helpers.commandHandler({ command, ...others });
  }

  function handleCancel() {
    handleCommand({ command: { type: "close_modal", target: "windowTop" } });
  }

  function handleSubmit() {
    handleCommand({ command: { type: "submit", id: "form" } });
  }

  if (!data) return null;

  return (
    <div className={styles.container}>
      <JSONComponents data={data?.content} extendComponents={extendComponents} />
      <Flex justify="end" gap={10}>
        <Button onClick={handleCancel}>取消</Button>
        <Button type="primary" onClick={handleSubmit}>
          确定
        </Button>
      </Flex>
    </div>
  );
}

export default OtherPenaltyEditor;
