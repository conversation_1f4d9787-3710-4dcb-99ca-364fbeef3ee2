import React, { useEffect, useRef, useState } from "react";
import styles from "./index.module.scss";

const eventSource = "sms_side_popup";
const temporarilyClosedKey = "subscribe_phone_popup_temporarily_closed";

function SuccessView({ content }) {
  return (
    <div className={styles.successWrapper}>
      <div className={styles.header}>
        <img alt="" {...content?.success?.icon?.image} />
        <div className={styles.title} style={content?.success?.title?.style}>
          {content?.success?.title?.text}
        </div>
      </div>
      <div className={styles.discount} style={content?.success?.discount?.style}>
        <div className={styles.tips} style={content?.success?.tips?.style}>
          {content?.success?.tips?.text}
        </div>
        <div className={styles.code} style={content?.success?.code?.style}>
          {content?.success?.code?.text}
        </div>
      </div>
      <div className={styles.message} style={content?.success?.message?.style}>
        {content?.success?.message?.text}
      </div>
    </div>
  );
}

function SubscribePhonePopupPc(props) {
  const {
    data = {},
    name,
    popup_id,
    device,
    countryId = "US",
    getCountryList,
    fetchBlock,
    trackViewPopup,
    trackSubscribe,
    validator,
    submitFetcher,
    usePopupControl,
    current,
    preview,
    isSubscribed,
  } = props;
  const [blockData, setBlockData] = useState(data);
  const [countryList, setCountryList] = useState([]);
  const [subscribed, setSubscribed] = useState(JSON.parse(isSubscribed ?? false));
  const [isFold, setIsFold] = useState(localStorage.getItem("subscribe_phone_popup_fold") === "true" ?? false);
  const [selectedCountryId, setSelectedCountryId] = useState(countryId);
  const [errorMessage, setErrorMessage] = useState(null);
  const [privacyPolicyError, setPrivacyPolicyError] = useState(false);
  const { open, setOpen, handleTemporarilyClose } = usePopupControl({
    React,
    data: blockData,
    subscribed,
    temporarilyClosedKey,
  });

  const inputRef = useRef(null);
  const formRef = useRef(null);
  const privacyPolicyCheckboxRef = useRef(null);
  const paramsRef = useRef({});

  paramsRef.current = {
    name,
    popup_id,
    device,
    fetchBlock,
    getCountryList,
    trackViewPopup,
    template_info: blockData?.template_info,
  };

  function handleOpen() {
    setOpen(true);
  }

  function validatePrivacyPolicyCheckbox() {
    const { show, error_message } = blockData?.content?.main?.form?.privacy_policy_checkbox ?? {};
    if (show && !privacyPolicyCheckboxRef.current?.checked) {
      formRef.current.classList.add("has-error");
      setPrivacyPolicyError(error_message);
      return false;
    }
    formRef.current?.classList.remove("has-error");
    setPrivacyPolicyError(null);
    return true;
  }

  async function handleSubmit(e) {
    e.preventDefault();

    if (!validatePrivacyPolicyCheckbox()) {
      return;
    }

    const phone = inputRef.current.value;
    const errors = await validator?.({ phone });
    if (errors?.length > 0) {
      formRef.current.classList.add("has-error");
      setErrorMessage(errors[0].message);
      return;
    }

    formRef.current?.classList.remove("has-error");
    try {
      const dial = countryList?.filter((item) => item?.country_id === selectedCountryId)?.[0]?.dia;
      const data = { country_id: selectedCountryId, phone, dial };
      const result = await submitFetcher?.(data);
      if (result?.status === "00") {
        setSubscribed(true);
        trackSubscribe?.({ eventSource, data });
      } else {
        setErrorMessage(result?.message);
        formRef.current?.classList.add("has-error");
      }
    } catch (error) {
      console.log("[subscribe-phone-popup-pc] handleSubmit error", error);
    }
  }

  useEffect(() => {
    (async () => {
      const { fetchBlock, name, popup_id, device, getCountryList } = paramsRef.current;
      if (fetchBlock) {
        const block = await fetchBlock({ name, id: popup_id, device });
        setBlockData(block);
      }

      if (getCountryList) {
        setCountryList(await getCountryList());
      }
    })();
  }, []);

  useEffect(() => {
    if (open && !isFold) {
      const { trackViewPopup } = paramsRef.current;
      trackViewPopup?.({ eventSource });
    }
  }, [open, isFold]);

  useEffect(() => {
    if (preview && data) {
      setBlockData(data);
    }
  }, [preview, data]);

  return blockData ? (
    <>
      <div
        className={styles.smsPopup}
        onClick={() => {
          if (open) {
            handleTemporarilyClose();
          } else {
            handleOpen?.();
            localStorage.removeItem(temporarilyClosedKey);
          }
        }}
        style={blockData?.content?.take_back?.style}
      >
        <img alt="" {...blockData?.content?.take_back?.icon?.image} />
      </div>
      {open ? (
        <div className={`${styles.container} ${isFold ? styles.fold : ""}`} style={blockData?.content?.main?.style}>
          <div
            className={styles.sideBar}
            onClick={() => {
              setIsFold(!isFold);
              localStorage.setItem("subscribe_phone_popup_fold", !isFold);
            }}
            style={blockData?.content?.main?.sideBar?.style}
          >
            <div className={styles.arrow}>
              <img alt="" {...blockData?.content?.main?.sideBar?.arrow?.image} />
            </div>
            <div className={styles.title} style={blockData?.content?.main?.sideBar?.title?.style}>
              {blockData?.content?.main?.sideBar?.title?.text}
            </div>
            <div className={styles.icon}>
              <img alt="" {...blockData?.content?.main?.sideBar?.icon?.image} />
            </div>
          </div>
          <div className={`${styles.content}`} style={blockData?.content?.main?.content?.style}>
            {subscribed || current === 2 ? (
              <SuccessView content={blockData?.content} />
            ) : (
              <div className={styles.contentWrapper}>
                <div className={styles.title} style={blockData?.content?.main?.title?.style}>
                  {blockData?.content?.main?.title?.text}
                </div>
                <form ref={formRef} className={styles.form}>
                  <div className={styles.label} style={blockData?.content?.main?.form?.label?.style}>
                    {blockData?.content?.main?.form?.label?.text}
                  </div>
                  <div className={styles.subscribeWrapper}>
                    <select
                      className={styles.countryArea}
                      value={selectedCountryId}
                      onChange={(e) => setSelectedCountryId(e.target.value)}
                    >
                      {countryList.map((option) => (
                        <option key={option.country_id} value={option.country_id}>
                          {`${option?.country_id}${option?.dia ? ` (${option?.dia})` : ""}`}
                        </option>
                      ))}
                    </select>
                    <div className={styles.inputWrapper} style={blockData?.content?.main?.form?.input?.style}>
                      <input ref={inputRef} placeholder={blockData?.content?.main?.form?.input?.placeholder} />
                    </div>
                  </div>
                  <div className={styles.errorMessage} style={blockData?.content?.main?.form?.error_message?.style}>
                    {errorMessage}
                  </div>
                  <div className={styles.tipsWrapper}>
                    {!blockData?.content?.main?.form?.privacy_policy_checkbox?.show && (
                      <span className={styles.asterisk}>* </span>
                    )}
                    <label className={styles.tipsLabel}>
                      <input
                        ref={privacyPolicyCheckboxRef}
                        className={`${styles.privacyPolicyCheckbox} ${
                          blockData?.content?.main?.form?.privacy_policy_checkbox?.show ? styles.show : ""
                        }`}
                        type="checkbox"
                        defaultChecked={blockData?.content?.main?.form?.privacy_policy_checkbox?.value}
                      />
                      <span className={styles.tips} style={blockData?.content?.main?.form?.tips?.style}>
                        {blockData?.content?.main?.form?.tips?.text}
                      </span>
                      <a
                        className={styles.privacyPolicy}
                        href={blockData?.content?.main?.form?.privacy_policy?.href}
                        style={blockData?.content?.main?.form?.privacy_policy?.style}
                        target="_blank"
                        rel="noreferrer"
                      >
                        {blockData?.content?.main?.form?.privacy_policy?.text}
                      </a>
                    </label>
                  </div>
                  {privacyPolicyError && <div className={styles.errorMessage}>{privacyPolicyError}</div>}
                  <div className={styles.submitWrapper} style={blockData?.content?.main?.form?.error_message?.style}>
                    <button style={blockData?.content?.main?.form?.button?.style} onClick={handleSubmit}>
                      {blockData?.content?.main?.form?.button?.text}
                    </button>
                  </div>
                  <div
                    className={styles.doNotShowAgain}
                    style={blockData?.content?.main?.do_not_show_again?.style}
                    onClick={handleTemporarilyClose}
                  >
                    {blockData?.content?.main?.do_not_show_again?.text}
                  </div>
                </form>
              </div>
            )}
          </div>
        </div>
      ) : null}
    </>
  ) : null;
}

export default SubscribePhonePopupPc;
