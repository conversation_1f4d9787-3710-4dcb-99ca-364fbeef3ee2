import styles from "./index.module.scss";
import classNames from "classnames";

import { useLocation } from "react-router-dom";
import { useState } from "react";

import useSWR from "swr";
import Fetchers from "@/fetchers";
import Helper from "@/helpers";
import Utils from "utils";

import { Divider, Collapse, Card } from "antd";
import Loading from "components/common/Loading";
import JSONComponents from "components/common/JSONComponent";
import CollapseLabel from "components/common/JSONComponent/components/Collapse/components/label";
import Tags from "components/business/Tags";

function OrderDetail(props) {
  const location = useLocation();
  const params = Utils.getQueryParams(decodeURIComponent(location.search));
  const [loading, setLoading] = useState(false);
  const isInsideIframe = Helper.isInsideIframe();

  const { data } = useSWR(params, async () => {
    try {
      setLoading(true);
      const result = await Fetchers.getOrderDetail({ params }).then((res) => res?.data?.data);
      return result;
    } finally {
      setLoading(false);
    }
  });

  const defaultActiveKey = data?.items.map((item) => item.key);
  const collapseItems = data?.items.map((item) => {
    const children = (
      <div className={styles.panel}>
        <JSONComponents data={item?.content} />
      </div>
    );

    return {
      key: item?.key,
      label: (
        <span id={item?.key}>
          <CollapseLabel data={{ label: item?.label, actions: item?.actions }} />
        </span>
      ),
      children,
    };
  });

  async function handleCommand({ command, ...others }) {
    Helper.commandHandler({ command, ...others });
  }

  if (!data) return null;

  return (
    <div className={classNames(styles.container, { [styles.insideIframe]: isInsideIframe })}>
      <div className={styles.navBar}>
        {data?.items?.map((item) => (
          <div key={item.key} className={styles.navBarItem}>
            <a href={`#${item.key}`}>{item?.label}</a>
          </div>
        ))}
        <Divider />
        <div className={styles.navBarItem}>
          <a href={`#${data?.items[0].key}`}>回顶部</a>
        </div>
      </div>
      <div className={styles.content}>
        <Loading loading={loading}>
          {collapseItems && <Collapse defaultActiveKey={defaultActiveKey} items={collapseItems} />}
        </Loading>
      </div>

      <div className={styles.right}>
        <div className={styles.actions}>
          {data?.actions?.map((action) => (
            <Card className={styles.actionsWrapper} title={action?.label} key={action?.key} size="small">
              {action?.children?.map((item, index) => {
                return (
                  <div
                    className={styles.operation}
                    key={index}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCommand({ command: item?.command });
                    }}
                  >
                    {item?.label}
                  </div>
                );
              })}
            </Card>
          ))}
        </div>

        {data?.tags && (
          <div className={styles.tags}>
            <Tags tags={data?.tags} />
          </div>
        )}
      </div>
    </div>
  );
}

export default OrderDetail;
