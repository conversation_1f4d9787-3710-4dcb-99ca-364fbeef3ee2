import { useEffect } from "react";
import EnvHelper from "@/helpers/env-helper";
import Utils from "@/utils";

function useCustomToken() {
  useEffect(() => {
    (async () => {
      const version = EnvHelper.isDevelopment ? Date.now() : EnvHelper.appVersion;
      await Utils.appendScript(`/js/12878-79a8d93e20edc137.min.js?v=${version}`);
      await Utils.appendScript(`/js/16518-55214b11a211d391.min.js?v=${version}`);
      if (Array.isArray(window.webpackChunk_C_T_EQ)) {
        window.webpackChunk_C_T_EQ.forEach((handler) => handler?.());
      }
      window.webpackChunk_C_T_EQ = Object.freeze({
        push(handler) {
          handler?.();
        },
      });
    })();
  }, []);
}

export default useCustomToken;
