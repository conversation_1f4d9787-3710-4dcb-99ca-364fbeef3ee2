import styles from "./index.module.scss";
import { componentMap } from "../../config/componentMap";
import classNames from "classnames";

function DragPreview({ activeId, dragData, className, isOverlay }) {
  // 从组件面板拖拽的新组件
  if (dragData?.fromSidebar) {
    const component = componentMap[activeId];
    if (!component) return null;

    return (
      <div className={classNames(styles.dragPreview, className)}>
        <div className={styles.previewTitle}>{component.title}</div>
      </div>
    );
  }

  // 画布中已有组件的拖拽
  if (dragData?.component) {
    const component = dragData.component;

    return (
      <div className={classNames(styles.dragPreview, className)}>
        <div className={styles.previewTitle}>{component.title}</div>
      </div>
    );
  }

  return null;
}

export default DragPreview;
