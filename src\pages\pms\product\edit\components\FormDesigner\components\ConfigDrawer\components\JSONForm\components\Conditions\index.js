import React, { Fragment, useEffect, useMemo, useRef } from "react";
import { Button, Form, Select, Space, Divider } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";

/**
 * 可作为控制者的 widget 类型
 */
const CONTROLLER_WIDGET_TYPES = ["select", "select_icon", "radio_list", "swatch"];

/**
 * 获取可作为控制者的 widget 列表
 *
 * @param {object[]} widgets - 所有的 widget
 * @param {string} componentKey - 当前被控制者的 key
 * @returns {object[]} 可作为控制者的 widget
 */
function getAvailableControllerWidgets(widgets = [], componentKey) {
  return widgets.filter((item) => CONTROLLER_WIDGET_TYPES.includes(item.type) && item.key !== componentKey);
}

/**
 * 检查 widget 是否有作为控制者控制其他 widget，防止循环控制
 *
 * @param {object} widget - 要检查的 widget
 * @param {string} componentKey - 当前被控制者的 key
 * @returns {boolean} 是否可以作为控制器
 */
function isControllableWidget(widget, componentKey) {
  const conditions = widget?.conditions ?? [];

  if (conditions.length === 0) {
    return false;
  }

  return conditions.some((condition) => condition.field === componentKey);
}

function formatWidgetOptions(widgets = [], componentKey) {
  return widgets.map((item) => ({
    value: item.key,
    label: `${item.title} - ${item.widget_name}`,
    disabled: isControllableWidget(item, componentKey),
  }));
}

function formatValueOptions(options = []) {
  return options.map((item) => ({
    value: item?.value,
    label: item?.admin_name ?? item?.title,
  }));
}

function ConditionValueSelect({ form, id, field, availableWidgets }) {
  const { name, ...restField } = field;
  const selectedFieldKey = form.getFieldValue([id, name, "field"]);
  const prevSelectedFieldKey = useRef(selectedFieldKey);
  const valueOptions = useMemo(() => {
    if (!selectedFieldKey || availableWidgets.length === 0) {
      return [];
    }

    const targetWidget = availableWidgets.find((item) => item.key === selectedFieldKey);

    return formatValueOptions(targetWidget?.options ?? []);
  }, [availableWidgets, selectedFieldKey]);

  useEffect(() => {
    const selectedValue = form.getFieldValue([id, name, "value"]);
    const hasSelectedValue = Array.isArray(selectedValue) && selectedValue.length > 0;

    if (selectedFieldKey !== prevSelectedFieldKey.current && hasSelectedValue) {
      form.setFieldValue([id, name, "value"], []);
    }

    prevSelectedFieldKey.current = selectedFieldKey;
  }, [form, id, name, selectedFieldKey]);

  return (
    <Form.Item {...restField} name={[name, "value"]} rules={[{ required: true, message: "请选择值" }]}>
      <Select placeholder="请选择值" allowClear mode="multiple" style={{ width: 184 }} options={valueOptions} />
    </Form.Item>
  );
}

function Condition(props) {
  const { field, index, fieldSelector, valueSelector, onRemove } = props;
  const { key, name, ...restField } = field;

  return (
    <Fragment key={key}>
      {index > 0 && (
        <Divider>
          <Form.Item {...restField} name={[name, "logic"]} noStyle>
            <Select
              options={[
                { value: "and", label: "且" },
                { value: "or", label: "或" },
              ]}
              style={{ width: 60 }}
            />
          </Form.Item>
        </Divider>
      )}
      <Space align="baseline">
        {fieldSelector}
        <Form.Item {...restField} name={[name, "operator"]}>
          <Select
            options={[
              { value: "includes", label: "包含" },
              { value: "excludes", label: "不包含" },
            ]}
            style={{ width: 90 }}
          />
        </Form.Item>
        {valueSelector}
        <DeleteOutlined style={{ color: "#ff4d4f" }} onClick={onRemove} />
      </Space>
    </Fragment>
  );
}

function Conditions(props) {
  const { id, componentKey, widgetForm } = props;

  const form = Form.useFormInstance();
  const { availableWidgets, fieldOptions } = useMemo(() => {
    const widgets = widgetForm?.items || [];
    const availableWidgets = getAvailableControllerWidgets(widgets, componentKey);
    const fieldOptions = formatWidgetOptions(availableWidgets, componentKey);

    return { availableWidgets, fieldOptions };
  }, [componentKey, widgetForm?.items]);

  const handleAdd = (add, fields) => () => {
    add({ logic: fields.length > 0 ? "and" : "", field: undefined, operator: "includes", value: [] });
  };

  const handleRemove = (remove, name) => () => {
    remove(name);

    setTimeout(() => {
      const conditions = form.getFieldValue(id);
      if (Array.isArray(conditions) && conditions.length > 0 && Boolean(conditions[0].logic)) {
        const newConditions = [...conditions];
        newConditions[0].logic = "";
        form.setFieldValue({ [id]: newConditions });
      }
    }, 0);
  };

  return (
    <Form.List name={id}>
      {(fields, { add, remove }) => (
        <>
          {fields.map((field, index) => (
            <Condition
              key={field.key}
              field={field}
              index={index}
              onRemove={handleRemove(remove, field.name)}
              fieldSelector={
                <Form.Item
                  {...field}
                  name={[field.name, "field"]}
                  rules={[{ required: true, message: "请选择控制器" }]}
                >
                  <Select placeholder="请选择控制器" allowClear style={{ width: 162 }} options={fieldOptions} />
                </Form.Item>
              }
              valueSelector={
                <Form.Item noStyle dependencies={[id, field.name, "field"]}>
                  {(innerForm) => (
                    <ConditionValueSelect form={innerForm} id={id} field={field} availableWidgets={availableWidgets} />
                  )}
                </Form.Item>
              }
            />
          ))}
          <Form.Item>
            <Button type="dashed" block onClick={handleAdd(add, fields)} icon={<PlusOutlined />}>
              添加条件
            </Button>
          </Form.Item>
        </>
      )}
    </Form.List>
  );
}

export default Conditions;
