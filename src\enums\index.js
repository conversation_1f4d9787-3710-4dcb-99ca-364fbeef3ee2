const Enums = Object.freeze({
  LocalStorageKey: {
    Token: "token",
    AsideFold: "asideFold",
    SidebarFilterFold: "sidebarFilterFold",
    SystemInfo: "systemInfo",
    ListsTableColumns: "listsTableColumns",
    PodLocalData: "podLocalData",
    PathSiteValues: "pathSiteValues",
  },
  SessionStorageKey: {
    Brand: "brand",
    PathSiteValues: "pathSiteValues",
    SidebarFilterFold: "sidebarFilterFold",
    PathPageConfigs: "pathPageConfigs",
    customHeaders: "customHeaders",
  },
  CookieName: {
    Token: "token",
  },
  ApiStatus: {
    Success: "00",
  },
  UploadFileStatus: {
    Done: "done",
    Uploading: "uploading",
    Success: "success",
    Error: "error",
    Removed: "removed",
  },
  Components: {
    Row: "Row",
    Col: "Col",
    Input: "Input",
    InputNumber: "InputNumber",
    Search: "Search",
    Textarea: "Textarea",
    Select: "Select",
    Radio: "Radio",
    Checkbox: "Checkbox",
    Tree: "Tree",
    Switch: "Switch",
    Upload: "Upload",
    ImageUpload: "ImageUpload",
    RichTextEditor: "RichTextEditor",
    JSONEditor: "JSONEditor",
    Modal: "Modal",
    Cascader: "Cascader",
    DatePicker: "DatePicker",
    RangePicker: "RangePicker",
    Card: "Card",
    Text: "Text",
    Transfer: "Transfer",
    EditTable: "EditTable",
    NativeTable: "NativeTable",
    Collapse: "Collapse",
    TreeSelect: "TreeSelect",
    Button: "Button",
    TextEditable: "TextEditable",
    FileUpload: "FileUpload",
    CameraPhotoUpload: "CameraPhotoUpload",
    ColorPicker: "ColorPicker",
    LangTabs: "LangTabs",
    FormDesigner: "FormDesigner",
    SpaceWrapper: "SpaceWrapper",
    CheckboxSingle: "CheckboxSingle",
    ExternalPageEditor: "ExternalPageEditor",
    Tabs: "Tabs",
    CommandTrigger: "CommandTrigger",
  },
  Device: {
    Pc: "pc",
    Mobile: "mobile",
  },
  LoginType: {
    Account: "account",
    Code: "code",
  },
  EventName: {
    FormSubmit: "formSubmit",
    SetCommandModal: "setCommandModal",
    ReloadTable: "reloadTable",
    SetCommandDrawer: "setCommandDrawer",
    SetSubmitButtonLoading: "setSubmitButtonLoading",
    SetCommandLoading: "setCommandLoading",
    AntdMessage: "antdMessage",
    AntdModal: "antdModal",
    GlobalMessage: "globalMessage",
    GlobalNavigate: "globalNavigate",
    UpdatePageData: "updatePageData",
    AntdNotification: "antdNotification",
    CalculateStonePrice: "calculateStonePrice",
  },
  TableValueType: {
    Operation: "operation",
    Image: "image",
    Link: "link",
    Html: "html",
    Command: "command",
    Key: "key",
    Tag: "tag",
    NativeTable: "native_table",
  },
  CommandType: {
    Request: "request",
    Redirect: "redirect",
    Modal: "modal",
    ReloadTable: "reload_table",
    Reload: "reload",
    Download: "download",
    Message: "message",
    Submit: "submit",
    CloseModal: "close_modal",
    Drawer: "drawer",
    CloseDrawer: "close_drawer",
    CodeExecution: "code_execution",
    UpdatePageData: "update_page_data",
    DirectDownload: "direct_download",
    Notification: "notification",
  },
  CommandTarget: {
    WindowTop: "windowTop",
    WindowSelf: "windowSelf",
  },
  PopupTypes: {
    SubscribePhonePopup: "SubscribePhonePopup",
    LuckyDrawSubscribePopup: "LuckyDrawSubscribePopup",
  },
  SideBarActionType: {
    SidebarFilter: "sidebarFilter",
    SidebarList: "sidebarList",
  },
  WebSocketType: {
    DownloadNotification: "download_notification",
  },
  HeaderName: {
    Language: "Language",
  },
});

export default Enums;
