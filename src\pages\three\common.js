import * as THREE from "three";
import { <PERSON><PERSON><PERSON>oa<PERSON>, G<PERSON><PERSON>oader } from "three/addons";
import cloneDeep from "lodash.clonedeep";
import Utils from "@/utils";
import Enums from "@/enums";

export async function load3dModel({
  width = 800,
  height = 800,
  background_color = "#fff",
  model_url,
  map_url,
  normal_map_url,
  ao_map_url,
  mesh_name,
}) {
  if (!map_url) {
    Utils.dispatchEvent(Enums.EventName.GlobalMessage, { type: "error", content: "纹理贴图不能为空" });
    throw new Error("纹理贴图不能为空");
  }

  // 场景
  const scene = new THREE.Scene();
  scene.background = new THREE.Color(background_color);

  // 相机
  const camera = new THREE.PerspectiveCamera(60, width / height, 0.1, 1000);
  camera.position.set(0, 0, -1.3);
  camera.lookAt(0, 0, 0);

  // 渲染器
  const renderer = new THREE.WebGLRenderer({ antialias: true, preserveDrawingBuffer: true });
  renderer.outputColorSpace = THREE.SRGBColorSpace;
  renderer.setSize(width, height);
  renderer.setPixelRatio(window.devicePixelRatio);

  // 环境光
  const ambientLight = new THREE.AmbientLight("#fff", 1);
  scene.add(ambientLight);

  // 平行光
  const directionalLight = new THREE.DirectionalLight("#fff", 2.6);
  directionalLight.position.copy(camera.position);
  directionalLight.castShadow = true;
  scene.add(directionalLight);

  // 创建 DRACOLoader 实例
  const dracoLoader = new DRACOLoader();

  // 设置 DRACO 解码器文件的路径
  dracoLoader.setDecoderPath("https://www.gstatic.com/draco/v1/decoders/");

  // 模型加载器
  const modelLoader = new GLTFLoader();
  modelLoader.setDRACOLoader(dracoLoader);

  // 贴图加载器
  const textureLoader = new THREE.TextureLoader();

  function loadTexture(url, onLoad = setMapParams) {
    return new Promise((resolve, reject) => {
      textureLoader.load(
        url,
        (texture) => {
          onLoad(texture);
          resolve(texture);
        },
        null,
        reject
      );
    });
  }

  function setColorMapParams(texture) {
    setMapParams(texture);
    texture.colorSpace = THREE.SRGBColorSpace;
  }

  function setMapParams(texture) {
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.flipY = false;
    texture.userData = { mimeType: "image/png" };
  }

  function centerModel(model, targetSize = 1) {
    // 1. 计算包围盒
    const box = new THREE.Box3().setFromObject(model);
    const size = new THREE.Vector3();
    box.getSize(size);
    const center = new THREE.Vector3();
    box.getCenter(center);

    // 2. 居中
    model.position.sub(center);

    // 3. 缩放
    const maxAxis = Math.max(size.x, size.y, size.z);
    model.scale.multiplyScalar(targetSize / maxAxis);
  }

  const [gltf, maps] = await Promise.all([
    new Promise((resolve, reject) => {
      modelLoader.load(model_url, resolve, null, reject);
    }),
    new Promise((resolve, reject) => {
      Promise.all([
        loadTexture(map_url, setColorMapParams).catch((err) => {
          Utils.dispatchEvent(Enums.EventName.GlobalMessage, { type: "error", content: "纹理贴图加载出错" });
          throw new Error("纹理贴图加载出错");
        }),
        loadTexture(normal_map_url).catch(() => null),
        loadTexture(ao_map_url).catch(() => null),
      ])
        .then(([map, normalMap, aoMap]) => {
          resolve({ map, normalMap, aoMap });
        })
        .catch(reject);
    }),
  ]);

  const model = gltf.scene;
  model.position.set(0, 0, 0);
  model.rotation.y = THREE.MathUtils.degToRad(180);

  let mesh;
  model.traverse((child) => {
    if (child.isMesh) {
      child.material.metalness = 0;
      child.material.roughness = 1;
      child.material.color = new THREE.Color("#fff");
      if (child.name === mesh_name) {
        mesh = child;
      }
    }
  });

  if (!mesh) {
    Utils.dispatchEvent(Enums.EventName.GlobalMessage, { type: "error", content: `mesh_name无效` });
    throw new Error(`mesh_name无效`);
  }

  mesh.material = new THREE.MeshStandardMaterial({
    color: "#fff",
    map: maps.map,
    normalMap: maps.normalMap,
    aoMap: maps.aoMap,
  });

  centerModel(model);

  scene.add(model);

  return { scene, model, camera, renderer, ambientLight, directionalLight };
}

export async function takePhotos({ scene, model, camera, renderer, ambientLight, directionalLight, groups }) {
  const photos = [];
  for (let i = 0; i < groups.length; i++) {
    const group = groups[i];
    restoreScene({ scene, model, camera, ambientLight, directionalLight, group });
    renderer.render(scene, camera);
    const src = renderer.domElement.toDataURL();
    photos.push({ id: group.id, name: group.name, src });
  }
  return photos;
}

export function restoreScene({ scene, model, camera, ambientLight, directionalLight, group }) {
  // 恢复场景
  scene.clear();
  scene.background = new THREE.Color(group.scene.background);

  // 恢复相机
  camera.fov = group.camera.fov;
  camera.aspect = group.camera.aspect;
  camera.near = group.camera.near;
  camera.far = group.camera.far;
  camera.position.copy(group.camera.position);
  camera.lookAt(0, 0, 0);

  // 恢复灯光
  ambientLight.color = new THREE.Color(group.ambientLight.color);
  ambientLight.intensity = group.ambientLight.intensity;
  directionalLight.color = new THREE.Color(group.directionalLight.color);
  directionalLight.intensity = group.directionalLight.intensity;
  directionalLight.position.set(
    group.directionalLight.position.x,
    group.directionalLight.position.y,
    group.directionalLight.position.z
  );
  scene.add(ambientLight, directionalLight);

  // 恢复主模型
  restoreModel({ model, data: group.model });
  scene.add(model);

  // 恢复克隆模型
  group.clonedModels?.forEach((data) => {
    const clonedModel = model.clone();
    restoreModel({ model: clonedModel, data });
    scene.add(clonedModel);
  });
}

export function restoreModel({ model, data }) {
  model.position.set(data.position.x, data.position.y, data.position.z);
  model.rotation.set(data.rotation.x, data.rotation.y, data.rotation.z);
  model.scale.x = model.scale.y = model.scale.z = data.scale;
  if (data.uuid) model.uuid = data.uuid;
  model.name = data.name;
  model.userData = cloneDeep(data.userData);
}
