module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: `<p>Hi&nbsp;dsfdgf dsdfd,</p>

    <p>&nbsp;</p>

    <p>Thank you for informing us of your intent to return your personalized item.</p>

    <p>&nbsp;</p>

    <p>We understand your request, but please be aware that due to the personalized nature of the item, so it is unable to return for a refund. And there will have a 35% restocking fee to be charged to exchange.&nbsp;Additionally, there will be shipping fees incurred for the return you need to pay, and the shipping fee for the original order will also be deducted from your refund amount.</p>

    <p>&nbsp;</p>

    <p>Please check the link to get more information&nbsp;<a href="https://www.jeulia.com/30-day-return-policy.html">https://www.jeulia.com/30-day-return-policy.html</a></p>

    <p><img alt="" height="243" src="https://img1.jeulia.com/s3/erp/ckeditor_images/2023/04/14/files/64392562910e3.png" width="1395" /></p>

    <p>&nbsp;</p>

    <p>In order to help alleviate any unnecessary loss, we would like to offer you a better solution. We suggest a refund for 50% of the item amount via store credit, and you can keep the item. This store credit can be used towards purchasing a different size or style that you may be satisfied with.&nbsp;</p>

    <p><br />
    We hope that this solution is reasonable for you. Please let us know if you are interested in our offer, and we will provide you with the necessary information.</p>

    <p><br />
    We appreciate your understanding, and please do not hesitate to contact us if you have any further questions or concerns.</p>

    <p>Please show us your idea in your next email.</p>

    <p>&nbsp;</p>

    <p>Thank you,</p>

    <p>{agent_name}</p>

    <p>The Jeulia Customer Support Team</p>`,
  });
};
