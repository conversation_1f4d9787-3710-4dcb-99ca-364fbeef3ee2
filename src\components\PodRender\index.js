import PropTypes from "prop-types";
import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from "react";
import ResponsiveBox from "@/components/ResponsiveBox";
import debounce from "lodash.debounce";
import PodHelper from "./pod-helper";
import Helper from "@/helpers";

function PodRender(props, ref) {
  const { layoutId, width, height, objects, style } = props;
  const [ready, setReady] = useState(false);
  const containerRef = useRef();
  const canvasRef = useRef();
  const objectsString = JSON.stringify(objects);

  const debounceRenderObjects = useMemo(() => {
    return debounce((arr) => {
      const { renderObjects } = paramsRef.current;
      renderObjects(arr);
    }, 100);
  }, []);

  const paramsRef = useRef({});
  paramsRef.current = {
    ...paramsRef.current,
    props,
    renderObjects,
    debounceRenderObjects,
    resizeObject,
    resizeAllObjects,
  };

  function getContainerRect() {
    return containerRef.current?.getBoundingClientRect();
  }

  function renderObjects(arr) {
    const { canvas, isLayoutChange } = paramsRef.current;
    const objects = canvas.getObjects();
    const objectDataMap = {};
    const promiseList = [];
    objects.forEach((item) => {
      objectDataMap[item.id] = item;
    });
    for (const data of arr) {
      const object = objectDataMap[data.id];
      if (!object) {
        promiseList.push(createObject({ data }));
      } else {
        if (data.type === "image") {
          if (data.image.src !== object.getSrc()) {
            promiseList.push(updateObject({ object, data }));
          }
        } else if (data.type === "text") {
          if (data.text !== object.text) {
            promiseList.push(updateObject({ object, data }));
          }
        }
      }
    }
    if (isLayoutChange) {
      paramsRef.current.isLayoutChange = false;
      const deletedObjects = objects.filter((a) => !arr.some((b) => a.id === b.id));
      canvas.remove(...deletedObjects);
      resizeAllObjects();
      setObjectsIndex();
    }
    Promise.allSettled(promiseList).finally(() => {
      // console.log(canvas.getObjects());
    });
  }

  function resizeObject({ object, data }) {
    const { width: targetWidth } = getContainerRect();
    const { width: originalWidth } = paramsRef.current.props;
    const scale = targetWidth / originalWidth;
    PodHelper.resizeObject({ object, data, scale });
  }

  function resizeAllObjects() {
    const { canvas, props } = paramsRef.current;
    canvas.getObjects().forEach((object) => {
      const data = props.objects.find((item) => item.id === object.id);
      resizeObject({ object, data });
    });
  }

  function setObjectProps({ object, data }) {
    object.id = data.id;
    // object.apiData = data;
    resizeObject({ object, data });
  }

  function setObjectsIndex() {
    const { canvas } = paramsRef.current;
    canvas.getObjects().forEach((object) => {
      const data = props.objects.find((item) => item.id === object.id);
      object.moveTo(data.zIndex || 0);
    });
  }

  async function updateObject({ object, data }) {
    const { canvas } = paramsRef.current;
    if (object && data) {
      await new Promise((resolve) => {
        if (data.type === "image") {
          object.setSrc(data.image.src, resolve);
        } else if (data.type === "text") {
          object.set({ text: data.text });
          resolve();
        }
      });
      canvas.renderAll();
      setObjectProps({ object, data });
      setObjectsIndex();
    }
  }

  async function createObject({ data }) {
    const { canvas, fabric } = paramsRef.current;
    const { Image: FabricImage } = fabric;
    const placeholder = new FabricImage();
    placeholder.id = data.id;
    // placeholder.apiData = data;
    canvas.add(placeholder);
    const object = await createFabricObjectFromJson({ data });
    if (object) {
      setObjectProps({ object, data });
      canvas.remove(placeholder);
      canvas.add(object);
      setObjectsIndex();
    }
  }

  async function createFabricObjectFromJson({ data }) {
    if (data.type === "image") {
      return await createImageObject({ data });
    } else if (data.type === "text") {
      return await createTextObject({ data });
    }
  }

  function createImageObject({ data }) {
    return new Promise((resolve) => {
      const { fabric } = paramsRef.current;
      const { Image: FabricImage } = fabric;
      const { src } = data.image;
      if (src) {
        FabricImage.fromURL(src, (object, isError) => {
          if (isError) {
            resolve();
          } else {
            resolve(object);
          }
        });
      } else {
        resolve();
      }
    });
  }

  function createTextObject({ data }) {
    return new Promise(async (resolve) => {
      const { fabric } = paramsRef.current;
      const { Textbox } = fabric;
      await Promise.all(data.fonts?.map((font) => Helper.loadFont(font.family, font.source, font.descriptors)));
      const textbox = new Textbox(data.text, data.object_props);
      resolve(textbox);
    });
  }

  useImperativeHandle(
    ref,
    () => {
      return {
        getCanvas() {
          return paramsRef.current.canvas;
        },
      };
    },
    []
  );

  useEffect(() => {
    import("fabric").then((module) => {
      const { fabric } = module;
      paramsRef.current.fabric = fabric;
      setReady(true);
    });
  }, []);

  useEffect(() => {
    if (ready) {
      const { fabric } = paramsRef.current;
      const { StaticCanvas } = fabric;
      const { width, height } = getContainerRect();
      const canvasElement = canvasRef.current;
      paramsRef.current.canvas = new StaticCanvas(canvasElement, { width, height });
    }

    return function () {
      paramsRef.current.canvas?.dispose();
    };
  }, [ready]);

  useEffect(() => {
    function handleWindowResize() {
      const { canvas, resizeAllObjects } = paramsRef.current;
      const { width, height } = getContainerRect();
      canvas.setWidth(width);
      canvas.setHeight(height);
      resizeAllObjects();
    }
    window.addEventListener("resize", handleWindowResize);

    return function () {
      window.removeEventListener("resize", handleWindowResize);
    };
  }, []);

  useEffect(() => {
    paramsRef.current.isLayoutChange = true;
  }, [layoutId]);

  useEffect(() => {
    if (ready) {
      const { debounceRenderObjects } = paramsRef.current;
      const objects = JSON.parse(objectsString || null);
      if (objects?.length > 0) {
        debounceRenderObjects(objects);
      }
    }
  }, [ready, objectsString]);

  return (
    <ResponsiveBox width={width} height={height} style={{ userSelect: "none", ...style }}>
      <div ref={containerRef} style={{ height: "100%" }}>
        <canvas ref={canvasRef}></canvas>
      </div>
    </ResponsiveBox>
  );
}

PodRender = forwardRef(PodRender);

PodRender.propTypes = {
  layoutId: PropTypes.string,
  width: PropTypes.number.isRequired,
  height: PropTypes.number.isRequired,
  objects: PropTypes.array,
  style: PropTypes.object,
};

export default PodRender;
