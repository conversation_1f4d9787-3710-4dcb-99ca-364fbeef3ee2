.page {
  display: flex;
  padding: 10px;
  gap: 10px;
  align-items: flex-start;
}

.canvasWrapper {
  width: 65%;
  max-width: 802px;
  aspect-ratio: 1/1;
  border: 1px solid #ddd;
  background-color: #fff;

  > div {
    width: 100%;
    height: 100%;
  }
}

.form {
  max-height: 760px;
  overflow: auto;
  border: 1px solid #ddd;
  border-top: 1px solid #fff;
  position: relative;
}

.formContent {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  padding: 10px;
  background-color: #fff;
}

.formControls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.formRow {
  display: flex;
  gap: 5px;
  align-items: center;

  .label {
    line-height: 1;
    min-width: 38px;
  }

  .control {
    flex: 1;
    line-height: 1;

    input {
      margin: 0;
      width: 100%;
    }
  }
}

.selectFile {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  opacity: 0;
}

.grid {
  display: grid;
  grid-template-columns: repeat(var(--grid-column), minmax(0px, 1fr));
  gap: 10px;
}

.tabs {
  --tab-border-color: #ddd;

  [class~="ant-tabs-tab-with-remove"] {
    gap: 8px;
  }

  [class~="ant-tabs-tab-remove"] {
    margin: 0;
    padding: 0;
  }

  [class~="ant-tabs-nav"] {
    margin: 0;

    &:before {
      border-color: var(--tab-border-color);
    }

    [class~="ant-tabs-tab"] {
      border-color: var(--tab-border-color);

      &[class~="ant-tabs-tab-active"] {
        border-bottom-color: #fff;
        position: relative;
        z-index: 2;
      }
    }

    button[class~="ant-tabs-nav-add"] {
      border-color: var(--tab-border-color);
    }
  }

  [class~="ant-tabs-nav-add"] {
    border-color: var(--tab-border-color);
  }
}

.removeSceneButton {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
