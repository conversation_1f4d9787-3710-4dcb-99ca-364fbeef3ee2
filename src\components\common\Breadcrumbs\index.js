import PropTypes from "prop-types";
import { useEffect } from "react";
import { Breadcrumb } from "antd";
import Helper from "helpers";

function Breadcrumbs(props) {
  const { data } = props;
  const isInsideIframe = Helper.isInsideIframe();

  useEffect(() => {
    if (data?.length) {
      document.title = `${data[data.length - 1].title}`;
    }
  }, [data]);

  return isInsideIframe ? null : (
    <Breadcrumb items={data}>
      {/*{data?.map((item, index) => {
        return <Breadcrumb.Item key={index}>{item.title}</Breadcrumb.Item>
      })}*/}
    </Breadcrumb>
  );
}

Breadcrumbs.propTypes = {
  data: PropTypes.array,
};

export default Breadcrumbs;
