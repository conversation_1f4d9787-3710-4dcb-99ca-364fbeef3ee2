import { useEffect, useRef, useState } from "react";
import Fetchers from "@/fetchers";
import { Button, Popconfirm, Table } from "antd";
import Link from "@/components/Link";
import styles from "./index.module.scss";
import TableHelper from "@/helpers/table-helper";
import { useLocation } from "react-router-dom";
import Utils from "@/utils";

function OptionSets(props) {
  const {} = props;
  const [tableData, setTableData] = useState();
  const [tableLoading, setTableLoading] = useState(false);
  const location = useLocation();

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current, loadTableData };

  async function loadTableData() {
    setTableLoading(true);
    const { page, pageSize } = Utils.getQueryParams(window.location.href);
    Fetchers.getPodOptionSets({ query: { page, pageSize } })
      .then((res) => {
        const result = res.data;
        if (result.success) {
          TableHelper.createColumnsRender({ columns: result.data.tableProps.columns });
          result.data.tableProps.columns.push({
            width: 1,
            render(text, item, index) {
              return (
                <div className="table-operations-column">
                  <Link to="/pod/edit-option-set" query={{ id: item.id }}>
                    <Button type="primary" size="small">
                      编辑
                    </Button>
                  </Link>
                  <Popconfirm
                    title="确定删除吗？"
                    onConfirm={() => {
                      setTableLoading(true);
                      Fetchers.deletePodOptionSet({ id: item.id }).then(() => {
                        loadTableData();
                      });
                    }}
                  >
                    <Button type="primary" size="small">
                      删除
                    </Button>
                  </Popconfirm>
                </div>
              );
            },
          });
          setTableData(result.data);
        }
      })
      .finally(() => {
        setTableLoading(false);
      });
  }

  useEffect(() => {
    const { loadTableData } = paramsRef.current;
    loadTableData();
  }, [location.search]);

  return (
    <div className={styles.page}>
      <div>
        <Table rowKey="id" {...tableData?.tableProps} loading={tableLoading} onChange={TableHelper.onChange}></Table>
      </div>
    </div>
  );
}

export default OptionSets;
