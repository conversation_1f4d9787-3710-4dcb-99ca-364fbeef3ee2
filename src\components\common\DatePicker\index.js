import { DatePicker as AntdDatePicker } from "antd";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import "dayjs/locale/en";

dayjs.locale("zh-cn");

function formatValue(value) {
  if (!value) return null;
  const dateArray = Array.isArray(value) ? value : value.split(",");
  return dateArray.map((dateString) => (dateString ? dayjs(dateString) : null));
}

function DatePicker(props) {
  const { value, onChange } = props;

  function handleChange(date, dateString) {
    onChange?.(dateString);
  }

  return <AntdDatePicker {...props} value={formatValue(value)} onChange={handleChange} />;
}

DatePicker.RangePicker = function (props) {
  const { value, onChange } = props;

  function handleChange(dates, dateStrings) {
    if (dateStrings?.length === 2) {
      if (!dateStrings[0] || !dateStrings[1]) {
        dateStrings = null;
      }
    }
    onChange?.(dateStrings);
  }

  return <AntdDatePicker.RangePicker {...props} value={formatValue(value)} onChange={handleChange} />;
};

export default DatePicker;
