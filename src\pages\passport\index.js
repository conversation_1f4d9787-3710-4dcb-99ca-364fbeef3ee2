import Loading from "components/common/Loading";
import { useEffect, useState } from "react";
import Enums from "enums";
import Helper from "helpers";
import Utils from "utils";

function Passport(props) {
  const params = Utils.toQueryParams(window.location.href);
  const [loading] = useState(true);

  useEffect(() => {
    const { redirect, token } = params;
    if (token) {
      Helper.setCookies({ [Enums.CookieName.Token]: token });
    }
    window.location.href = redirect ?? "/";
  }, [params]);

  return (
    <Loading loading={loading}>
      <div style={{ width: "100vw", height: "100vh" }}></div>
    </Loading>
  );
}

export default Passport;
