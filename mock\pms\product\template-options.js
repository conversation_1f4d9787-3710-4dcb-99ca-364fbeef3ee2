module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: [
      {
        id: "1",
        value: "test",
        label: "test",
        data: [
          {
            key: "master_attr_dictionary_identifier",
            icon: "el-icon-tickets",
            name: "",
            type: "swatch",
            style: "color",
            title: "color",
            drakey: "master_attr_dictionary_identifier",
            options: [
              {
                id: "459",
                cost: 0,
                price: null,
                title: "白色",
                value: "white",
                checked: true,
              },
              {
                id: "460",
                cost: 0,
                price: null,
                title: "黑色",
                value: "_000",
                checked: true,
              },
              {
                id: "461",
                cost: 0,
                price: null,
                title: "灰色",
                value: "_919496",
                checked: true,
              },
              {
                id: "462",
                cost: 0,
                price: null,
                title: "红色",
                value: "_FF0000",
                checked: true,
              },
              {
                id: "463",
                cost: 0,
                price: null,
                title: "绿色",
                value: "_52e4ab",
                checked: true,
              },
              {
                id: "464",
                cost: 0,
                price: null,
                title: "天蓝",
                value: "skyblue",
                checked: true,
              },
              {
                id: "477",
                cost: 0,
                price: null,
                title: "宝蓝色",
                value: "royalblue",
                checked: true,
              },
              {
                id: "478",
                cost: 0,
                price: null,
                title: "黄色",
                value: "yellow",
                checked: true,
              },
              {
                id: "603",
                cost: 0,
                price: null,
                title: "粉色",
                value: "_EC808D",
                checked: true,
              },
              {
                id: "1344",
                cost: 0,
                price: null,
                title: "紫色",
                value: "purple",
                checked: true,
              },
            ],
            required: true,
            identifier: "product_custom_option_stone_shape",
            isDisabled: true,
            title_label: "Color",
            title_value: "color",
            widget_name: "色卡",
            change_title: true,
          },
          {
            key: "upload3",
            icon: "el-icon-picture-outline",
            name: "",
            type: "upload",
            style: "crop-free-size",
            title: "upload_photo",
            drakey: "1665737776000_83726",
            shadow: false,
            required: true,
            mask_info: [],
            isDisabled: false,
            title_label: "Upload Photo",
            title_value: "upload_photo",
            widget_name: "上传图片",
          },
          {
            key: "second_attr_dictionary_identifier",
            icon: "el-icon-arrow-down",
            type: "select",
            title: "size",
            drakey: "second_attr_dictionary_identifier",
            options: [
              {
                id: "387",
                cost: 0,
                price: null,
                title: "Child Foot Length 14cm=5.5in",
                value: "childfootlength14cm55in",
                checked: true,
              },
              {
                id: "388",
                cost: 0,
                price: null,
                title: "Woman Foot Length 18cm=7.08in",
                value: "womanfootlength18cm708in",
                checked: true,
              },
              {
                id: "389",
                cost: 0,
                price: null,
                title: "Man Foot Length 20cm=7.87in",
                value: "manfootlength20cm787in",
                checked: true,
              },
            ],
            required: true,
            identifier: "product_custom_option_metal_color",
            isDisabled: true,
            placeholder: "-- Please select --",
            title_label: "Size",
            title_value: "",
            widget_name: "下拉选项",
          },
        ],
      },
      {
        id: "18",
        value: "月球灯模板",
        label: "月球灯模板",
        data: '[{"key": "stepper2", "icon": "el-icon-guide", "name": "", "type": "stepper", "drakey": "1659581024000_36886", "children": [{"key": "upload4", "icon": "el-icon-picture-outline", "name": "", "type": "upload", "style": "drag", "title": "upload_photo", "drakey": "1659581034000_12542", "shadow": false, "required": true, "mask_info": {"size": {"width": 800, "height": 800}, "image": "product/f/2/62eb327a7f12f.png"}, "isDisabled": false, "title_label": "Upload Photo", "title_value": "upload_photo", "widget_name": "上传图片"}, {"key": "engraving11", "icon": "el-icon-document-copy", "name": "", "type": "composite_group", "image": "product/a/4/62eb32f59094a.png", "title": "engraving", "drakey": "1659581159000_80801", "children": [{"key": "text_group9", "svg": "", "icon": "el-icon-document-add", "name": "", "type": "text_group", "style": "fixed", "title": "engraving", "drakey": "1659581185000_28029", "children": [{"key": "textarea10", "icon": "el-icon-copy-document", "name": "", "rows": 2, "type": "textarea", "price": {"base_price": ""}, "title": "engraving", "drakey": "1659581195000_68250", "required": true, "isDisabled": false, "max_length": 20, "placeholder": "enter_the_engraving", "title_label": "Engraving", "title_value": "engraving", "widget_name": "多行刻字", "placeholder_label": "Enter the engraving"}], "required": true, "isDisabled": false, "button_text": "enter_the_engraving", "title_label": "Engraving", "title_value": "engraving", "widget_name": "多行刻字组", "placeholder_label": "Enter the engraving"}], "required": true, "isDisabled": false, "placeholder": "addengraving", "title_label": "Engraving", "title_value": "engraving", "widget_name": "组合组件", "background_image": "product/9/9/62eb32fcac099.png", "placeholder_label": "Add Engraving"}, {"key": "second_attr_dictionary_identifier", "icon": "el-icon-arrow-down", "type": "select", "title": "size", "drakey": "second_attr_dictionary_identifier", "options": [{"id": "911", "price": null, "title": "10cm", "value": "10cm", "checked": true}, {"id": "912", "price": 10, "title": "13cm", "value": "13cm", "checked": true}, {"id": "913", "price": 20, "title": "15cm", "value": "15cm", "checked": true}, {"id": "914", "price": 30, "title": "18cm", "value": "18cm", "checked": true}, {"id": "915", "price": 40, "title": "20cm", "value": "20cm", "checked": true}, {"id": "1130", "price": 50, "title": "22cm", "value": "22cm", "checked": true}, {"id": "1131", "price": 60, "title": "24cm", "value": "24cm", "checked": true}], "required": true, "identifier": "product_custom_option_moon_lamp_measuring", "isDisabled": true, "placeholder": "-- Please select --", "title_label": "Size", "title_value": "size", "widget_name": "下拉选项"}], "required": false, "isDisabled": false, "widget_name": "步骤分组"}]',
      },
    ],
  });
};
