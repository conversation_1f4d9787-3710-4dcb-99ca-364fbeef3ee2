import { LibraryType } from "@/pages/pod/common";
import PropTypes from "prop-types";
import AddImageItemsForm from "./add-image-items-form";
import AddColorItemForm from "./add-color-item-form";
import AddFontItemForm from "./add-font-item-form";

function AddLibraryItemsForm(props) {
  const { libraryType } = props;

  if (libraryType === LibraryType.Image) {
    return <AddImageItemsForm {...props}></AddImageItemsForm>;
  } else if (libraryType === LibraryType.Color) {
    return <AddColorItemForm {...props}></AddColorItemForm>;
  } else if (libraryType === LibraryType.Font) {
    return <AddFontItemForm {...props}></AddFontItemForm>;
  }
}

AddLibraryItemsForm.propTypes = {
  libraryId: PropTypes.any,
  libraryType: PropTypes.string,
  categoryId: PropTypes.any,
  onSubmit: PropTypes.func,
  onError: PropTypes.func,
  onSuccess: PropTypes.func,
  onFinally: PropTypes.func,
};

export default AddLibraryItemsForm;
