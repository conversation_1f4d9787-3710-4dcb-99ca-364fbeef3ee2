import styles from "./index.module.scss";
import { Modal, Button, Upload, Input, Checkbox, Radio } from "antd";
import { UploadOutlined } from "@ant-design/icons";
import { useState, useRef, useEffect, useCallback } from "react";
import Cropper from "cropperjs";
import "cropperjs/dist/cropper.css";

import Cookies from "js-cookie";
import Enums from "enums";
import Utils from "utils";

import Loading from "components/common/Loading";
import DraggableResizable from "@/components/common/DraggableResizable";

const RadioGroup = Radio.Group;

const CONFIG = {
  BACKGROUND_IMAGES: {
    barcode: "https://images.drawelry.com/assets/product/custom-options/barcode.png",
    barcode_player: "https://images.drawelry.com/assets/product/custom-options/spotify-player.png",
    cover: "https://images.drawelry.com/assets/product/custom-options/spotify-default-cover.jpg",
    player: "https://images.drawelry.com/assets/product/custom-options/player1.png",
  },
  DIMENSIONS: {
    barcode: { width: 160, height: 40 },
    barcode_player: { width: 200, height: 150 },
    container: { width: 560, height: 560 },
    cover: { width: 100, height: 100 },
    player: { width: 234, height: 117 },
  },
};

const initialSpotifyConfig = {
  barcodeConfig: {
    player: false,
    hasCover: false,
    hasPlayer: false,
  },
  coverData: {
    default_photo: CONFIG.BACKGROUND_IMAGES.cover,
    width: CONFIG.DIMENSIONS.cover.width,
    top: 0,
    left: 0,
    shadow: false,
  },
  playerData: {
    width: null,
    height: null,
    top: null,
    left: null,
    rotate: 0,
  },
};

function SpotifyImageUpload(props) {
  const { value, onChange, formRef } = props;
  const [spotifyConfig, setSpotifyConfig] = useState(initialSpotifyConfig);
  const [modalState, setModalState] = useState({
    visible: false,
    loading: false,
    isPreview: false,
    isMounted: false,
  });
  const [imageData, setImageData] = useState(null);
  const [coverFileList, setCoverFileList] = useState([]);

  const imageRef = useRef(null);
  const cropperRef = useRef(null);
  const token = Cookies.get()[Enums.CookieName.Token];
  const fileList = getFileList(value);
  const spotifyImageHost = localStorage.getItem("spotifyImageHost");

  function updateSpotifyConfig(updates) {
    setSpotifyConfig((prev) => ({
      ...prev,
      ...updates,
    }));
  }

  function updateModalState(updates) {
    setModalState((prev) => ({
      ...prev,
      ...updates,
    }));
  }

  function getFileList(value) {
    if (!value) {
      return [];
    }

    if (Object.prototype.toString.call(value) === "[object Object]") {
      return [
        {
          uid: "-1",
          url: value.src,
          status: "done",
        },
      ];
    }

    return value;
  }

  const handleChange = ({ file, fileList }) => {
    handleReset();
    if (!fileList?.length) {
      onChange?.({});
      return;
    }

    if (file.status !== Enums.UploadFileStatus.Done) {
      onChange?.(fileList);
      return;
    }

    const fileData = file?.response?.data;
    const { image } = fileData;

    localStorage.setItem("spotifyImageHost", fileData?.host);
    setImageData(image);
    updateModalState({ visible: true });
    onChange?.({
      src: image?.src,
      width: value?.width || 560,
      height: value?.height || 560,
    }); // 原组件没有改变width和height width: image?.width, height: image?.height
  };

  function handleReset() {
    // 重置所有状态
    setImageData(null);
    updateModalState({ isPreview: false });
    updateSpotifyConfig({
      barcodeConfig: { player: false, hasCover: false, hasPlayer: false },
      coverData: {
        default_photo: CONFIG.BACKGROUND_IMAGES.cover,
        width: CONFIG.DIMENSIONS.cover.width,
        top: 0,
        left: 0,
        shadow: false,
      },
    });
    updateSpotifyConfig({
      playerData: {
        width: null,
        height: null,
        top: null,
        left: null,
        rotate: 0,
      },
    });
  }

  function handleOk() {
    if (!cropperRef.current) return;
    const cropData = cropperRef.current.getCropBoxData(); // 裁剪区域数据
    const formValues = {
      barcode: {
        width: cropData.width.toFixed(),
        height: cropData.height.toFixed(),
        top: cropData.top.toFixed(),
        left: cropData.left.toFixed(),
        player: spotifyConfig.barcodeConfig.player,
        rotate: 0,
      },
      cover: spotifyConfig.coverData,
      player: spotifyConfig.playerData,
      style: getCropperStyle(),
    };

    formRef.current.setFieldsValue(formValues);

    updateModalState({ visible: false });
  }

  function handleCancel() {
    updateModalState({ isPreview: false, visible: false });
  }

  const handleModalAfterOpen = () => {
    updateModalState({ isMounted: true });
  };

  // Modal关闭后的回调
  const handleModalAfterClose = () => {
    updateModalState({ isMounted: false });
  };

  function handlePreview() {
    const formValues = formRef.current?.getFieldsValue();
    if (formValues) {
      const { barcode, cover, player, style } = formValues;
      updateSpotifyConfig({
        barcodeConfig: {
          player: barcode?.player || false,
          hasCover: style === "barcode_cover",
          hasPlayer: player?.top !== null,
        },
      });

      // 设置封面配置
      if (cover) {
        updateSpotifyConfig({
          coverData: {
            default_photo: cover.default_photo || CONFIG.BACKGROUND_IMAGES.cover,
            width: Number(cover.width) || CONFIG.DIMENSIONS.cover.width,
            top: Number(cover.top) || 0,
            left: Number(cover.left) || 0,
            shadow: cover.shadow || false,
          },
        });
      }

      // 设置播放器配置
      if (player) {
        updateSpotifyConfig({
          playerData: {
            width: Number(player.width) || CONFIG.DIMENSIONS.player.width,
            height: Number(player.height) || CONFIG.DIMENSIONS.player.height,
            top: Number(player.top) || 0,
            left: Number(player.left) || 0,
            rotate: Number(player.rotate) || 0,
          },
        });
      }
    }

    if (fileList?.[0]?.url) {
      setImageData({
        src: fileList[0].url.replace(spotifyImageHost, ""),
      });
    }

    updateModalState({ isPreview: true, visible: true });
  }

  function getCropperStyle() {
    if (spotifyConfig.barcodeConfig?.hasCover) {
      return "barcode_cover";
    }

    return "barcode";
  }

  function calculateImageDimensions(imageData, containerData) {
    let newWidth = imageData.naturalWidth;
    let newHeight = imageData.naturalHeight;

    if (newWidth > CONFIG.DIMENSIONS.container.width || newHeight > CONFIG.DIMENSIONS.container.height) {
      const ratio = Math.min(
        CONFIG.DIMENSIONS.container.width / newWidth,
        CONFIG.DIMENSIONS.container.height / newHeight
      );
      newWidth = Math.floor(newWidth * ratio);
      newHeight = Math.floor(newHeight * ratio);
    }

    const left = Math.max(0, (containerData.width - newWidth) / 2);
    const top = Math.max(0, (containerData.height - newHeight) / 2);

    return { newWidth, newHeight, left, top };
  }

  const initCropper = useCallback(() => {
    if (cropperRef.current) {
      cropperRef.current.destroy();
    }

    const { width, height } = spotifyConfig.barcodeConfig.player
      ? CONFIG.DIMENSIONS.barcode_player
      : CONFIG.DIMENSIONS.barcode;

    if (imageData?.src) {
      updateModalState({ loading: true });
      cropperRef.current = new Cropper(imageRef.current, {
        viewMode: 0,
        zoomOnWheel: false,
        center: true,
        autoCrop: true,
        highlight: false,
        aspectRatio: width / height,
        movable: true,
        scalable: false,
        rotatable: false,
        zoomable: false, // 缩放
        minContainerWidth: 560,
        minContainerHeight: 560,
        maxContainerWidth: 560,
        maxContainerHeight: 560,
        responsive: false, // 在窗口尺寸调整后,禁止响应式的重渲染
        restore: false, // 在窗口尺寸调整后,禁止恢复被裁剪的区域
        cropBoxData: {
          width,
          height,
        },
        ready: () => {
          updateModalState({ loading: false });
          const cropper = cropperRef.current;
          const imageData = cropper.getImageData();
          const containerData = cropper.getContainerData();

          const { newWidth, newHeight, left, top } = calculateImageDimensions(imageData, containerData);

          // 更改图像包装器的位置和大小
          cropper.setCanvasData({
            width: newWidth,
            height: newHeight,
            left,
            top,
          });

          // 如果是预览模式，使用表单中保存的裁剪区域数据
          if (modalState.isPreview) {
            const formValues = formRef.current?.getFieldsValue();
            if (formValues?.barcode) {
              cropper.setCropBoxData({
                width: Number(formValues.barcode.width),
                height: Number(formValues.barcode.height),
                left: Number(formValues.barcode.left),
                top: Number(formValues.barcode.top),
              });
            }
          } else {
            // 非预览模式，使用默认居中位置
            const cropBoxLeft = Math.max(0, (containerData.width - width) / 2);
            const cropBoxTop = Math.max(0, (containerData.height - height) / 2);
            cropper.setCropBoxData({
              width,
              height,
              left: cropBoxLeft,
              top: cropBoxTop,
            });
          }

          const cropperMove = document.getElementsByClassName("cropper-face")[0];
          cropperMove.style.backgroundImage = `url(${
            CONFIG.BACKGROUND_IMAGES[spotifyConfig.barcodeConfig?.player ? "barcode_player" : "barcode"]
          })`;
        },
      });
    }
  }, [imageData, spotifyConfig.barcodeConfig?.player, modalState.isPreview, formRef]);

  const handleCoverDrag = useCallback(
    (e, data) => {
      updateSpotifyConfig({
        coverData: {
          ...spotifyConfig.coverData,
          top: data.y,
          left: data.x,
        },
      });
    },
    [spotifyConfig.coverData]
  );

  function handleCoverResize(e, direction, ref, delta, size) {
    updateSpotifyConfig({
      coverData: {
        ...spotifyConfig.coverData,
        width: parseInt(ref.style.width),
        height: parseInt(ref.style.height),
      },
    });
  }

  function handlePlayerDrag(e, data) {
    updateSpotifyConfig({
      playerData: {
        ...spotifyConfig.playerData,
        top: data.y,
        left: data.x,
      },
    });
  }

  function handlePlayerResize(e, direction, ref, delta, size) {
    updateSpotifyConfig({
      playerData: {
        ...spotifyConfig.playerData,
        width: parseInt(ref.style.width),
        height: parseInt(ref.style.height),
      },
    });
  }

  function handlePlayerChange(e) {
    const checked = e.target.checked;
    if (checked) {
      updateSpotifyConfig({
        barcodeConfig: {
          ...spotifyConfig.barcodeConfig,
          hasPlayer: true,
        },
        playerData: {
          width: CONFIG.DIMENSIONS.player.width,
          height: CONFIG.DIMENSIONS.player.height,
          top: 0,
          left: 0,
          rotate: 0,
        },
      });
    } else {
      updateSpotifyConfig({
        barcodeConfig: {
          ...spotifyConfig.barcodeConfig,
          hasPlayer: false,
        },
        playerData: {
          width: null,
          height: null,
          top: null,
          left: null,
          rotate: 0,
        },
      });
    }
  }

  function handleCoverImageChange({ file, fileList }) {
    setCoverFileList(fileList);

    if (file.status !== Enums.UploadFileStatus.Done) {
      return;
    }

    const fileData = file?.response?.data;
    const { image } = fileData;

    if (image?.src) {
      updateSpotifyConfig({
        coverData: {
          ...spotifyConfig.coverData,
          default_photo: image?.src,
        },
      });
    }
  }

  useEffect(() => {
    // 初始化cropper
    initCropper();

    return () => {
      cropperRef.current?.destroy();
    };
  }, [initCropper]);

  return (
    <div className={styles.container}>
      <Upload
        className={styles.upload}
        {...props}
        headers={{
          authorization: `Bearer ${token}`,
        }}
        fileList={fileList}
        onChange={handleChange}
        accept="image/*"
      >
        <Button type="primary" icon={<UploadOutlined />} block>
          Upload
        </Button>
      </Upload>
      <Input value={value?.src ?? ""} disabled />
      <Button type="primary" onClick={handlePreview} block>
        预览
      </Button>

      <Modal
        title="选择歌曲码位置"
        className={styles.modal}
        width={608}
        open={modalState.visible}
        onOk={handleOk}
        onCancel={handleCancel}
        maskClosable={false}
        footer={!modalState.isPreview ? undefined : null}
        afterOpenChange={(visible) => {
          if (visible) {
            handleModalAfterOpen();
          } else {
            handleModalAfterClose();
          }
        }}
      >
        <div className={styles.modalContent}>
          <div id="imageContainer" className={styles.imageContainer}>
            <Loading loading={modalState.loading}>
              <div className={styles.imageWrapper}>
                <img ref={imageRef} src={`${spotifyImageHost}${imageData?.src}`} alt="" />
              </div>
            </Loading>
            {modalState.isMounted && spotifyConfig.barcodeConfig?.hasCover && (
              <DraggableResizable
                left={spotifyConfig.coverData?.left || 0}
                top={spotifyConfig.coverData?.top || 0}
                width={spotifyConfig.coverData?.width || CONFIG.DIMENSIONS.cover.width}
                height={spotifyConfig.coverData?.width || CONFIG.DIMENSIONS.cover.width}
                lockAspectRatio={CONFIG.DIMENSIONS.cover.width / CONFIG.DIMENSIONS.cover.height}
                isResizable={true}
                isDraggable={true}
                style={{ zIndex: 200, position: "absolute" }}
                onDrag={handleCoverDrag}
                onResize={handleCoverResize}
              >
                <img
                  src={
                    Utils.isFullUrl(spotifyConfig.coverData?.default_photo)
                      ? spotifyConfig.coverData?.default_photo
                      : `${spotifyImageHost}${spotifyConfig.coverData?.default_photo}`
                  }
                  width="100%"
                  height="100%"
                  draggable={false}
                  alt=""
                />
              </DraggableResizable>
            )}

            {modalState.isMounted && spotifyConfig.barcodeConfig?.hasPlayer && (
              <DraggableResizable
                left={spotifyConfig.playerData?.left || 0}
                top={spotifyConfig.playerData?.top || 0}
                width={spotifyConfig.playerData?.width || CONFIG.DIMENSIONS.player.width}
                height={spotifyConfig.playerData?.height || CONFIG.DIMENSIONS.player.height}
                lockAspectRatio={CONFIG.DIMENSIONS.player.width / CONFIG.DIMENSIONS.player.height}
                isResizable={true}
                isDraggable={true}
                style={{ zIndex: 300, position: "absolute" }}
                onDrag={handlePlayerDrag}
                onResize={handlePlayerResize}
              >
                <img src={CONFIG.BACKGROUND_IMAGES.player} width="100%" height="100%" draggable={false} alt="" />
              </DraggableResizable>
            )}

            {modalState.isPreview && <div className={styles.mask}></div>}
          </div>
          {!modalState.isPreview && (
            <div className={styles.spotifyConfig}>
              <Checkbox
                checked={spotifyConfig.barcodeConfig?.hasCover}
                onChange={() =>
                  updateSpotifyConfig({
                    barcodeConfig: { ...spotifyConfig.barcodeConfig, hasCover: !spotifyConfig.barcodeConfig.hasCover },
                  })
                }
              >
                是否有封面
              </Checkbox>
              {spotifyConfig.barcodeConfig?.hasCover && (
                <Upload
                  className={styles.upload}
                  {...props}
                  headers={{
                    authorization: `Bearer ${token}`,
                  }}
                  fileList={coverFileList}
                  onChange={handleCoverImageChange}
                  accept="image/*"
                  maxCount={1}
                >
                  <Button type="primary" size="small" icon={<UploadOutlined />} block>
                    上传专辑封面
                  </Button>
                </Upload>
              )}

              <RadioGroup value={spotifyConfig.barcodeConfig?.player ? "barcode_player" : "barcode"}>
                <Radio
                  value="barcode"
                  onChange={() =>
                    updateSpotifyConfig({ barcodeConfig: { ...spotifyConfig.barcodeConfig, player: false } })
                  }
                >
                  条码
                </Radio>
                <Radio
                  value="barcode_player"
                  onChange={() =>
                    updateSpotifyConfig({ barcodeConfig: { ...spotifyConfig.barcodeConfig, player: true } })
                  }
                >
                  条码播放器
                </Radio>
              </RadioGroup>
              <Checkbox checked={spotifyConfig.barcodeConfig?.hasPlayer} onChange={handlePlayerChange}>
                播放器
              </Checkbox>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
}

export default SpotifyImageUpload;
