import Fetchers from "@/fetchers";
import Utils from "@/utils";
import styles from "./index.module.scss";
import { useEffect, useRef, useState } from "react";
import { fabric } from "fabric";
import {} from "../common/init-fabric-types";
import { App, <PERSON><PERSON>, ColorPicker, Input } from "antd";
import { ControlWrapper, Form, FormControl } from "@/components/react-form-x";
import FormControlLabel from "@/pages/pod/components/form-control-label";
import {
  createOptionSets,
  getFontsFromFabricObjects,
  loadFabricObjectFonts,
  convertToPreviewCanvasJson,
  checkImageSize,
} from "@/pages/pod/common";
import Helper from "@/helpers";
import { useNavigate } from "react-router-dom";

const defaultValue = { width: 1000, height: 1000, zoom: 1 };

function EditTemplateBg() {
  const [submitLoading, setSubmitLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const canvasBoxRef = useRef();
  const canvasRef = useRef();
  const { message, modal } = App.useApp();
  const formRef = useRef();
  const navigate = useNavigate();

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current, modal, navigate };

  useEffect(() => {
    const { id } = Utils.getQueryParams(window.location.href);
    let handleWindowResize = () => {};
    Fetchers.getPodTemplate({ id })
      .then((res) => res.data.data.item)
      .then(async (template) => {
        if (template.canvas?.objects?.length === 0) {
          const { modal, navigate } = paramsRef.current;
          modal.error({
            content: "模板未保存，请返回上一步保存模板",
            onOk() {
              navigate(`/pod/edit-template?id=${id}`);
            },
          });
          return;
        }
        const initZoom = template?.preview_canvas?.zoom || defaultValue.zoom;
        const initSize = {
          width: template?.preview_canvas?.width / initZoom || defaultValue.width,
          height: template?.preview_canvas?.height / initZoom || defaultValue.height,
        };
        const fitSize = Utils.getAspectRatioScaleMinSize({
          width: initSize.width,
          height: initSize.height,
          maxWidth: canvasBoxRef.current?.clientWidth,
          maxHeight: canvasBoxRef.current?.clientHeight,
        });
        const canvas = new fabric.Canvas(canvasRef.current, {
          width: fitSize.width,
          height: fitSize.height,
          preserveObjectStacking: true,
        });
        canvas.setZoom(fitSize.width / initSize.width);
        paramsRef.current.canvas = canvas;
        paramsRef.current.template = template;

        formRef.current?.updateValue({
          backgroundImage: template?.preview_canvas?.backgroundImage?.src ?? "",
          color: template?.preview_canvas?.background ?? "",
        });

        console.log(canvas);

        handleWindowResize = async () => {
          await Utils.sleep(300);
          const { width, height } = Utils.getAspectRatioScaleMinSize({
            width: canvas.getElement().width,
            height: canvas.getElement().height,
            maxWidth: canvasBoxRef.current?.clientWidth,
            maxHeight: canvasBoxRef.current?.clientHeight,
          });
          canvas.setZoom(width / initSize.width);
          canvas.setDimensions({ width, height });
        };
        window.addEventListener("resize", handleWindowResize);
        handleWindowResize();

        const fonts = getFontsFromFabricObjects({ objects: template.canvas.objects });
        await loadFabricObjectFonts({ canvas, fonts });

        await new Promise((resolve) => {
          if (template.preview_canvas?.objects?.length > 0) {
            handleWindowResize();
            canvas.loadFromJSON(template.preview_canvas, async () => {
              if (canvas.backgroundImage) {
                canvas.backgroundImage.scaleToWidth(defaultValue.width);
              }
              const oldGroup = canvas.getObjects().find((x) => x.type === "group");
              const group = await new Promise((resolve) => {
                fabric.util.enlivenObjects(
                  template.canvas.objects.map((item) => ({ ...item, stroke: false })),
                  (objects) => {
                    const newGroup = new fabric.Group(objects);
                    newGroup.set({ top: oldGroup.top, left: oldGroup.left });
                    newGroup.scaleToWidth(oldGroup.width * oldGroup.scaleX);
                    resolve(newGroup);
                  }
                );
              });
              group.setControlsVisibility({ mt: false, mb: false, ml: false, mr: false });
              group.set({ stroke: false });
              canvas.remove(oldGroup);
              canvas.add(group);
              canvas.renderAll();
              resolve();
            });
          } else {
            canvas.loadFromJSON(template.canvas, async () => {
              const objects = await Promise.all(
                canvas.getObjects().map(
                  (object) =>
                    new Promise((resolve) => {
                      object.set({ stroke: false });
                      object.clone((cloned) => {
                        resolve(cloned);
                      });
                    })
                )
              );
              const group = new fabric.Group(objects);
              const size = Utils.getAspectRatioScaleMinSize({
                width: group.width,
                height: group.height,
                maxWidth: canvas.width,
                maxHeight: canvas.height,
              });
              group.scaleToWidth(size.width - canvas.width * 0.1);
              group.setControlsVisibility({ mt: false, mb: false, ml: false, mr: false });
              group.set({ stroke: false });
              canvas.clear();
              canvas.add(group);
              canvas.centerObject(group);
              canvas.renderAll();
              resolve();
            });
          }
        });
      });

    return function () {
      window.removeEventListener("resize", handleWindowResize);
    };
  }, []);

  return (
    <div className={styles.page}>
      <div className={styles.left}>
        <div ref={canvasBoxRef} className={styles.canvasBox}>
          <div className={styles.canvasWrapper}>
            <canvas ref={canvasRef} width={0} height={0}></canvas>
          </div>
        </div>
      </div>
      <div className={styles.right}>
        <Form
          ref={formRef}
          style={{ display: "flex", flexDirection: "column", gap: 20 }}
          onSubmit={async (event, values) => {
            try {
              if (values.backgroundImage) {
                await checkImageSize(values.backgroundImage);
              }

              setSubmitLoading(true);
              const { canvas } = paramsRef.current;
              await Promise.all([
                new Promise((resolve) => {
                  canvas.setBackgroundImage(
                    values.backgroundImage,
                    () => {
                      canvas.backgroundImage?.scaleToWidth?.(canvas.getWidth());
                      canvas.renderAll();
                      resolve();
                    },
                    { crossOrigin: "anonymous" }
                  );
                }),
                new Promise((resolve) => {
                  canvas.setBackgroundColor(values.color, () => {
                    canvas.renderAll();
                    resolve();
                  });
                }),
              ]);
            } catch (error) {
              message.error(error.message);
            } finally {
              setSubmitLoading(false);
            }
          }}
        >
          <div>
            <FormControl
              name="backgroundImage"
              render={(props) => (
                <ControlWrapper
                  {...props}
                  render={(props) => {
                    return (
                      <FormControlLabel label="背景图：">
                        <Input {...props}></Input>
                      </FormControlLabel>
                    );
                  }}
                ></ControlWrapper>
              )}
            ></FormControl>
          </div>
          <div>
            <FormControl
              name="color"
              render={(props) => (
                <ControlWrapper
                  {...props}
                  render={(props) => {
                    const { name, value, onChange } = props;
                    return (
                      <FormControlLabel label="背景色：">
                        <ColorPicker
                          value={value}
                          onChange={(value) => {
                            onChange(null, { [name]: value.toRgbString() });
                          }}
                          showText={(color) => color.toRgbString()}
                          style={{ width: `100%`, justifyContent: "start" }}
                        ></ColorPicker>
                      </FormControlLabel>
                    );
                  }}
                ></ControlWrapper>
              )}
            ></FormControl>
          </div>
          <div style={{ display: "flex", gap: 20 }}>
            <Button
              type="primary"
              block
              onClick={() => {
                Helper.pageLoading(true);
                const { template, canvas } = paramsRef.current;
                const preview_canvas = convertToPreviewCanvasJson(canvas.toJSON());
                delete preview_canvas.width;
                delete preview_canvas.height;
                delete preview_canvas.zoom;
                Fetchers.updatePodTemplate({ id: +template.id, data: { preview_canvas } })
                  .then(() => {
                    window.location.reload();
                  })
                  .catch(() => {
                    Helper.openMessage({ type: "error", message: "复位失败！" });
                  })
                  .finally(() => {
                    Helper.pageLoading(false);
                  });
              }}
            >
              复位
            </Button>
            <Button type="primary" htmlType="submit" block loading={submitLoading}>
              设置
            </Button>
            <Button
              type="primary"
              danger
              block
              onClick={() => {
                const { canvas } = paramsRef.current;
                canvas.setBackgroundColor(null);
                canvas.setBackgroundImage(null, () => {
                  canvas.renderAll();
                });
              }}
            >
              删除
            </Button>
          </div>
        </Form>
        <div style={{ marginTop: 20 }}>
          <Button
            type="primary"
            className="ant-btn-success"
            block
            loading={saveLoading}
            onClick={async () => {
              setSaveLoading(true);
              const { template, canvas } = paramsRef.current;
              const { id } = Utils.getQueryParams(window.location.href);
              const templateId = +id;
              await Promise.all([
                (async () => {
                  const [fetchedItem, controls] = await Promise.all([
                    Fetchers.getPodTemplateOptionSet({
                      template_id: templateId,
                    }).then((res) => res.data?.data?.item),
                    createOptionSets({ objects: template.canvas.objects }),
                  ]);
                  if (fetchedItem?.id) {
                    await Fetchers.updatePodOptionSet({
                      id: fetchedItem.id,
                      data: {
                        controls: controls,
                        title: template.name,
                        title_i18n_key: template.name_i18n_key,
                        title_i18n_params: template.name_i18n_params,
                      },
                    });
                  } else {
                    await Fetchers.addPodOptionSet({
                      template_id: templateId,
                      product_id: null,
                      controls: controls,
                      title: template.name,
                      title_i18n_key: template.name_i18n_key,
                      title_i18n_params: template.name_i18n_params,
                      create_from_template: true,
                    });
                  }
                })(),
                Fetchers.updatePodTemplate({
                  id: template.id,
                  data: { preview_canvas: convertToPreviewCanvasJson(canvas.toJSON()) },
                }).finally(() => {
                  message.success(`保存成功！`);
                  setSaveLoading(false);
                }),
              ]);
            }}
          >
            保存
          </Button>
        </div>
      </div>
    </div>
  );
}

export default EditTemplateBg;
