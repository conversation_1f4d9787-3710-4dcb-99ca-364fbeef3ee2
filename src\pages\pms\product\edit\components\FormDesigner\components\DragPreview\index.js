import { widgets } from "../../common/widgets";
import ComponentCard from "../Sidebar/component/ComponentCard";
import GroupContainer from "../FormCanvas/components/GroupContainer";
import FormFieldItem from "../FormCanvas/components/FormFieldItem";
// import SortableItem from "../FormCanvas/components/SortableItem";

function DragPreview(props) {
  const { activeId, dragData, ...otherProps } = props;
  const component = widgets.find((component) => component.type === activeId);
  const isDraggingNewWidget = dragData?.fromSidebar;
  const isGroup = dragData?.isGroup;

  if (isDraggingNewWidget) {
    return <ComponentCard component={component} {...otherProps} />;
  }

  if (isGroup) {
    return <GroupContainer widget={dragData?.component} {...otherProps} />;
  }

  return <FormFieldItem widget={dragData?.component} {...otherProps} />;
}

export default DragPreview;
