const Api = require("../../src/fetchers/api");

module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      items: [
        {
          key: "basic_info",
          label: "基础信息",
          content: {
            type: "json",
            component: "JSONComponents",
            children: [
              {
                component: "PanelEditor",
                children: [
                  {
                    component: "Row",
                    props: { gutter: [16, 6] },
                    children: [
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "code",
                            label: "编号",
                            component: "Text",
                            props: {},
                            content: "JE20230719210",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "order_no",
                            label: "订单号",
                            component: "Text",
                            props: {},
                            content: "JEJP030964",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "order_status",
                            label: "订单状态",
                            component: "Text",
                            props: {},
                            content:
                              "<span style='color:red;padding: 5px 10px;border:1px solid red;border-radius: 10px;'>待发货</span>",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "create_time",
                            label: "创建时间",
                            component: "Text",
                            props: {},
                            content: "2023-07-19 22:04:41",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "pay_time",
                            label: "支付时间",
                            component: "Text",
                            props: {},
                            content: "2023-07-19 22:04:41",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "pay_status",
                            label: "支付状态",
                            component: "Text",
                            props: {},
                            content: "已支付",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "pay_type",
                            label: "付款方式",
                            component: "Text",
                            props: {},
                            content: "adyen_scheme",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "total_amount",
                            label: "总金额(退款后剩余)/ 最初支付",
                            component: "Text",
                            props: {},
                            content: "11781(JPY) / 11781(JPY)",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "warehouse",
                            label: "仓库",
                            component: "Text",
                            props: {},
                            content: "广州仓",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "logistics",
                            label: "物流方式",
                            component: "Text",
                            props: {},
                            content: "LTW （standard）",
                            editable: {
                              component: "Select",
                              props: {
                                // mode: "multiple", // 多选
                              },
                              // 支持远程搜索
                              searchApi: Api.searchProductSpu,
                              request: {
                                url: Api.order,
                                data: { id: 1 },
                              },
                            },
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "logistics_number",
                            label: "物流单号",
                            component: "Text",
                            props: {},
                            content: "518797228110",
                            editable: { component: "Input", props: {}, request: { url: Api.order, data: { id: 1 } } },
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "order_site",
                            label: "订单站点",
                            component: "Text",
                            props: {},
                            content: "JE-日本站",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "freight_insurance",
                            label: "运费险",
                            component: "Text",
                            props: {},
                            content: "0(JPY)",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "customer_level",
                            label: "客户等级(当前等级/下单时等级) ",
                            component: "Text",
                            props: {},
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "remote_payment",
                            label: "偏远费/偏远费优惠",
                            component: "Text",
                            props: {},
                            content: "0(JPY) /0(JPY)",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "tax_type",
                            label: "消费税类型",
                            component: "Text",
                            props: {},
                            content: "0(JPY)",
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        },
        {
          key: "payment_info",
          label: "支付信息",
          actions: [
            {
              label: "edit",
              command: {
                type: "message",
                config: {
                  type: "success",
                  content: "这里是编辑command",
                  duration: 3,
                },
              },
            },
          ],
          content: {
            type: "json",
            component: "JSONComponents",
            children: [
              {
                component: "PanelEditor",
                children: [
                  {
                    component: "Row",
                    props: { gutter: [16, 6] },
                    children: [
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "code",
                            label: "编号",
                            component: "Text",
                            props: {},
                            content: "JE20230719210",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "order_no",
                            label: "订单号",
                            component: "Text",
                            props: {},
                            content: "JEJP030964",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "order_status",
                            label: "订单状态",
                            component: "Text",
                            props: {},
                            content:
                              "<span style='color:red;padding: 5px 10px;border:1px solid red;border-radius: 10px;'>待发货</span>",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "create_time",
                            label: "创建时间",
                            component: "Text",
                            props: {},
                            content: "2023-07-19 22:04:41",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "pay_time",
                            label: "支付时间",
                            component: "Text",
                            props: {},
                            content: "2023-07-19 22:04:41",
                          },
                        ],
                      },
                    ],
                  },
                  {
                    component: "Row",
                    props: { gutter: [16, 56] },
                    children: [
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "pay_status",
                            label: "支付状态",
                            component: "Text",
                            props: {},
                            content: "已支付",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "pay_type",
                            label: "付款方式",
                            component: "Text",
                            props: {},
                            content: "adyen_scheme",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "total_amount",
                            label: "总金额(退款后剩余)/ 最初支付",
                            component: "Text",
                            props: {},
                            content: "11781(JPY) / 11781(JPY)",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "warehouse",
                            label: "仓库",
                            component: "Text",
                            props: {},
                            content: "广州仓",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "logistics",
                            label: "物流方式",
                            component: "Text",
                            props: {},
                            content: "LTW （standard）",
                            editable: {
                              component: "Select",
                              props: {
                                // mode: "multiple", // 多选
                              },
                              // 支持远程搜索
                              searchApi: Api.searchProductSpu,
                              request: {
                                url: Api.order,
                                data: { id: 1 },
                              },
                            },
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "logistics_number",
                            label: "物流单号",
                            component: "Text",
                            props: {},
                            content: "518797228110",
                            editable: { component: "Input", props: {}, request: { url: Api.order, data: { id: 1 } } },
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "order_site",
                            label: "订单站点",
                            component: "Text",
                            props: {},
                            content: "JE-日本站",
                          },
                        ],
                      },
                      {
                        component: "Col",
                        props: { span: 8 },
                        children: [
                          {
                            key: "freight_insurance",
                            label: "运费险",
                            component: "Text",
                            props: {},
                            content: "0(JPY)",
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        },
        {
          key: "user_info",
          label: "用户信息",
          content: {
            type: "json",
            component: "JSONComponents",
            children: [
              {
                component: "NativeTable",
                children: [
                  [
                    {
                      tag: "th",
                      value: "source",
                      props: {
                        style: {
                          width: "100px",
                        },
                      },
                    },
                    {
                      tag: "th",
                      value: "medium",
                      props: {
                        style: {
                          width: "100px",
                        },
                      },
                    },
                    {
                      tag: "th",
                      value: "campaign",
                      props: {
                        style: {
                          width: "100px",
                        },
                      },
                    },
                    {
                      tag: "th",
                      value: "term",
                    },
                    {
                      tag: "th",
                      value: "content",
                    },
                    {
                      tag: "th",
                      value: "time",
                    },
                  ],
                  [
                    {
                      tag: "td",
                      valueType: "text",
                      value: "Google",
                    },
                    {
                      tag: "td",
                      valueType: "text",
                      value: "CPC",
                    },
                    {
                      tag: "td",
                      valueType: "text",
                      value: "无",
                    },
                    {
                      tag: "td",
                      valueType: "text",
                      value: "无",
                    },
                    {
                      tag: "td",
                      valueType: "text",
                      value: "无",
                    },
                    {
                      tag: "td",
                      valueType: "text",
                      value: "2024-06-30 18:23:06",
                    },
                  ],
                ],
                props: {
                  header: {
                    content:
                      "客户等级(当前等级/下单时等级):  <span style='font-weight: bold;color:#ff0500;'>member</span>  订单设备:  <span style='font-weight: bold; color:#ff0500;'>PC</span>",
                  },
                },
              },
            ],
          },
        },
      ],
      actions: [
        {
          key: "send_template_mail",
          label: "发送模板邮件",
          children: [
            {
              command: {
                type: "request",
                request: {
                  url: "/rest/v1/order/print-label?order_id=842",
                  method: "get",
                  data: [],
                },
              },
              label: "打印面单",
            },
            {
              command: {
                type: "request",
                confirm: "你确认要进行此操作吗？",
                request: {
                  url: "/rest/v1/order/cancel-label?order_id=842",
                  method: "get",
                  data: [],
                },
              },
              label: "取消面单",
            },
          ],
        },
        {
          key: "export_invoice",
          label: "导出发票",
          children: [
            {
              command: {
                type: "request",
                confirm: "你确认要进行此操作吗？",
                request: {
                  url: "/rest/v1/order/list/create-shipping-label?reload_iframe=true&order_id=842",
                  method: "get",
                  data: [],
                },
              },
              label: "生成面单",
            },
          ],
        },
      ],
      tags: [
        {
          text: "异常",
          style: {
            color: "#333333",
            borderColor: "#ffccc7",
            backgroundColor: " #fff2f0",
          },
        },
      ],
    },
  });
};
