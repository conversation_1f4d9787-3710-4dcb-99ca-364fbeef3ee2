const EnvHelper = Object.freeze({
  get isDevelopment() {
    return EnvHelper.RuntimeEnv === "development";
  },

  get isTest() {
    return EnvHelper.RuntimeEnv === "test";
  },

  get isProduction() {
    return EnvHelper.RuntimeEnv === "production";
  },

  get RuntimeEnv() {
    return process.env.REACT_APP_RUNTIME_ENV || "development";
  },

  get ApiEnv() {
    return process.env.REACT_APP_API_ENV || "production";
  },

  get version() {
    return process.env.REACT_APP_VERSION || "development";
  },

  get isServer() {
    return typeof window === "undefined";
  },

  get RedirectOrigin() {
    return EnvHelper.isProduction ? "https://auth-je.myzlerp.com" : "https://test-auth-je.myzlerp.com";
  },

  get ApiHost() {
    return process.env.REACT_APP_API_HOST || "localhost";
  },

  get appVersion() {
    return process.env.REACT_APP_VERSION;
  },
});

module.exports = EnvHelper;
