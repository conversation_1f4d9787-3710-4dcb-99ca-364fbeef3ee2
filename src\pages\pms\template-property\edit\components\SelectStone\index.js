import { useState, useRef, useCallback, useEffect, useMemo } from "react";

import request from "@/fetchers/request";
import styles from "./index.module.scss";
import Enums from "@/enums";
import useFilters from "./hooks/useFilters";
import calculator from "./utils/calculate";
import PropTypes from "prop-types";

import { SearchOutlined, EyeOutlined } from "@ant-design/icons";
import { Button, Table, Image } from "antd";
import EditTable from "@/components/common/EditTable";
import StoneDrawer from "./components/StoneDrawer";

const DEFAULT_PAGE_SIZE = 20;
const PAGE_SIZE_OPTIONS = [10, 20, 50, 100, 500];
const EDGE_MARGIN = 193;

function SelectStone(props) {
  const { listApiUrl, columns, value, form, identifiers, ...restProps } = props;
  const { onChange } = restProps;
  const [tableScrollHeight, setTableScrollHeight] = useState(0);
  const [loading, setLoading] = useState(false);
  const [drawerState, setDrawerState] = useState({
    open: false,
    selectedKeys: [],
    selectedRows: value || [],
  });
  const [tableState, setTableState] = useState({
    dataSource: [],
    columns: [],
    pagination: {
      current: 1,
      pageSize: DEFAULT_PAGE_SIZE,
      total: 0,
      pageSizeOptions: PAGE_SIZE_OPTIONS,
    },
    sorter: {},
  });
  const [searchParams, setSearchParams] = useState({});

  const paramsRef = useRef({
    getList,
    sorter: {},
    searchParams,
  });

  const { createSearchFilter, createSelectFilter } = useFilters({
    searchParams,
    setSearchParams,
    paramsRef,
  });

  const processFilterData = useCallback(
    ({ column }) => {
      const { filter } = column;
      const { component } = filter;
      if (component === Enums.Components.Select) {
        createSelectFilter({ column });
      } else if (component === Enums.Components.Search) {
        createSearchFilter({ column });
      }
      column.filterIcon = (filtered) => <SearchOutlined style={{ color: filtered ? "#1890ff" : null }} />;
    },
    [createSearchFilter, createSelectFilter]
  );

  const initColumnsValue = useCallback(
    (columns) => {
      columns.forEach((column, index) => {
        const { filter } = column;
        if (filter) {
          processFilterData({ column });
        }
        column.render = (value, row, index, action) => columnRender({ column, row, value, index, action });
        column.onHeaderCell = (column) => {
          return { column, index };
        };
      });

      return columns;
    },
    [processFilterData]
  );

  async function getList(params = {}) {
    if (!listApiUrl) return;

    try {
      setLoading(true);
      const result = await request(listApiUrl, {
        method: "GET",
        params: {
          page: tableState.pagination.current,
          page_size: tableState.pagination.pageSize,
          ...tableState.sorter,
          ...searchParams,
          ...params,
        },
      }).then((res) => res.data);

      setTableState((prev) => ({
        ...prev,
        columns: initColumnsValue(result?.data?.columns),
        dataSource: result?.data?.dataSource,
        pagination: {
          ...prev.pagination,
          total: result?.data?.pagination?.total || 0,
        },
      }));
    } finally {
      setLoading(false);
    }
  }

  function columnRender({ column, row, value }) {
    const extraData = row[`${column?.dataIndex}_extra_data`];
    const { preview_group: previewGroup } = extraData || {};

    function renderValue(column) {
      if (column?.valueType === Enums.TableValueType.Image) {
        return value?.map((item, index) => (
          <Image.PreviewGroup key={index} {...previewGroup?.[index]}>
            <Image {...item} {...column.image} preview={{ mask: <EyeOutlined /> }} />
          </Image.PreviewGroup>
        ));
      }

      return value;
    }

    return <span className="my-table-cell">{renderValue(column)}</span>;
  }

  function handleOpen() {
    setDrawerState((prev) => ({
      ...prev,
      open: true,
    }));
    setTableState((prev) => ({
      ...prev,
      pagination: { ...prev.pagination, current: 1 },
      sorter: {},
    }));
    getList();
  }

  function getSortKey(dataIndex) {
    return `sort[${dataIndex}]`;
  }

  const handleDrawerClose = useCallback(() => {
    setDrawerState((prev) => ({
      ...prev,
      open: false,
      selectedKeys: [],
      selectedRows: [],
    }));
  }, []);

  const handleSelect = useCallback(() => {
    const currentValue = Array.isArray(value) ? value : [];
    const selectedRows = drawerState.selectedRows.map((row) => ({ ...row, amount: 1, type: "side_stone" }));
    const stones = [...currentValue, ...selectedRows];
    onChange?.(stones);
    handleDrawerClose();
    if (form) {
      calculator.calculate({ stones, identifiers, form });
    }
  }, [onChange, drawerState.selectedRows, handleDrawerClose, value, form, identifiers]);

  function handleEditTableChange(stones) {
    onChange?.(stones);
    if (form) {
      calculator.calculate({ stones, identifiers, form });
    }
  }

  const handleTableChange = useCallback(
    (newPagination, filters, sorter) => {
      const { getList } = paramsRef.current;
      const sorters = Array.isArray(sorter) ? sorter : [sorter];
      const newSorter = {};

      sorters.forEach((item) => {
        if (item.field) {
          const key = getSortKey(item.field);
          if (item.order) {
            newSorter[key] = item.order;
          }
        }
      });

      paramsRef.current.sorter = newSorter;

      setTableState((prev) => ({
        ...prev,
        sorter: newSorter,
        pagination: {
          ...prev.pagination,
          current: newPagination.current,
          pageSize: newPagination.pageSize,
        },
      }));

      getList({
        page: newPagination.current,
        page_size: newPagination.pageSize,
        ...filters,
        ...newSorter,
        ...searchParams,
      });
    },
    [searchParams]
  );

  const updateTableScrollHeight = useCallback(() => {
    const viewportHeight = window.innerHeight - EDGE_MARGIN;
    setTableScrollHeight(viewportHeight);
  }, []);

  const StoneTable = useMemo(() => {
    return (
      <Table
        rowKey="id"
        size="small"
        height="100%"
        className={styles.table}
        loading={loading}
        columns={tableState.columns}
        dataSource={tableState.dataSource}
        scroll={{ y: tableScrollHeight }}
        rowSelection={{
          selectedRowKeys: drawerState.selectedKeys,
          onChange: (selectedRowKeys, selectedRows) => {
            setDrawerState((prev) => ({
              ...prev,
              selectedKeys: selectedRowKeys,
              selectedRows: selectedRows,
            }));
          },
        }}
        pagination={{
          showTotal: (total, range) => `第${range[0]}-${range[1]}条/总共${total}条`,
          ...tableState.pagination,
        }}
        onChange={handleTableChange}
      />
    );
  }, [drawerState.selectedKeys, handleTableChange, loading, tableScrollHeight, tableState]);

  useEffect(() => {
    if (!loading) {
      updateTableScrollHeight();
    }
  }, [loading, updateTableScrollHeight]);

  return (
    <div className={styles.container}>
      <div className={styles.actions}>
        <Button size="small" type="primary" onClick={handleOpen}>
          选择石头
        </Button>
      </div>
      <div className={styles.content}>
        <EditTable
          {...restProps}
          columns={columns.filter((col) => col?.show !== false)}
          value={value}
          onChange={handleEditTableChange}
        />
      </div>

      <StoneDrawer open={drawerState.open} onClose={handleDrawerClose} onSelect={handleSelect} title="选择石头">
        <div className={styles.drawerContent}>{StoneTable}</div>
      </StoneDrawer>
    </div>
  );
}

SelectStone.propTypes = {
  listApiUrl: PropTypes.string.isRequired,
  columns: PropTypes.array.isRequired,
  form: PropTypes.any,
  identifiers: PropTypes.object,
};

export default SelectStone;
