import styles from "./index.module.scss";
import { Upload, Button } from "antd";
import { PlusOutlined, UploadOutlined } from "@ant-design/icons";
import Helper from "helpers";
import axios from "fetchers/request";
import Enums from "enums";
import Cookies from "js-cookie";
import PropTypes from "prop-types";
import Utils from "@/utils";
import { useTranslation } from "react-i18next";

function FileUpload(props) {
  const { t } = useTranslation();
  const {
    fileList = [],
    beforeUpload,
    onChange,
    maxCount = 1,
    listType,
    maxSize = "5MB", // 默认最大5MB
    children = t("Upload"),
  } = props;
  const token = Cookies.get()[Enums.CookieName.Token];

  function handleRemove(file) {
    const { fid, url } = file;
    return new Promise((resolve) => {
      Helper.modal.confirm({
        title: t("AreYouSureYouWantToDelete"),
        onOk: async () => {
          try {
            const result = await axios({
              url: props.action,
              method: "DELETE",
              data: { ...props.data, fid, url },
            }).then((res) => res.data);
            if (result.success) {
              resolve(true);
            } else {
              resolve(false);
            }
          } catch (error) {
            resolve(false);
          }
        },
      });
    });
  }

  function customBeforeUpload(file) {
    try {
      const isLtMaxSize = file.size < Utils.humanize.bytes.parse(maxSize);

      if (!isLtMaxSize) {
        Helper.openMessage({ type: "error", content: `文件必须小于 ${maxSize}!` });
        return false;
      }

      if (beforeUpload) {
        return beforeUpload(file);
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  function handleChange({ file, fileList }) {
    if (!file.status) return;

    if (file.status === Enums.UploadFileStatus.Done && file.response.success) {
      const src = file.response?.data?.host + file.response?.data?.file?.src;
      const newFile = {
        fid: file.fid,
        url: src,
        status: file.status,
        name: file?.name,
        uid: file?.uid,
        ...file.response?.data,
      };
      const index = fileList.findIndex((f) => f.uid === file.uid);
      if (index !== -1) {
        fileList[index] = newFile;
      }
    }
    onChange?.(fileList);
  }

  const uploadButton = (
    <div className={styles.uploadButton}>
      {listType === "picture-card" ? (
        <>
          <PlusOutlined />
          <div style={{ marginTop: 8 }}>{children}</div>
        </>
      ) : (
        <Button icon={<UploadOutlined />}>{children}</Button>
      )}
    </div>
  );

  return (
    <Upload
      {...props}
      headers={{
        authorization: `Bearer ${token}`,
      }}
      onRemove={handleRemove}
      beforeUpload={customBeforeUpload}
      onChange={handleChange}
    >
      {fileList.length >= maxCount ? null : uploadButton}
    </Upload>
  );
}

FileUpload.propTypes = {
  beforeUpload: PropTypes.func,
  onChange: PropTypes.func,
  maxCount: PropTypes.number,
  listType: PropTypes.string,
  accept: PropTypes.string,
  maxSize: PropTypes.string,
  disabled: PropTypes.bool,
  children: PropTypes.node,
};

export default FileUpload;
