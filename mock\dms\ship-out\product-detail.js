module.exports = async (req, res) => {
  let command = null,
    success = true;
  const { code } = req.query;
  if (code === "123") {
    success = false;
    command = { type: "message", config: { type: "error", content: "订单商品未找到", duration: 3 } };
  }
  res.status(200).json({
    success,
    data: {
      item_id: "102039554",
      gallery: [
        {
          label: "产品图",
          images: [
            {
              src: "https://picsum.photos/300/300",
              width: 800,
              height: 800,
              title: 'Unforgettable Moment" Personalized Photo Necklace',
            },
            {
              src: "https://picsum.photos/300/300",
              width: 800,
              height: 800,
              title: 'Unforgettable Moment" Personalized Photo Necklace',
            },
          ],
        },
        {
          label: "效果图",
          images: [
            {
              src: "https://picsum.photos/300/300",
              width: 800,
              height: 800,
              title: 'Unforgettable Moment" Personalized Photo Necklace',
            },
          ],
        },
        {
          label: "元素图",
          images: [
            {
              src: "https://picsum.photos/300/300",
              width: 800,
              height: 800,
              title: 'Unforgettable Moment" Personalized Photo Necklace',
            },
            {
              src: "https://picsum.photos/300/300",
              width: 800,
              height: 800,
              title: 'Unforgettable Moment" Personalized Photo Necklace',
            },
          ],
        },
      ],
      content: {
        component: "JSONComponents",
        type: "json",
        children: [
          {
            component: "NativeTable",
            props: {
              // style: { width: "100px" },
            },
            children: [
              [
                {
                  tag: "th",
                  valueType: "text",
                  value: "运费险",
                  props: {
                    style: { width: "50px" },
                  },
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "0(usd)",
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "客户等级(当前等级/下单时等级)",
                  props: { rowSpan: 3 },
                },
              ],
              [
                {
                  tag: "th",
                  valueType: "text",
                  value: "运费险",
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "0(usd)",
                },
              ],
              [
                {
                  tag: "th",
                  valueType: "text",
                  value: "运费险",
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "0(usd)",
                },
              ],
              [
                {
                  tag: "th",
                  valueType: "text",
                  value: "定制属性",
                },
                {
                  tag: "td",
                  valueType: "native_table",
                  value: {
                    props: {},
                    children: [
                      [
                        {
                          tag: "th",
                          valueType: "text",
                          value: "类型",
                        },
                        {
                          tag: "td",
                          valueType: "text",
                          value: "镜片",
                        },
                        {
                          tag: "td",
                          valueType: "text",
                          value: "镜框",
                        },
                      ],
                      [
                        {
                          tag: "th",
                          valueType: "text",
                          value: "处方类型",
                        },
                        {
                          tag: "td",
                          valueType: "text",
                          value: "处方",
                          props: { colSpan: 2 },
                        },
                      ],
                      [
                        {
                          tag: "th",
                          valueType: "text",
                          value: "Glasses Type",
                        },
                        {
                          tag: "td",
                          valueType: "text",
                          value: "处方眼镜",
                        },
                        {
                          tag: "td",
                          valueType: "text",
                          value: "渐进处方",
                        },
                      ],
                    ],
                  },
                  props: {},
                },
              ],
            ],
          },
        ],
      },
    },
    command,
  });
};
