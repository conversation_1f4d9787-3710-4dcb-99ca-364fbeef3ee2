import PropTypes from "prop-types";
import { Checkbox, Col, Collapse, Input, InputNumber, Row } from "antd";
import { ControlWrapper, FormControl, useForm } from "@/components/react-form-x";
import OptionValueExtra from "../option-value-extra";

function DropdownValues(props) {
  const { optionSet, control, keyPath } = props;
  const form = useForm();

  return (
    <div>
      <Collapse
        items={control?.options?.map((option, index) => {
          const optionKeyPath = [...keyPath, "options", index];
          return {
            key: option.value,
            label: `Dropdown Value ${option.value}`,
            children: (
              <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
                <Row gutter={16}>
                  <Col span={8}>
                    <FormControl
                      keyPath={optionKeyPath}
                      name="label"
                      render={(props) => {
                        return (
                          <ControlWrapper
                            {...props}
                            render={(props) => {
                              return (
                                <div>
                                  <div>Label:</div>
                                  <div>
                                    <Input {...props} disabled></Input>
                                  </div>
                                </div>
                              );
                            }}
                          ></ControlWrapper>
                        );
                      }}
                    ></FormControl>
                  </Col>
                  <Col span={8}>
                    <FormControl
                      keyPath={optionKeyPath}
                      name="value"
                      render={(props) => {
                        return (
                          <ControlWrapper
                            {...props}
                            render={(props) => {
                              const { name, value, onChange } = props;
                              return (
                                <div>
                                  <div>Value:</div>
                                  <div>
                                    <InputNumber
                                      value={value}
                                      onChange={(value) => {
                                        onChange(null, { [name]: value });
                                      }}
                                      style={{ width: "100%" }}
                                      disabled
                                    ></InputNumber>
                                  </div>
                                </div>
                              );
                            }}
                          ></ControlWrapper>
                        );
                      }}
                    ></FormControl>
                  </Col>
                  <Col span={8}>
                    <FormControl
                      keyPath={optionKeyPath}
                      name="selected"
                      render={(props) => {
                        return (
                          <ControlWrapper
                            {...props}
                            render={(props) => {
                              const { name, value, onChange } = props;
                              return (
                                <div>
                                  <div>&nbsp;</div>
                                  <div style={{ display: "flex", alignItems: "center", height: 30 }}>
                                    <Checkbox
                                      checked={value}
                                      onChange={(event) => {
                                        control.options.forEach((option) => {
                                          option.selected = false;
                                        });
                                        form.updateValue(optionSet);
                                        onChange(null, { [name]: event.target.checked });
                                      }}
                                    >
                                      Selected
                                    </Checkbox>
                                  </div>
                                </div>
                              );
                            }}
                          ></ControlWrapper>
                        );
                      }}
                    ></FormControl>
                  </Col>
                </Row>
                <OptionValueExtra optionSet={optionSet} control={control} keyPath={optionKeyPath}></OptionValueExtra>
              </div>
            ),
          };
        })}
      ></Collapse>
    </div>
  );
}

DropdownValues.propTypes = {
  optionSet: PropTypes.object,
  control: PropTypes.object,
  keyPath: PropTypes.array,
  forceUpdate: PropTypes.func,
};

export default DropdownValues;
