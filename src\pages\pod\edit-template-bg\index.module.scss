.page {
  display: flex;
  height: 100%;

  > div {
    flex: 1;
  }
}

.left {
  padding: 20px;
  background-color: #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  max-height: calc(100vh - var(--header-height));
  flex: 1;

  .canvasBox {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .canvasWrapper {
      background-color: #fff;
    }

    canvas {
      display: block;
    }
  }
}

.right {
  padding: 20px;
}
