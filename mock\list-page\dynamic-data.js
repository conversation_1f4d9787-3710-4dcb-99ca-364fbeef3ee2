module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      dataSource: [
        {
          id: 1,
          order_number: "DMUS202203627400-1",
          order_number_extra_data: {
            command: {
              type: "drawer",
              closable: true,
              title: "新建订单",
              props: {
                width: 1200,
                placement: "right",
              },
              content: {
                component: "JSONComponents",
                type: "json",
                props: {},
                children: [
                  {
                    component: "Iframe",
                    props: {
                      src: "/dms/order/detail?id=0",
                    },
                  },
                ],
              },
            },
          },
          site_name: "DM-美国站",
          language: "英语",
          language_extra_data: {
            editable: {
              component: "Select",
              isGetDefaultOptions: true,
              props: {
                options: [
                  {
                    label: "英语",
                    value: "en",
                  },
                  {
                    label: "日语",
                    value: "jp",
                  },
                  {
                    label: "法语",
                    value: "fr",
                  },
                  {
                    label: "德语",
                    value: "de",
                  },
                  {
                    label: "意大利语",
                    value: "it",
                  },
                ],
              },
              searchApi: "http://localhost:8081/rest/v1/product/spu",
              request: {
                url: "http://localhost:8081/rest/v1/order",
                data: {
                  action: "edit",
                },
              },
            },
          },
          email: "<EMAIL>",
          email_extra_data: {
            style: {
              backgroundColor: "#ccc",
              color: "#fff",
            },
          },
          product_image: [
            {
              src: "https://test-res.jeulia.com/product/0/7/800x800/6571369a28570.jpg",
            },
            {
              src: "https://test-je5-oms.cnzlerp.com/uploads/product/e/9/6015357862b9e.jpg",
            },
          ],
          product_image_extra_data: {
            preview_group: [
              {
                items: [
                  {
                    src: "https://test-res.jeulia.com/product/0/7/800x800/6571369a28570.jpg",
                  },
                  {
                    src: "https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg",
                  },
                  {
                    src: "https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg",
                  },
                  {
                    src: "https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg",
                  },
                  {
                    src: "https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg",
                  },
                ],
                fallback: "https://www.google.com.hk/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png",
              },
            ],
            badges: [
              {
                count: 5,
                color: "red",
                size: "small",
              },
              {
                count: 1,
                color: "red",
                size: "small",
              },
            ],
          },
          grand_total: "107.85",
          grand_total_extra_data: {
            tags: [
              {
                text: "金额",
                style: {
                  color: "#fff",
                  backgroundColor: "#f00",
                  borderColor: "#f00",
                },
              },
            ],
          },
          currency: "USD",
          order_status: "Processing",
          order_status_extra_data: {
            tags: [
              {
                text: "待审核",
                color: "warning",
              },
              {
                text: "风控",
                style: {
                  color: "#fff",
                  backgroundColor: "#f00",
                  borderColor: "#f00",
                },
              },
              {
                text: "默认",
                color: "success",
              },
            ],
            command: {
              type: "modal",
              closable: true,
              title: "订单状态变更历史",
              footer: null,
              props: {
                width: 800,
              },
              content: {
                component: "JSONComponents",
                type: "api",
                props: {},
                fetcher: {
                  request: {
                    url: "http://localhost:8081/rest/v1/order/modify/history",
                    data: {},
                  },
                },
              },
            },
          },
          payment_status: "success",
          payment_method: "PayPal Express",
          risk: "是",
          risk_extra_data: {
            style: {
              color: "#f00",
            },
            icon: {
              src: "https://images.aoolia.com/static/images/time_fill.svg",
              style: {
                width: 16,
                height: 16,
                color: "#f00",
              },
            },
          },
          operation_record:
            '\n        <div data-clipboard="https://detail.1688.com/offer/654423996015.html?spm=a26352.b28411319.offerlist.20.60321e62PVWVtT">\n          <div><strong>订单出库</strong></div>\n          <div>打印：<span style="color: var(--ant-error-color)">2023-02-01 15:00:00</span></div>\n          <div>打印：<span style="color: var(--ant-success-color)">2023-02-01 15:00:00</span></div>\n          <div>出库：<span style="color: var(--ant-primary-color)">2023-02-01 15:00:00</span></div>\n        </div>\n      ',
          created_at: "2022-12-15 04:15:15",
          updated_at: "2022-12-15 04:16:02",
          operations: [
            {
              key: "edit",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "编辑",
              command: {
                type: "redirect",
                url: "/edit/order?a=1",
                openInNewTab: true,
              },
            },
            {
              key: "modal_edit",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "弹窗编辑",
              command: {
                type: "modal",
                closable: true,
                title: "编辑",
                footer: [
                  {
                    title: "确认",
                    props: {
                      type: "primary",
                    },
                    command: {
                      type: "request",
                      request: {
                        url: "http://localhost:8081/rest/v1/customer",
                        data: {
                          action: "contact",
                        },
                      },
                      confirm: "确定要联系客户吗？",
                      command: {
                        type: "reload_table",
                      },
                    },
                  },
                  {
                    title: "保存",
                    props: {
                      type: "primary",
                    },
                    command: {
                      type: "submit",
                      id: "form2",
                    },
                  },
                ],
                content: {
                  component: "JSONComponents",
                  type: "json",
                  props: {},
                  children: [
                    {
                      component: "Form",
                      type: "json",
                      props: {
                        layout: "vertical",
                        id: "form2",
                        initialValues: {
                          field5: "field5",
                          field6: "field6",
                        },
                        labelCol: {
                          span: 8,
                        },
                        wrapperCol: {
                          span: 16,
                        },
                      },
                      formItems: [
                        {
                          component: "Row",
                          props: {
                            gutter: 16,
                          },
                          children: [
                            {
                              component: "Col",
                              props: {
                                span: 12,
                              },
                              children: [
                                {
                                  key: "field1",
                                  label: "Field1",
                                  component: "Input",
                                  props: {},
                                  rules: [
                                    {
                                      required: true,
                                      message: "This is a required field",
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              component: "Col",
                              props: {
                                span: 12,
                              },
                              children: [
                                {
                                  key: "field2",
                                  label: "Field2",
                                  component: "Input",
                                  props: {},
                                  rules: [
                                    {
                                      required: true,
                                      message: "This is a required field",
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              component: "Col",
                              props: {
                                span: 12,
                              },
                              children: [
                                {
                                  key: "field3",
                                  label: "Field3",
                                  component: "Input",
                                  props: {},
                                  rules: [
                                    {
                                      required: true,
                                      message: "This is a required field",
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              component: "Col",
                              props: {
                                span: 12,
                              },
                              children: [
                                {
                                  key: "field4",
                                  label: "Field4",
                                  component: "Textarea",
                                  props: {
                                    rows: 5,
                                  },
                                  rules: [],
                                },
                              ],
                            },
                          ],
                        },
                        {
                          key: "field5",
                          label: "Field5",
                          component: "Input",
                          props: {},
                          rules: [
                            {
                              required: true,
                              message: "This is a required field",
                            },
                          ],
                        },
                        {
                          key: "field6",
                          label: "Field6",
                          component: "Input",
                          props: {},
                          rules: [
                            {
                              required: true,
                              message: "This is a required field",
                            },
                          ],
                        },
                      ],
                      fetcher: {},
                      submit: {
                        request: {
                          url: "http://localhost:8081/rest/v1/customer",
                          data: {},
                        },
                      },
                    },
                  ],
                },
              },
            },
            {
              key: "modal_gallery",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "弹窗gallery",
              command: {
                type: "modal",
                closable: true,
                title: "查看面单",
                props: {
                  width: 1500,
                  style: {
                    maxHeight: "90vh",
                  },
                },
                content: {
                  component: "JSONComponents",
                  type: "json",
                  props: {},
                  children: [
                    {
                      component: "Gallery",
                      props: {
                        centered: true,
                      },
                      items: [
                        {
                          image: {
                            src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/27/171682111566549c7babddb0.png",
                          },
                          style: {},
                        },
                        {
                          image: {
                            src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/30/1717080509665891bd60f400.png",
                          },
                        },
                        {
                          image: {
                            src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/29/17169541166656a404adbca0.png",
                          },
                        },
                        {
                          image: {
                            src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/28/1716848967665509476374f0.png",
                          },
                        },
                        {
                          image: {
                            src: "https://erp5-dms.cnzlerp.com/uploads/logistics/hsd/waybill/2024/05/27/171678882566541e59bc80b0.png?1=%2Fuploads%2Flogistics%2Fhsd%2Fwaybill%2F2024%2F05%2F27%2F171678882566541e59bc80b1.png",
                          },
                        },
                      ],
                    },
                  ],
                },
              },
            },
            {
              key: "view",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "查看",
              command: {
                type: "redirect",
                url: "/detail/order",
              },
            },
            {
              key: "contact",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "联系客户",
              command: {
                type: "request",
                request: {
                  url: "http://localhost:8081/rest/v1/customer",
                  data: {
                    action: "contact",
                  },
                },
                confirm: "确定要联系客户吗？",
                command: {
                  type: "reload_table",
                },
              },
            },
            {
              key: "reload_table",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "刷新table",
              command: {
                type: "reload_table",
              },
            },
            {
              key: "reload",
              title: "刷新页面",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              command: {
                type: "reload",
              },
            },
            {
              key: "expedite",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "加急",
              command: {
                type: "request",
                request: {
                  url: "http://localhost:8081/rest/v1/customer",
                  data: {
                    action: "expedite",
                  },
                },
                confirm: "我是二次确认弹窗title",
              },
            },
            {
              key: "remark",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "备注",
              command: {
                type: "modal",
                title: "备注",
                footer: [
                  {
                    title: "保存",
                    props: {
                      type: "primary",
                    },
                    command: {
                      type: "submit",
                      id: "form3",
                    },
                  },
                ],
                content: {
                  component: "JSONComponents",
                  type: "json",
                  props: {},
                  children: [
                    {
                      component: "Form",
                      props: {
                        id: "form3",
                        initialValues: {
                          type: "order_remark",
                          content: "hhhh",
                          a: 1,
                          b: 2,
                          c: 3,
                        },
                      },
                      formItems: [
                        {
                          key: "type",
                          label: "备注类型",
                          component: "Select",
                          props: {
                            options: [
                              {
                                label: "订单备注",
                                value: "order_remark",
                              },
                              {
                                label: "状态备注",
                                value: "status_remark",
                              },
                            ],
                          },
                          rules: [
                            {
                              required: true,
                              message: "This is a required field",
                            },
                          ],
                        },
                        {
                          key: "content",
                          label: "备注内容",
                          component: "Textarea",
                          props: {
                            rows: 5,
                          },
                          rules: [
                            {
                              required: true,
                              message: "This is a required field",
                            },
                          ],
                        },
                      ],
                      submit: {
                        preserveInitialValues: true,
                        request: {
                          url: "http://localhost:8081/rest/v1/customer",
                          data: {},
                        },
                      },
                    },
                  ],
                },
              },
            },
            {
              key: "查看table",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "弹窗table",
              command: {
                type: "drawer",
                closable: true,
                title: "新建订单",
                props: {
                  closeIcon: null,
                  placement: "bottom",
                  height: "70vh",
                },
                extra: [
                  {
                    title: "保存",
                    props: {
                      type: "primary",
                    },
                    command: {
                      type: "close_drawer",
                    },
                  },
                ],
                content: {
                  component: "JSONComponents",
                  type: "json",
                  props: {},
                  children: [
                    {
                      component: "Iframe",
                      props: {
                        src: "/list/order-list",
                      },
                    },
                  ],
                },
              },
            },
            {
              key: "delete",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "删除",
              command: {
                type: "request",
                request: {
                  url: "http://localhost:8081/rest/v1/order",
                  data: {
                    action: "delete",
                  },
                },
                confirm: "确定删除吗？",
              },
              props: {
                danger: true,
              },
            },
          ],
        },
        {
          id: 2,
          order_number: "DMUS202203627400-2",
          order_number_extra_data: {
            command: {
              type: "drawer",
              closable: true,
              title: "新建订单",
              props: {
                width: 1200,
                placement: "right",
              },
              content: {
                component: "JSONComponents",
                type: "json",
                props: {},
                children: [
                  {
                    component: "Iframe",
                    props: {
                      src: "/dms/order/detail?id=1",
                    },
                  },
                ],
              },
            },
          },
          site_name: "DM-美国站",
          language: "英语",
          language_extra_data: null,
          email: "<EMAIL>",
          email_extra_data: {
            style: {
              backgroundColor: "#ccc",
              color: "#fff",
            },
          },
          product_image: [
            {
              src: "https://test-res.jeulia.com/product/0/7/800x800/6571369a28570.jpg",
            },
            {
              src: "https://test-je5-oms.cnzlerp.com/uploads/product/e/9/6015357862b9e.jpg",
            },
          ],
          product_image_extra_data: {
            preview_group: [
              {
                items: [
                  {
                    src: "https://test-res.jeulia.com/product/0/7/800x800/6571369a28570.jpg",
                  },
                  {
                    src: "https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg",
                  },
                  {
                    src: "https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg",
                  },
                  {
                    src: "https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg",
                  },
                  {
                    src: "https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg",
                  },
                ],
                fallback: "https://www.google.com.hk/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png",
              },
            ],
            badges: [
              {
                count: 5,
                color: "red",
                size: "small",
              },
              {
                count: 1,
                color: "red",
                size: "small",
              },
            ],
          },
          grand_total: "107.85",
          grand_total_extra_data: {
            tags: [
              {
                text: "金额",
                style: {
                  color: "#fff",
                  backgroundColor: "#f00",
                  borderColor: "#f00",
                },
              },
            ],
          },
          currency: "USD",
          order_status: "Processing",
          order_status_extra_data: {
            tags: [
              {
                text: "风控",
                style: {
                  color: "#fff",
                  backgroundColor: "#f00",
                  borderColor: "#f00",
                },
              },
            ],
            command: {
              type: "modal",
              closable: true,
              title: "订单状态变更历史",
              footer: null,
              props: {
                width: 800,
              },
              content: {
                component: "JSONComponents",
                type: "api",
                props: {},
                fetcher: {
                  request: {
                    url: "http://localhost:8081/rest/v1/order/modify/history",
                    data: {},
                  },
                },
              },
            },
          },
          payment_status: "pending",
          payment_method: "PayPal Express",
          risk: "否",
          risk_extra_data: {
            style: {},
            icon: null,
          },
          operation_record:
            '\n        <div data-clipboard="https://detail.1688.com/offer/654423996015.html?spm=a26352.b28411319.offerlist.20.60321e62PVWVtT">\n          <div><strong>订单出库</strong></div>\n          <div>打印：<span style="color: var(--ant-error-color)">2023-02-01 15:00:00</span></div>\n          <div>打印：<span style="color: var(--ant-success-color)">2023-02-01 15:00:00</span></div>\n          <div>出库：<span style="color: var(--ant-primary-color)">2023-02-01 15:00:00</span></div>\n        </div>\n      ',
          created_at: "2022-12-15 04:15:15",
          updated_at: "2022-12-15 04:16:02",
          operations: [
            {
              key: "edit",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "编辑",
              command: {
                type: "redirect",
                url: "/edit/order?a=1",
                openInNewTab: true,
              },
            },
            {
              key: "modal_edit",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "弹窗编辑",
              command: {
                type: "modal",
                closable: true,
                title: "编辑",
                footer: [
                  {
                    title: "确认",
                    props: {
                      type: "primary",
                    },
                    command: {
                      type: "request",
                      request: {
                        url: "http://localhost:8081/rest/v1/customer",
                        data: {
                          action: "contact",
                        },
                      },
                      confirm: "确定要联系客户吗？",
                      command: {
                        type: "reload_table",
                      },
                    },
                  },
                  {
                    title: "保存",
                    props: {
                      type: "primary",
                    },
                    command: {
                      type: "submit",
                      id: "form2",
                    },
                  },
                ],
                content: {
                  component: "JSONComponents",
                  type: "json",
                  props: {},
                  children: [
                    {
                      component: "Form",
                      type: "json",
                      props: {
                        layout: "vertical",
                        id: "form2",
                        initialValues: {
                          field5: "field5",
                          field6: "field6",
                        },
                        labelCol: {
                          span: 8,
                        },
                        wrapperCol: {
                          span: 16,
                        },
                      },
                      formItems: [
                        {
                          component: "Row",
                          props: {
                            gutter: 16,
                          },
                          children: [
                            {
                              component: "Col",
                              props: {
                                span: 12,
                              },
                              children: [
                                {
                                  key: "field1",
                                  label: "Field1",
                                  component: "Input",
                                  props: {},
                                  rules: [
                                    {
                                      required: true,
                                      message: "This is a required field",
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              component: "Col",
                              props: {
                                span: 12,
                              },
                              children: [
                                {
                                  key: "field2",
                                  label: "Field2",
                                  component: "Input",
                                  props: {},
                                  rules: [
                                    {
                                      required: true,
                                      message: "This is a required field",
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              component: "Col",
                              props: {
                                span: 12,
                              },
                              children: [
                                {
                                  key: "field3",
                                  label: "Field3",
                                  component: "Input",
                                  props: {},
                                  rules: [
                                    {
                                      required: true,
                                      message: "This is a required field",
                                    },
                                  ],
                                },
                              ],
                            },
                            {
                              component: "Col",
                              props: {
                                span: 12,
                              },
                              children: [
                                {
                                  key: "field4",
                                  label: "Field4",
                                  component: "Textarea",
                                  props: {
                                    rows: 5,
                                  },
                                  rules: [],
                                },
                              ],
                            },
                          ],
                        },
                        {
                          key: "field5",
                          label: "Field5",
                          component: "Input",
                          props: {},
                          rules: [
                            {
                              required: true,
                              message: "This is a required field",
                            },
                          ],
                        },
                        {
                          key: "field6",
                          label: "Field6",
                          component: "Input",
                          props: {},
                          rules: [
                            {
                              required: true,
                              message: "This is a required field",
                            },
                          ],
                        },
                      ],
                      fetcher: {},
                      submit: {
                        request: {
                          url: "http://localhost:8081/rest/v1/customer",
                          data: {},
                        },
                      },
                    },
                  ],
                },
              },
            },
            {
              key: "modal_gallery",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "弹窗gallery",
              command: {
                type: "modal",
                closable: true,
                title: "查看面单",
                props: {
                  width: 1500,
                  style: {
                    maxHeight: "90vh",
                  },
                },
                content: {
                  component: "JSONComponents",
                  type: "json",
                  props: {},
                  children: [
                    {
                      component: "Gallery",
                      props: {
                        centered: true,
                      },
                      items: [
                        {
                          image: {
                            src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/27/171682111566549c7babddb0.png",
                          },
                          style: {},
                        },
                        {
                          image: {
                            src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/30/1717080509665891bd60f400.png",
                          },
                        },
                        {
                          image: {
                            src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/29/17169541166656a404adbca0.png",
                          },
                        },
                        {
                          image: {
                            src: "https://erp5-dms.cnzlerp.com/uploads/logistics/yunexpress/waybill/2024/05/28/1716848967665509476374f0.png",
                          },
                        },
                        {
                          image: {
                            src: "https://erp5-dms.cnzlerp.com/uploads/logistics/hsd/waybill/2024/05/27/171678882566541e59bc80b0.png?1=%2Fuploads%2Flogistics%2Fhsd%2Fwaybill%2F2024%2F05%2F27%2F171678882566541e59bc80b1.png",
                          },
                        },
                      ],
                    },
                  ],
                },
              },
            },
            {
              key: "view",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "查看",
              command: {
                type: "redirect",
                url: "/detail/order",
              },
            },
            {
              key: "contact",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "联系客户",
              command: {
                type: "request",
                request: {
                  url: "http://localhost:8081/rest/v1/customer",
                  data: {
                    action: "contact",
                  },
                },
                confirm: "确定要联系客户吗？",
                command: {
                  type: "reload_table",
                },
              },
            },
            {
              key: "reload_table",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "刷新table",
              command: {
                type: "reload_table",
              },
            },
            {
              key: "reload",
              title: "刷新页面",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              command: {
                type: "reload",
              },
            },
            {
              key: "expedite",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "加急",
              command: {
                type: "request",
                request: {
                  url: "http://localhost:8081/rest/v1/customer",
                  data: {
                    action: "expedite",
                  },
                },
                confirm: "我是二次确认弹窗title",
              },
            },
            {
              key: "remark",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "备注",
              command: {
                type: "modal",
                title: "备注",
                footer: [
                  {
                    title: "保存",
                    props: {
                      type: "primary",
                    },
                    command: {
                      type: "submit",
                      id: "form3",
                    },
                  },
                ],
                content: {
                  component: "JSONComponents",
                  type: "json",
                  props: {},
                  children: [
                    {
                      component: "Form",
                      props: {
                        id: "form3",
                        initialValues: {
                          type: "order_remark",
                          content: "hhhh",
                          a: 1,
                          b: 2,
                          c: 3,
                        },
                      },
                      formItems: [
                        {
                          key: "type",
                          label: "备注类型",
                          component: "Select",
                          props: {
                            options: [
                              {
                                label: "订单备注",
                                value: "order_remark",
                              },
                              {
                                label: "状态备注",
                                value: "status_remark",
                              },
                            ],
                          },
                          rules: [
                            {
                              required: true,
                              message: "This is a required field",
                            },
                          ],
                        },
                        {
                          key: "content",
                          label: "备注内容",
                          component: "Textarea",
                          props: {
                            rows: 5,
                          },
                          rules: [
                            {
                              required: true,
                              message: "This is a required field",
                            },
                          ],
                        },
                      ],
                      submit: {
                        preserveInitialValues: true,
                        request: {
                          url: "http://localhost:8081/rest/v1/customer",
                          data: {},
                        },
                      },
                    },
                  ],
                },
              },
            },
            {
              key: "查看table",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "弹窗table",
              command: {
                type: "drawer",
                closable: true,
                title: "新建订单",
                props: {
                  closeIcon: null,
                  placement: "bottom",
                  height: "70vh",
                },
                extra: [
                  {
                    title: "保存",
                    props: {
                      type: "primary",
                    },
                    command: {
                      type: "close_drawer",
                    },
                  },
                ],
                content: {
                  component: "JSONComponents",
                  type: "json",
                  props: {},
                  children: [
                    {
                      component: "Iframe",
                      props: {
                        src: "/list/order-list",
                      },
                    },
                  ],
                },
              },
            },
            {
              key: "delete",
              icon: "https://images.aoolia.com/static/icons/edit.svg",
              title: "删除",
              command: {
                type: "request",
                request: {
                  url: "http://localhost:8081/rest/v1/order",
                  data: {
                    action: "delete",
                  },
                },
                confirm: "确定删除吗？",
              },
              props: {
                danger: true,
              },
            },
          ],
        },
      ],
      summary_data: {
        grand_total: 9527,
        currency: "USD",
      },
      pagination: {
        total: 500,
      },
    },
  });
};
