import Fetchers from "fetchers";

export const tableProps = {
  rowSelection: false,
  isShowAddRow: true,
};

export const initialValues = {
  order_no: "",
  sku: "",
  qty: null,
  remark: "",
  list: [],
};

export const defaultFilterStatus = {
  order_no: {
    readOnly: false,
  },
  sku: {
    readOnly: true,
  },
  qty: {
    readOnly: true,
  },
};

export const editTableOperations = [
  {
    title: "打印条码",
    action: async ({ row, index }) => {
      try {
        await Fetchers.restockOrderItemPrintLabel({
          data: { qty: row?.qty },
          ids: `${row?.id}`,
        }).then((res) => res?.data);
      } finally {
      }
    },
  },
];

export function filterStatusReducer(state, action = {}) {
  return { ...state, ...action };
}
