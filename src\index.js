// import reportWebVitals from './reportWebVitals';
import React from "react";
import ReactDOM from "react-dom/client";
import { ConfigProvider, App as AntdApp } from "antd";
import Container from "./container";
import Router from "./router";
import "./index.scss";
import "./styles/index.scss";
import { SWRConfig } from "swr";
import MyWebSocket from "@/websocket";
import { I18nProvider } from "./context/I18nContext";
import { getAntdLocale } from "utils/i18n";

const swrConfig = {
  revalidateOnFocus: false,
  refreshInterval: 0,
  revalidateOnReconnect: false,
  errorRetryInterval: 0,
  errorRetryCount: 0,
};

MyWebSocket.connect();

function App() {
  const urlParams = new URLSearchParams(window.location.search);
  const language = urlParams.get("language") || "zh-CN";
  const antdLocale = getAntdLocale(language);

  return (
    <SWRConfig value={swrConfig}>
      <I18nProvider>
        <ConfigProvider theme={{ token: { borderRadius: 2 } }} locale={antdLocale} button={{ autoInsertSpace: false }}>
          <AntdApp>
            <Container>
              <Router />
            </Container>
          </AntdApp>
        </ConfigProvider>
      </I18nProvider>
    </SWRConfig>
  );
}

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(<App />);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
// reportWebVitals();
