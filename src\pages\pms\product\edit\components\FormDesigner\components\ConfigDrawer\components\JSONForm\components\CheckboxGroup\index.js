import styles from "./index.module.scss";
import { Checkbox } from "antd";
import { useState } from "react";

import useSWR from "swr";
import Fetchers from "@/fetchers";

const AntdCheckboxGroup = Checkbox.Group;

function CheckboxGroup(props) {
  const {
    id,
    value,
    identifiers,
    onChange,
    fieldNames = { value: "value", label: "title" },
    style,
    valueType = "object",
  } = props;
  const checkedList = value ? value.map((item) => item.value) : [];
  const [indeterminate, setIndeterminate] = useState(false);

  const identifier = identifiers[id];

  const { data: options = [] } = useSWR([identifier, Fetchers.Api.getCustomizeDictionaryOption], async () => {
    try {
      if (!identifier) return;

      return await Fetchers.getCustomizeDictionaryOption({ params: { dict: [identifier] } }).then(
        (res) => res?.data?.data[identifier]
      );
    } finally {
    }
  });

  function formatValue(checkedValues) {
    if (valueType === "string") {
      return checkedValues;
    }

    return checkedValues.map((checkedValue) => {
      const option = options.find((opt) => opt[fieldNames.value] === checkedValue);
      return {
        label: decodeURI(option[fieldNames.label]),
        value: option[fieldNames.value],
      };
    });
  }

  function handleChange(checkedValues) {
    setIndeterminate(checkedValues.length > 0 && checkedValues.length < options.length);
    const newValue = formatValue(checkedValues);
    onChange?.(newValue);
  }

  function onCheckAllChange(e) {
    const checkedValues = e.target.checked ? options.map((item) => item[fieldNames?.value]) : [];
    setIndeterminate(checkedValues.length > 0 && checkedValues.length < options.length);
    const newValue = formatValue(checkedValues);
    onChange?.(newValue);
  }

  return (
    <>
      <Checkbox
        indeterminate={indeterminate}
        onChange={onCheckAllChange}
        checked={checkedList.length === options.length}
      >
        全选
      </Checkbox>
      <AntdCheckboxGroup className={styles.checkboxGroup} value={checkedList} onChange={handleChange}>
        {options.map((item) => (
          <Checkbox key={item[fieldNames?.value]} value={item[fieldNames?.value]} style={style}>
            {decodeURI(item[fieldNames?.label])}
          </Checkbox>
        ))}
      </AntdCheckboxGroup>
    </>
  );
}

export default CheckboxGroup;
