import { useEffect, useRef, useState } from "react";
import Fetchers from "@/fetchers";
import Axios from "axios";
import { Button } from "antd";
import Utils from "@/utils";

const axios = Axios.create();

function TestPod() {
  const [template, setTemplate] = useState();
  const [imageSrc, setImageSrc] = useState(null);

  useEffect(() => {
    const queryParams = Utils.getQueryParams(window.location.href);
    Fetchers.getPodTemplate({ id: queryParams.id || 1 }).then(async (res) => {
      const template = res.data.data.item;
      setTemplate(template);
    });
  }, []);

  return (
    <div>
      <div>
        <Button
          type="primary"
          onClick={async () => {
            const url = `https://sandbox2.jeulia.com/rest/v1/pod/draw`;
            // const url = `http://localhost:8083/rest/v1/pod/draw`;
            axios.post(url, { data: template.canvas }).then((res) => {
              setImageSrc(res.data.data.image.src);
            });
          }}
          disabled={!template}
        >
          开始生成
        </Button>
      </div>
      <div>
        <img width="100%" src={imageSrc} alt="" />
      </div>
    </div>
  );
}

export default TestPod;
