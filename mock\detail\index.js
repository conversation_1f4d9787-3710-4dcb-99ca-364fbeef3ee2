const Api = require("../../src/fetchers/api");

module.exports = {
  "GET /rest/v1/detail/:url_key": (req, res) => {
    res.status(200).json({
      success: true,
      data: {
        content: {
          component: "JSONComponents",
          type: "json",
          children: [
            {
              component: "Card",
              props: { title: "产品信息", styles: { header: { backgroundColor: "#343a40", color: "#FFFFFF" } } },
              children: [
                {
                  component: "NativeTable",
                  props: {
                    // style: { width: "100px" },
                    header: { content: "展示header" },
                    footer: {
                      content: "<div style='color:#000'>展示footer</div>",
                      style: { background: "#fafafa" },
                    },
                  },
                  children: [
                    [
                      {
                        tag: "th",
                        valueType: "text",
                        value: "运费险",
                        props: {
                          style: { width: "50px" },
                        },
                        editable: {
                          component: "ExternalPageEditor",
                          props: {
                            children: "External修改1",
                            target: "edit-custom-option-window",
                            targetUrl:
                              "https://sandbox2.jeulia.com/edit-product-jeulia-vintage-custom-halo-heart-engagement-ring-cid2489-jewe0822-k?carat=0.8&materials_0=10Kwhitegold&gemstones_0=simulated%7Ccrystal&gemstones_1=simulated%7Ccrystal&gemstones_2=simulated%7Ccrystal",
                            messageAction: "submit_customize",
                            defaultAction: "edit_customize",
                            filterPrimaryAndSecondary: true,
                            submit: {
                              request: {
                                url: Api.customer,
                              },
                            },
                          },
                        },
                      },
                      {
                        key: "freight",
                        tag: "td",
                        valueType: "text",
                        value: "input 编辑",
                        editable: { component: "Input", props: {}, request: { url: Api.order, data: { id: 1 } } },
                      },
                      {
                        tag: "td",
                        valueType: "html",
                        value: `<p>这里是富文本内容显示<img src="http://localhost:8081/rest/v1/files/ac33cf68-3e0d-4c14-baea-fe6a69f1de4c.jpg"/></p>`,
                        props: { rowSpan: 3, style: { width: 300 } },
                      },
                    ],
                    [
                      {
                        tag: "th",
                        valueType: "text",
                        value: "运费险",
                        editable: {
                          component: "ExternalPageEditor",
                          props: {
                            children: "External修改2",
                            target: "edit-custom-option-window",
                            targetUrl:
                              "https://sandbox2.drawelry.com/edit-product-drtzyx003-cid139-drtzyx003?__currency__=USD",
                            messageAction: "submit_customize",
                            defaultValue: {
                              group5: {
                                value: "2",
                              },
                              group5_items: [
                                {
                                  crossword6: {
                                    data: {
                                      words: ["dsdff", "dfdfgf"],
                                      options: {
                                        width: 800,
                                        height: 800,
                                        cellBgImg: null,
                                        textColor: "#49D57C",
                                        enableScore: true,
                                      },
                                      response: {
                                        status: 200,
                                        success: true,
                                      },
                                      crossword_image:
                                        "https://test-res.drawelry.com/media/uploads/drawelry/2025/02/10/2/e/67a96829d0ce2.png",
                                    },
                                  },
                                },
                                {
                                  crossword6: {
                                    data: {
                                      words: ["xcxcvvc", "cvcvfd"],
                                      options: {
                                        width: 800,
                                        height: 800,
                                        cellBgImg: null,
                                        textColor: "#49D57C",
                                        enableScore: true,
                                      },
                                      response: {
                                        status: 200,
                                        success: true,
                                      },
                                      crossword_image:
                                        "https://test-res.drawelry.com/media/uploads/drawelry/2025/02/10/5/4/67a9682a00045.png",
                                    },
                                  },
                                },
                              ],
                              second_attr_dictionary_identifier: {
                                value: "8_x_8_inches__(suggest_1-8_names)_",
                              },
                            },
                            defaultAction: "edit_customize",
                            submit: {
                              request: {
                                url: Api.customer,
                              },
                            },
                          },
                        },
                      },
                      {
                        tag: "td",
                        valueType: "text",
                        value: "0(usd)",
                        copyable: true,
                      },
                    ],
                    [
                      {
                        tag: "th",
                        valueType: "text",
                        value: "运费险",
                      },
                      {
                        tag: "td",
                        valueType: "text",
                        value: "0(usd)",
                      },
                    ],
                    [
                      {
                        tag: "th",
                        valueType: "text",
                        value: "定制属性",
                      },
                      {
                        tag: "td",
                        valueType: "native_table",
                        value: {
                          props: {},
                          children: [
                            [
                              {
                                tag: "th",
                                valueType: "text",
                                value: "类型",
                              },
                              {
                                tag: "td",
                                valueType: "command",
                                value: "镜片-可以点击打开弹窗",
                                command: {
                                  type: "modal",
                                  closable: true,
                                  title: "订单详情",
                                  props: {
                                    width: 1500,
                                  },
                                  content: {
                                    component: "JSONComponents",
                                    type: "json",
                                    props: {},
                                    children: [
                                      {
                                        component: "Iframe",
                                        props: {
                                          src: `/dms/order/detail?id=1`,
                                        },
                                      },
                                    ],
                                  },
                                },
                              },
                              {
                                tag: "td",
                                valueType: "text",
                                value: "镜框",
                              },
                            ],
                            [
                              {
                                tag: "th",
                                valueType: "text",
                                value: "处方类型",
                              },
                              {
                                tag: "td",
                                valueType: "text",
                                value: "处方",
                                props: { colSpan: 2 },
                              },
                            ],
                            [
                              {
                                tag: "th",
                                valueType: "text",
                                value: "Glasses Type",
                              },
                              {
                                tag: "td",
                                valueType: "text",
                                value: "处方眼镜",
                              },
                              {
                                tag: "td",
                                valueType: "text",
                                value: "渐进处方",
                              },
                            ],
                          ],
                        },
                        props: {},
                      },
                    ],
                  ],
                },
              ],
            },
            {
              component: "Card",
              props: { title: "出厂前数据" },
              children: [
                {
                  component: "NativeTable",
                  props: {},
                  children: [
                    [
                      {
                        tag: "th",
                        valueType: "text",
                        value: "SPU",
                      },
                      {
                        tag: "td",
                        key: "spu",
                        valueType: "text",
                        value: "Select 编辑",
                        editable: {
                          component: "Select",
                          props: {
                            options: [
                              { label: "英语", value: "en" },
                              { label: "日语", value: "jp" },
                              { label: "法语", value: "fr" },
                              { label: "德语", value: "de" },
                              { label: "意大利语", value: "it" },
                            ],
                          },
                          request: {
                            url: Api.order,
                            data: { id: 1 },
                          },
                        },
                      },
                      {
                        tag: "th",
                        valueType: "text",
                        value: "唯一码",
                      },
                      {
                        tag: "td",
                        valueType: "text",
                        value: "102036",
                      },
                      {
                        tag: "th",
                        valueType: "text",
                        value: "尺码",
                      },
                      {
                        tag: "td",
                        valueType: "html",
                        value: '<span style="color:red;font-weight:bold;">5.0</span>',
                      },
                    ],
                    [
                      {
                        tag: "th",
                        valueType: "text",
                        value: "产品图",
                      },
                      {
                        tag: "td",
                        valueType: "image",
                        value: [
                          {
                            width: 50,
                            height: 50,
                            src: "https://res2022.shesaidyes.com/uploads/produce-image/order/1286564/stone_setting_fids/2/3/660d16799b332.jpg",
                            text: "头-斑马: 序号47-1",
                            props: {
                              items: [
                                {
                                  src: "https://test-res.jeulia.com/product/0/7/800x800/6571369a28570.jpg",
                                },
                                { src: "https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg" },
                                { src: "https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg" },
                                { src: "https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg" },
                                { src: "https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg" },
                              ],
                            },
                          },
                          {
                            width: 50,
                            height: 50,
                            src: "https://test-je5-oms.cnzlerp.com/uploads/product/e/9/6015357862b9e.jpg",
                            text: "头-大象: 序号41",
                          },
                          {
                            width: 50,
                            height: 50,
                            src: "https://test-je5-oms.cnzlerp.com/uploads/product/e/9/6015357862b9e.jpg",
                            text: "头-狮子: 序号43",
                          },
                          {
                            width: 50,
                            height: 50,
                            src: "https://test-je5-oms.cnzlerp.com/uploads/product/e/9/6015357862b9e.jpg",
                            text: "头-斑马: 序号41",
                          },
                        ],
                      },
                      {
                        tag: "th",
                        valueType: "text",
                        value: "标签",
                      },
                      {
                        tag: "td",
                        valueType: "tag",
                        value: [
                          {
                            text: "加急",
                            props: {
                              color: "orange",
                            },
                          },
                          {
                            text: "高级定制",
                            props: {
                              color: "red",
                            },
                          },
                        ],
                      },
                      {
                        tag: "th",
                        valueType: "text",
                        value: "尺码",
                      },
                      {
                        tag: "td",
                        valueType: "html",
                        value: '<span style="color:red;font-weight:bold;">5.0</span>',
                      },
                    ],
                  ],
                },
              ],
            },
          ],
        },
      },
    });
  },
};
