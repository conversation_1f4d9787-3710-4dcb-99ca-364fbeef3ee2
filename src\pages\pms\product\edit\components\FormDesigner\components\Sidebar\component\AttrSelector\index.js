import { Modal, Radio } from "antd";
import Select from "components/common/Select";

import styles from "./index.module.scss";
import { widgets } from "../../../../common/widgets";
import { useState } from "react";
import Enums from "../../../../common/enums";
import { sanitizeItems } from "../../../../common/utils";

const radioOptions = [
  { label: "下拉选项", value: "select" },
  { label: "下拉选项（带图标）", value: "select_icon" },
  { label: "平铺选项", value: "radio_list" },
  { label: "色卡", value: "swatch" },
  { label: "艺术字", value: "preview_text" },
];

function AttrSelector(props) {
  const {
    widgetForm,
    onChange,
    masterProperty,
    secondProperty,
    setWidgetForm,
    onSelect,
    masterPropertyKey,
    secondPropertyKey,
  } = props;

  const [open, setOpen] = useState(false);
  const [selectType, setSelectType] = useState(radioOptions[0].value);
  const [currentPropertyType, setCurrentPropertyType] = useState(null);

  function handlePropertyChange(value, propertyType) {
    const propertyKey = propertyType === Enums.PropertyType.master ? masterPropertyKey : secondPropertyKey;

    setWidgetForm((prev) => {
      let updatedItems = [...prev.items];

      if (!value) {
        updatedItems = updatedItems.filter((item) => item.key !== propertyType);
      }

      const newData = {
        ...prev,
        [propertyKey]: value,
        items: updatedItems,
      };

      if (value) {
        setCurrentPropertyType(propertyType);
        setOpen(true);
      }

      onChange({ ...newData, items: sanitizeItems(updatedItems) });
      return newData;
    });
  }

  function handleMasterPropertyChange(value) {
    handlePropertyChange(value, Enums.PropertyType.master);
  }

  function handleSecondPropertyChange(value) {
    handlePropertyChange(value, Enums.PropertyType.second);
  }

  function handleOk() {
    const currentWidget = widgets.find((widget) => widget.type === selectType);
    // 为组件配置中，选项数据添加默认值，key为identifier
    const identifier =
      currentPropertyType === Enums.PropertyType.master ? widgetForm[masterPropertyKey] : widgetForm[secondPropertyKey];

    setWidgetForm((prev) => {
      const updatedItems = [
        ...prev.items.filter((item) => item.key !== currentPropertyType),
        {
          ...currentWidget,
          required: true, // 默认设置为必填
          key: currentPropertyType,
          identifier,
        },
      ];

      const newData = {
        ...prev,
        items: updatedItems,
      };

      onChange({ ...newData, items: sanitizeItems(updatedItems) });
      return newData;
    });

    setOpen(false);
    onSelect(null);
    setSelectType(radioOptions[0].value);
  }

  return (
    <>
      <div className={styles.property}>
        <div className={styles.masterProperty}>
          <div className={styles.title}>主属性</div>
          <div className={styles.content}>
            <Select
              value={widgetForm?.[masterPropertyKey]}
              onChange={handleMasterPropertyChange}
              allowClear
              showSearch
              optionFilterProp="label"
              {...masterProperty?.props}
            />
          </div>
        </div>
        <div className={styles.secondProperty}>
          <div className={styles.title}>次属性</div>
          <div className={styles.content}>
            <Select
              value={widgetForm?.[secondPropertyKey]}
              onChange={handleSecondPropertyChange}
              allowClear
              showSearch
              optionFilterProp="label"
              {...secondProperty?.props}
            />
          </div>
        </div>
      </div>

      <Modal title="选择展示方式" open={open} onCancel={() => setOpen(false)} onOk={handleOk} maskClosable={false}>
        <Radio.Group value={selectType} onChange={(e) => setSelectType(e.target.value)} options={radioOptions} />
      </Modal>
    </>
  );
}

export default AttrSelector;
