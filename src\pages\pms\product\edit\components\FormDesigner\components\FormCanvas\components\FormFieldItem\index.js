import classNames from "classnames";
import styles from "./index.module.scss";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Setting<PERSON>andle, DeleteHandle } from "../Handles";
import Renderer from "./component/Renderer";
import Enums from "../../../../common/enums";

function FormFieldItem({
  widget,
  index,
  isSelected,
  isDragging,
  handleProps,
  isOverlay,
  isGroup,
  openConfigDrawer,
  onWidgetDelete,
  ...restProps
}) {
  const isGhost = widget?.type === "ghost";

  return (
    <div className={styles.widgetFormItemWrapper}>
      <div
        className={classNames(
          !isGhost && styles.widgetFormItem,
          { [styles.selected]: isSelected || isDragging },
          { [styles.isOverlay]: isOverlay },
          { [styles.ghost]: isGhost }
        )}
      >
        <Renderer widget={widget} {...restProps} />

        {isSelected && !isGhost && (
          <>
            <DragHandle className={styles.dragHandle} {...handleProps} />

            <SettingHandle
              className={styles.configButton}
              onClick={(e) => {
                e.stopPropagation();
                openConfigDrawer();
              }}
            />

            {Enums.PropertyType.master !== widget.key && Enums.PropertyType.second !== widget.key && (
              <DeleteHandle
                className={styles.deleteButton}
                onClick={(e) => {
                  e.stopPropagation();
                  onWidgetDelete(widget.key);
                }}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
}

export default FormFieldItem;
