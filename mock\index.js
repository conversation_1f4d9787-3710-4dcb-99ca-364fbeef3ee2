const express = require("express");
const cors = require("cors");
const routes = require("./routes");
const common = require("./common");

const Auth = {
  noTokenApis: ["/rest/v1/admin/user/login", "/rest/v1/upload", "/rest/v1/files/:fileId", "/rest/v1/websocket/send"],

  Authentication: (req, res, next) => {
    return true;
    // const route = common.matchRoute({ req, routes });
    // if (!Auth.noTokenApis.includes(route?.pathname)) {
    //   if (req.headers.authorization) {
    //     const token = req.headers?.authorization?.split(" ")?.[1] || "";
    //     return true;
    //   }
    // } else {
    //   return true;
    // }
  },
};

async function main() {
  const port = process.env.MOCK_PORT || 8080;
  const server = express();

  // for parsing application/json
  server.use(express.json({ limit: "1000mb" }));
  // for parsing application/x-www-form-urlencoded
  server.use(express.urlencoded({ limit: "1000mb", extended: true }));
  // for cors
  server.use(cors());

  server.all(/(.*)/, async (req, res, next) => {
    try {
      const authorized = Auth.Authentication(req, res, next);
      if (authorized) {
        const route = common.matchRoute({ req, routes });
        if (route.key && route.handler) {
          req.pathParams = common.getPathParams({ req, route });
          await route.handler(req, res, next);
        } else {
          res.status(404).json({ success: false, message: "Not Found" });
        }
      } else {
        res.status(401).end();
      }
    } catch (err) {
      console.error(err);
      res.status(500).json({ success: false, message: err.message });
    }
  });

  server.listen(port, "0.0.0.0", () => {
    console.log(`mock server start: http://localhost:${port}`);
  });

  // process.on("SIGUSR2", async () => {
  //   await new Promise((resolve) => {
  //     server.close(resolve);
  //   });
  //   process.kill(process.pid, "SIGTERM");
  // });
}

main().catch((err) => {
  console.error(err);
});
