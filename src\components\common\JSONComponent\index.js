import { useState, lazy, Suspense } from "react";
import useSWR from "swr";
import axios from "@/fetchers/request";
import { Spin } from "antd";
import Loading from "components/common/Loading";
import PropTypes from "prop-types";

const basicComponents = {
  Row: lazy(() => import("./components/Row")),
  Col: lazy(() => import("./components/Col")),
  Form: lazy(() => import("./components/Form")),
  Text: lazy(() => import("./components/Text")),
  Iframe: lazy(() => import("./components/Iframe")),
  Card: lazy(() => import("./components/Card")),
  Table: lazy(() => import("./components/Table")),
  LangTabs: lazy(() => import("./components/LangTabs")),
  Tree: lazy(() => import("components/common/Tree")),
  NativeTable: lazy(() => import("./components/NativeTable")),
  Collapse: lazy(() => import("./components/Collapse")),
  Gallery: lazy(() => import("./components/Gallery")),
  CameraPhotoUpload: lazy(() => import("@/components/common/CameraPhotoUpload")),
  QueryFilter: lazy(() => import("./components/QueryFilter")),
  TextEditable: lazy(() => import("./components/TextEditable")),
  ExternalPageEditor: lazy(() => import("components/business/ExternalPageEditor")),
  CommandTrigger: lazy(() => import("components/business/CommandTrigger")),
};

function JSONComponents(props) {
  const { data, extendComponents, extraData } = props;
  const { type = "json", fetcher } = data || {};
  const [loading, setLoading] = useState(false);
  const dataString = JSON.stringify(data);

  const { data: componentsData } = useSWR(dataString, async () => {
    if (type === "json") {
      return data;
    } else if (type === "api") {
      try {
        setLoading(true);
        const { url, ...options } = fetcher?.request;
        return await axios({
          url,
          method: "GET",
          ...options,
          data: { ...options.data },
        }).then((res) => res.data?.data?.content || {});
      } finally {
        setLoading(false);
      }
    }
  });

  return loading ? (
    <Loading loading={loading} />
  ) : (
    <>
      {componentsData?.children?.map((item, index) => {
        const Components = { ...basicComponents, ...extendComponents };
        const Component = Components[item?.component];

        return Component ? (
          <Suspense
            key={index}
            fallback={
              <div style={{ textAlign: "center" }}>
                <Spin />
              </div>
            }
          >
            <Component data={item} extendComponents={extendComponents} extraData={extraData} />
          </Suspense>
        ) : null;
      })}
    </>
  );
}

JSONComponents.propTypes = {
  data: PropTypes.object,
  extendComponents: PropTypes.object,
};

export default JSONComponents;
