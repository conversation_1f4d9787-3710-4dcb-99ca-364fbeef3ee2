import styles from "./index.module.scss";
import { useEffect, useRef } from "react";
import { fabric } from "fabric";
import Utils from "@/utils";
import FabricPropsEditor, { setActiveObjects } from "@/pages/pod/components/fabric-props-editor";
import podStore from "@/pages/pod/stores";
import Fetchers from "@/fetchers";
import Helper from "@/helpers";
import { FabricObjectType, defaultControlProps } from "@/pages/pod/common/init-fabric-types";
import { observer } from "mobx-react-lite";
import { createLayouts, editorHistory } from "@/pages/pod/components/fabric-props-editor/common";
import { FabricEvent } from "@/pages/pod/common";

function TemplateDesign(props) {
  const canvasBoxRef = useRef();
  const canvasRef = useRef();

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current };

  useEffect(() => {
    let canvas = null;
    let clearCanvasEvents = () => {};
    let handleWindowResize = () => {};

    (async () => {
      Helper.pageLoading(true);
      podStore.clear();
      editorHistory.reset();

      const query = Utils.getQueryParams(window.location.href);
      const template = await Fetchers.getPodTemplate({ id: query.id }).then((res) => res.data.data.item);
      podStore.setTemplate(template);

      const initZoom = template?.canvas?.zoom || 1;
      const initSize = {
        width: (template?.canvas?.width ?? template?.initWidth) / initZoom || 1000,
        height: (template?.canvas?.height ?? template?.initHeight) / initZoom || 1000,
      };
      const canvas = await Utils.sleep().then(() => {
        const fitSize = Utils.getAspectRatioScaleMinSize({
          width: template?.initWidth,
          height: template?.initHeight,
          maxWidth: canvasBoxRef.current?.clientWidth,
          maxHeight: canvasBoxRef.current?.clientHeight,
        });
        const canvas = new fabric.Canvas(canvasRef.current, {
          width: fitSize.width,
          height: fitSize.height,
          preserveObjectStacking: true,
        });
        console.log(canvas);
        canvas.setZoom(fitSize.width / initSize.width);
        canvas.extraData = { originalWidth: initSize.width, originalHeight: initSize.height };
        paramsRef.current.canvas = canvas;
        podStore.setCanvas(canvas);
        return canvas;
      });

      function updateObjectData(event) {
        canvas.getObjects().forEach((object) => {
          if (object.selectable) {
            object.set({ stroke: "#000" });
          } else {
            object.set({ stroke: "#f00" });
            const objects = canvas
              .getActiveObjects()
              .filter((item) => item.extraData.id !== event.target?.extraData.id);
            setActiveObjects({ canvas, objects, render: false });
          }
        });
        const activeObject = canvas.getActiveObject();
        if (activeObject?.type === FabricObjectType.ActiveSelection) {
          activeObject.extraData = { id: `activeSelection` };
        }
        const objects = canvas.getObjects().sort((a, b) => b.extraData.createTime - a.extraData.createTime);
        const layouts = createLayouts({ objects });
        podStore.setObjects(objects);
        podStore.setLayouts(layouts);
        podStore.setActiveObject(activeObject);
        canvas.renderAll();
      }

      const canvasEvents = {
        [FabricEvent.ObjectAdded]: (event) => {
          updateObjectData();
          const object = event.target;
          object.set({ ...defaultControlProps });
          if (!object.extraData) object.extraData = {};
          canvas.renderAll();
        },
        [FabricEvent.ObjectRemoved]: updateObjectData,
        [FabricEvent.ObjectModified]: updateObjectData,
        [FabricEvent.SelectChange]: (event) => {
          const layout = canvas.getActiveObjects()[0]?.extraData.layout;
          if (layout) {
            canvas.getObjects().forEach((object) => {
              if (object.extraData.layout && object.extraData.layout?.id !== layout.id) {
                object.set({ visible: false });
              } else {
                object.set({ visible: true });
              }
            });
            canvas.renderAll();
          }
          updateObjectData(event);
        },
        [FabricEvent.SelectionCreated]: updateObjectData,
        [FabricEvent.SelectionUpdated]: updateObjectData,
        [FabricEvent.SelectionCleared]: updateObjectData,
      };

      (function bindCanvasEvents() {
        Object.entries(canvasEvents).forEach(([name, handler]) => {
          canvas.on(name, handler);
        });
      })();

      clearCanvasEvents = function () {
        Object.entries(canvasEvents).forEach(([name, handler]) => {
          canvas.off(name, handler);
        });
      };

      handleWindowResize = async () => {
        await Utils.sleep(300);
        const { width, height } = Utils.getAspectRatioScaleMinSize({
          width: canvas.getElement().width,
          height: canvas.getElement().height,
          maxWidth: canvasBoxRef.current?.clientWidth,
          maxHeight: canvasBoxRef.current?.clientHeight,
        });
        canvas.setZoom(width / initSize.width);
        canvas.setDimensions({ width, height });
      };
      window.addEventListener("resize", handleWindowResize);
      handleWindowResize();

      if (template.canvas?.objects?.length > 0) {
        await new Promise((resolve) => {
          canvas.loadFromJSON(template.canvas, resolve);
        });
      }
      podStore.init();

      Helper.pageLoading(false);
    })();

    return function () {
      canvas?.dispose?.();
      clearCanvasEvents();
      window.removeEventListener("resize", handleWindowResize);
    };
  }, []);

  return (
    <div className={styles.page}>
      <div className={styles.content}>
        <div className={styles.left}>
          <div ref={canvasBoxRef} className={styles.canvasBox}>
            <div className={styles.canvasWrapper}>
              <canvas ref={canvasRef} width={0} height={0}></canvas>
            </div>
          </div>
        </div>
        <div className={styles.right}>
          <FabricPropsEditor></FabricPropsEditor>
        </div>
      </div>
    </div>
  );
}

TemplateDesign = observer(TemplateDesign);

export default TemplateDesign;
