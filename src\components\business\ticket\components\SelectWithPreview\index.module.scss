.selectWrapper {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  position: relative;

  .addonBeforeText {
    height: 100%;
    background-color: rgba(0, 0, 0, 0.02);
    border: 1px solid #d9d9d9;
    border-right: none;
    line-height: 30px;
    padding: 0 11px;
  }

  [class*="Select_container"] {
    flex: 1;
  }
}

.previewTemplate {
  display: none;
  width: 800px;
  max-height: 100vh;
  overflow-y: auto;
  padding: 20px;
  border: 1px solid #000;
  background-color: #fff;
  position: fixed;
  z-index: 1999;

  img {
    max-width: 100%;
  }
}

.previewTemplateOpen {
  display: block;
}
