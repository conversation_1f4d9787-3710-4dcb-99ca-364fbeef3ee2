import { makeAutoObservable, reaction, when } from "mobx";
import config from "./config";
import TokenHelper from "@/helpers/token.helper";
let ws;
let retryCount = 0;

const Enums = Object.freeze({
  WebSocket: {
    Status: {
      Disconnected: 0,
      Connecting: 1,
      Connected: 2,
      Retrying: 3,
    },
    MessageType: {
      Event: "event",
      Request: "request",
      Response: "response",
    },
    Event: {
      Open: "MyWebSocketOpen",
      Error: "MyWebSocketError",
      Message: "MyWebSocketMessage",
      Close: "MyWebSocketClose",
      StatusChange: "MyWebSocketStatusChange",
    },
  },
});

const Events = [];
const Utils = Object.freeze({
  get uniqueId() {
    const hash = () => (+Math.random().toString().substring(2)).toString(16);
    return (hash() + hash() + hash() + hash()).substring(0, 40);
  },

  JSON: {
    parse(str, defaultValue = {}) {
      try {
        return JSON.parse(str) || defaultValue;
      } catch (err) {
        return defaultValue;
      }
    },
    stringify(data, defaultValue = "") {
      try {
        return JSON.stringify(data);
      } catch (err) {
        return defaultValue;
      }
    },
  },

  addEventListener(name, handler, id = Utils.uniqueId) {
    Events.push({ name, handler, id });
  },

  removeEventListener(name, handler) {
    let arr;
    if (typeof handler === "string") {
      arr = Events.filter((item) => !(item.name === name && item.id === handler));
    } else {
      arr = Events.filter((item) => !(item.name === name && item.handler === handler));
    }
    Events.length = 0;
    Events.push(...arr);
  },

  dispatchEvent(name, ...args) {
    const events = Events.filter((item) => item.name === name);
    for (const event of events) {
      event.handler(...args);
    }
  },

  async executeEvent(name, ...args) {
    const events = Events.filter((item) => item.name === name);
    return await Promise.all([...events.map((event) => event?.handler(...args))]);
  },
});

const store = makeAutoObservable({
  status: Enums.WebSocket.Status.Disconnected,

  setStatus(status) {
    this.status = status;
  },
});

let removeStatusListener = () => {};

const MyWebSocket = Object.freeze({
  DISCONNECTED: Enums.WebSocket.Status.Disconnected,
  CONNECTING: Enums.WebSocket.Status.Connecting,
  CONNECTED: Enums.WebSocket.Status.Connected,
  RETRYING: Enums.WebSocket.Status.Retrying,

  Event: Enums.WebSocket.Event,

  get status() {
    return store.status;
  },

  get user() {
    const storageKey = "ERPWebsocketUserId";
    let user = localStorage.getItem(storageKey);
    if (!user) {
      const tokenPayload = TokenHelper.getTokenPayload();
      user = tokenPayload?.uid || Utils.uniqueId.substring(0, 16);
      localStorage.setItem(storageKey, user);
    }
    return user;
  },

  addEventListener(...args) {
    Utils.addEventListener(...args);
  },

  removeEventListener(...args) {
    Utils.removeEventListener(...args);
  },

  connect({ isRetry = false } = {}) {
    return new Promise(async (resolve) => {
      if (this.status === Enums.WebSocket.Status.Connecting || ws?.readyState === WebSocket.OPEN) {
        return resolve({ success: false });
      }

      const urlObj = new URL(`wss://message.cnzlerp.com/websocket`);
      Object.entries({ app: config.app, group: "frontend", user: this.user }).forEach(([key, value]) => {
        urlObj.searchParams.set(key, value);
      });
      const url = urlObj.toString();

      if (ws && ws?.readyState === WebSocket.OPEN) this.disconnect();
      ws = new WebSocket(url);

      removeStatusListener();
      removeStatusListener = reaction(
        () => store.status,
        (status) => {
          Utils.dispatchEvent(Enums.WebSocket.Event.StatusChange, status);
        }
      );

      ws.onopen = (event) => {
        store.setStatus(Enums.WebSocket.Status.Connected);
        retryCount = 0;
        Utils.dispatchEvent(Enums.WebSocket.Event.Open, event);
        resolve({ success: true });
      };

      ws.onerror = (event) => {
        Utils.dispatchEvent(Enums.WebSocket.Event.Error, event);
        resolve({ success: false });
      };

      ws.onclose = (event) => {
        store.setStatus(Enums.WebSocket.Status.Disconnected);
        if (event.code < 4000) {
          // 断开需要重试 1006:断开连接/断网
          if (
            retryCount <= config.retry.max &&
            ![Enums.WebSocket.Status.Connecting, Enums.WebSocket.Status.Retrying].includes(this.status)
          ) {
            store.setStatus(Enums.WebSocket.Status.Retrying);
            setTimeout(async () => {
              retryCount++;
              this.connect({ isRetry: true }).catch(() => {});
            }, config.retry.delay);
          }
        }
        Utils.dispatchEvent(Enums.WebSocket.Event.Close, event);
      };

      ws.onmessage = (event) => {
        Utils.dispatchEvent(Enums.WebSocket.Event.Message, event);
      };

      if (!isRetry) {
        window.addEventListener("online", () => {
          retryCount = 0;
          this.connect({ isRetry: true });
        });
      }
    });
  },

  disconnect({ code = 4000, reason = "" } = {}) {
    if (code < 4000) throw new Error("Close code cannot be less than 4000");
    ws.close(code, reason);
  },

  async send({ to, data }) {
    await when(() => store.status === Enums.WebSocket.Status.Connected);
    ws.send(
      Utils.JSON.stringify({
        action: "send",
        to: { ...to, app: config.app },
        data,
      })
    );
  },
});

export default MyWebSocket;
