import axios from "./request";
import Api from "./api";
import Utils from "@/utils";
import MD5 from "crypto-js/md5";
import Axios from "axios";
import tools from "./tools";

const Fetchers = Object.freeze({
  Api,

  getMenuItems() {
    return axios.get(Api.getMenuItems);
  },

  getSystemInfo() {
    return axios.get(Api.getSystemInfo);
  },

  login({ email, password }) {
    return axios.post(Api.login, { email, password });
  },

  getToken({ code }) {
    return axios.post(Api.getToken, { code });
  },

  getListPageData({ url_key, params } = {}) {
    const url = tools.setPathParams(Api.getListPageData, { url_key });
    return axios.get(url, { params });
  },

  getListPageHeader({ url_key }) {
    const url = tools.setPathParams(Api.getListPageHeader, { url_key });
    return axios.get(url);
  },

  getEditPageData({ url_key }) {
    const url = tools.setPathParams(Api.getEditPageData, { url_key });
    return axios.get(url);
  },

  getDetailPageData({ url_key }) {
    const url = tools.setPathParams(Api.getDetailPageData, { url_key });
    return axios.get(url);
  },

  getSearchDetailPageData({ url_key, params, data, method = "get" }) {
    const url = tools.setPathParams(Api.getSearchDetailPageData, { url_key });
    method = method.toLowerCase();

    if (method === "get") {
      return axios.get(url, { params });
    }

    // 其他请求方法（POST, PUT等）
    return axios[method](url, data, { params });
  },

  saveEditPageData({ url_key, data }) {
    const url = tools.setPathParams(Api.saveEditPageData, { url_key });
    return axios.post(url, data);
  },

  getEditPopupData({ url_key }) {
    const url = tools.setPathParams(Api.getEditPopupData, { url_key });
    return axios.get(url);
  },

  saveEditPopupData({ url_key, data }) {
    const url = tools.setPathParams(Api.saveEditPopupData, { url_key });
    return axios.post(url, data);
  },

  getRestockOrderCountIn({ params } = {}) {
    const url = tools.setPathParams(Api.getRestockOrderCountIn, {});
    return axios.get(url, { params });
  },

  restockOrderCountInVerify({ data } = {}) {
    const url = tools.setPathParams(Api.restockOrderCountInVerify, {});
    return axios.post(url, data);
  },

  restockOrderCountInConfirm({ data } = {}) {
    const url = tools.setPathParams(Api.restockOrderCountInConfirm, {});
    return axios.post(url, data);
  },

  restockOrderItemPrintLabel({ data, ids } = {}) {
    const url = tools.setPathParams(Api.restockOrderItemPrintLabel, {});
    return axios.post(url, data, { params: { ids } });
  },

  restockGetQcProductInfo({ data } = {}) {
    const url = tools.setPathParams(Api.restockGetQcProductInfo, {});
    return axios.post(url, data);
  },

  restockQualityControlVerify({ data } = {}) {
    const url = tools.setPathParams(Api.restockQualityControlVerify, {});
    return axios.post(url, data);
  },

  restockQualityControlConfirm({ data } = {}) {
    const url = tools.setPathParams(Api.restockQualityControlConfirm, {});
    return axios.post(url, data);
  },

  getConfigurationList({ url_key }) {
    const url = tools.setPathParams(Api.getConfigurationList, { url_key });
    return axios.get(url);
  },

  getShipOutOrderInfo({ params } = {}) {
    const url = tools.setPathParams(Api.getShipOutOrderInfo, {});
    return axios.get(url, { params });
  },

  getShipOutProductDetail({ params } = {}) {
    const url = tools.setPathParams(Api.getShipOutProductDetail, {});
    return axios.get(url, { params });
  },

  shipOrder({ data } = {}) {
    const url = tools.setPathParams(Api.shipOrder, {});
    return axios.post(url, data);
  },

  offlineShip({ data } = {}) {
    const url = tools.setPathParams(Api.offlineShip, {});
    return axios.post(url, data);
  },

  shipOrderRepair({ data } = {}) {
    const url = tools.setPathParams(Api.shipOrderRepair, {});
    return axios.post(url, data);
  },

  orderPrintLabel({ params } = {}) {
    const url = tools.setPathParams(Api.orderPrintLabel, {});
    return axios.get(url, { params });
  },

  orderPrintCard({ params } = {}) {
    const url = tools.setPathParams(Api.orderPrintCard, {});
    return axios.get(url, { params });
  },

  orderPrintGpsrLabel({ params } = {}) {
    const url = tools.setPathParams(Api.orderPrintGpsrLabel, {});
    return axios.get(url, { params });
  },

  orderRestore({ params } = {}) {
    const url = tools.setPathParams(Api.orderRestore, {});
    return axios.get(url, { params });
  },

  getOrderDetail({ params }) {
    const url = tools.setPathParams(Api.getOrderDetail, {});
    return axios.get(url, { params });
  },

  getTicketDetail({ params }) {
    const url = tools.setPathParams(Api.getTicketDetail, {});
    return axios.get(url, { params });
  },

  getIncrementDetail({ params }) {
    const url = tools.setPathParams(Api.getIncrementDetail, {});
    return axios.get(url, { params });
  },

  getVersion() {
    const url = `${window.location.origin}/app.json?v=${new Date().getTime()}`;
    return Axios.create({}).get(url);
  },

  async checkUploadFileExists({ file }) {
    const dataURL = await new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = function (e) {
        resolve(e.target.result);
      };
      reader.readAsDataURL(file);
    });
    const contentHash = MD5(dataURL).toString();
    const url = tools.setPathParams(Api.checkUploadFileExists, { contentHash });
    return await axios.get(url);
  },

  async uploadFile({ file, host = "", headers }) {
    // const dataURL = await new Promise((resolve) => {
    //   const reader = new FileReader();
    //   reader.onload = function (e) {
    //     resolve(e.target.result);
    //   };
    //   reader.readAsDataURL(file);
    // });
    // const contentHash = MD5(dataURL).toString();
    // const url = Utils.setQueryParams(Api.uploadFile, { hash: contentHash });
    return await axios.post(
      Api.uploadFile,
      tools.toFormData({
        data: {
          file,
          file_type: file?.type,
          file_size: file?.size,
          file_size_humanized: Utils.humanize.bytes.stringify(file?.size),
          host,
        },
      }),
      {
        headers: {
          ...headers,
          "Content-Type": "multipart/form-data;charset=UTF-8",
        },
      }
    );
  },

  async getPodLibraries({ type, page, pageSize }) {
    const url = tools.setPathParams(Api.getPodLibraries, {});
    return await axios.get(url, { params: { type, page, pageSize } });
  },

  async addPodLibrary({ data }) {
    const url = tools.setPathParams(Api.createPodLibrary, {});
    return await axios.post(url, data);
  },

  async deletePodLibrary({ id }) {
    const url = tools.setPathParams(Api.deletePodLibrary, { id });
    return await axios.delete(url);
  },

  async updatePodLibrary({ id, data }) {
    const url = tools.setPathParams(Api.updatePodLibrary, { id });
    return await axios.patch(url, { data });
  },

  async getPodLibrary({ id }) {
    const url = tools.setPathParams(Api.getPodLibrary, { id });
    return await axios.get(url);
  },

  async addPodLibraryCategory(category) {
    const url = tools.setPathParams(Api.addPodLibraryCategory, {});
    return await axios.post(url, category);
  },

  async deletePodLibraryCategory({ id }) {
    const url = tools.setPathParams(Api.deletePodLibraryCategory, { id });
    return await axios.delete(url);
  },

  async getPodLibraryCategory({ id, page, pageSize }) {
    const url = tools.setPathParams(Api.getPodLibraryCategory, { id });
    return await axios.get(url, { params: { page, pageSize } });
  },

  async getPodLibraryCategories({ query }) {
    const url = tools.setPathParams(Api.getPodLibraryCategories);
    return await axios.get(url, { params: query });
  },

  async addPodLibraryItems({ library_id, category_id, items, cover }) {
    const url = tools.setPathParams(Api.addPodLibraryItems, {});
    return await axios.post(url, { library_id, category_id, items, cover });
  },

  async deletePodLibraryItem({ id }) {
    const url = tools.setPathParams(Api.deletePodLibraryItem, { id });
    return await axios.delete(url);
  },

  async deletePodLibraryItems({ ids }) {
    const url = tools.setPathParams(Api.deletePodLibraryItems, {});
    return await axios.delete(url, { data: { ids } });
  },

  async updatePodLibraryItem({ id, data }) {
    const url = tools.setPathParams(Api.updatePodLibraryItem, { id });
    return await axios.patch(url, { data });
  },

  async sortPodLibraryItems(data) {
    const url = tools.setPathParams(Api.sortPodLibraryItems, {});
    return await axios.patch(url, { action: "sort", data });
  },

  async getPodLibraryItems({ query }) {
    const url = tools.setPathParams(Api.getPodLibraryItems, {});
    return await axios.get(url, { params: query });
  },

  async getPodTemplates({ query } = {}) {
    const url = tools.setPathParams(Api.getPodTemplates, {});
    return await axios.get(url, { params: query });
  },

  async createPodTemplate(data) {
    const url = tools.setPathParams(Api.createPodTemplate, {});
    return await axios.post(url, { data });
  },

  async deletePodTemplate({ id }) {
    const url = tools.setPathParams(Api.deletePodTemplate, { id });
    return await axios.delete(url);
  },

  async getPodTemplate({ id }) {
    const url = tools.setPathParams(Api.getPodTemplate, { id });
    return await axios.get(url);
  },

  async updatePodTemplate({ id, data }) {
    const url = tools.setPathParams(Api.updatePodTemplate, { id });
    return await axios.patch(url, { data });
  },

  async addPodOptionSet({ template_id, product_id, controls, title, create_from_template, linked_to_product }) {
    const url = tools.setPathParams(Api.addPodOptionSet, {});
    return await axios.post(url, { template_id, product_id, controls, title, create_from_template, linked_to_product });
  },

  async updatePodOptionSet({ id, data }) {
    const url = tools.setPathParams(Api.updatePodOptionSet, { id });
    return await axios.patch(url, { data });
  },

  async getPodTemplateOptionSet({ template_id }) {
    const url = tools.setPathParams(Api.getPodTemplateOptionSet, { templateId: template_id });
    return await axios.get(url);
  },

  async getPodOptionSets({ query } = {}) {
    const url = tools.setPathParams(Api.getPodOptionSets, {});
    return await axios.get(url, { params: query });
  },

  async deletePodOptionSet({ id }) {
    const url = tools.setPathParams(Api.deletePodOptionSet, { id });
    return await axios.delete(url);
  },

  async getPodOptionSet({ id }) {
    const url = tools.setPathParams(Api.getPodOptionSet, { id });
    return await axios.get(url);
  },

  async searchPodI18n({ search }) {
    const url = tools.setPathParams(Api.searchPodI18n);
    return await axios.get(url, { params: { search } });
  },
  getTicketUpdate({ params }) {
    const url = tools.setPathParams(Api.getTicketUpdate, {});
    return axios.get(url, { params });
  },

  getDownloadList({ params } = {}) {
    const url = tools.setPathParams(Api.getDownloadList, {});
    return axios.get(url, { params });
  },

  deleteDownloadFile({ id }) {
    const url = tools.setPathParams(Api.deleteDownloadFile, { id });
    return axios.delete(url);
  },

  getGlobalMessageQuery({ params } = {}) {
    const url = tools.setPathParams(Api.getGlobalMessageQuery, {});
    return axios.get(url, { params });
  },

  setGlobalMessageResolved({ data } = {}) {
    const url = tools.setPathParams(Api.setGlobalMessageResolved, {});
    return axios.post(url, data);
  },

  getPmsProductEdit({ params = {} }) {
    const url = tools.setPathParams(Api.getPmsProductEdit, {});
    return axios.get(url, { params });
  },

  getCustomizeDictionaryOption({ params = {} }) {
    const url = tools.setPathParams(Api.getCustomizeDictionaryOption, {});
    return axios.get(url, { params });
  },

  getDictionary({ params = {} }) {
    const url = tools.setPathParams(Api.getDictionary, {});
    return axios.get(url, { params });
  },

  getOtherPenaltyDetail({ params }) {
    const url = tools.setPathParams(Api.getOtherPenaltyDetail, {});
    return axios.get(url, { params });
  },

  getOtherPenaltyChangeDate({ data } = {}) {
    const url = tools.setPathParams(Api.getOtherPenaltyChangeDate, {});
    return axios.post(url, data);
  },

  getTemplateProperty({ params }) {
    const url = tools.setPathParams(Api.getTemplateProperty, {});
    return axios.get(url, { params });
  },

  getPageInfo({ params }) {
    const url = tools.setPathParams(Api.getPageInfo, {});
    return axios.get(url, { params });
  },

  getIframePageData({ url_key }) {
    const url = tools.setPathParams(Api.getIframePageData, { url_key });
    return axios.get(url);
  },

  get3dModelData({ id } = {}) {
    if (!id) throw Error("id is required");
    const url = tools.setPathParams(Api.get3dModelData, { id });
    return axios.get(url);
  },

  save3dModelData({ id, data } = {}) {
    if (!id) throw Error("id is required");
    const url = tools.setPathParams(Api.save3dModelData, { id });
    return axios.patch(url, data);
  },

  get3dProductData({ data_id }) {
    if (!data_id) throw Error("data_id is required");
    const url = tools.setPathParams(Api.get3dProductData, { data_id });
    return axios.get(url);
  },

  async save3dProductImages({ data }) {
    const url = tools.setPathParams(Api.save3dProductImages, {});
    return await axios.post(url, data);
  },

  getI18nResource({ lang }) {
    const url = tools.setPathParams(Api.getI18nResource, { lang });
    return axios.get(url);
  },
});

const ApiTools = tools;

export default Fetchers;
export { ApiTools };
export { Api, tools, axios };
