import axios from "fetchers/request";
import Cookies from "js-cookie";
import Enums from "enums";
import apiTools from "@/fetchers/tools";

/**
 *
 * @param {Object} baseConfig 基础配置
 * @param {string} uploadApi 上传api地址
 * @returns {Object} 编辑器配置
 */
export const getEditorConfig = (baseConfig, uploadApi) => {
  const editorConfig = { ...baseConfig };

  if (uploadApi) {
    const removePlugins = [...(editorConfig.removePlugins || [])];
    if (!removePlugins.includes("Base64UploadAdapter")) {
      removePlugins.push("Base64UploadAdapter");
    }

    editorConfig.simpleUpload = {
      uploadUrl: uploadApi,
    };

    editorConfig.removePlugins = removePlugins;
  }

  return editorConfig;
};

/**
 * 文件上传适配器
 * @param {string} uploadApi 上传api地址
 * @returns {Object} 适配器实例
 */
export const createUploadAdapter = (uploadConfig) => (loader) => ({
  upload: () => {
    const { uploadApi, data } = uploadConfig;

    return new Promise((resolve, reject) => {
      loader.file.then(async (file) => {
        try {
          const result = await axios
            .post(
              uploadApi,
              apiTools.toFormData({
                data: {
                  ...data,
                  file,
                },
              }),
              {
                headers: {
                  "Content-Type": "multipart/form-data;charset=UTF-8",
                  Authorization: `Bearer ${Cookies.get()[Enums.CookieName.Token]}`,
                },
              }
            )
            .then((res) => res.data);

          if (result && result.data) {
            const imageUrl = result?.data?.host + result?.data?.image?.src;
            resolve({
              default: imageUrl,
            });
          } else {
            reject("上传失败");
          }
        } catch (error) {
          reject(error);
        }
      });
    });
  },

  abort: () => {
    // 取消上传
  },
});
