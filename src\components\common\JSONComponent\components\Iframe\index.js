import Loading from "components/common/Loading";
import styles from "./index.module.scss";
import { useState } from "react";

function Iframe(props) {
  const { data, title = "iframe" } = props;
  const { height, onLoad, ...iframeProps } = data?.props ?? {};
  const [loading, setLoading] = useState(true);

  function handleLoad() {
    onLoad?.();
    setLoading(false);
    // window.parent.document.querySelectorAll("iframe")[0].height = 0;
    // window.parent.document.querySelectorAll("iframe")[0].height = document.body.scrollHeight;
    // setTimeout(() => {
    //   window.parent.document.querySelectorAll("iframe")[0].height =
    //     window.parent.document.querySelectorAll("iframe")[0].contentWindow.document.documentElement.scrollHeight;
    // });
  }

  return (
    <Loading loading={loading}>
      <iframe
        className={styles.iframe}
        title={title}
        {...iframeProps}
        style={{ height, minHeight: height }}
        onLoad={handleLoad}
      ></iframe>
      {/* <div style={{ position: "relative", width: "100%", paddingTop: "calc(100% * 720 / 1280)" }}>
        <iframe className={styles.iframe} title={title} {...data?.props} onLoad={onLoad}></iframe>
      </div> */}
    </Loading>
  );
}

export default Iframe;
