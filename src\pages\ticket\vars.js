import SubmitButton from "components/common/Button";
import Icon from "components/common/Icon";

export function renderActions({ actions, onClick }) {
  return actions?.map((item, index) => {
    return (
      <SubmitButton
        key={index}
        type="primary"
        {...item.props}
        icon={item.props.icon ? <Icon src={item.props.icon} /> : null}
        command={item.command}
        onClick={async (e) => {
          await onClick(item);
        }}
      >
        {item.title}
      </SubmitButton>
    );
  });
}
