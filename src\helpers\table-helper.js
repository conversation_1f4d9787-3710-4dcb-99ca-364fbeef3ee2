import moment from "moment";
import ResponsiveBox from "@/components/ResponsiveBox";
import { Button, ColorPicker, Image, Input } from "antd";
import Editable from "@/components/Editable";
import { axios, tools } from "@/fetchers";
import I18nEditable from "@/pages/pod/components/i18n-editable";
import { CheckOutlined, EditOutlined } from "@ant-design/icons";
import Popover from "@/pages/pod/components/popover";
import Helper from "@/helpers/index";
import Utils from "@/utils";

const TableHelper = Object.freeze({
  createColumnsRender({ columns, reloadData }) {
    const handleEditableSave = ({ column, item, requestData }) => {
      const { url, method } = column.editable.request;
      return axios({
        url: tools.setPathParams(url, { id: item.id }),
        method: method.toLowerCase(),
        data: { data: requestData },
      })
        .then((res) => {
          return { success: res.data.success };
        })
        .catch((err) => {
          Helper.openMessage({ type: "error", content: err.message });
          return { success: false };
        });
    };
    const createEditable = ({ column, text, item }) => {
      return (
        <Editable
          onSave={({ value }) => {
            handleEditableSave({ column, item, requestData: { [column.dataIndex]: value } });
          }}
        >
          {text}
        </Editable>
      );
    };
    columns?.forEach?.((column) => {
      if (column.valueType === "datetime") {
        column.render = (text, item, index) => {
          return moment(text).format(column.format);
        };
      } else if (column.valueType === "image") {
        const key = column.dataIndex;
        column.render = (text, item, index) => {
          const src = item[key]?.src || "";
          let input, popover;
          return (
            <div style={{ display: "flex", alignItems: "center", gap: 2 }}>
              <div style={{ width: 40 }} className="ant-image-no-preview-text ant-image-responsive">
                <ResponsiveBox width={40} height={40}>
                  <Image {...column.imageProps} src={src}></Image>
                </ResponsiveBox>
              </div>
              {column.editable ? (
                <Popover
                  ref={(ref) => {
                    popover = ref;
                  }}
                  content={
                    <div style={{ display: "flex" }}>
                      <div>
                        <Input
                          ref={(ref) => {
                            if (ref) {
                              input = ref.input;
                            }
                          }}
                          defaultValue={src}
                          style={{ width: 500 }}
                        ></Input>
                      </div>
                      <div>
                        <Button
                          icon={<CheckOutlined />}
                          onClick={async () => {
                            popover.setOpen(false);
                            const image = await Helper.createImage({ src: input.value });
                            handleEditableSave({
                              column,
                              item,
                              requestData: {
                                [column.dataIndex]: { src: image.src, width: image.width, height: image.height },
                              },
                            })
                              .then(() => {
                                Helper.openMessage({ type: "success", content: "保存成功！" });
                                reloadData?.();
                              })
                              .catch(() => {
                                Helper.openMessage({ type: "error", content: "保存失败！" });
                              });
                          }}
                        ></Button>
                      </div>
                    </div>
                  }
                  trigger="click"
                >
                  <span style={{ cursor: "pointer" }}>
                    <EditOutlined />
                  </span>
                </Popover>
              ) : null}
            </div>
          );
        };
      } else if (column.valueType === "withImage") {
        column.render = (text, item, index) => {
          return (
            <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
              <div
                style={{ width: 40, border: `1px solid #ddd` }}
                className="ant-image-no-preview-text ant-image-responsive"
              >
                <ResponsiveBox width={40} height={40}>
                  <Image {...column.imageProps} src={item?.[column.imageDataKey]?.src || ""}></Image>
                </ResponsiveBox>
              </div>
              <div>{column.editable ? createEditable({ column, text, item }) : text}</div>
            </div>
          );
        };
      } else if (column.valueType === "boolean") {
        column.render = (text, item, index) => {
          return text?.toString();
        };
      } else if (column.valueType === "color") {
        column.render = (text, item, index) => {
          return (
            <div style={{ pointerEvents: "none" }}>
              <ColorPicker value={text} showText={(color) => color.toRgbString()} readOnly></ColorPicker>
            </div>
          );
        };
      } else if (column.valueType === "i18n_editable") {
        column.render = (text, item, index) => {
          return (
            <div>
              <I18nEditable
                onSave={({ value, option }) => {
                  handleEditableSave({
                    column,
                    item,
                    requestData: { [column.dataIndex]: value, [`${column.dataIndex}_i18n_key`]: option.value },
                  });
                }}
              >
                {text}
              </I18nEditable>
            </div>
          );
        };
      }
    });
  },

  onChange(pagination, filters, sorter, extra) {
    const url = window.location.href.replace(window.location.origin, "");
    const { current: page, pageSize } = pagination;
    Helper.navigate({ url: Utils.setQueryParams(url, { page, pageSize }) });
  },
});

export default TableHelper;
