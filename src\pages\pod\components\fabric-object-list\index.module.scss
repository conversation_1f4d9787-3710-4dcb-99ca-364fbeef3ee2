.layers {
  --item-height: 40px;

  > .item {
    display: flex;
    gap: 20px;
    height: var(--item-height);
    align-items: center;
    padding-inline: 16px;
    cursor: pointer;

    > :nth-child(2) {
      flex: 1;
    }

    > :nth-child(3) {
      width: 35px;
      overflow: hidden;
      white-space: nowrap;
    }

    &.lock {
      background-color: #ddd;
      color: #999;
    }
  }

  .layerActions {
    display: flex;

    > * {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }

  .groupItems {
    .groupName {
      height: var(--item-height);
      display: flex;
      align-items: center;
      padding-inline: 16px;
      cursor: pointer;
    }

    > [class~="fabric-object-list"] {
      padding-inline-start: 16px;
    }
  }

  .selected {
    color: #fff;
    background-color: var(--ant-primary-color);

    .icon {
      color: #fff !important;
    }
  }
}
