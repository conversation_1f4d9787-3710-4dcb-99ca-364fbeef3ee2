import styles from "./index.module.scss";
import Loading from "components/common/Loading";
import Fetchers from "@/fetchers";
import Helper from "@/helpers";
import { useLocation, useParams, useNavigate } from "react-router-dom";
import Breadcrumbs from "components/common/Breadcrumbs";
import { But<PERSON>, Card } from "antd";
import JSONComponent from "components/common/JSONComponent";
import SubmitButton from "components/common/Button";
import { useEffect, useRef, useState } from "react";
import classNames from "classnames";

function EditPage(props) {
  const location = useLocation();
  const params = useParams();
  const navigate = useNavigate();
  const [data, setData] = useState({});
  const [saveLoading, setSaveLoading] = useState(false);
  const formRef = useRef();
  const url_key = Helper.getUrlKey({ location, params });
  const isInsideIframe = Helper.isInsideIframe();

  function handleBackClick() {
    navigate(-1);
  }

  async function handleSubmit() {
    // formRef.current?.submit();
    Helper.commandHandler({ command: { type: "submit", id: "form" } });
  }

  async function handleSave(values) {
    try {
      setSaveLoading(true);
      await Fetchers.saveEditPageData({ url_key, data: values }).then((res) => res.data?.data);
      setSaveLoading(false);
    } catch (error) {
      setSaveLoading(false);
    }
  }

  useEffect(() => {
    (async () => {
      try {
        Helper.pageLoading(true);
        setData({});
        const result = await Fetchers.getEditPageData({ url_key }).then((res) => res.data);
        setData(result?.data);
        // Promise.resolve().then(() => {
        //   formRef.current?.setFieldsValue(result?.data?.form?.props?.initialValues);
        // });
      } finally {
        Helper.pageLoading(false);
      }
    })();
  }, [url_key]);

  return (
    <div className={styles.editPage}>
      <div className={classNames("page-header", { [styles.pageHeader]: isInsideIframe })}>
        {!isInsideIframe ? (
          <div>
            <Breadcrumbs data={data?.breadcrumbs}></Breadcrumbs>
          </div>
        ) : null}

        <div className="page-header-actions">
          {!isInsideIframe && (
            <Button type="default" onClick={handleBackClick}>
              返回
            </Button>
          )}

          {/* <SubmitButton type="primary" onClick={handleSubmit}>
            保存
          </SubmitButton> */}
          {data?.actions?.map((item, index) => {
            return (
              <SubmitButton
                key={index}
                type="primary"
                {...item.props}
                command={item.command}
                onClick={async () => {
                  await Helper.commandHandler({ command: item.command });
                }}
              >
                {item.title}
              </SubmitButton>
            );
          })}
        </div>
      </div>
      <div className={styles.content}>
        <Card size="small">{data?.content ? <JSONComponent data={data?.content} /> : null}</Card>
      </div>
    </div>
  );
}

export default EditPage;
