import styles from "./index.module.scss";
import Fetchers from "@/fetchers";
import Helper from "@/helpers";
import J<PERSON>NComponent from "components/common/JSONComponent";
import { useLocation, useParams, useNavigate } from "react-router-dom";
import Breadcrumbs from "components/common/Breadcrumbs";
import { Button, Card } from "antd";
import SubmitButton from "components/common/Button";
import { useEffect, useRef, useState } from "react";
import classNames from "classnames";
import axiosRequest from "@/fetchers/request";
import Utils from "@/utils";
import { afterRender } from "functions";

function EditPage() {
  const location = useLocation();
  const params = useParams();
  const navigate = useNavigate();
  const [data, setData] = useState({});
  const [initialValues, setInitialValues] = useState(null);
  const paramsRef = useRef();
  const url_key = Helper.getUrlKey({ location, params });
  const isInsideIframe = Helper.isInsideIframe();

  paramsRef.current = {
    fetchPageData,
    fetchDynamicData,
  };

  async function commandRequest({ request, values }) {
    const { url, ...options } = request;
    return axiosRequest({
      url,
      method: "POST",
      ...options,
      data: { ...options.data, ...values },
    });
  }

  function handleBackClick() {
    navigate(-1);
  }

  async function fetchPageData() {
    try {
      Helper.pageLoading(true);
      setData({});
      const result = await Fetchers.getEditPageData({ url_key }).then((res) => res.data);
      setData(result?.data);

      if (result.data?.dynamicDataApi) {
        await fetchDynamicData(result.data.dynamicDataApi);
      }
    } finally {
      Helper.pageLoading(false);
    }
  }

  async function fetchDynamicData(api) {
    try {
      const result = await commandRequest({
        request: { url: api, method: "GET", params: Utils.getQueryParams(location.search) },
      }).then((res) => res.data);

      if (result?.success) {
        afterRender(() => {
          setInitialValues(result.data?.initialValues);
        });
      }
    } finally {
      Helper.pageLoading(false);
    }
  }

  useEffect(() => {
    const { fetchPageData } = paramsRef.current;
    fetchPageData();
  }, [url_key]);

  return (
    <div className={styles.editPage}>
      <div className={classNames("page-header", { [styles.pageHeader]: isInsideIframe })}>
        {!isInsideIframe ? (
          <div>
            <Breadcrumbs data={data?.breadcrumbs}></Breadcrumbs>
          </div>
        ) : null}

        <div className="page-header-actions">
          {!isInsideIframe && (
            <Button type="default" onClick={handleBackClick}>
              返回
            </Button>
          )}

          {data?.actions?.map((item, index) => {
            return (
              <SubmitButton
                key={index}
                type="primary"
                {...item.props}
                command={item.command}
                onClick={async () => {
                  await Helper.commandHandler({ command: item.command });
                }}
              >
                {item.title}
              </SubmitButton>
            );
          })}
        </div>
      </div>
      <div className={styles.content}>
        <Card size="small">
          {data?.content ? <JSONComponent data={data.content} initialValues={initialValues} /> : null}
        </Card>
      </div>
    </div>
  );
}

export default EditPage;
