.container {
  .header,
  .footer {
    border: 1px solid #dee2e6;
    padding: 8px 5px;
  }

  .header {
    border-bottom: none;
    border-radius: 8px 8px 0 0;
  }

  .footer {
    border-top: none;
    border-radius: 0 0 8px 8px;
  }

  table {
    width: 100%;
    border: 1px solid #dee2e6;
    background-color: transparent;
    border-collapse: collapse;

    th,
    td {
      text-align: left;
      border: 1px solid #dee2e6;
      padding: 5px;
      position: relative;

      &:hover {
        .copyableText {
          display: flex;
          opacity: 1;
        }
      }

      .copyableText {
        position: absolute;
        width: 20px;
        background-color: #fff;
        border-radius: 5px;
        top: 5px;
        left: 0;
        display: none;
        opacity: 0;
        align-items: center;
        justify-content: center;

        [class~="ant-typography-copy"] {
          margin-inline-start: 0px;
        }
      }
    }

    td img {
      width: 100%;
    }

    .imageGroupWrapper {
      display: flex;
      align-items: flex-start;
      flex-wrap: wrap;
      gap: 5px;

      .imageWrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        // & + .imageWrapper {
        //   margin-left: 5px;
        // }

        .imageText {
          background-color: #000;
          color: #fff;
          padding: 0 3px;
          margin-top: 5px;
        }
      }
    }

    .editIcon,
    .command {
      padding: 2px;
      color: var(--ant-primary-color);
      cursor: pointer;
    }
  }
}
