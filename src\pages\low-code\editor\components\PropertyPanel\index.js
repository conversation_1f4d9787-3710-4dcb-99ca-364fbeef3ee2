import styles from "./index.module.scss";
import { Tabs, Empty, Card, Button } from "antd";
import { useEffect, useRef, useState, useCallback } from "react";

import JSONForm from "./components/JSONForm";

import Helper from "@/helpers";
import Utils from "@/utils";

function PropertyPanel({ component, flattenedComponents, onUpdateComponent }) {
  const [initialValues, setInitialValues] = useState({});
  const { component_config } = component ?? {};
  const formRef = useRef(null);
  const prevComponentIdRef = useRef(null);

  const handleValuesChange = useCallback(
    (changedValues, allValues) => {
      const newComponent = Utils.cloneDeep(component);

      onUpdateComponent?.({ ...newComponent, ...allValues });
    },
    [component, onUpdateComponent]
  );

  const handleSubmit = useCallback(() => {
    formRef.current.validateFields().then((values) => {
      const newComponent = Utils.cloneDeep(component);

      console.log({ ...newComponent, ...values });
      onUpdateComponent?.({ ...newComponent, ...values });
      Helper.openMessage({ type: "success", content: "应用成功" });
    });
  }, [component, onUpdateComponent]);

  const renderForm = useCallback(() => {
    if (!component || !component_config) {
      return <Empty description="请选择一个组件" />;
    }

    return (
      <Card
        size="small"
        title={component?.title || "未知组件"}
        // extra={
        //   <Button type="primary" size="small" onClick={handleSubmit}>
        //     应用配置
        //   </Button>
        // }
      >
        {component_config?.props_config ? (
          <JSONForm
            ref={formRef}
            data={{
              ...component_config?.props_config,
              props: { ...component_config?.props_config?.props, initialValues },
            }}
            onChange={handleValuesChange}
            componentKey={component?.id}
            flattenedComponents={flattenedComponents}
          />
        ) : null}
      </Card>
    );
  }, [component, component_config, initialValues, flattenedComponents, handleValuesChange]);

  const tabItems = [
    {
      key: "property",
      label: "属性",
      children: renderForm(),
    },
  ];

  useEffect(() => {
    if (component) {
      const { component_config, component: componentName, id, title, ...restValues } = component;
      if (prevComponentIdRef.current !== component.id) {
        prevComponentIdRef.current = component.id;
        setInitialValues(restValues);
      } else {
        // 如果是同一组件的更新，保持表单状态
        setInitialValues((prev) => ({ ...prev, ...restValues }));
      }
    } else {
      setInitialValues({});
      prevComponentIdRef.current = null;
    }
  }, [component]);

  return (
    <div className={styles.propertyPanel}>
      <Tabs defaultActiveKey="component" items={tabItems}></Tabs>
    </div>
  );
}

export default PropertyPanel;
