module.exports = async (req, res) => {
  const dictParam = req.query["dict[]"];

  const dict = Array.isArray(dictParam) ? dictParam : dictParam ? [dictParam] : [];

  const data = {
    product_custom_name_option: [
      {
        cost: 0,
        id: 157,
        price: "15.00",
        title: "Engraving",
        value: "engraving",
        res_url: "",
      },
      {
        cost: 0,
        id: 158,
        price: null,
        title: "Size",
        value: "size",
        res_url: "",
      },
      {
        cost: 0,
        id: 162,
        price: null,
        title: "Length",
        value: "length",
        res_url: "",
      },
      {
        cost: 0,
        id: 185,
        price: null,
        title: "Color",
        value: "color",
        res_url: "",
      },
    ],
    product_custom_tips_option: [
      {
        cost: 0,
        id: 159,
        price: null,
        title: "please_enter_your_name_here",
        value: "please_enter_your_name_here",
        res_url: "",
      },
      {
        cost: 0,
        id: 190,
        price: null,
        title: "enter_the_engraving",
        value: "enter_the_engraving",
        res_url: "",
      },
      {
        cost: 0,
        id: 378,
        price: null,
        title: "pleaseenteryournamehereseparatedbycommas",
        value: "pleaseenteryournamehereseparatedbycommas",
        res_url: "",
      },
      {
        cost: 0,
        id: 421,
        price: null,
        title: "select",
        value: "select",
        res_url: "",
      },
      {
        id: 1758,
        value: "-- Please select --",
        price: null,
        res_url: "",
        title: "-- Please select --",
      },
    ],
    product_custom_option_wordart_font: [
      {
        cost: 0,
        id: 196,
        price: null,
        admin_name: "Dancing",
        title: "dancing",
        value: "dancing",
        res_url: "https://images.drawelry.com/assets/fonts/Dancing-Script/Dancing-Script.woff2",
      },
      {
        cost: 0,
        id: 609,
        price: null,
        admin_name: "ALS-Script",
        title: "als-script",
        value: "als-script",
        res_url: "https://images.drawelry.com/assets/fonts/ALS-Script/ALS-Script.woff2",
      },
      {
        cost: 0,
        id: 626,
        price: null,
        admin_name: "Alex-Brush",
        title: "alex-brush",
        value: "alex-brush",
        res_url: "https://images.drawelry.com/assets/fonts/Alex-Brush/Alex-Brush.woff2",
      },
      {
        cost: 0,
        id: 627,
        price: null,
        admin_name: "Amaze",
        title: "amaze",
        value: "amaze",
        res_url: "https://images.drawelry.com/assets/fonts/Amaze/Amaze.woff2",
      },
      {
        cost: 0,
        id: 824,
        price: null,
        admin_name: "Autumn-Chant",
        title: "autumn-chant",
        value: "autumn-chant",
        res_url: "https://images.drawelry.com/assets/fonts/Autumn-Chant/Autumn-Chant.woff2",
      },
      {
        cost: 0,
        id: 825,
        price: null,
        admin_name: "Beautiful-ES",
        title: "beautiful-es",
        value: "beautiful-es",
        res_url: "https://images.drawelry.com/assets/fonts/Beautiful-ES/Beautiful-ES.woff2",
      },
      {
        cost: 0,
        id: 826,
        price: null,
        admin_name: "Brock",
        title: "brock-script",
        value: "brock-script",
        res_url: "https://images.drawelry.com/assets/fonts/Brock-Script/Brock-Script.woff2",
      },
      {
        cost: 0,
        id: 827,
        price: null,
        admin_name: "Brush",
        title: "brush",
        value: "brush",
        res_url: "https://images.drawelry.com/assets/fonts/Brush-Script-Std/Brush-Script-Std.woff2",
      },
      {
        cost: 0,
        id: 828,
        price: null,
        admin_name: "Commercial",
        title: "commercial",
        value: "commercial",
        res_url: "https://images.drawelry.com/assets/fonts/CommercialScript/CommercialScript.woff2",
      },
      {
        cost: 0,
        id: 829,
        price: null,
        admin_name: "Freebooter",
        title: "freebooter",
        value: "freebooter",
        res_url: "https://images.drawelry.com/assets/fonts/Freebooter-script/Freebooter-script.woff2",
      },
      {
        cost: 0,
        id: 830,
        price: null,
        admin_name: "Great-Vibes",
        title: "great-vibes",
        value: "great-vibes",
        res_url: "https://images.drawelry.com/assets/fonts/Great-Vibes/Great-Vibes.woff2",
      },
      {
        cost: 0,
        id: 831,
        price: null,
        admin_name: "ITALIANNO",
        title: "italianno",
        value: "italianno",
        res_url: "https://images.drawelry.com/assets/fonts/ITALIANNO/ITALIANNO.woff2",
      },
      {
        cost: 0,
        id: 832,
        price: null,
        admin_name: "Lobster",
        title: "lobster",
        value: "lobster",
        res_url: "https://images.drawelry.com/assets/fonts/Lobster/Lobster.woff2",
      },
      {
        cost: 0,
        id: 833,
        price: null,
        admin_name: "Neon",
        title: "neon",
        value: "neon",
        res_url: "https://images.drawelry.com/assets/fonts/Neon/Neon.woff2",
      },
      {
        cost: 0,
        id: 834,
        price: null,
        admin_name: "Pacifico",
        title: "pacifico",
        value: "pacifico",
        res_url: "https://images.drawelry.com/assets/fonts/Pacifico/Pacifico.woff2",
      },
    ],
    product_custom_option_emoji: [
      {
        cost: 0,
        id: 3949,
        price: null,
        title: "%F0%9F%98%83",
        value: "%F0%9F%98%83",
        res_url: "",
      },
      {
        cost: 0,
        id: 3950,
        price: null,
        title: "%F0%9F%98%81",
        value: "%F0%9F%98%81",
        res_url: "",
      },
      {
        cost: 0,
        id: 3951,
        price: null,
        title: "%F0%9F%98%86",
        value: "%F0%9F%98%86",
        res_url: "",
      },
      {
        cost: 0,
        id: 3952,
        price: null,
        title: "%F0%9F%98%85",
        value: "%F0%9F%98%85",
        res_url: "",
      },
      {
        cost: 0,
        id: 3953,
        price: null,
        title: "%F0%9F%A4%A3",
        value: "%F0%9F%A4%A3",
        res_url: "",
      },
      {
        cost: 0,
        id: 3954,
        price: null,
        title: "%F0%9F%98%82",
        value: "%F0%9F%98%82",
        res_url: "",
      },
      {
        cost: 0,
        id: 3955,
        price: null,
        title: "%F0%9F%8C%99",
        value: "%F0%9F%8C%99",
        res_url: "",
      },
      {
        cost: 0,
        id: 3956,
        price: null,
        title: "%F0%9F%8C%9E",
        value: "%F0%9F%8C%9E",
        res_url: "",
      },
      {
        cost: 0,
        id: 3957,
        price: null,
        title: "%E2%AD%90",
        value: "%E2%AD%90",
        res_url: "",
      },
      {
        cost: 0,
        id: 3958,
        price: null,
        title: "%E2%9D%A4",
        value: "%E2%9D%A4",
        res_url: "",
      },
    ],
    product_custom_option_stone_shape: [
      {
        cost: 0,
        id: 77,
        price: null,
        admin_name: "正八宝塔",
        title: "asscher",
        value: "asscher",
        res_url: "https://static.bizseas.com/assets/stone-shape-icon/asscher.svg",
      },
      {
        cost: 0,
        id: 78,
        price: null,
        admin_name: "长方形",
        title: "emerald",
        value: "emerald",
        res_url: "https://static.bizseas.com/assets/stone-shape-icon/emerald.svg",
      },
      {
        cost: 0,
        id: 79,
        price: null,
        admin_name: "心形",
        title: "heart",
        value: "heart",
        res_url: "https://static.bizseas.com/assets/stone-shape-icon/heart.svg",
      },
      {
        cost: 0,
        id: 81,
        price: null,
        admin_name: "马眼",
        title: "marquise",
        value: "marquise",
        res_url: "https://static.bizseas.com/assets/stone-shape-icon/marquise.svg",
      },
      {
        cost: 0,
        id: 82,
        price: null,
        admin_name: "蛋形",
        title: "oval",
        value: "oval",
        res_url: "https://static.bizseas.com/assets/stone-shape-icon/oval.svg",
      },
      {
        cost: 0,
        id: 83,
        price: null,
        admin_name: "梨形",
        title: "pear",
        value: "pear",
        res_url: "https://static.bizseas.com/assets/stone-shape-icon/pear.svg",
      },
      {
        cost: 0,
        id: 84,
        price: null,
        admin_name: "公主方",
        title: "princess",
        value: "princess",
        res_url: "https://static.bizseas.com/assets/stone-shape-icon/princess.svg",
      },
      {
        cost: 0,
        id: 85,
        price: null,
        admin_name: "酒桶",
        title: "radiant",
        value: "radiant",
        res_url: "https://static.bizseas.com/assets/stone-shape-icon/cushion1.svg",
      },
    ],
    product_custom_group_option_1: [
      {
        cost: 0,
        id: 339,
        price: null,
        admin_name: "1组",
        title: "1",
        value: "1",
        res_url: "",
      },
      {
        cost: 0,
        id: 340,
        price: null,
        admin_name: "2组",
        title: "2",
        value: "2",
        res_url: "",
      },
      {
        cost: 0,
        id: 341,
        price: null,
        admin_name: "3组",
        title: "3",
        value: "3",
        res_url: "",
      },
      {
        cost: 0,
        id: 342,
        price: null,
        admin_name: "4组",
        title: "4",
        value: "4",
        res_url: "",
      },
    ],
    product_custom_group_option_5: [
      {
        cost: 0,
        id: 399,
        price: null,
        admin_name: "1组",
        title: "1",
        value: "1",
        res_url: "",
      },
      {
        cost: 0,
        id: 400,
        price: null,
        admin_name: "2组",
        title: "2",
        value: "2",
        res_url: "",
      },
      {
        cost: 0,
        id: 401,
        price: null,
        admin_name: "3组",
        title: "3",
        value: "3",
        res_url: "",
      },
      {
        cost: 0,
        id: 926,
        price: null,
        admin_name: "4组",
        title: "4",
        value: "4",
        res_url: "",
      },
    ],
    product_custom_option_wordart_color: [
      {
        cost: 0,
        id: 605,
        price: null,
        admin_name: "银色",
        title: "sliver",
        value: "sliver",
        res_url: "https://images.drawelry.com/assets/material/silver.jpg",
      },
      {
        cost: 0,
        id: 610,
        price: null,
        admin_name: "黑金色",
        title: "black_gold",
        value: "black_gold",
        res_url: "https://images.drawelry.com/assets/material/black_gold.jpg",
      },
      {
        cost: 0,
        id: 611,
        price: null,
        admin_name: "金色",
        title: "yellow_gold",
        value: "yellow_gold",
        res_url: "https://images.drawelry.com/assets/material/yellow_gold.jpg",
      },
      {
        cost: 0,
        id: 612,
        price: null,
        admin_name: "玫瑰金",
        title: "rose_gold",
        value: "rose_gold",
        res_url: "https://images.drawelry.com/assets/material/rose_gold.jpg",
      },
    ],
    product_custom_option_font_family: [
      {
        cost: 0,
        id: 197,
        price: null,
        admin_name: "Italianno-Regular",
        title: "cursive",
        value: "cursive",
        res_url: "",
      },
      {
        cost: 0,
        id: 198,
        price: null,
        admin_name: "NHaasGroteskTXStd-55Rg",
        title: "classic",
        value: "classic",
        res_url: "",
      },
      {
        cost: 0,
        id: 199,
        price: null,
        admin_name: "DancingScript",
        title: "italic",
        value: "italic",
        res_url: "",
      },
      {
        cost: 0,
        id: 587,
        price: null,
        admin_name: "手写体",
        title: "pacifico",
        value: "pacifico",
        res_url: "",
      },
    ],
    product_tag_style: [
      {
        value: "vintage",
        id: "18",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "halo",
        id: "19",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "side_stones",
        id: "12",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "milgrain",
        id: "20",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "flowers_leafs",
        id: "13",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "solitaire",
        id: "29",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "art_deco",
        id: "14",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "three_stone",
        id: "30",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "interchangeable",
        id: "32",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "enhancer",
        id: "33",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "3_pieces_set",
        id: "34",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "intertwined_twist",
        id: "35",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "knot_bowknot_rope",
        id: "36",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "animal",
        id: "38",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "stackable",
        id: "39",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "couples",
        id: "41",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "infinity",
        id: "42",
        title: "",
        price: null,
        cost: 0,
      },
      {
        value: "men_s",
        id: "46",
        title: "",
        price: null,
        cost: 0,
      },
    ],
    product_custom_option_crystal: [
      {
        value: "no",
        id: "397",
        title: "否",
        price: null,
        cost: 0,
      },
      {
        value: "yes",
        id: "398",
        title: "是",
        price: "4.00",
        cost: 0,
      },
    ],
  };

  res.status(200).json({
    success: true,
    data: Object.fromEntries(dict.map((item) => [item, data[item]])),
  });
};
