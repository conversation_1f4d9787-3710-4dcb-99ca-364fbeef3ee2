import Helper from "helpers";
import PropTypes from "prop-types";

function CommandTrigger(props) {
  const { data, children } = props;
  const { command, props: commandProps } = data;

  function handleCommand({ command, ...others }) {
    Helper.commandHandler({ command, ...others });
  }

  return (
    <span className="primary-color cursor-pointer" onClick={() => handleCommand({ command })}>
      {children || commandProps.children}
    </span>
  );
}

CommandTrigger.propTypes = {
  data: PropTypes.object,
};

export default CommandTrigger;
