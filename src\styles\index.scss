@import "antd-theme";
@import "theme-vars";

.primary-color {
  color: var(--ant-primary-color);
}

.cursor-pointer {
  cursor: pointer;
}

.page {
}

.page-header {
  position: sticky;
  top: 50px;
  z-index: 99;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // height: 50px;
  padding: 0 20px;
  background-color: rgba(255, 255, 255, 0.6);
  border-bottom: 1px solid var(--theme-border-color);
  backdrop-filter: blur(2px);

  .page-header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 10px 0;
  }
}

.app-page-loading {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9000;
  background: rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

iframe {
  width: 100%;
  height: 100%;
}

.inside-iframe-body {
  overflow-y: scroll;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #e0e0e0;
  }
}

::-webkit-scrollbar {
  height: 8px;
  width: 4px;
  // background: #fff;
}

::-webkit-scrollbar-thumb {
  background: #9c9c9c;
  border-radius: 30px;
  // border-left: 3px solid #d5d5d5;
  // border-right: 3px solid #d5d5d5;
  cursor: pointer;
}

::-webkit-scrollbar-thumb:hover {
  background: #858383;
}

.custom-modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.input-number-block {
  width: 100%;
}

.table-operations-column {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
}

.overflow-hidden {
  overflow: hidden;
}
