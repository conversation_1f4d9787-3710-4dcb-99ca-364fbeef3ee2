import { forwardRef, useImperativeHandle, useState } from "react";
import PropTypes from "prop-types";
import { getValueFromEvent } from "./common";

function ControlWrapper(props, ref) {
  const {
    name,
    defaultValue,
    value: propValue,
    onChange,
    render,
    getValue = (event, value) => getValueFromEvent({ event, name, value }),
  } = props;
  const [innerValue, setInnerValue] = useState(defaultValue);
  const isControlled = props.hasOwnProperty("value");
  const value = isControlled ? propValue : innerValue;

  function handleChange(event, value) {
    const nextValue = getValue(event, value);
    setInnerValue(nextValue);
    onChange?.(event, { [name]: nextValue });
  }

  useImperativeHandle(ref, () => {
    return {
      get value() {
        return value;
      },
      set value(value) {
        setInnerValue(value);
      },
      get defaultValue() {
        return defaultValue;
      },
    };
  });

  return render ? (
    render({
      name,
      value: value ?? "",
      onChange: handleChange,
    })
  ) : (
    <></>
  );
}

ControlWrapper = forwardRef(ControlWrapper);

ControlWrapper.propTypes = {
  control: PropTypes.elementType,
  defaultValue: PropTypes.any,
  value: PropTypes.any,
  onChange: PropTypes.func,
  render: PropTypes.func,
  getValue: PropTypes.func,
};

export default ControlWrapper;
