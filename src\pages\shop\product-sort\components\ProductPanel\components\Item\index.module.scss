.item {
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid #d9d9d9;
  font-size: 12px;
  cursor: move;
  position: relative;
  background-color: #fff;
  user-select: none;

  .count {
    position: absolute;
    right: -7px;
    top: -8px;
    font-size: 14px;
    font-weight: 700;
    color: white;
    display: flex !important;
    justify-content: center;
    align-items: center;
    height: 26px;
    width: 26px;
    background-color: #1890ff;
    border-radius: 50%;
    display: inline-block;
  }

  &:focus {
    outline: none;
  }
}

.dragging {
  // z-index: 1000;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
}
