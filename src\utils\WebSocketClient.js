class WebSocketClient {
  #url;
  #options = {};
  #WebSocket;
  #ws;
  #events = [];
  #reconnectCount = 0;
  #readyState = 0;
  #connected = false;

  constructor(
    url,
    {
      WebSocket,
      params = {},
      reconnectAttempts = 5,
      reconnectDelay = 1000,
      debug = false,
      autoConnect = true,
      logMaxLength = 2048,
      beforeSendJson,
    } = {}
  ) {
    this.#WebSocket = WebSocket;
    this.#url = this.#setQueryParams(url, params);
    this.#options = {
      reconnectAttempts,
      reconnectDelay,
      debug,
      logMaxLength,
      beforeSendJson,
    };
    if (autoConnect) {
      this.connect();
    }
  }

  get readyState() {
    return this.#readyState;
  }

  #try(func, defaultValue) {
    try {
      return func();
    } catch (err) {
      return defaultValue;
    }
  }

  #uniqueId({ length = 40 } = {}) {
    let str = "";
    while (str.length < length) {
      str += (+Math.random().toString().substring(2)).toString(16);
    }
    return str.substring(0, length);
  }

  #setQueryParams(url, params) {
    const urlObj = new URL(url);
    Object.entries(params || {}).forEach(([key, value]) => {
      if (value) {
        urlObj.searchParams.set(key, value.toString());
      } else {
        urlObj.searchParams.delete(key);
      }
    });
    return urlObj.toString();
  }

  #setReadyState(readyState) {
    this.#readyState = readyState;
    this.#connected = readyState === this.#WebSocket.OPEN;
    this.fire("readyStateChange", { readyState });
  }

  connect() {
    return new Promise((resolve, reject) => {
      this.fire("connecting");
      console.log("正在建立WebSocket连接...");

      this.#ws = new this.#WebSocket(this.#url);

      this.#ws.onopen = (event) => {
        console.log("WebSocket连接已建立");
        this.#setReadyState(this.#WebSocket.OPEN);
        this.#reconnectCount = 0;
        this.fire("open", event);
        resolve();
      };

      this.#ws.onmessage = (event) => {
        if (this.#options.debug) {
          const message = event.data.toString();
          if (message.length > this.#options.logMaxLength) {
            const data = this.#try(() => JSON.parse(message));
            data.data = `{ too large... }`;
            console.log("收到消息:", JSON.stringify(data));
          } else {
            console.log("收到消息:", event.data);
          }
        }
        const message = this.#try(() => JSON.parse(event.data.toString()), {});
        if (message.ack?.type === "send") {
          this.json({ action: "send", to: message.from, ack: { type: "reply", id: message.ack.id } });
        }
        if (message.ack?.type === "reply") {
          this.fire("ackReply", event);
        } else {
          this.fire("message", event);
        }
      };

      this.#ws.onclose = (event) => {
        console.log("WebSocket连接已关闭");
        this.#setReadyState(this.#WebSocket.CLOSED);
        this.#reconnect();
        this.fire("close", event);
        if (this.#reconnectCount >= this.#options.reconnectAttempts) {
          reject();
        }
      };

      this.#ws.onerror = (event) => {
        console.error("WebSocket错误:", event);
        this.#setReadyState(this.#WebSocket.CLOSING);
        this.fire("error", event);
      };
    });
  }

  #reconnect() {
    if (this.#reconnectCount < this.#options.reconnectAttempts) {
      this.#reconnectCount++;
      console.log(`尝试重新连接 ${this.#reconnectCount}/${this.#options.reconnectAttempts}`);
      if (this.#reconnectCount === 1) {
        this.connect();
      } else {
        setTimeout(() => this.connect(), this.#options.reconnectDelay);
      }
    }
  }

  #sleep(milliseconds) {
    return new Promise((resolve) => {
      setTimeout(resolve, milliseconds);
    });
  }

  send(message) {
    if (this.#options.debug) {
      if (message.length > this.#options.logMaxLength) {
        const data = JSON.parse(message);
        data.data = `{ too large... }`;
        console.log(`发送消息:`, JSON.stringify(data));
      } else {
        console.log(`发送消息:`, message);
      }
    }
    this.#ws.send(message);
  }

  async json(data) {
    await this.#options.beforeSendJson?.(data);
    this.send(JSON.stringify(data));
  }

  jsonAck(data, { timeout = 1000, retryAttempts = 3 } = {}) {
    return new Promise(async (resolve, reject) => {
      const id = this.#uniqueId();
      const send = () => {
        this.json({ ...data, ack: { type: "send", id } });
      };
      let receiveAck = false;
      let timeoutId;
      const handleAckReply = async (event) => {
        const receivedData = this.#try(() => JSON.parse(event.data.toString()), null);
        if (receivedData?.ack?.id === id) {
          this.off("ackReply", handleAckReply);
          receiveAck = true;
          clearTimeout(timeoutId);
          this.fire("ackReceived");
          resolve(receivedData);
        }
      };
      this.on("ackReply", handleAckReply);
      for (let i = 0; i < retryAttempts; i++) {
        send();
        await new Promise((resolve) => {
          this.once("ackReceived", resolve);
          timeoutId = setTimeout(resolve, timeout);
        });
        if (receiveAck) break;
      }
      if (!receiveAck) {
        reject(`[WebSocketClient] ${id} ack timeout!`);
      }
    });
  }

  close() {
    if (this.#ws) {
      this.#ws.close();
      this.#ws = null;
    }
  }

  on(event, handler) {
    const id = this.#uniqueId();
    this.#events.push({ id, event, handler });
    return id;
  }

  once(event, handler) {
    const handleOnce = (...args) => {
      this.off(event, handleOnce);
      handler?.(...args);
    };
    this.on(event, handleOnce);
  }

  off(event, handler) {
    const key = typeof handler === "function" ? "handler" : "id";
    this.#events = this.#events.filter((x) => !(x.event === event && x[key] === handler));
  }

  fire(event, ...args) {
    return this.exec(event, ...args);
  }

  async exec(event, ...args) {
    let promises = [];
    for (const item of this.#events) {
      if (item.event === event) {
        promises.push((async () => await item.handler?.(...args))());
      }
    }
    return await Promise.allSettled(promises);
  }
}

export default WebSocketClient;
