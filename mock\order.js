const Api = require("../src/fetchers/api");

module.exports = {
  "POST /rest/v1/order": async (req, res) => {
    const { id, field, value, command } = req.body;
    const rows = [];
    const row = rows.find((a) => a.id === id);
    if (row) {
      row[field] = value;
    }
    res.status(200).json({
      success: true,
      data: {},
      command: {
        type: "modal",
        closable: true,
        title: "删除订单备注",
        footer: [
          {
            title: "保存",
            props: { type: "primary" },
            command: {
              type: "submit",
              id: "form2",
            },
          },
        ],
        content: {
          component: "JSONComponents",
          type: "json",
          props: {},
          children: [
            {
              component: "Form",
              props: {
                id: "form2",
                initialValues: {},
              },
              formItems: [
                {
                  key: "remark",
                  label: "备注",
                  component: "Textarea",
                  props: { rows: 5 },
                  rules: [{ required: true, message: "This is a required field" }],
                },
              ],
              submit: {
                request: {
                  url: Api.customer,
                  pathParams: { url_key: "order-remark" },
                },
              },
            },
          ],
        },
      },
      message: "操作成功！",
      // command: {
      //   type: "message",
      //   config: {
      //     duration: 3,
      //     type: "error",
      //     content: "已发货订单，禁止做修改",
      //   },
      //   command: {
      //     type: "reload_table",
      //     command: {
      //       type: "close_drawer",
      //     },
      //   },
      // },
    });
  },
};
