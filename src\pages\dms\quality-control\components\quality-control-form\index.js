import { useRef, useState, useEffect, forwardRef, useImperative<PERSON>andle, useReducer } from "react";
import { Form, Input, Radio, Button, InputNumber, message } from "antd";
import Select from "components/common/Select";
import styles from "./index.module.scss";
import Fetchers from "fetchers";
import Helper from "helpers";
import { initialValues, passOptions, optionsApi, iframeSrc, defaultFilterStatus, reducer } from "../../common/vars";
import CameraPhotoUpload from "components/common/CameraPhotoUpload";

const { Search } = Input;

const CameraPhotoUploadProps = {
  width: "100%",
  height: "auto",
  action: "/rest/v1/stock/restock/order/item/qc-log/upload-file",
  autoUpload: false,
  data: { disk: "qc-image" },
};

function QualityControlForm(props, ref) {
  const { updateData, type } = props;
  const [filterStatus, dispatch] = useReducer(reducer, defaultFilterStatus);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const formRef = useRef();
  const orderSearchRef = useRef();
  const skuSearchRef = useRef();
  const taxonomySearchRef = useRef();
  const paramsRef = useRef();
  const isBatchControl = type === "batch_control";

  paramsRef.current = {
    onReset,
    isInputFocused,
    shortcutKeyUp,
  };

  const handleFocusOrderSearch = () => {
    setTimeout(() => {
      orderSearchRef.current?.focus();
    }, 10);
  };

  const handleFocusSkuSearch = () => {
    setTimeout(() => {
      skuSearchRef.current?.focus();
    }, 10);
  };

  const onOrderSearch = async (value, e, info) => {
    e.preventDefault();
    if (!value) {
      return Helper.openMessage({ type: "warning", content: "请输入采购单号" });
    }
    try {
      Helper.pageLoading(true);
      const result = await handleVerify({ order_no: value });
      if (!result?.success) return;
      dispatch({ order_no: { readOnly: true }, sku: { readOnly: false } });
      skuSearchRef.current.focus();
    } finally {
      Helper.pageLoading(false);
    }
  };

  const onSkuSearch = async (value, e, info) => {
    e.preventDefault();
    if (!value) {
      return Helper.openMessage({ type: "warning", content: "请输入商品编码" });
    }
    try {
      const values = formRef.current.getFieldsValue();
      Helper.pageLoading(true);
      const verify = await handleVerify({ order_no: values.order_no, sku: value });
      if (!verify?.success) {
        return;
      }
      const result = await getProductInfo();
      updateData?.(result);
      if (isBatchControl) {
        dispatch({ order_no: { readOnly: true }, sku: { readOnly: true } });
      }
      e.target.blur();
    } finally {
      Helper.pageLoading(false);
    }
  };

  const handleVerify = async (data) => {
    return await Fetchers.restockQualityControlVerify({ data }).then((res) => res?.data);
  };

  const getProductInfo = async () => {
    const values = formRef.current.getFieldsValue();
    return await Fetchers.restockGetQcProductInfo({
      data: { order_no: values.order_no, sku: values.sku },
    }).then((res) => res?.data?.data);
  };

  const onFinish = async (values) => {
    try {
      setSubmitLoading(true);
      const result = await Fetchers.restockQualityControlConfirm({
        data: { qty: 1, ...values },
      }).then((res) => res?.data);
      if (!result?.success) return;
      const value = formRef.current.getFieldsValue();
      formRef.current.resetFields();
      formRef.current.setFieldValue("order_no", value.order_no);
      updateData(null);
      dispatch({ order_no: { readOnly: true }, sku: { readOnly: false } });
    } finally {
      setSubmitLoading(false);
    }
  };

  function onReset() {
    formRef.current.resetFields();
    updateData(null);
    dispatch(defaultFilterStatus);
    handleFocusOrderSearch();
  }

  // 快捷键枚举
  const keyShortcuts = {
    0: () => {
      // 选中合格并且自动提交
      formRef.current.setFieldValue("pass", 1);
      formRef.current.submit();
    },
    1: () => {
      // 重置
      onReset();
    },
    2: () => {
      // 提交
      formRef.current.submit();
    },
    3: () => {
      // 选中合格需加工，自动提交
      formRef.current.setFieldValue("pass", 2);
      formRef.current.submit();
    },
    9: () => {
      // 选中次品并弹出次品原因选择框，支持键盘的箭头选择原因，敲回车选中原因
      formRef.current.setFieldValue("pass", 0);
      setTimeout(() => {
        taxonomySearchRef.current.focus();
      }, 0);
    },
    // 默认处理函数
    default: () => {},
  };

  function shortcutKeyUp(e) {
    const { isInputFocused } = paramsRef.current;
    if (!isInputFocused) {
      const handler = keyShortcuts[e.key] || keyShortcuts.default;
      handler();
    }
  }

  useImperativeHandle(ref, () => formRef.current);

  useEffect(() => {
    if (isBatchControl) {
      if (!filterStatus.order_no.readOnly) {
        handleFocusOrderSearch();
      } else if (!filterStatus.sku.readOnly) {
        handleFocusSkuSearch();
      }
    } else {
      handleFocusSkuSearch();
    }
  }, [isBatchControl, filterStatus]);

  useEffect(() => {
    const { onReset } = paramsRef.current;
    onReset();
  }, [type]);

  useEffect(() => {
    const { shortcutKeyUp } = paramsRef.current;
    document.addEventListener("keyup", shortcutKeyUp, false);

    return () => {
      document.removeEventListener("keyup", shortcutKeyUp, false);
    };
  }, []);

  useEffect(() => {
    function handleFocusIn() {
      setIsInputFocused(true);
    }

    function handleFocusOut() {
      setIsInputFocused(false);
    }

    document.addEventListener("focusin", handleFocusIn);
    document.addEventListener("focusout", handleFocusOut);

    return () => {
      document.removeEventListener("focusin", handleFocusIn);
      document.removeEventListener("focusout", handleFocusOut);
    };
  }, []);

  return (
    <Form ref={formRef} initialValues={initialValues} onFinish={onFinish} layout="vertical" autoComplete="off">
      {isBatchControl ? (
        <Form.Item label="采购单" name="order_no" rules={[{ required: true, message: "请输入采购单号" }]}>
          <Search
            ref={orderSearchRef}
            placeholder="请输入采购单号"
            readOnly={filterStatus.order_no.readOnly}
            onSearch={onOrderSearch}
          />
        </Form.Item>
      ) : null}
      <Form.Item label="商品编码" name="sku" rules={[{ required: true, message: "请输入商品编码" }]}>
        <Search
          ref={skuSearchRef}
          placeholder="请输入商品编码"
          readOnly={isBatchControl && filterStatus.sku.readOnly}
          onSearch={onSkuSearch}
        />
      </Form.Item>
      {isBatchControl ? (
        <Form.Item label="数量" name="qty" rules={[{ required: true, message: "请输入数量" }]}>
          <InputNumber placeholder="请输入数量" style={{ width: "100%" }} />
        </Form.Item>
      ) : null}
      <Form.Item name="pass" rules={[{ required: true, message: "请选择是否合格" }]}>
        <Radio.Group options={passOptions} optionType="button" buttonStyle="solid" />
      </Form.Item>
      <Form.Item noStyle shouldUpdate={(prevValues, curValues) => prevValues.pass !== curValues.pass}>
        {({ getFieldValue }) =>
          getFieldValue("pass") === 0 ? (
            <>
              <Form.Item label="" name="taxonomy_ids" rules={[{ required: true, message: "请选择次品原因" }]}>
                <Select
                  ref={taxonomySearchRef}
                  mode="multiple"
                  autoFocus
                  defaultOpen
                  addOptions={{
                    title: "维护次品原因",
                    command: {
                      type: "modal",
                      closable: true,
                      title: "维护次品原因",
                      props: {
                        width: 1000,
                      },
                      content: {
                        component: "JSONComponents",
                        type: "json",
                        props: {},
                        children: [
                          {
                            component: "Iframe",
                            props: {
                              src: iframeSrc,
                            },
                          },
                        ],
                      },
                    },
                  }}
                  // options={[]}
                  placeholder="次品原因"
                  optionsApi={optionsApi}
                />
              </Form.Item>
              <Form.Item label="备注" name="note">
                <Input.TextArea type="textarea" rows={3} />
              </Form.Item>
            </>
          ) : null
        }
      </Form.Item>

      <div className={styles.actions}>
        <Button onClick={onReset}>重置</Button>
        <Button htmlType="submit" type="primary" loading={submitLoading}>
          确认质检
        </Button>
      </div>
      <Form.Item name="files" label="拍照上传">
        <CameraPhotoUpload {...CameraPhotoUploadProps} />
      </Form.Item>
    </Form>
  );
}

QualityControlForm = forwardRef(QualityControlForm);

export default QualityControlForm;
