import styles from "./index.module.scss";
import { useEffect, useState, useRef } from "react";
import classNames from "classnames";
import PropTypes from "prop-types";
import { Table, Popover, Tooltip, Checkbox, Tree } from "antd";
import {
  SettingOutlined,
  VerticalAlignMiddleOutlined,
  VerticalAlignTopOutlined,
  VerticalAlignBottomOutlined,
} from "@ant-design/icons";
import Utils from "utils";
import { afterRender } from "functions";
import TableFooter from "./components/TableFooter";
import { useI18n } from "context/I18nContext";

function ProTable(props) {
  const {
    className,
    toolbar = {},
    columns = [],
    dataSource = [],
    pagination,
    initColumnsValue,
    columnsState = {},
    rowSelection,
    virtual,
    onChange,
    summary_data,
    summary,
    footerData,
    ...otherProps
  } = props;
  const { subTitle, actions } = toolbar;
  const [settingOpen, setSettingOpen] = useState(false);
  const [tableColumns, setTableColumns] = useState([...(columns || [])]);
  const contentRef = useRef({});
  const paramsRef = useRef({});
  const { persistenceKey, persistenceType } = columnsState;
  const storageData = Utils.JSON.parse(window?.[persistenceType].getItem(persistenceKey));
  const { t } = useI18n();

  paramsRef.current = {
    ...paramsRef.current,
    initColumnsValue,
    mergeColumns,
    getInitialTableColumns,
    getInitialTablePagination,
  };

  function handleSettingChange(open) {
    setSettingOpen(open);
  }

  function updateTableColumns(columns) {
    setTableColumns(columns);
    if (persistenceKey) {
      updateLocalStorageTableProps({
        columns,
        persistenceKey,
      });
    }
    columnsState?.onChange?.(columns?.filter((item) => item.dataIndex));
  }

  function updateLocalStorageTableProps({
    columns = tableColumns,
    paginationMap = { pageSize: pagination?.pageSize },
    persistenceKey,
  }) {
    try {
      const columnsMap = {};
      columns.forEach((column, index) => {
        columnsMap[column.dataIndex] = {
          width: column?.width ?? 130,
          show: column.show,
          fixed: column.fixed,
          order: index,
        };
      });
      const data = {
        columnsMap,
        paginationMap,
      };
      window?.[persistenceType].setItem(persistenceKey, Utils.JSON.stringify(data));
    } catch (error) {}
  }

  function enhanceColumns(columns) {
    return columns?.map((item, index) => {
      item.key = item?.key || index;
      item._order = index;
      item.show = item?.show !== false;
      return item;
    });
  }

  // 合并新增列，过滤掉不存在的列
  function mergeColumns(columns, localColumns) {
    const dataIndexMap = new Map();
    columns.forEach((column) => {
      dataIndexMap.set(column.dataIndex, column);
    });

    localColumns = localColumns.filter((column) => dataIndexMap.has(column.dataIndex));

    columns.forEach((column) => {
      if (!localColumns.some((c) => c.dataIndex === column.dataIndex)) {
        localColumns.push(column);
      }
    });

    return localColumns;
  }

  function sortColumnsByStorageData(columns, columnsMap) {
    return columns.sort(function (a, b) {
      const orderA = columnsMap[a.dataIndex]?.order;
      const orderB = columnsMap[b.dataIndex]?.order;
      return orderA - orderB;
    });
  }

  function applyStorageDataToColumns(columns, columnsMap) {
    return columns.map((column) => {
      const localColumn = columnsMap?.[column.dataIndex] || {};
      const mergedWidth = localColumn?.width ? localColumn.width : column.width;
      return {
        ...column,
        ...localColumn,
        width: mergedWidth,
      };
    });
  }

  function getInitialTableColumns(columns) {
    if (!columns.length) return columns;

    let currentColumns = [...columns];

    if (persistenceKey && persistenceType) {
      if (storageData?.columnsMap) {
        const { columnsMap } = storageData;
        currentColumns = sortColumnsByStorageData(currentColumns, columnsMap);
        currentColumns = applyStorageDataToColumns(currentColumns, columnsMap);
        // currentColumns = mergeColumns(columns, currentColumns);
      }
    }

    return enhanceColumns(currentColumns);
  }

  function getInitialTablePagination(pagination) {
    let newPagination = { ...pagination };
    // if (persistenceKey && persistenceType) {
    //   newPagination = { ...newPagination, ...storageData?.paginationMap };
    // }
    return newPagination;
  }

  function handleResetTableColumnSetting(columns) {
    const tableColumns = enhanceColumns(columns);
    updateTableColumns([...tableColumns]);
  }

  function handleChangeAllColumns(e) {
    const columns = [...tableColumns];
    for (const column of columns) {
      column.show = e.target.checked;
    }
    updateTableColumns(columns);
  }

  function getTreeItems({ columns, type }) {
    if (!type) {
      return columns.filter((item) => !["left", "right"].includes(item.fixed));
    } else {
      return columns.filter((item) => item.fixed === type);
    }
  }

  function findColumnByKey(columns, _order) {
    return columns.find((item) => item._order === _order);
  }

  function handleFixedColumn({ column, fixed }) {
    let columns = Utils.cloneDeep(tableColumns);
    const realFixed = fixed || column.fixed;
    const selectedNode = findColumnByKey(columns, column._order);
    initColumnsValue?.(columns);

    let leftFixedColumns = getTreeItems({ columns, type: "left" });
    let rightFixedColumns = getTreeItems({ columns, type: "right" });
    const notFixedColumns = columns.filter((item) => item._order !== selectedNode._order && !item.fixed);
    if (selectedNode) {
      selectedNode.fixed = fixed;
    }

    if (fixed === "left") {
      leftFixedColumns = [...leftFixedColumns, selectedNode].sort((a, b) => a._order - b._order);
      rightFixedColumns = rightFixedColumns?.filter((item) => item._order !== column._order);
    } else if (fixed === "right") {
      rightFixedColumns = [...rightFixedColumns, selectedNode].sort((a, b) => a._order - b._order);
      leftFixedColumns = leftFixedColumns?.filter((item) => item._order !== column._order);
    } else {
      let nextIndex, currentNodeIndex;
      if (realFixed === "left") {
        currentNodeIndex = leftFixedColumns.findIndex((item) => item._order === selectedNode._order);
        nextIndex = selectedNode._order > notFixedColumns?.length ? selectedNode._order - 1 : selectedNode._order;
        leftFixedColumns.splice(currentNodeIndex, 1);
      } else {
        currentNodeIndex = rightFixedColumns.findIndex((item) => item._order === selectedNode._order);
        nextIndex = selectedNode._order > 0 ? selectedNode._order - 1 : 0;
        rightFixedColumns.splice(currentNodeIndex, 1);
      }

      notFixedColumns.splice(nextIndex, 0, selectedNode);
    }

    updateTableColumns([...leftFixedColumns, ...notFixedColumns, ...rightFixedColumns]);
  }

  function onDrop(info) {
    const dropOrder = info.node._order;
    const dragOrder = info.dragNode._order;
    const dropPos = info.node.pos.split("-");
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);
    const loop = (data, _order, callback) => {
      for (let i = 0; i < data.length; i++) {
        if (data[i]._order === _order) {
          return callback(data[i], i, data);
        }
        if (data[i].children) {
          loop(data[i].children, _order, callback);
        }
      }
    };
    const data = [...tableColumns];

    let dragObj;
    loop(data, dragOrder, (item, index, arr) => {
      arr.splice(index, 1);
      dragObj = item;
    });

    function dropToChild() {
      loop(data, dropOrder, (item) => {
        item.children = item.children || [];
        item.children.unshift(dragObj);
      });
    }

    function dropDefault() {
      let ar = [];
      let i;
      loop(data, dropOrder, (_item, index, arr) => {
        ar = arr;
        i = index;
      });
      if (dropPosition === -1) {
        ar.splice(i, 0, dragObj);
      } else {
        ar.splice(i + 1, 0, dragObj);
      }
    }

    if (!info.dropToGap) {
      // dropToChild();
      dropDefault();
    } else if ((info.node.props.children || []).length > 0 && info.node.props.expanded && dropPosition === 1) {
      loop(data, dropOrder, (item) => {
        item.children = item.children || [];
        item.children.unshift(dragObj);
      });
    } else {
      dropDefault();
    }
    updateTableColumns(data);
  }

  function renderTableColumnSettingTitle() {
    const showColumnsLength = tableColumns.filter((item) => item.show).length;
    return (
      <div className={styles.tableColumnSettingTitle}>
        <Checkbox
          onChange={handleChangeAllColumns}
          indeterminate={showColumnsLength && showColumnsLength < tableColumns?.length}
          checked={showColumnsLength === tableColumns?.length}
        >
          {t({ key: "ColumnShow", defaultText: "列展示" })}
        </Checkbox>
        <span className={styles.tableColumnSettingResetButton} onClick={() => handleResetTableColumnSetting(columns)}>
          {t({ key: "Reset", defaultText: "重置" })}
        </span>
      </div>
    );
  }

  function handleTableColumnSettingTreeCheck(checkedKeys, info) {
    const { _order } = info.node;
    const columns = [...tableColumns];
    for (const column of columns) {
      if (column._order === _order) {
        column.show = info.checked;
      }
    }
    updateTableColumns(columns);
  }

  function renderTableColumnSetting() {
    const renderTreeTitle = (column) => {
      return (
        <div className={styles.treeTitle}>
          <div className={styles.title}>{column.title}</div>
          <div className={styles.treeItemActions}>
            {column?.fixed === "left" ? (
              <Tooltip title={t({ key: "Unfixed", defaultText: "不固定" })}>
                <VerticalAlignMiddleOutlined onClick={() => handleFixedColumn({ column, fixed: false })} />
              </Tooltip>
            ) : null}
            {!column?.fixed || column?.fixed === "right" ? (
              <Tooltip title={t({ key: "FixedOnTheBeginning", defaultText: "固定在列首" })}>
                <VerticalAlignTopOutlined onClick={() => handleFixedColumn({ column, fixed: "left" })} />
              </Tooltip>
            ) : null}
            {!column?.fixed || column?.fixed === "left" ? (
              <Tooltip title={t({ key: "FixedOnTheTail", defaultText: "固定在列尾" })}>
                <VerticalAlignBottomOutlined onClick={() => handleFixedColumn({ column, fixed: "right" })} />
              </Tooltip>
            ) : null}
            {column?.fixed === "right" ? (
              <Tooltip title={t({ key: "Unfixed", defaultText: "不固定" })}>
                <VerticalAlignMiddleOutlined onClick={() => handleFixedColumn({ column, fixed: false })} />
              </Tooltip>
            ) : null}
          </div>
        </div>
      );
    };
    const getContent = ({ title, type }) => {
      const items = getTreeItems({ columns: tableColumns, type });
      if (!items?.length) return null;
      return (
        <>
          <span className={styles.tableColumnSettingListTitle}>{title}</span>
          <div className={styles.tableColumnSettingTree}>
            <Tree
              height={otherProps?.scroll?.y || 330}
              checkable
              draggable={items.length > 1}
              onDrop={onDrop}
              checkedKeys={items.filter((item) => item?.show)?.map((item) => item?.key)}
              onCheck={handleTableColumnSettingTreeCheck}
              treeData={items}
              blockNode
              titleRender={renderTreeTitle}
            />
          </div>
        </>
      );
    };

    return (
      <div className={styles.tableColumnSettingList}>
        {getContent({ title: t({ key: "FixedOnTheLeftSide", defaultText: "固定在左侧" }), type: "left" })}
        {getContent({ title: t({ key: "Unfixed", defaultText: "不固定" }) })}
        {getContent({ title: t({ key: "FixedOnTheRightSide", defaultText: "固定在右侧" }), type: "right" })}
      </div>
    );
  }

  function renderSummary(pageData) {
    if (!summary_data) return null;

    return (
      <Table.Summary fixed>
        <Table.Summary.Row className={styles.summaryRow}>
          {rowSelection && (
            <Table.Summary.Cell index={0} key="selection">
              #
            </Table.Summary.Cell>
          )}
          {tableColumns
            ?.filter((column) => column.show)
            .map((column, index) => {
              const dataIndex = column.dataIndex;
              const summaryValue = props.summary_data[dataIndex];

              return (
                <Table.Summary.Cell index={index + (rowSelection ? 1 : 0)} key={dataIndex}>
                  {summaryValue !== undefined ? summaryValue : null}
                </Table.Summary.Cell>
              );
            })}
        </Table.Summary.Row>
      </Table.Summary>
    );
  }

  function resizableHeader(props) {
    const { column, index, children, ...otherProps } = props;
    const currentNodeIndex = tableColumns
      .filter((column) => column.show)
      .findIndex((item) => item.dataIndex === column?.dataIndex);
    const nthChildIndex = currentNodeIndex + (rowSelection ? 2 : 1);
    const selector = virtual
      ? `.ant-table .ant-table-header table colgroup col:nth-child(${nthChildIndex})`
      : `.ant-table .ant-table-body table colgroup col:nth-child(${nthChildIndex})`;
    const virtualTbodyColSelector = `.ant-table .rc-virtual-list .ant-table-cell:nth-child(${nthChildIndex})`;
    const col = typeof index === "number" ? contentRef.current?.querySelector(selector) : null;
    const virtualTbodyCol =
      typeof index === "number" ? contentRef.current?.querySelector(virtualTbodyColSelector) : null;
    const params = {
      dragging: false,
      startX: 0,
      startWidth: col?.offsetWidth,
      width: col?.offsetWidth,
      parentElement: null,
      target: null,
    };
    function handleMouseMove(e) {
      const inc = e.clientX - params.startX;
      const width = params.startWidth + inc;
      params.width = width ?? column?.width;
      if (col) {
        params.target.style.left = width + "px";
        // if (virtual) {
        //   virtualTbodyCol.style.width = width + "px";
        // }
        params.target.style.opacity = 1;
        col.style.width = width + "px";
      }
    }
    function handleMouseUp() {
      params.dragging = false;
      params.parentElement.style.pointerEvents = "auto";
      contentRef.current.style.userSelect = "auto";
      const columns = [...tableColumns];
      const showColumns = columns.filter((column) => column.show);
      if (showColumns[currentNodeIndex]) {
        showColumns[currentNodeIndex].width = params.width;
      }
      afterRender(updateTableColumns(columns));
      document.body.removeEventListener("mousemove", handleMouseMove);
      document.body.removeEventListener("mouseup", handleMouseUp);
    }
    return (
      <th {...otherProps}>
        {children}
        {column?.width ? (
          <span
            className="react-resizable-handle"
            onMouseDown={(e) => {
              params.target = e.target;
              params.dragging = true;
              params.startX = e.clientX;
              params.parentElement = e.currentTarget.parentElement;
              params.parentElement.style.pointerEvents = "none";
              contentRef.current.style.userSelect = "none";
              document.body.addEventListener("mousemove", handleMouseMove);
              document.body.addEventListener("mouseup", handleMouseUp);
            }}
          ></span>
        ) : null}
      </th>
    );
  }

  function renderCustomPagination(pagination) {
    if (!pagination) return pagination;

    function originalShowTotal(total, range) {
      const rangeText = `${range[0]}-${range[1]}`;

      if (pagination?.showTotal) {
        return pagination.showTotal.replace("{range}", rangeText).replace("{total}", total);
      }

      return `第${range[0]}-${range[1]}条/总共${total}条`;
    }

    return {
      ...getInitialTablePagination(pagination),
      showTotal: (total, range) => (
        <div className={styles.paginationWithFooter}>
          <div className={styles.footerContent}>{footerData && <TableFooter data={footerData} />}</div>
          <div className={styles.totalInfo}>{originalShowTotal(total, range)}</div>
        </div>
      ),
    };
  }

  function handleChange(pagination, filters, sorter, extra, action) {
    // pageSize 持久化
    updateLocalStorageTableProps({
      paginationMap: { pageSize: pagination?.pageSize },
      persistenceKey,
    });
    onChange?.(pagination, filters, sorter, extra, action);
  }

  useEffect(() => {
    if (columns?.length > 0) {
      const { initColumnsValue, getInitialTableColumns } = paramsRef.current;
      setTableColumns(initColumnsValue(getInitialTableColumns(columns)));
    }
  }, [columns]);

  return (
    <div className={classNames(styles.container, className)}>
      <div className={classNames("my-pro-table-list-toolbar", styles.listToolbar)}>
        <div className={styles.listToolbarLeft}>{subTitle}</div>
        <div className={styles.listToolbarRight}>
          {actions}
          <div className={styles.tableActionsItem}>
            <Popover
              content={renderTableColumnSetting}
              title={renderTableColumnSettingTitle}
              trigger="click"
              open={settingOpen}
              onOpenChange={handleSettingChange}
              overlayClassName={styles.tableColumnSettingPopover}
              titleMinWidth={200}
              placement="bottomRight"
            >
              <Tooltip key="fullscreen" title={t({ key: "ColumnSettings", defaultText: "列设置" })}>
                <SettingOutlined style={{ fontSize: 16 }} />
              </Tooltip>
            </Popover>
          </div>
        </div>
      </div>
      <div ref={contentRef} className={styles.tableWrapper}>
        <Table
          bordered
          rowKey="id"
          size="small"
          rowSelection={rowSelection}
          virtual={virtual}
          components={{ header: { cell: resizableHeader } }}
          {...otherProps}
          onChange={handleChange}
          columns={tableColumns.filter((column) => column.show)}
          dataSource={dataSource}
          summary={summary || renderSummary}
          // pagination={{
          //   showTotal: (total, range) => `第${range[0]}-${range[1]}条/总共${total}条`,
          //   ...getInitialTablePagination(pagination),
          // }}
          pagination={renderCustomPagination(pagination)}
        />
      </div>
    </div>
  );
}

ProTable.propTypes = {
  toolbar: PropTypes.object,
  columns: PropTypes.array,
  dataSource: PropTypes.array,
  pagination: PropTypes.object,
  initColumnsValue: PropTypes.func,
  columnsState: PropTypes.object,
  onChange: PropTypes.func,
  summary_data: PropTypes.object,
  summary: PropTypes.func,
  footerData: PropTypes.object,
};

export default ProTable;
