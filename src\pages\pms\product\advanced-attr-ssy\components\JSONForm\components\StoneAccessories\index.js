import styles from "./index.module.scss";
import { <PERSON><PERSON>, Modal, Table } from "antd";
import { useState } from "react";
import Fetchers from "fetchers";

function StoneAccessories(props) {
  const { data, info, params, onChange } = props;
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState(data?.options?.map((item) => item.id) || []);
  const [selectedRows, setSelectedRows] = useState(data?.options || []);

  const columns = [
    {
      title: "编号",
      dataIndex: "id",
      key: "id",
      width: 55,
    },
    {
      title: "所属石头",
      dataIndex: "belong_to",
      key: "belong_to",
      width: 100,
    },
    {
      title: "石头形状",
      dataIndex: "shape",
      key: "shape",
      width: 100,
    },
    {
      title: "重量最小值",
      dataIndex: "carat_from",
      key: "carat_from",
      width: 120,
    },
    {
      title: "重量最大值",
      dataIndex: "carat_to",
      key: "carat_to",
      width: 120,
    },
    {
      title: "Color",
      dataIndex: "color",
      key: "color",
      width: 120,
    },
    {
      title: "Clarity",
      dataIndex: "clarity",
      key: "clarity",
      width: 100,
    },
    {
      title: "Cut",
      dataIndex: "cut",
      key: "cut",
      width: 100,
    },
    {
      title: "Certificate",
      dataIndex: "certificate",
      key: "certificate",
      width: 100,
    },
    {
      title: "成本RMB/1ct",
      dataIndex: "cost",
      key: "cost",
      width: 100,
    },
    {
      title: "售价USD",
      dataIndex: "price",
      key: "price",
      width: 100,
    },
  ];

  function onSelectChange(selectedRowKeys, selectedRows) {
    setSelectedRowKeys(selectedRowKeys);
    setSelectedRows(selectedRows);
  }

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  function handleModalToggle() {
    setVisible((visible) => !visible);
  }

  async function getDataSource() {
    try {
      setLoading(true);
      const result = await Fetchers.getDiamondLibraryData({
        params: { ...info, product_id: params?.product_id },
      }).then((res) => res.data?.data);
      setDataSource(result);
    } finally {
      setLoading(false);
    }
  }

  function handleModalVisible() {
    handleModalToggle();
    getDataSource();
  }

  function handleOk() {
    onChange?.(selectedRows);
    handleModalToggle();
  }

  return (
    <div className={styles.stoneAccessories}>
      <Button type="link" onClick={handleModalVisible}>
        {selectedRowKeys ? `已选择` : `请选择`}
      </Button>

      <Modal title="选择石头" width={1400} open={visible} onCancel={handleModalToggle} destroyOnClose onOk={handleOk}>
        <Table
          rowKey="id"
          rowSelection={rowSelection}
          dataSource={dataSource}
          columns={columns}
          size="small"
          scroll={{ y: 500 }}
          border
          loading={loading}
          pagination={false}
        />
      </Modal>
    </div>
  );
}

export default StoneAccessories;
