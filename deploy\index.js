const { runCommand, getAppName, backup, lsBackup, sendWechatMessage } = require("./common");
const { updateNginxConfig } = require("./helper");
const fs = require("node:fs/promises");
const moment = require("moment");

async function main() {
  const branch = process.env.BRANCH || "master";
  const app = process.env.APP || getAppName();
  const runtime = process.env.RUNTIME || (branch === "test" ? "test" : "production");

  console.log(`BRANCH: ${branch}`);
  console.log(`APP: ${app}`);
  console.log(`RUNTIME: ${runtime}`);
  console.log(``);

  const startTime = Date.now();
  const rootDir = process.cwd();

  await runCommand(`git reset --hard`);
  await runCommand(`git clean -df`);
  await runCommand(`git fetch --prune`);
  // await runCommand(`git remote prune origin`);
  await runCommand(`git pull`);
  await runCommand(`git checkout ${branch}`);
  const version = await runCommand(`git rev-parse HEAD`).then((x) => x.trim());
  await runCommand(`npm install`);
  await fs.writeFile(`${rootDir}/public/app.json`, JSON.stringify({ version }), { encoding: "utf8" });
  await runCommand(`cross-env REACT_APP_RUNTIME_ENV=${runtime} REACT_APP_VERSION=${version} npm run build`);
  await updateNginxConfig({ app });

  console.log(`\n`);
  console.log(`backup in progress...`);
  await backup({ version });
  console.log(`\n`);
  await lsBackup();

  const useTime = Math.ceil((Date.now() - startTime) / 1000);

  console.log(``);
  console.log(`use time: ${useTime}s`);
  console.log(`version: ${version}`);
  console.log(`last deploy time: ${moment().utcOffset(8).format("YYYY-MM-DD HH:mm:ss Z")}`);
  console.log(``);

  if (runtime === "production") {
    await sendWechatMessage({ content: "ERP6正式环境已发布" });
  }
}

main().catch((err) => {
  console.error(err);
});
