const { exec, spawn } = require("node:child_process");
const fs = require("node:fs");
const path = require("node:path");
const config = require("./config");
const moment = require("moment");
const axios = require("axios");

async function runCommand(command, { quiet, hideCommand, ...options } = {}) {
  return await new Promise((resolve, reject) => {
    if (!hideCommand) {
      console.log(command);
    }
    const subprocess = exec(command, options, (stderr, stdout) => {
      if (stderr) {
        reject(stderr);
      } else {
        resolve(stdout);
      }
    });
    subprocess.stdout.on("data", (data) => {
      if (!quiet) {
        console.log(data);
      }
    });
    subprocess.stderr.on("data", (data) => {
      if (!quiet) {
        console.error(data);
      }
    });
    subprocess.on("error", reject);
    subprocess.on("close", resolve);
  });
}

function bashCommand(command) {
  return new Promise((resolve, reject) => {
    const bash = spawn("bash");
    bash.stdout.on("data", (data) => {
      console.log(data.toString());
    });
    bash.stderr.on("data", (data) => {
      console.log(data.toString());
    });
    bash.on("error", reject);
    bash.on("close", resolve);
    bash.stdin.write(`export NVM_DIR="$HOME/.nvm"\n`);
    bash.stdin.write(`[ -s "$NVM_DIR/nvm.sh" ] && \\. "$NVM_DIR/nvm.sh"\n`);
    bash.stdin.write(`[ -s "$NVM_DIR/bash_completion" ] && \\. "$NVM_DIR/bash_completion"\n`);
    bash.stdin.write(`${command} && exit\n`);
  });
}

async function getCurrentPort() {
  const file = `/etc/nginx/sites-available/default`;
  const content = await fs.promises.readFile(file, { encoding: "utf-8" });
  const regex = new RegExp(`proxy_pass http://localhost:(300\\d);`, "gim");
  const port = regex.exec(content)?.[1];
  if (!port) {
    throw new Error("获取当前运行端口错误");
  }
  return +port;
}

async function readNginxConfig() {
  const file = `/etc/nginx/sites-available/default`;
  const content = await fs.promises.readFile(file, { encoding: "utf-8" });
  return content.toString();
}

const Common = {
  runCommand,
  bashCommand,

  async readNginxConfig() {
    return await readNginxConfig();
  },

  getAppName() {
    return /[^/]*$/.exec(process.cwd())?.[0] || "";
  },

  async getCommitHash() {
    return await runCommand("git rev-parse HEAD", { hideCommand: true, quiet: true }).then((x) => x.trim());
  },

  async getCurrentApp() {
    const content = await readNginxConfig();
    const matches = content.match(/root \/var\/www\/html\/frontend\/([^/]*)\/build/i);
    return (matches?.[1] || "").trim();
  },

  // getCluster(name = process.env.CLUSTER) {
  //   let clusterName = name;
  //   if (!clusterName) {
  //     const ipv4 = NodeHelper.getIPv4();
  //     clusterName = config.clusters.find((item) => item.master.internal === ipv4)?.name;
  //   }
  //   if (!clusterName) throw new Error("必须指定要部署的集群名称！");
  //   return config.clusters.find((item) => item.name === clusterName);
  // },

  // getClusterLabel() {
  //   const cluster = Common.getCluster();
  //   const clusterLabelMap = {
  //     "gcp-1": "drawelry",
  //     "gcp-2": "垂直站",
  //     "aws-1": "jeulia",
  //   };
  //   return clusterLabelMap[cluster.name] || "";
  // },

  async readDirStats(dir) {
    const list = await fs.promises.readdir(dir);
    const stats = await Promise.all(
      list.map(async (dirname) => {
        const pathname = path.resolve(`${dir}/${dirname}`);
        const stat = await fs.promises.stat(pathname);
        return { dirname, pathname, stat };
      })
    );
    return stats.sort((a, b) => b.stat.birthtime - a.stat.birthtime);
  },

  get backupDir() {
    return config.backup.pathname;
  },

  async backup({ version }) {
    if (!version) {
      throw new Error(`version is required!`);
    }
    const dir = "build";
    const targetDir = `${Common.backupDir}/${version}/${dir}`;
    await runCommand(`mkdir -p ${targetDir}`);
    await runCommand(`cp -R ${process.cwd()}/${dir}/* ${targetDir}`);
    await Common.clearExpiredBackup();
  },

  async clearExpiredBackup() {
    const stats = await Common.readDirStats(Common.backupDir);
    for (let i = 0; i < stats.length; i++) {
      const { count = 10 } = config.backup;
      if (i >= count) {
        const item = stats[i];
        console.log(`clear backup ${item.dirname}`);
        await runCommand(`rm -rf ${item.pathname}`);
      }
    }
  },

  async lsBackup() {
    const list = await Common.readDirStats(Common.backupDir);
    console.log(`backup directories: `);
    console.log(
      list
        .map(
          (item, index) =>
            `${index}\t${moment(item.stat.birthtime).utcOffset(8).format(`YYYY-MM-DD HH:mm:ss`)}\t${item.dirname}`
        )
        .join("\n")
    );
  },

  argsToString(args) {
    const list = [];
    Object.keys(args || {}).forEach((key) => {
      if (args[key]?.length > 0) {
        list.push(`${key.toUpperCase()}=${args[key]}`);
      }
    });
    return list.join(" ");
  },

  async sendWechatMessage({ content }) {
    return await Promise.allSettled([
      axios.post(`https://open.feishu.cn/open-apis/bot/v2/hook/e9042bae-4ffe-4899-996a-0766cc25c343`, {
        msg_type: "post",
        content: {
          post: {
            zh_cn: {
              // title: "测试标题",
              content: [
                [{ tag: "text", text: content }],
                [
                  { tag: "at", user_id: "7432633650270797825", user_name: "任宽" },
                  { tag: "at", user_id: "7432633670340771842", user_name: "杨磊" },
                ],
              ],
            },
          },
        },
      }),
      await axios
        .post(`https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=4da46739-138a-42dd-9fca-7bbc67da367a`, {
          msgtype: "text",
          text: {
            content,
            mentioned_mobile_list: [
              // 任宽
              "13572543260",
              // 杨磊
              "15135210985",
            ],
          },
        })
        .catch((err) => {
          console.error(`发送企业微信消息失败: ${err.message}`);
        }),
    ]);
  },

  processData(data) {
    return runCommand(data);
  },

  async createZipFile({ dir, items, level = 6, filename }) {
    return new Promise((resolve, reject) => {
      const archiver = require("archiver");

      const output = fs.createWriteStream(filename);
      const archive = archiver("zip", {
        zlib: { level }, // 设置压缩级别
      });

      output.on("close", () => {
        // console.log(`Archive ${filename} has been finalized. Total bytes: ${archive.pointer()}`);
        resolve();
      });

      archive.on("error", (err) => {
        reject(err);
      });

      archive.pipe(output);

      const addItems = async () => {
        for (const item of items) {
          const itemPath = path.join(dir, item);
          const stat = await fs.promises.stat(itemPath).catch(() => null);
          if (stat) {
            if (stat.isDirectory()) {
              archive.directory(itemPath, item);
            } else {
              archive.file(itemPath, { name: item });
            }
          } else {
            // console.warn(`Warning: ${itemPath} does not exist and will be skipped.`);
          }
        }
      };

      addItems()
        .then(() => {
          archive.finalize();
        })
        .catch((err) => {
          // console.error("Error processing items:", err);
          reject(err);
        });
    });
  },
};

module.exports = Object.freeze(Common);
