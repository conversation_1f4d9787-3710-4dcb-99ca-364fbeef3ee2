const Api = require("../src/fetchers/api");

module.exports = {
  "GET /rest/v1/api-form": (req, res) => {
    res.status(200).json({
      status: "00",
      success: true,
      data: {
        type: "json",
        props: {
          layout: "vertical",
          id: "form1",
          initialValues: { field1: 13123 },
          labelCol: {
            span: 8,
          },
          wrapperCol: {
            span: 16,
          },
        },
        formItems: [
          {
            component: "Row",
            props: { gutter: 16 },
            children: [
              {
                component: "Col",
                props: { span: 12 },
                children: [
                  {
                    key: "field1",
                    label: "Field1",
                    component: "Input",
                    props: {},
                    rules: [{ required: true, message: "This is a required field" }],
                  },
                ],
              },
              {
                component: "Col",
                props: { span: 12 },
                children: [
                  {
                    key: "field2",
                    label: "Field2",
                    component: "Input",
                    props: {},
                    rules: [{ required: true, message: "This is a required field" }],
                  },
                ],
              },
            ],
          },
        ],
        submit: {
          request: {
            url: Api.customer,
            data: {
              default: "test",
            },
          },
        },
      },
    });
  },
};
