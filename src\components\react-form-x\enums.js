const FormEnums = Object.freeze({
  Events: {
    Message: "FormX.message",
    FormListenerReady: "FormX.formListenerReady",
    SetErrorMessage: "FormX.setErrorMessage",
  },
  Actions: {
    InitFormControl: "FormX.initFormControl",
    UpdateFormControl: "FormX.updateFormControl",
    ValidateForm: "FormX.validateForm",
    ValidateFormCallback: "FormX.validateFormCallback",
    UnmountFormControl: "FormX.unmountFormControl",
    ValidateFields: "FormX.validateFields",
    ClearErrorMessage: "FormX.clearErrorMessage",
  },
});

export default FormEnums;
