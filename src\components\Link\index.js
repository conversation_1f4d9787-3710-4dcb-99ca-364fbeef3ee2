import { Link as ReactRouterLink } from "react-router-dom";
import Utils from "@/utils";
import PropTypes from "prop-types";

function Link(props) {
  const { children, to, query, ...otherProps } = props;

  function getToParam() {
    if (typeof query === "object") {
      if (typeof to === "object") {
        return { ...to, search: Utils.setQueryParams(to.search, query) };
      } else if (typeof to === "string") {
        return Utils.setQueryParams(to, query);
      }
    }
    return to;
  }

  return (
    <ReactRouterLink {...otherProps} to={getToParam()}>
      {children}
    </ReactRouterLink>
  );
}

Link.propTypes = {
  to: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  query: PropTypes.object,
};

export default Link;
