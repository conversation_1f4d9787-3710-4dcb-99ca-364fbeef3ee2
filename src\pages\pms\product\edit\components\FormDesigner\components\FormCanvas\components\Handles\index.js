import { DragOutlined, SettingOutlined, DeleteOutlined } from "@ant-design/icons";

function IconHandle({ children, className, onClick, ...restProps }) {
  return (
    <div className={className} onClick={onClick} {...restProps}>
      {children}
    </div>
  );
}

export function DragHandle(props) {
  return (
    <IconHandle {...props}>
      <DragOutlined />
    </IconHandle>
  );
}

export function SettingHandle(props) {
  return (
    <IconHandle {...props}>
      <SettingOutlined />
    </IconHandle>
  );
}

export function DeleteHandle(props) {
  return (
    <IconHandle {...props}>
      <DeleteOutlined />
    </IconHandle>
  );
}

export default {
  DragHandle,
  SettingHandle,
  DeleteHandle,
};
