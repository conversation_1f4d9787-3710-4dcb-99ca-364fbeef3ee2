import { Rnd } from "react-rnd";

function DraggableResizable(props) {
  const {
    children,
    isResizable,
    isDraggable,
    left = 0,
    top = 0,
    width = 100,
    height = 100,
    onDrag,
    onResize,
    bounds = "parent",
    lockAspectRatio = true,
    style,
    ...restProps
  } = props;

  return (
    <Rnd
      size={{ width, height }}
      position={{ x: left, y: top }}
      minWidth={100}
      minHeight={100}
      bounds={bounds}
      disableDragging={!isDraggable}
      enableResizing={
        isResizable
          ? {
              top: true,
              right: true,
              bottom: true,
              left: true,
              topRight: true,
              bottomRight: true,
              bottomLeft: true,
              topLeft: true,
            }
          : false
      }
      lockAspectRatio={lockAspectRatio}
      onDragStop={onDrag}
      onResizeStop={onResize}
      style={style}
      {...restProps}
    >
      {children}
    </Rnd>
  );
}

export default DraggableResizable;
