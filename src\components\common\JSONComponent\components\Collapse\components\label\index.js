import styles from "./index.module.scss";
import { Button } from "antd";
import Helper from "helpers";

function CollapseLabel(props) {
  const { data } = props;
  const { label, actions } = data;

  function handleCommand({ command, ...others }) {
    Helper.commandHandler({ command, ...others });
  }

  return (
    <div className={styles.container}>
      <span>{label}</span>
      <span>
        {actions?.map((action, index) => {
          return (
            <Button
              key={index}
              type="link"
              size="small"
              {...action?.props}
              onClick={(e) => {
                e.stopPropagation();
                handleCommand({ command: action?.command });
              }}
            >
              {action?.label}
            </Button>
          );
        })}
      </span>
    </div>
  );
}

export default CollapseLabel;
