const edit = require("./edit");
const dictionaryOptions = require("./dictionary-options");
const dictionary = require("./dictionary");
const advancedOption = require("./advanced-option");
const templateOptions = require("./template-options");

module.exports = {
  "GET /rest/v1/pms/product/edit": async (req, res) => edit(req, res),
  "GET /rest/v1/customize/dictionary/option": async (req, res) => dictionaryOptions(req, res),
  "GET /rest/v1/dictionary": async (req, res) => dictionary(req, res),
  "GET /rest/v1/customize/advanced-option": async (req, res) => advancedOption(req, res),
  "GET /rest/v1/customize/template-options": async (req, res) => templateOptions(req, res),
};
