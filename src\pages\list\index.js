import { useEffect, useRef, useState, memo } from "react";
import { Link, useNavigate, useLocation, useParams } from "react-router-dom";
import { Button, Dropdown, Form, Input, Space, Tag, Image, Tooltip, Badge, Typography } from "antd";
import styles from "./index.module.scss";
import {
  SearchOutlined,
  CaretDownOutlined,
  EyeOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  SyncOutlined,
  LeftOutlined,
  RightOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import debounce from "lodash.debounce";
import { observer } from "mobx-react-lite";
import store from "@/stores";
import Utils from "@/utils";
import Enums from "@/enums";
import Helper from "helpers";
import axios from "@/fetchers/request";
import Fetchers from "@/fetchers";
import { Counter } from "functions";
import FilterSelect from "@/pages/list/components/FilterSelect";
import FilterTree from "@/pages/list/components/FilterTree";
import FilterCheckbox from "@/pages/list/components/FilterCheckbox";
import FilterRangePicker from "./components/FilterRangePicker";
import Breadcrumbs from "@/components/common/Breadcrumbs";
import classNames from "classnames";
import Icon from "@/components/common/Icon";
import HtmlBlock from "@/components/common/HtmlBlock";
import ProTable from "components/common/ProTable";
import EditablePopover from "components/common/EditablePopover";
import SidebarActions from "./components/SidebarActions";
import ListHeader from "./components/ListHeader";
import HeaderAction from "./components/HeaderAction";
import ResizableHeader from "./components/ResizableHeader";
import Tags from "components/business/Tags";
import { useI18n } from "@/context/I18nContext";

const { Search } = Input;
const Params = {
  selectedId: "",
  selectedOperation: null,
};

const axiosRequest = async (args) => {
  return await axios(args);
};
const fetchListPageData = async ({ url_key }) => {
  return await Fetchers.getListPageData({ url_key })
    .then((res) => res.data)
    .catch((err) => ({}));
};

const getComputedStyle = (node) => {
  return window.getComputedStyle(node || document.createElement("div"));
};

const FilterBar = memo(function ({
  data,
  filterParams,
  clearFilters,
  getFilterLabel,
  location,
  getFilterKey,
  updateSearchParams,
}) {
  const { t } = useI18n();

  return (
    <div className={styles.filterTags}>
      <span style={{ lineHeight: "26px", minWidth: 70 }}>{t({ key: "FilterParams", defaultText: "筛选条件" })}：</span>
      <span>
        {Object.keys(filterParams).map((dataIndex, index) => {
          const column = data?.tableProps?.columns?.find((item) => item.dataIndex === dataIndex);
          const title = column?.title;
          const label = getFilterLabel({ column });
          return title ? (
            <Tag
              key={dataIndex + index}
              closable
              onClose={() => {
                const searchParams = new URLSearchParams(location.search);
                const filterKey = getFilterKey(dataIndex);
                searchParams.delete(filterKey);
                updateSearchParams({ searchParams });
              }}
            >
              <span className={styles.filterTagTitle}>
                {title}: {label}
              </span>
            </Tag>
          ) : null;
        })}
      </span>
      <Tag className={styles.clearAll} onClick={clearFilters}>
        Clear All
      </Tag>
    </div>
  );
});

const defaultIsVirtual = true;
const tableRootClassName = "ant-table-body";
const virtualListRootClassName = "ant-table-virtual"; // rc-virtual-list

function ListPage(props) {
  // const { history, location, params } = props;
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const [tableScrollHeight, setTableScrollHeight] = useState(0);
  const [sidebarFilterHeight, setSidebarFilterHeight] = useState(0);
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [buttonsLoading, setButtonsLoading] = useState({});
  const [tableFullscreen, setTableFullscreen] = useState(false);
  const [polling, setPolling] = useState(false);
  const [remoteFilterOptionsMap, setRemoteFilterOptionsMap] = useState({});
  const filterSearchRef = useRef();
  const filterSelectRef = useRef();
  const filterCheckboxFormRef = useRef();
  const filterTreeFormRef = useRef();
  const filterTreeRef = useRef();
  const contentRef = useRef();
  const contentTopRef = useRef();
  const paramsRef = useRef({ filterTreeExpandedKeys: {} });
  const popoverRef = useRef();
  const operationRef = useRef();
  const actionRef = useRef();
  const sidebarActionsRef = useRef();
  const listHeaderRef = useRef();
  const pageHeaderRef = useRef();
  const isFlattenQueryParams = window.location.host.includes("kol");
  // const isFlattenQueryParams = window.location.host.includes("*************");
  const filterParams = getFilterParams();
  const sortParams = getSortParams();
  const isInsideIframe = Helper.isInsideIframe();
  const persistenceKey = location?.pathname;
  const persistenceType = "localStorage";
  const storageData = Utils.JSON.parse(window?.[persistenceType].getItem(persistenceKey));
  const isVirtual = typeof data?.tableProps?.virtual === "boolean" ? data?.tableProps?.virtual : defaultIsVirtual;
  const { t } = useI18n();

  paramsRef.current = {
    ...paramsRef.current,
    data,
    processFilterData,
    updateTableScrollHeight,
    sortParams,
    columnRender,
    fetchTableData,
    tableFullscreen,
    popoverOpen: { open: false },
    location,
    params,
    isVirtual,
    filterParams,
    remoteFilterOptionsMap,
    persistenceKey,
    persistenceType,
    updateSearchParams,
    storageData,
  };

  function forceUpdateTable() {
    const { data } = paramsRef.current;
    data.tableProps.dataSource = Utils.cloneDeep(data.tableProps.dataSource);
    setData({ ...data });
  }

  function updateSearchParams({ searchParams, deletePage = false }) {
    if (deletePage) {
      searchParams.delete("page");
    }
    navigate({ pathname: location.pathname, search: decodeURIComponent(searchParams.toString()) });
  }

  function handleTableChange(pagination, filters, sorter) {
    const searchParams = new URLSearchParams(location.search);
    processSort({ searchParams, sorter });
    processPagination({ searchParams, pagination });
    updateSearchParams({ searchParams });
  }

  function processSort({ searchParams, sorter }) {
    const sorters = sorter?.length > 0 ? sorter : [sorter];
    [...searchParams.keys()].forEach((key) => {
      if (key.indexOf("sort[") > -1) {
        searchParams.delete(key);
      }
    });
    sorters.forEach((item) => {
      const key = getSortKey(item.field);
      const value = item.order;
      if (value) {
        searchParams.set(key, value);
      } else {
        searchParams.delete(key);
      }
    });
  }

  function processPagination({ searchParams, pagination }) {
    const { current: page, pageSize, pageSizeOptions } = pagination;
    const defaultPageSize = pageSizeOptions?.[0] || pageSize;
    if (page > 1) {
      searchParams.set("page", page);
    } else {
      searchParams.delete("page");
    }
    if (pageSize !== defaultPageSize) {
      searchParams.set("pageSize", pageSize);
    } else {
      searchParams.delete("pageSize");
    }
  }

  function getFilterKey(dataIndex) {
    return `filter[${dataIndex}]`;
  }

  function getSortKey(dataIndex) {
    return `sort[${dataIndex}]`;
  }

  function createSearchFilter({ column }) {
    const { dataIndex, filter } = column;
    column.filterDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
      return (
        <div className={styles.filterControl}>
          <Search
            allowClear
            {...filter?.props}
            ref={filterSearchRef}
            defaultValue={decodeURIComponent(filterParams[dataIndex] || "")}
            onSearch={(keywords) => {
              close();
              const searchParams = new URLSearchParams(location.search);
              const filterKey = isFlattenQueryParams ? dataIndex : getFilterKey(dataIndex);

              if (keywords) {
                searchParams.set(filterKey, keywords);
              } else {
                searchParams.delete(filterKey);
              }
              updateSearchParams({ searchParams, deletePage: true });
            }}
          ></Search>
        </div>
      );
    };
    column.onFilterDropdownOpenChange = (visible) => {
      if (visible) {
        setTimeout(() => filterSearchRef.current?.focus(), 100);
      }
    };
  }

  function createSelectFilter({ column }) {
    column.filterDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
      const { dataIndex, filter } = column;
      const filterKeyType = typeof filter?.props?.options?.[0]?.value;
      const selectedValue = filterKeyType === "number" ? +filterParams[dataIndex] : filterParams[dataIndex];
      return (
        <div className={styles.filterControl}>
          <FilterSelect
            {...filter?.props}
            ref={filterSelectRef}
            value={selectedValue}
            searchable={filter?.searchable}
            searchApi={filter?.searchApi}
            onClick={(e, item) => {
              if (selectedValue !== item.value) {
                close();
                const searchParams = new URLSearchParams(location.search);
                const filterKey = isFlattenQueryParams ? dataIndex : getFilterKey(dataIndex);
                searchParams.set(filterKey, item.value);
                updateSearchParams({ searchParams, deletePage: true });
                setRemoteFilterOptionsMap((remoteFilterOptionsMap) => ({
                  ...remoteFilterOptionsMap,
                  [dataIndex]: [item],
                }));
              }
            }}
          />
        </div>
      );
    };
    column.onFilterDropdownOpenChange = async (visible) => {
      if (visible) {
        await Utils.sleep(100);
        filterSelectRef.current?.scrollToSelected();
      }
    };
  }

  function createCheckboxFilter({ column }) {
    const { dataIndex, filter } = column;
    const getDefaultValue = (dataIndex) => {
      if (!filterParams[dataIndex]) return null;
      const filterKeyType = typeof filter?.props?.options?.[0]?.value;
      return decodeURIComponent(filterParams[dataIndex] || "")
        ?.split(",")
        .map((item) => {
          if (filterKeyType === "number") {
            return +item;
          }
          return item;
        });
    };
    column.filterDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
      const checkedValue = getDefaultValue(dataIndex);
      return (
        <div className={styles.filterControl}>
          <Form
            ref={filterCheckboxFormRef}
            initialValues={{ [dataIndex]: checkedValue }}
            onValuesChange={(changedValues) => {
              filterCheckboxFormRef.current?.setFieldsValue(changedValues);
            }}
            onFinish={(values) => {
              close();
              const selected = values[dataIndex];
              const searchParams = new URLSearchParams(location.search);
              const filterKey = isFlattenQueryParams ? dataIndex : getFilterKey(dataIndex);
              if (selected?.length > 0) {
                searchParams.set(filterKey, selected);
              } else {
                searchParams.delete(filterKey);
              }
              updateSearchParams({ searchParams, deletePage: true });
            }}
          >
            <div className={styles.filterCheckbox}>
              <Form.Item name={dataIndex}>
                <FilterCheckbox {...filter?.props} checkedValue={checkedValue}></FilterCheckbox>
              </Form.Item>
            </div>
            <div className={styles.actions}>
              <Space>
                <Button
                  size="small"
                  onClick={() => {
                    filterCheckboxFormRef.current?.setFieldValue(dataIndex, []);
                  }}
                >
                  {t({ key: "Clear", defaultText: "清除" })}
                </Button>
                <Button size="small" type="primary" htmlType="submit">
                  {t({ key: "Confirm", defaultText: "确定" })}
                </Button>
              </Space>
            </div>
          </Form>
        </div>
      );
    };
    column.onFilterDropdownOpenChange = (visible) => {
      if (visible) {
        filterCheckboxFormRef.current?.setFieldValue(dataIndex, getDefaultValue());
      }
    };
  }

  function createTreeFilter({ column }) {
    const { dataIndex, filter } = column;
    column.filterDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
      const defaultValue = filterParams[dataIndex] ? decodeURIComponent(filterParams[dataIndex]).split(",") : [];
      return (
        <div className={styles.filterControl}>
          <Form
            ref={filterTreeFormRef}
            initialValues={{ [dataIndex]: defaultValue }}
            onValuesChange={(changedValues) => {
              filterTreeFormRef.current?.setFieldsValue(changedValues);
            }}
            onFinish={(values) => {
              close();
              const selected = values[dataIndex];
              const searchParams = new URLSearchParams(location.search);
              const filterKey = isFlattenQueryParams ? dataIndex : getFilterKey(dataIndex);
              if (selected?.length > 0) {
                searchParams.set(filterKey, selected);
              } else {
                searchParams.delete(filterKey);
              }
              updateSearchParams({ searchParams, deletePage: true });
            }}
          >
            <div className={styles.filterCheckbox}>
              <Form.Item name={dataIndex}>
                <FilterTree {...filter?.props} ref={filterTreeRef} defaultExpandAll={filter?.defaultExpandAll} />
              </Form.Item>
            </div>
            <div className={styles.actions}>
              <Space>
                <Button
                  size="small"
                  onClick={() => {
                    filterTreeFormRef.current?.setFieldValue(dataIndex, []);
                  }}
                >
                  {t({ key: "Clear", defaultText: "清除" })}
                </Button>
                <Button size="small" type="primary" htmlType="submit">
                  {t({ key: "Confirm", defaultText: "确定" })}
                </Button>
              </Space>
            </div>
          </Form>
        </div>
      );
    };
    column.onFilterDropdownOpenChange = (visible) => {
      if (visible) {
        filterTreeFormRef.current?.setFieldValue(dataIndex, filterParams[dataIndex]?.split(","));
        filterTreeRef.current?.setExpandedKeys(paramsRef.current.filterTreeExpandedKeys[dataIndex]);
      }
    };
  }

  function createRangePickerFilter({ column }) {
    const { dataIndex, filter } = column;
    const dateFormat = "YYYY-MM-DD";
    const param = filterParams[dataIndex] ? decodeURIComponent(filterParams[dataIndex]).split(",") : null;
    const defaultValue = param ? [dayjs(param[0], dateFormat), dayjs(param[1], dateFormat)] : param;
    column.filterDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
      return (
        <div className={styles.filterControl}>
          <FilterRangePicker
            {...filter?.props}
            defaultValue={defaultValue}
            onChange={(value) => {
              close();
              const searchParams = new URLSearchParams(location.search);
              const filterKey = isFlattenQueryParams ? dataIndex : getFilterKey(dataIndex);
              if (value) {
                searchParams.set(filterKey, value);
              } else {
                searchParams.delete(filterKey);
              }
              updateSearchParams({ searchParams, deletePage: true });
            }}
          />
        </div>
      );
    };
  }

  function processFilterData({ column }) {
    const { filter } = column;
    const { component } = filter;
    if (component === Enums.Components.Select) {
      createSelectFilter({ column });
    } else if (component === Enums.Components.Checkbox) {
      createCheckboxFilter({ column });
    } else if (component === Enums.Components.Search) {
      createSearchFilter({ column });
    } else if (component === Enums.Components.Tree) {
      createTreeFilter({ column });
    } else if (component === Enums.Components.RangePicker) {
      createRangePickerFilter({ column });
    }
    column.filterIcon = (filtered) => <SearchOutlined style={{ color: filtered ? "#1890ff" : null }} />;
  }

  function getFilterParams() {
    const queryParams = Utils.getQueryParams(decodeURIComponent(location.search));
    const filterParams = {};
    Object.keys(queryParams).forEach((key) => {
      let dataIndex = "";
      if (isFlattenQueryParams) {
        const columns = data?.tableProps?.columns || [];
        dataIndex = columns.find((column) => column.dataIndex === key)?.dataIndex;
      } else {
        dataIndex = /^filter\[(\w+)\]/.exec(key)?.[1];
      }
      if (dataIndex) {
        filterParams[dataIndex] = queryParams[key];
      }
    });
    return filterParams;
  }

  function getSortParams() {
    const queryParams = Utils.getQueryParams(decodeURIComponent(location.search));
    const sortParams = {};
    Object.keys(queryParams).forEach((key) => {
      const dataIndex = /^sort\[(\w+)\]/.exec(key)?.[1];
      if (dataIndex) {
        sortParams[dataIndex] = queryParams[key];
      }
    });
    return sortParams;
  }

  function clearFilters() {
    const searchParams = new URLSearchParams(decodeURIComponent(location.search));
    [...searchParams.keys()].forEach((key) => {
      if (isFlattenQueryParams) {
        const columns = data?.tableProps?.columns || [];
        const dataIndex = columns.find((column) => column.dataIndex === key)?.dataIndex;
        if (dataIndex) {
          searchParams.delete(key);
        }
      } else {
        if (key.startsWith("filter[")) {
          searchParams.delete(key);
        }
      }
    });
    updateSearchParams({ searchParams, deletePage: true });
  }

  function getHeightWithMargin(node) {
    const style = node ? getComputedStyle(node) : {};
    return node ? node.offsetHeight + +style.marginTop.replace("px", "") + +style.marginBottom.replace("px", "") : 0;
  }

  function updateTableScrollHeight() {
    const { tableFullscreen } = paramsRef.current;
    const table = document.querySelector(".ant-table-body");
    const pageHeaderHeight = pageHeaderRef.current?.offsetHeight || 53;
    const viewportHeight = window.innerHeight - pageHeaderHeight; // 减去 page-header 高度
    const headerHeight = document.querySelector(".layout-header")?.offsetHeight || 50;
    const footerHeight = document.querySelector(`[class~="page-footer"]`)?.offsetHeight || 35;
    const { paddingTop, paddingBottom } = getComputedStyle(document.querySelector(".page-content"));
    const pageContentPadding = +paddingTop.replace("px", "") + +paddingBottom.replace("px", "");
    const contentHeight = viewportHeight - headerHeight - footerHeight - pageContentPadding;
    const contentTopHeight = getHeightWithMargin(contentTopRef.current);
    const tableHeaderHeight = contentRef.current?.querySelector(".ant-table-header")?.offsetHeight;
    // const toolbarHeight = contentRef.current?.querySelector(".pro-table-list-toolbar")?.offsetHeight || 0;
    const toolbarHeight = contentRef.current?.querySelector(".my-pro-table-list-toolbar")?.offsetHeight || 0;
    const paginationHeight = getHeightWithMargin(contentRef.current?.querySelector(".ant-table-pagination"));
    const listHeaderHeight = getHeightWithMargin(listHeaderRef.current);
    let scrollHeight, sidebarFilterHeight;
    if (tableFullscreen) {
      const tableContainerPadding = 32;
      scrollHeight = window.innerHeight - tableContainerPadding - toolbarHeight - tableHeaderHeight - paginationHeight;
    } else {
      scrollHeight =
        contentHeight - contentTopHeight - tableHeaderHeight - toolbarHeight - paginationHeight - listHeaderHeight;
      sidebarFilterHeight = contentHeight - toolbarHeight - 10;
    }

    if (data?.tableProps?.summary_data) {
      scrollHeight = scrollHeight - document.querySelector(".ant-table-summary")?.offsetHeight;
    }

    if (table) {
      table.style.height = `${scrollHeight}px`;
    }

    const hasHeaderActions = data?.headerActions?.length > 0;
    const hasBreadcrumbs = data?.breadcrumbs?.length > 0;
    // 补偿高度
    if (!isInsideIframe) {
      if (!hasHeaderActions && !hasBreadcrumbs) {
        scrollHeight = scrollHeight + 55;
      }
    } else {
      if (!hasHeaderActions && !hasBreadcrumbs) {
        scrollHeight = scrollHeight + 110;
      } else if (hasHeaderActions || hasBreadcrumbs) {
        scrollHeight = scrollHeight + 55;
      }
    }

    setTableScrollHeight(scrollHeight);
    setSidebarFilterHeight(sidebarFilterHeight);
  }

  function getFilterLabel({ column }) {
    if (column) {
      const { dataIndex, filter } = column;
      const { component, props = {}, searchApi } = filter;
      const { options } = props;
      const value = decodeURIComponent(filterParams[dataIndex]);
      if (component === Enums.Components.Select) {
        if (searchApi) {
          return remoteFilterOptionsMap[dataIndex]?.find((item) => item.value + "" === value)?.label;
        } else {
          return options?.find((item) => item.value + "" === value)?.label;
        }
      } else if (component === Enums.Components.Checkbox) {
        const values = value.split(",");
        return options
          ?.filter((item) => values.includes(item.value + ""))
          .map((item) => item.label)
          .join(", ");
      } else if (component === Enums.Components.Tree) {
        const keys = value.split(",");
        const titles = [];
        Utils.forEachTree({
          treeData: filter?.props?.treeData,
          forEach: (item) => {
            if (keys.includes(item.key)) {
              titles.push(item.title);
            }
          },
        });
        return titles.join(", ");
      }
      return value;
    }
  }

  function createOperations({ operations, id }) {
    return operations.map((item, index) => {
      const { command } = item;
      const menu = { ...item?.props };
      const icon = item.icon ? <img className={styles.operationIcon} src={item.icon} alt="" /> : null;
      menu.label = (
        <div
          className={styles.operation}
          onClick={() => {
            handleCommand({ command, id });
          }}
        >
          {icon}
          {item.title}
        </div>
      );
      return menu;
    });
  }

  function columnRender({ column, row, value, index, action }) {
    const { id } = row;
    const extraData = row[`${column?.dataIndex}_extra_data`];
    const { style, icon, preview_group: previewGroup, badges, link, command, tags: tagData } = extraData || {};

    function renderValue() {
      if (column?.valueType === Enums.TableValueType.Operation) {
        // 优先渲染每行返回的operations
        const operations = value || column?.operations || null;
        if (!operations) return null;
        const items = createOperations({ operations, id });
        return (
          <div ref={operationRef}>
            <Dropdown
              menu={{ items }}
              trigger={["click"]}
              // getPopupContainer={() => operationRef.current}
              placement="bottomLeft"
              overlayClassName={styles.tableRowOperationsOverlay}
            >
              <Button
                type="primary"
                size="small"
                onClick={() => {
                  Params.selectedId = id;
                }}
              >
                <span>{column?.title ?? `操作`}</span>
                <CaretDownOutlined style={{ margin: 0, fontSize: 10 }} />
              </Button>
            </Dropdown>
          </div>
        );
      } else if (column?.editable || extraData?.editable) {
        const { dataIndex } = column;
        const editable = extraData?.editable || column?.editable;

        const extraSubmitData = { id };
        if (Array.isArray(editable.extraFields)) {
          editable.extraFields.forEach((field) => {
            if (row[field] !== undefined) {
              extraSubmitData[field] = row[field];
            }
          });
        }

        return (
          <div>
            <span>{value}</span>
            <Tags tags={tagData} />
            <EditablePopover
              ref={popoverRef}
              defaultValue={row[dataIndex]}
              field={dataIndex}
              editable={editable}
              isShowSubmitButton={editable?.component !== Enums.Components.ExternalPageEditor}
              extraSubmitData={extraSubmitData}
              rowData={row}
              onFinish={async (values, options) => {
                try {
                  Helper.pageLoading(true);
                  const optionLabel = options?.find((a) => a.value === values[dataIndex])?.label || undefined;
                  const result = await commandRequest({
                    request: editable?.request,
                    values: { field: dataIndex, value: values[dataIndex], label: optionLabel, ...extraSubmitData },
                  }).then((res) => res.data || []);

                  if (result?.success) {
                    if (options?.length > 0) {
                      row[dataIndex] = optionLabel;
                    } else {
                      row[dataIndex] = values[dataIndex];
                    }
                    popoverRef.current.hidden();
                    forceUpdateTable();
                  }
                  Helper.pageLoading(false);
                } catch (error) {
                  Helper.pageLoading(false);
                  if (!error?.response?.data?.command) {
                    Utils.dispatchEvent(Enums.EventName.AntdModal, "error", { title: "操作失败！" });
                  }
                }
              }}
            />
          </div>
        );
      } else if (column?.valueType === Enums.TableValueType.Command) {
        return (
          <span
            className="primary-color cursor-pointer"
            style={style}
            onClick={async () => {
              handleCommand({ command, id });
            }}
          >
            {value}
            <Tags tags={tagData} />
          </span>
        );
      } else if (column?.valueType === Enums.TableValueType.Image) {
        return value?.map((item, index) => (
          <Image.PreviewGroup key={index} {...previewGroup?.[index]}>
            <Badge {...badges?.[index]} className={classNames({ [styles.imageBadge]: badges?.[index]?.count > 0 })}>
              <Image {...item} {...column.image} preview={{ mask: <EyeOutlined /> }} />
            </Badge>
          </Image.PreviewGroup>
        ));
      } else if (column?.valueType === Enums.TableValueType.Link) {
        return (
          <Link to={link} className={styles.tableCellLink}>
            {value}
          </Link>
        );
      } else if (column?.valueType === Enums.TableValueType.Html) {
        return <HtmlBlock html={value} />;
      } else {
        return column.dataIndex ? (
          <div style={{ display: "flex", alignItems: "center", flexWrap: "wrap" }}>
            <Icon {...icon} />
            <span className={styles.tableCellText} style={style}>
              {value}
            </span>
            <Tags tags={tagData} />
          </div>
        ) : null;
      }
    }

    function getCopyContent(value) {
      if (typeof value === "string") {
        if (value.includes("data-clipboard=")) {
          const tempDiv = document.createElement("div");
          tempDiv.innerHTML = value;
          const copyContent = tempDiv.querySelector("[data-clipboard]")?.getAttribute("data-clipboard");
          return copyContent || value;
        }

        return value;
      }

      return "";
    }

    return (
      <span className={classNames("my-table-cell", styles.tableCellContent)}>
        {renderValue()}
        {column?.copyable && value && typeof value === "string" ? (
          <Typography.Text
            className={styles.copyableText}
            copyable={{
              text: getCopyContent(value),
            }}
          ></Typography.Text>
        ) : null}
      </span>
    );
  }

  const handleResize =
    ({ index }) =>
    (e, { size }) => {
      contentRef.current.style.userSelect = "none";
      setData((data) => {
        data.tableProps.columns[index].width = size.width;
        return { ...data };
      });
    };

  function initColumnsValue(columns) {
    columns?.forEach((column, index) => {
      const { filter, dataIndex } = column;
      const { processFilterData, sortParams, columnRender } = paramsRef.current;
      if (filter) {
        processFilterData({ column });
      }
      column.render = (value, row, index, action) => columnRender({ column, row, value, index, action });
      column.sortOrder = sortParams[dataIndex] || null;
      column.onHeaderCell = (column) => {
        return { column, index };
      };
      // column.onResize = handleResize({ index });
      column.onCell = (row, rowIndex) => {
        const extraData = row[`${dataIndex}_extra_data`];
        const { style } = extraData || {};
        const rowSpan = column.rowSpan?.[rowIndex];

        const cellAttr = {
          style,
          className: column?.valueType === Enums.TableValueType.Image ? "imageCell" : "",
        };
        if (rowSpan > -1) {
          return { rowSpan, ...cellAttr };
        }
        return cellAttr;
      };
    });
    return columns;
  }

  async function fetchTableData({ scrollToTop, hasLoading = true } = {}) {
    try {
      const { location, params, isVirtual } = paramsRef.current;
      const rootClass = isVirtual ? virtualListRootClassName : tableRootClassName;
      const antTableBody = contentRef.current.querySelector(".ant-table-body");

      if (antTableBody && scrollToTop) {
        antTableBody.scrollTop = 0;
      }

      if (hasLoading) {
        setLoading(true);
      }

      clearTableRowActive({ rootClass });
      clearTableActiveTableCell({ rootClass });

      // pageSize 持久化
      let newLocation = location;
      const { pagination } = storageData;
      if (pagination?.pageSize) {
        const searchParams = new URLSearchParams(location.search);
        searchParams.set("pageSize", pagination?.pageSize);
        newLocation = {
          ...newLocation,
          search: `?${decodeURIComponent(searchParams.toString())}`,
        };
      }

      const result = await fetchListPageData({
        url_key: Helper.getUrlKey({ location: newLocation, params }),
      });
      if (result?.data) {
        initColumnsValue(result?.data?.tableProps?.columns);
        setData(result.data);
      } else {
        setData({});
      }
      return result || {};
    } finally {
      if (hasLoading) {
        setLoading(false);
      }
      setTimeout(updateTableScrollHeight);
    }
  }

  async function handleCommand({ command, ...others }) {
    Helper.commandHandler({ command, ...others });
  }

  function commandRequest({ request, values }) {
    const { url, ...options } = request;
    return axiosRequest({
      url,
      method: "POST",
      ...options,
      data: { ...options.data, ...values },
    });
  }

  function handleTableFullscreenClick() {
    setTableFullscreen(!tableFullscreen);
  }

  function onRow(record) {
    return {
      onClick: (e) => {
        const { target } = e;
        const isTableBody = !!target.closest(`.${tableRootClassName}`);
        const isVirtualList = !!target.closest(`.${virtualListRootClassName}`);
        const { isVirtual } = paramsRef.current;
        const isPopover = !!target.closest(`.${styles.editablePopover}`);
        const isDropdown = !!target.closest(`.ant-dropdown`);
        if ((isTableBody || isVirtualList) && !isPopover && !isDropdown) {
          const rootClass = isVirtual ? virtualListRootClassName : tableRootClassName;
          clearTableRowActive({ rootClass });
          target.closest(".ant-table-row")?.classList?.add(styles.tableRowActive);
          clearTableActiveTableCell({ rootClass });
          if (target.closest(".ant-table-cell")) {
            target.closest(".ant-table-cell").setAttribute("id", "active-table-cell");
          }
        }
      },
    };
  }

  function clearTableRowActive({ rootClass }) {
    contentRef.current
      ?.querySelectorAll(`.${rootClass} .${styles.tableRowActive}`)
      .forEach((node) => node.classList.remove(styles.tableRowActive));
  }

  function clearTableActiveTableCell({ rootClass }) {
    contentRef.current
      ?.querySelectorAll(`.${rootClass} #active-table-cell`)
      .forEach((node) => node.removeAttribute("id"));
  }

  function renderTableActions({ actions }) {
    return actions?.map((item, index) => {
      const key = `action${index}`;
      const isDropdown = item.component === "Dropdown";
      const button = (
        <div className={styles.tableActionButtonWrapper}>
          <Icon src={item.icon} style={{ width: 16, height: 16 }} />
          <Button
            type="link"
            {...item.props}
            onClick={() => {
              if (!isDropdown)
                handleCommand({
                  command: item.command,
                  ids: selectedRowKeys,
                  ...Utils.getQueryParams(location.search),
                });
            }}
          >
            {item?.title}
          </Button>
        </div>
      );
      if (isDropdown) {
        return (
          <div className={styles.tableActionsItem} key={key}>
            <Dropdown
              {...item.dropdownProps}
              menu={{
                items: item.dropdownProps.menu.items?.map((item) => ({
                  ...item,
                  icon: <Icon src={item.icon} style={{ width: 16, height: 16 }} />,
                })),
                onClick({ key }) {
                  const menuItem = item.dropdownProps.menu.items?.find((item) => item.key === key);
                  handleCommand({
                    command: menuItem.command,
                    ids: selectedRowKeys,
                    ...Utils.getQueryParams(location.search),
                  });
                },
              }}
              trigger={["click"]}
              placement="bottomRight"
            >
              <Tooltip title={item?.tooltip}>{button}</Tooltip>
            </Dropdown>
          </div>
        );
      } else {
        return (
          <div className={styles.tableActionsItem} key={key}>
            {button}
          </div>
        );
      }
    });
  }

  useEffect(() => {
    const { fetchTableData } = paramsRef.current;
    function handleFetchTableData() {
      setSelectedRowKeys([]);
      fetchTableData({ hasLoading: true });
    }
    Utils.addEventListener(Enums.EventName.ReloadTable, handleFetchTableData);

    return function () {
      Utils.removeEventListener(Enums.EventName.ReloadTable, handleFetchTableData);
    };
  }, []);

  useEffect(() => {
    (async () => {
      const { fetchTableData } = paramsRef.current;
      fetchTableData({ scrollToTop: true });
    })();
  }, [location, params]);

  useEffect(() => {
    if (!loading) {
      const { updateTableScrollHeight } = paramsRef.current;
      updateTableScrollHeight();
    }
  }, [location, params, loading]);

  useEffect(() => {
    function handleWindowResize() {
      const { updateTableScrollHeight } = paramsRef.current;
      setTimeout(updateTableScrollHeight, 300);
    }
    window.addEventListener("resize", handleWindowResize);

    return function () {
      window.removeEventListener("resize", handleWindowResize);
    };
  }, []);

  useEffect(() => {
    const debounceUpdateTableScrollHeight = debounce(
      () => {
        const { updateTableScrollHeight } = paramsRef.current;
        updateTableScrollHeight();
      },
      0,
      { leading: false, trailing: true }
    );
    const observer = new MutationObserver(() => {
      debounceUpdateTableScrollHeight();
    });
    observer.observe(contentRef.current, { childList: true, subtree: true });
  }, []);

  useEffect(() => {
    document.body.classList.add(styles.disablePageScroll);

    return function () {
      document.body.classList.remove(styles.disablePageScroll);
    };
  }, []);

  useEffect(() => {
    function handleKeyDown(e) {
      const { tableFullscreen } = paramsRef.current;
      if (tableFullscreen && e.code === "Escape") {
        setTableFullscreen(false);
      }
    }
    document.addEventListener("keydown", handleKeyDown);

    return function () {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  useEffect(() => {
    const { updateTableScrollHeight } = paramsRef.current;
    updateTableScrollHeight();
  }, [tableFullscreen]);

  useEffect(() => {
    if (data?.polling > 0) {
      const { fetchTableData } = paramsRef.current;
      paramsRef.current.counter = new Counter({
        interval: data.polling,
        onTick() {
          fetchTableData({ hasLoading: false });
        },
      });
    }

    return function () {
      paramsRef.current.counter?.destroy();
    };
  }, [data?.polling]);

  useEffect(() => {
    const { counter } = paramsRef.current;
    polling ? counter?.start() : counter?.stop();
  }, [polling]);

  useEffect(() => {
    async function getRemoteFilterOptions() {
      const { filterParams, remoteFilterOptionsMap } = paramsRef.current;

      if (Object.keys(remoteFilterOptionsMap).length > 0) {
        return;
      }
      if (data?.tableProps?.columns && filterParams) {
        const requestArr = Object.keys(filterParams).map((dataIndex) => {
          const column = data?.tableProps?.columns?.find((item) => item.dataIndex === dataIndex);
          if (column?.filter?.searchApi) {
            return axios(column?.filter?.searchApi, {
              method: "GET",
              params: { type: "value", query: filterParams[dataIndex] },
            }).then((res) => res.data?.data || []);
          }
          return [];
        });

        try {
          const result = await Promise.all(requestArr);
          const remoteFilterOptionsMap = {};

          Object.keys(filterParams).forEach((dataIndex, index) => {
            const options = result[index] || [];
            remoteFilterOptionsMap[dataIndex] = options;
          });
          setRemoteFilterOptionsMap(remoteFilterOptionsMap);
        } catch (error) {
          // console.error("Failed to fetch remote filter options:", error);
        }
      }
    }
    getRemoteFilterOptions();
  }, [data]);

  return (
    <>
      <div ref={contentRef} className={styles.listPage}>
        <div className={styles.listPageWrapper}>
          {data?.breadcrumbs?.length || data?.headerActions?.length ? (
            <div ref={pageHeaderRef} className="page-header" style={{ position: isInsideIframe ? "static" : "sticky" }}>
              <div style={{ padding: "10px 0" }}>
                <Breadcrumbs data={data?.breadcrumbs}></Breadcrumbs>
              </div>

              <HeaderAction actions={data?.headerActions} selectedRowKeys={selectedRowKeys} />
            </div>
          ) : null}

          <ListHeader ref={listHeaderRef} />
          <div className={classNames(styles.container, { [styles.sidebarFolded]: store.sidebarFilterFold })}>
            {data?.sidebarActions && !isInsideIframe ? (
              <div className={styles.sidebarActions}>
                <div className={styles.sidebarActionsHeader}>
                  {!store?.sidebarFilterFold && !data?.sidebarActions?.type ? (
                    <div>
                      <Button
                        type="text"
                        onClick={() => {
                          sidebarActionsRef.current.clearFilters();
                        }}
                      >
                        清空
                      </Button>
                    </div>
                  ) : null}

                  <div
                    className={styles.sidebarFold}
                    onClick={() => {
                      localStorage.setItem(Enums.LocalStorageKey.SidebarFilterFold, !store.sidebarFilterFold);
                      store.setSidebarFilterFold(!store.sidebarFilterFold);
                    }}
                  >
                    {store.sidebarFilterFold ? <RightOutlined /> : <LeftOutlined />}
                  </div>
                </div>
                <div className={styles.sidebarFilterWrapper} style={{ height: sidebarFilterHeight }}>
                  <SidebarActions
                    ref={sidebarActionsRef}
                    data={data?.sidebarActions}
                    isFlattenQueryParams={isFlattenQueryParams}
                  />
                </div>
              </div>
            ) : null}

            <div
              className={classNames(styles.tableContainer, {
                [styles.tableFullscreen]: tableFullscreen,
                [styles.hasSidebarActions]: data?.sidebarActions && !isInsideIframe,
              })}
            >
              <ProTable
                {...data?.tableProps}
                className={styles.proTable}
                virtual={data?.tableProps?.dataSource?.length ? isVirtual : false}
                onRow={onRow}
                initColumnsValue={initColumnsValue}
                actionRef={actionRef}
                loading={loading}
                onChange={handleTableChange}
                scroll={{ y: tableScrollHeight }}
                search={false}
                rowSelection={
                  data?.tableProps?.rowSelection
                    ? {
                        selectedRowKeys,
                        columnWidth: 32,
                        onChange: (selectedRowKeys, selectedRows) => {
                          setSelectedRowKeys(selectedRowKeys);
                        },
                      }
                    : false
                }
                columnsState={{
                  persistenceKey,
                  persistenceType,
                }}
                toolbar={{
                  subTitle:
                    Object.keys(filterParams).length > 0 ? (
                      <FilterBar
                        data={data}
                        filterParams={filterParams}
                        clearFilters={clearFilters}
                        getFilterLabel={getFilterLabel}
                        location={location}
                        getFilterKey={getFilterKey}
                        updateSearchParams={updateSearchParams}
                      />
                    ) : null,
                  actions: (
                    <div className={styles.tableActions}>
                      {renderTableActions({ actions: data?.toolbarActions })}
                      <div className={styles.tableActionsItem} onClick={async () => setPolling(!polling)}>
                        <Tooltip
                          title={
                            polling ? t({ key: "Stop", defaultText: "停止" }) : t({ key: "Poll", defaultText: "轮询" })
                          }
                          spin={polling}
                        >
                          <SyncOutlined spin={polling} />
                        </Tooltip>
                      </div>
                      <div className={styles.tableActionsItem} onClick={handleTableFullscreenClick}>
                        {!tableFullscreen ? (
                          <Tooltip key="fullscreen" title={t({ key: "FullScreen", defaultText: "全屏" })}>
                            <FullscreenOutlined style={{ fontSize: 16 }} />
                          </Tooltip>
                        ) : (
                          <Tooltip key="exitFullscreen" title={t({ key: "ExitFullScreen", defaultText: "退出全屏" })}>
                            <FullscreenExitOutlined style={{ fontSize: 16 }} />
                          </Tooltip>
                        )}
                      </div>
                    </div>
                  ),
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

ListPage = observer(ListPage);

export default ListPage;
