import { Checkbox, Col, Collapse, Input, InputNumber, Row } from "antd";
import { ControlWrapper, FormControl, useForm } from "@/components/react-form-x";
import ImageUpload from "@/components/ImageUpload/v2";
import ColorPicker from "@/components/ColorPicker";
import PropTypes from "prop-types";
import styles from "./index.module.scss";
import OptionValueExtra from "../option-value-extra";

function SwatchValues(props) {
  const { control, keyPath, optionSet } = props;
  const form = useForm();

  return (
    <div>
      <Collapse
        items={control.options.map((option, index) => {
          const optionKeyPath = [...keyPath, "options", index];
          return {
            key: option.value,
            label: `Swatch Value: ${option.value}`,
            children: (
              <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
                <Row gutter={16}>
                  <Col span={6}>
                    <div style={{ display: "flex", flexDirection: "column", gap: 14 }}>
                      <FormControl
                        keyPath={optionKeyPath}
                        name="label"
                        render={(props) => {
                          return (
                            <ControlWrapper
                              {...props}
                              render={(props) => {
                                return (
                                  <div>
                                    <div>Label:</div>
                                    <div>
                                      <Input {...props} disabled></Input>
                                    </div>
                                  </div>
                                );
                              }}
                            ></ControlWrapper>
                          );
                        }}
                      ></FormControl>
                      <FormControl
                        keyPath={optionKeyPath}
                        name="value"
                        render={(props) => {
                          return (
                            <ControlWrapper
                              {...props}
                              render={(props) => {
                                const { name, value, onChange } = props;
                                return (
                                  <div>
                                    <div>Value:</div>
                                    <div>
                                      <InputNumber
                                        value={value}
                                        onChange={(value) => {
                                          onChange(null, { [name]: value });
                                        }}
                                        style={{ width: "100%" }}
                                        disabled
                                      ></InputNumber>
                                    </div>
                                  </div>
                                );
                              }}
                            ></ControlWrapper>
                          );
                        }}
                      ></FormControl>
                    </div>
                  </Col>
                  <Col>
                    <FormControl
                      keyPath={optionKeyPath}
                      name="thumb_image"
                      render={(props) => {
                        return (
                          <ControlWrapper
                            {...props}
                            render={(props) => {
                              const { name, value, onChange } = props;
                              return (
                                <div>
                                  <div>Swatch Image:</div>
                                  <div>
                                    <ImageUpload
                                      {...props}
                                      width={100}
                                      height={100}
                                      fileList={value?.src ? [{ ...value, url: value.src }] : []}
                                      onChange={(file, fileList) => {
                                        const nextFile = fileList[0];
                                        const { width, height } = nextFile?.response?.data?.image || {};
                                        const nextValue = { src: nextFile?.url, width, height };
                                        onChange(null, { [name]: nextValue });
                                      }}
                                      listType="picture-card"
                                      disabled
                                    ></ImageUpload>
                                  </div>
                                </div>
                              );
                            }}
                          ></ControlWrapper>
                        );
                      }}
                    ></FormControl>
                  </Col>
                  <Col>
                    <FormControl
                      keyPath={optionKeyPath}
                      name="bg_color"
                      render={(props) => {
                        return (
                          <ControlWrapper
                            {...props}
                            render={(props) => {
                              const { name, value, onChange } = props;
                              return (
                                <div>
                                  <div>Swatch Color:</div>
                                  <div>
                                    <ColorPicker
                                      value={value}
                                      onChange={(value, hex) => {
                                        onChange(null, { [name]: value?.toRgbString() });
                                      }}
                                      allowClear
                                      className={styles.fullWidthColorPicker}
                                      style={{ width: 100, height: 100 }}
                                      disabled
                                    ></ColorPicker>
                                  </div>
                                </div>
                              );
                            }}
                          ></ControlWrapper>
                        );
                      }}
                    ></FormControl>
                  </Col>
                  <Col>
                    <FormControl
                      keyPath={optionKeyPath}
                      name="selected"
                      render={(props) => {
                        return (
                          <ControlWrapper
                            {...props}
                            render={(props) => {
                              const { name, value, onChange } = props;
                              return (
                                <div>
                                  <div>&nbsp;</div>
                                  <div>
                                    <Checkbox
                                      checked={value ?? false}
                                      onChange={(event) => {
                                        control.options.forEach((option) => {
                                          option.selected = false;
                                        });
                                        form.updateValue(optionSet);
                                        onChange(null, { [name]: event.target.checked });
                                      }}
                                    >
                                      Selected
                                    </Checkbox>
                                  </div>
                                </div>
                              );
                            }}
                          ></ControlWrapper>
                        );
                      }}
                    ></FormControl>
                  </Col>
                </Row>
                <OptionValueExtra optionSet={optionSet} control={control} keyPath={optionKeyPath}></OptionValueExtra>
              </div>
            ),
          };
        })}
      ></Collapse>
    </div>
  );
}

SwatchValues.propTypes = {
  optionSet: PropTypes.object,
  control: PropTypes.object,
  keyPath: PropTypes.array,
  forceUpdate: PropTypes.func,
};

export default SwatchValues;
