import styles from "./index.module.scss";
import { useLocation } from "react-router-dom";
import useSWR from "swr";
import { useState, useRef } from "react";
import { Card, Upload } from "antd";
import Utils from "utils";
import Fetchers from "fetchers";
import Helper from "helpers";
import Enums from "enums";
import request from "@/fetchers/request";
import JSONForm from "components/common/JSONForm";
import EditPageHeader from "pages/edit/components/edit-page-header";
import Loading from "components/common/Loading";
import SelectWithPreview from "components/business/ticket/components/SelectWithPreview";
import RichTextEditor from "components/common/RichTextEditor";
import FileUpload from "components/common/FileUpload";

function TicketUpdate(props) {
  const location = useLocation();
  const queryParams = Utils.getQueryParams(decodeURIComponent(location.search));
  const [loading, setLoading] = useState();
  const [selectWithPreviewOptions, setSelectWithPreviewOptions] = useState([]);
  const formRef = useRef();
  const richTextEditorRef = useRef();

  const { data } = useSWR(queryParams, async () => {
    try {
      setLoading(true);
      return Fetchers.getTicketUpdate({ params: queryParams }).then((res) => res.data?.data);
    } finally {
      setLoading(false);
    }
  });

  function handleBeforeUpload(file) {
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      Helper.openMessage({ type: "error", content: "文件大小不能超过5M" });
      return Upload.LIST_IGNORE;
    }

    return true;
  }

  function renderExtraComponent({ item, cascaderOptions }) {
    const { component, key: name } = item;
    const validate = () => formRef.current?.validateFields([name]);
    const setFieldValue = (value) => formRef.current?.setFieldValue(name, value);

    if (component === "SelectTemplate") {
      if (cascaderOptions?.[name]) {
        item.props.options = cascaderOptions[name];
      }
      return (
        <SelectWithPreview
          richTextEditorRef={richTextEditorRef}
          item={item}
          form={formRef.current}
          options={item.props.options?.length ? item.props.options : selectWithPreviewOptions}
        />
      );
    } else if (component === "RichTextEditorTemplate") {
      return <RichTextEditor ref={richTextEditorRef} {...item.props} validate={validate} />;
    } else if (component === Enums.Components.FileUpload) {
      return <FileUpload {...item.props} beforeUpload={handleBeforeUpload} setFieldValue={setFieldValue} />;
    }
    return null;
  }

  async function fetchOptions(params) {
    const searchApi = `/rest/v1/ticket/ticket/ajax-all-template`;
    const result = await request(searchApi, {
      method: "GET",
      params,
    }).then((res) => res.data);
    return result?.data;
  }

  async function handleValuesChange(changedValues, allValues) {
    const keys = Object.keys(changedValues);

    if (keys.includes("brand_id") || keys.includes("target_lang")) {
      const { brand_id, target_lang } = allValues;
      if (brand_id && target_lang) {
        const options = await fetchOptions({ brand_id, target_lang });
        setSelectWithPreviewOptions(options);
      }
    }
  }

  return (
    <>
      <EditPageHeader breadcrumbs={data?.breadcrumbs} actions={data?.actions} />
      <div className={styles.container}>
        <Card>
          <Loading loading={loading}>
            {data?.content ? (
              <JSONForm
                ref={formRef}
                data={data?.content}
                onValuesChange={handleValuesChange}
                renderExtraComponent={renderExtraComponent}
              />
            ) : null}
          </Loading>
        </Card>
      </div>
    </>
  );
}

export default TicketUpdate;
