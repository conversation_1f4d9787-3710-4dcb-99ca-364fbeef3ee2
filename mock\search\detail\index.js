module.exports = async (req, res) => {
  const { order_number } = req.query;

  res.status(200).json({
    success: true,
    data: {
      // breadcrumbs: [
      //   {
      //     title: "搜索页",
      //     url: "/",
      //   },
      //   {
      //     title: "商品搜索",
      //     url: "/",
      //   },
      // ],
      content: {
        component: "JSONComponents",
        type: "json",
        children: [
          {
            component: "Row",
            props: { gutter: 16 },
            children: [
              {
                component: "Col",
                props: { span: 6 },
                children: [
                  {
                    component: "QueryFilter",
                    type: "json",
                    showButtons: false,
                    formItems: [
                      {
                        component: "Row",
                        children: [
                          {
                            component: "Col",
                            props: {
                              xs: {
                                span: 24,
                              },
                              lg: {
                                span: 24,
                              },
                            },
                            children: [
                              {
                                key: "order_number",
                                component: "Search",
                                props: {
                                  placeholder: "请扫描商品上的条形码",
                                  enterButton: "查询",
                                  isPressEnterSubmit: true,
                                  isSelected: true,
                                },
                              },
                              // {
                              //   key: "search",
                              //   component: "Search",
                              //   props: {
                              //     placeholder: "请扫描商品上的条形码",
                              //     enterButton: "查询",
                              //     isPressEnterSubmit: true,
                              //   },
                              // },
                            ],
                          },
                        ],
                      },
                    ],
                    submit: {
                      request: {
                        method: "get",
                      },
                    },
                  },
                ],
              },
              {
                component: "Col",
                props: { span: 18 },
                children: data?.[order_number] || null,
              },
            ],
          },
        ],
      },
    },
  });
};

const data = {
  order: [
    {
      component: "Card",
      props: { title: "产品信息", styles: { header: { backgroundColor: "#343a40", color: "#FFFFFF" } } },
      children: [
        {
          component: "NativeTable",
          props: {
            // style: { width: "100px" },
            header: { content: "展示header" },
            footer: {
              content: "<div style='color:#000'>展示footer</div>",
              style: { background: "#fafafa" },
            },
          },
          children: [
            [
              {
                tag: "th",
                valueType: "text",
                value: "运费险",
                props: {
                  style: { width: "50px" },
                },
              },
              {
                key: "freight",
                tag: "td",
                valueType: "text",
                value: "input 编辑",
              },
              {
                tag: "td",
                valueType: "text",
                value: "客户等级(当前等级/下单时等级)",
                props: { rowSpan: 3 },
              },
            ],
            [
              {
                tag: "th",
                valueType: "text",
                value: "运费险",
              },
              {
                tag: "td",
                valueType: "text",
                value: "0(usd)",
              },
            ],
            [
              {
                tag: "th",
                valueType: "text",
                value: "运费险",
              },
              {
                tag: "td",
                valueType: "text",
                value: "0(usd)",
              },
            ],
            [
              {
                tag: "th",
                valueType: "text",
                value: "定制属性",
              },
              {
                tag: "td",
                valueType: "native_table",
                value: {
                  props: {},
                  children: [
                    [
                      {
                        tag: "th",
                        valueType: "text",
                        value: "类型",
                      },
                      {
                        tag: "td",
                        valueType: "command",
                        value: "镜片-可以点击打开弹窗",
                        command: {
                          type: "modal",
                          closable: true,
                          title: "订单详情",
                          props: {
                            width: 1500,
                          },
                          content: {
                            component: "JSONComponents",
                            type: "json",
                            props: {},
                            children: [
                              {
                                component: "Iframe",
                                props: {
                                  src: `/dms/order/detail?id=1`,
                                },
                              },
                            ],
                          },
                        },
                      },
                      {
                        tag: "td",
                        valueType: "text",
                        value: "镜框",
                      },
                    ],
                    [
                      {
                        tag: "th",
                        valueType: "text",
                        value: "处方类型",
                      },
                      {
                        tag: "td",
                        valueType: "text",
                        value: "处方",
                        props: { colSpan: 2 },
                      },
                    ],
                    [
                      {
                        tag: "th",
                        valueType: "text",
                        value: "Glasses Type",
                      },
                      {
                        tag: "td",
                        valueType: "text",
                        value: "处方眼镜",
                      },
                      {
                        tag: "td",
                        valueType: "text",
                        value: "渐进处方",
                      },
                    ],
                  ],
                },
                props: {},
              },
            ],
          ],
        },
      ],
    },
    {
      component: "Card",
      props: { title: "出厂前数据" },
      children: [
        {
          component: "NativeTable",
          props: {},
          children: [
            [
              {
                tag: "th",
                valueType: "text",
                value: "SPU",
              },
              {
                tag: "td",
                key: "spu",
                valueType: "text",
                value: "Select 编辑",
                editable: {
                  component: "Select",
                  props: {
                    options: [
                      { label: "英语", value: "en" },
                      { label: "日语", value: "jp" },
                      { label: "法语", value: "fr" },
                      { label: "德语", value: "de" },
                      { label: "意大利语", value: "it" },
                    ],
                  },
                },
              },
              {
                tag: "th",
                valueType: "text",
                value: "唯一码",
              },
              {
                tag: "td",
                valueType: "text",
                value: "102036",
              },
              {
                tag: "th",
                valueType: "text",
                value: "尺码",
              },
              {
                tag: "td",
                valueType: "html",
                value: '<span style="color:red;font-weight:bold;">5.0</span>',
              },
            ],
            [
              {
                tag: "th",
                valueType: "text",
                value: "产品图",
              },
              {
                tag: "td",
                valueType: "image",
                value: [
                  {
                    width: 50,
                    height: 50,
                    src: "https://res2022.shesaidyes.com/uploads/produce-image/order/1286564/stone_setting_fids/2/3/660d16799b332.jpg",
                    text: "头-斑马: 序号47",
                    // props: {
                    //   preview: {
                    //     src: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png",
                    //   },
                    // },
                  },
                  {
                    width: 50,
                    height: 50,
                    src: "https://test-je5-oms.cnzlerp.com/uploads/product/e/9/6015357862b9e.jpg",
                    text: "头-大象: 序号41",
                  },
                  {
                    width: 50,
                    height: 50,
                    src: "https://test-je5-oms.cnzlerp.com/uploads/product/e/9/6015357862b9e.jpg",
                    text: "头-狮子: 序号43",
                  },
                  {
                    width: 50,
                    height: 50,
                    src: "https://test-je5-oms.cnzlerp.com/uploads/product/e/9/6015357862b9e.jpg",
                    text: "头-斑马: 序号41",
                  },
                ],
              },
              {
                tag: "th",
                valueType: "text",
                value: "标签",
              },
              {
                tag: "td",
                valueType: "tag",
                value: [
                  {
                    text: "加急",
                    props: {
                      color: "orange",
                    },
                  },
                  {
                    text: "高级定制",
                    props: {
                      color: "red",
                    },
                  },
                ],
              },
              {
                tag: "th",
                valueType: "text",
                value: "尺码",
              },
              {
                tag: "td",
                valueType: "html",
                value: '<span style="color:red;font-weight:bold;">5.0</span>',
              },
            ],
          ],
        },
      ],
    },
  ],
  search: [
    {
      component: "NativeTable",
      props: {
        // style: { width: "100px" },
        header: { content: "展示header" },
        footer: {
          content: "<div style='color:#000'>展示footer</div>",
          style: { background: "#fafafa" },
        },
      },
      children: [
        [
          {
            tag: "th",
            valueType: "text",
            value: "运费险",
            props: {
              style: { width: "50px" },
            },
          },
          {
            key: "freight",
            tag: "td",
            valueType: "text",
            value: "input 编辑",
          },
          {
            tag: "td",
            valueType: "text",
            value: "客户等级(当前等级/下单时等级)",
            props: { rowSpan: 3 },
          },
        ],
        [
          {
            tag: "th",
            valueType: "text",
            value: "运费险",
          },
          {
            tag: "td",
            valueType: "text",
            value: "0(usd)",
          },
        ],
        [
          {
            tag: "th",
            valueType: "text",
            value: "运费险",
          },
          {
            tag: "td",
            valueType: "text",
            value: "0(usd)",
          },
        ],
        [
          {
            tag: "th",
            valueType: "text",
            value: "定制属性",
          },
          {
            tag: "td",
            valueType: "native_table",
            value: {
              props: {},
              children: [
                [
                  {
                    tag: "th",
                    valueType: "text",
                    value: "类型",
                  },
                  {
                    tag: "td",
                    valueType: "command",
                    value: "镜片-可以点击打开弹窗",
                    command: {
                      type: "modal",
                      closable: true,
                      title: "订单详情",
                      props: {
                        width: 1500,
                      },
                      content: {
                        component: "JSONComponents",
                        type: "json",
                        props: {},
                        children: [
                          {
                            component: "Iframe",
                            props: {
                              src: `/dms/order/detail?id=1`,
                            },
                          },
                        ],
                      },
                    },
                  },
                  {
                    tag: "td",
                    valueType: "text",
                    value: "镜框",
                  },
                ],
                [
                  {
                    tag: "th",
                    valueType: "text",
                    value: "处方类型",
                  },
                  {
                    tag: "td",
                    valueType: "text",
                    value: "处方",
                    props: { colSpan: 2 },
                  },
                ],
                [
                  {
                    tag: "th",
                    valueType: "text",
                    value: "Glasses Type",
                  },
                  {
                    tag: "td",
                    valueType: "text",
                    value: "处方眼镜",
                  },
                  {
                    tag: "td",
                    valueType: "text",
                    value: "渐进处方",
                  },
                ],
              ],
            },
            props: {},
          },
        ],
      ],
    },
  ],
};
