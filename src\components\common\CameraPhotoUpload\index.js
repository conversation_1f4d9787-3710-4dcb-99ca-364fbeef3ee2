import { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Image } from "antd";
import styles from "./index.module.scss";
import Helper from "helpers";
import axios from "fetchers/request";
import PreviewImage from "./components/PreviewImage";
import Loading from "components/common/Loading";
import classNames from "classnames";

const ScreenshotConfig = {
  width: 800,
  height: 800,
};

function CameraPhotoUpload(props) {
  const { width = 300, height = 400, maxCount = 100, onChange, screenshot = ScreenshotConfig } = props;
  const [loading, setLoading] = useState(true);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [fileList, setFileList] = useState(props?.value || []);
  const [imgData, setImageData] = useState(null);
  const [hasOpenCamera, setHasOpenCamera] = useState(false);
  const previewRef = useRef();
  const screenshotRef = useRef();
  const canvasRef = useRef();
  const snapRef = useRef();

  const paramsRef = useRef();

  paramsRef.current = {
    initCamera,
    tracks: [],
  };

  function handleOpenCamera() {
    initCamera({ showError: true });
  }

  function dataURLtoBlob(url) {
    const arr = url.split(",");
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    const u8arr = new Uint8Array(bstr.length);
    u8arr.set(Array.from(bstr, (c) => c.charCodeAt(0)));
    return new Blob([u8arr], { type: mime });
  }

  function handleSnap() {
    // 手动上传模式，重新拍照逻辑
    if (imgData) {
      return setImageData(null);
    }

    const canvas = canvasRef.current;
    // const { width, height } = previewRef.current?.getBoundingClientRect();
    const { width, height } = screenshot;
    canvas.width = width;
    canvas.height = height;
    canvas.getContext("2d").drawImage(screenshotRef.current, 0, 0, width, height);
    const data = canvas.toDataURL("image/png");
    setImageData(data);
    if (props?.autoUpload && fileList?.length < maxCount) {
      handleUpload({ data });
    }
  }

  function handleOpenCameraError({ showError, error }) {
    if (showError) {
      console.error("无法打开摄像头", error);
      Helper.openMessage({ type: "error", content: "没有检测到可用的视频设备。请连接摄像头或确保权限已授予。" });
    }

    setHasOpenCamera(false);
    setLoading(false);
  }

  function handleDeviceDisconnected(track) {
    try {
      setHasOpenCamera(false);
      screenshotRef.current.srcObject = null;
      previewRef.current.srcObject = null;
      setImageData(null);
      if (track) {
        track.removeEventListener("ended", handleDeviceDisconnected);
      }
    } catch (error) {
      console.log(error);
    }
  }

  function handleListeningTrack({ stream }) {
    const tracks = stream.getTracks();
    tracks.forEach((track) => {
      // 确保每个track只有一个监听器
      track.removeEventListener("ended", handleDeviceDisconnected);
      track.addEventListener("ended", () => handleDeviceDisconnected(track));
    });
    paramsRef.current.tracks = tracks;
  }

  function initCamera({ showError = false } = {}) {
    try {
      setLoading(true);
      navigator.mediaDevices
        .getUserMedia({ video: true, audio: false })
        .then(function (stream) {
          screenshotRef.current.srcObject = stream;
          previewRef.current.srcObject = stream;

          handleListeningTrack({ stream });
          setHasOpenCamera(true);
        })
        .catch((error) => {
          handleOpenCameraError({ showError, error });
        })
        .finally(() => {
          setLoading(false);
        });
    } catch (error) {
      setLoading(false);
      handleOpenCameraError({ showError, error });
    }
  }

  async function handleUpload({ data }) {
    const formData = new FormData();
    const blob = dataURLtoBlob(data || imgData);
    formData.append("file", blob);
    if (props?.data) {
      Object.keys(props.data).forEach((key) => {
        formData.append(key, props.data[key]);
      });
    }

    try {
      setUploadLoading(true);
      const result = await axios({
        url: props.action || "/rest/v1/upload",
        method: "POST",
        headers: {
          "Content-Type": "multipart/form-data",
        },
        data: formData,
      }).then((res) => res.data);
      setImageData(null);

      const src = result?.data?.host + result?.data?.image?.src;
      const image = { fid: result?.data?.fid, url: src, ...result?.data };

      const newFileList = [...fileList];
      newFileList.push(image);
      onChange?.(newFileList);
      setFileList(newFileList);
    } finally {
      setUploadLoading(false);
    }
  }

  function handleRemove(file) {
    const { fid, url } = file;
    return new Promise((resolve) => {
      Helper.modal.confirm({
        title: "确定删除吗？",
        onOk: async () => {
          const result = await axios({
            url: props.action,
            method: "DELETE",
            data: { ...props.data, fid, url },
          }).then((res) => res.data);
          if (result.success) {
            const newFileList = [...fileList];
            const index = newFileList.findIndex((item) => item.fid === fid);
            if (index !== -1) {
              newFileList.splice(index, 1);
            }
            setFileList(newFileList);
            onChange?.(newFileList);
            resolve(true);
          }
        },
      });
    });
  }

  useEffect(() => {
    const { initCamera } = paramsRef.current;
    initCamera();
  }, []);

  return (
    <div className={styles.container}>
      <Loading loading={loading}>
        <div className={styles.content}>
          <div className={styles.previewWrapper}>
            {imgData && !props?.autoUpload ? (
              <Image
                rootClassName={styles.photoPreview}
                crossOrigin="anonymous"
                src={imgData}
                alt="预览图片"
                width={width}
              />
            ) : null}
            <video
              className={classNames(styles.preview, { [styles.activePreview]: hasOpenCamera })}
              ref={previewRef}
              width={width}
              height={height}
              muted
              autoPlay
            />
            <video
              ref={screenshotRef}
              className={styles.screenshot}
              width={screenshot.width}
              height={screenshot.height}
              muted
              autoPlay
            />
            <canvas ref={canvasRef}></canvas>
          </div>
          <div className={styles.actionsWrapper}>
            {!hasOpenCamera ? (
              <Button className={styles.openCame} size="small" onClick={handleOpenCamera}>
                打开摄像头
              </Button>
            ) : null}
            {hasOpenCamera ? (
              <Button ref={snapRef} type="primary" size="small" onClick={handleSnap} disabled={uploadLoading}>
                {imgData ? "重新拍照" : "拍照"}
              </Button>
            ) : null}
            {!props?.autoUpload && fileList?.length < maxCount ? (
              <Button type="primary" size="small" onClick={handleUpload} disabled={!imgData} loading={uploadLoading}>
                上传图片
              </Button>
            ) : null}
          </div>
        </div>
      </Loading>

      <div className={styles.imageList}>
        {fileList?.map((item, index) => (
          <div className={styles.imageItem} key={index}>
            <PreviewImage data={item} imageUrl={item.url} onRemove={handleRemove} />
          </div>
        ))}
      </div>
    </div>
  );
}

export default CameraPhotoUpload;
