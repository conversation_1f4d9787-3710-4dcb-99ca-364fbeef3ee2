import styles from "./index.module.scss";
import PropTypes from "prop-types";
import JSONComponents from "components/common/JSONComponent";

function TableFooter(props) {
  const { data } = props;

  if (!data) return null;

  return (
    <div className={styles.container}>
      <JSONComponents data={data} />
    </div>
  );
}

TableFooter.propTypes = {
  data: PropTypes.object,
  height: PropTypes.number,
};

export default TableFooter;
