.container {
  width: 100%;
  height: 100%;
  position: relative;
  min-height: 30px;
}

.spin {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 19999;
  display: flex;
  align-items: center;
  justify-content: center;
  //background-color: rgba(255, 255, 255, 0.65);
  padding: 8px;
}

.children {
  width: 100%;
  height: 100%;
  opacity: 1;
}

.loading {
  .children {
    opacity: 0;
  }
}
