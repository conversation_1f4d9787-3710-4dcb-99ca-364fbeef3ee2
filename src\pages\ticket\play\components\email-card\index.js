import styles from "./index.module.scss";
import Helper from "helpers";
import { Card, Radio, Tag, Collapse, Image } from "antd";
import Icon from "components/common/Icon";
import SubmitButton from "components/common/Button";
import HTMLBlock from "components/common/HtmlBlock";
import { renderActions } from "../../../vars";

function EmailCard(props) {
  const { data, updateData } = props;
  const { props: dataProps, content } = data;

  const items = [
    {
      key: "1",
      label: <span style={{ color: "#1677ff" }}>查看源文</span>,
      children: <HTMLBlock html={content?.translate_content} />,
    },
  ];

  async function handleActionClick({ command, values, refresh }) {
    await Helper.commandHandler({
      command: {
        ...command,
        request: {
          ...command.request,
          data: {
            ...command.request.data,
            ...values,
            external_id: content?.external_id,
            comment_id: content?.comment_id,
          },
        },
      },
    });
    updateData({ ...refresh?.params });
  }

  async function handleEmojiChange(e, emoji) {
    handleActionClick({ command: emoji?.command, refresh: emoji?.refresh, values: { emoji: e.target.value } });
  }

  function renderTitle() {
    return (
      <div className={styles.title}>
        <div className={styles.avatarWrapper}>
          <Icon className={styles.avatar} src={content?.profile_image} width="35" height="35" />
          <Icon src={content?.emoji} width="25" height="25" />
        </div>
        <div className={styles.info}>
          <div className={styles.name}>
            {content?.requester ? <span>{content.requester}</span> : null}
            {content?.emoji_radio ? (
              <Radio.Group onChange={(e) => handleEmojiChange(e, content.emoji_radio)}>
                {content?.emoji_radio?.props?.options?.map((item) => (
                  <Radio.Button key={item.value} value={item.value}>
                    <Icon src={item?.label} width="30" height="30" />
                  </Radio.Button>
                ))}
              </Radio.Group>
            ) : null}
            {content?.tags ? (
              <div>
                {content?.tags?.map((tag) => (
                  <Tag key={tag.text} {...tag?.props}>
                    {tag?.text}
                  </Tag>
                ))}
              </div>
            ) : null}
            {content?.actions ? (
              <div>
                {renderActions({
                  actions: content.actions,
                  onClick: async (item) => {
                    await handleActionClick({ command: item.command, refresh: item?.refresh });
                  },
                })}
              </div>
            ) : null}
          </div>
          {content?.datetime ? <div className={styles.dateTime}>{content.datetime}</div> : null}
          {content?.subject ? <div className={styles.subject}>Subject: {content.subject}</div> : null}
        </div>
      </div>
    );
  }

  function renderAttachments() {
    if (!content?.attachments?.length) return null;
    const images = content?.attachments?.filter((item) => item.type === "image") || [];
    const videos = content?.attachments?.filter((item) => item.type === "video") || [];
    const files = content?.attachments?.filter((item) => item.type === "file") || [];

    return (
      <div className={styles.attachmentWrapper}>
        <div>附件：</div>
        <div className={styles.attachmentImages}>
          <Image.PreviewGroup>
            {images.map((image, index) => (
              <Image width={80} height={80} {...image} key={index} />
            ))}
          </Image.PreviewGroup>
        </div>
        <div className={styles.attachmentVideos}>
          {videos.map((video) => (
            <video width={200} height={200} controls key={video.src}>
              <source src={video.src} type="video/mp4" />
            </video>
          ))}
        </div>
        <div className={styles.attachmentFiles}>
          {files.map((file, index) => (
            <div key={index}>
              <a href={file.src} download={file.name}>
                {file.name}
              </a>
            </div>
          ))}
        </div>
      </div>
    );
  }

  function renderCardContent() {
    return (
      <>
        <div className={styles.content}>
          <HTMLBlock html={content?.content} />
          {content?.translate_content ? <Collapse ghost items={items} /> : null}
        </div>
        {renderAttachments()}
      </>
    );
  }

  return (
    <div className={styles.container}>
      <Card title={renderTitle()} {...dataProps}>
        {renderCardContent()}
      </Card>
    </div>
  );
}

export default EmailCard;
