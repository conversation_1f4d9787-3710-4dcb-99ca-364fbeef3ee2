.container {
  position: relative;
  width: 100%;

  .stoneWrapper {
    width: 100%;
    overflow-x: auto;
  }

  [class~="ant-card"] {
    margin-bottom: 15px;
  }

  .labelWrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    gap: 10px;
  }

  table {
    table-layout: fixed;
    border-collapse: collapse;

    thead th {
      border-bottom: 2px solid #dee2e6;
      border-bottom-width: 1px;
      text-align: center;
      line-height: 35px;
      background-color: #f5f4f4;
    }

    th,
    td {
      padding: 2px;
      border: 1px solid #dee2e6;
    }

    td {
      vertical-align: top;
    }

    .stoneHeader {
      display: flex;
      align-items: center;
      justify-content: center;

      .stoneImage {
        max-height: 32px;
      }

      [class~="ant-radio-wrapper"] {
        height: 32px;
        align-items: center;
        margin: 0;

        span {
          display: flex;
        }
      }
    }

    tbody {
      .stoneTdWrapper {
        width: 100px;
        display: flex;
        flex-direction: column;
        gap: 5px;

        .stoneItemContent {
          width: 100%;
          height: 25px;
          line-height: 25px;
          text-align: center;
        }
      }
    }
  }
}
