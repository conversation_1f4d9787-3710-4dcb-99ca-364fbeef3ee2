import { Cascader, Checkbox, Form, Input, InputNumber, Radio, Select, Switch, Tree } from "antd";
import PropTypes from "prop-types";
import { forwardRef, useImperativeHandle, useRef } from "react";
import RemoteSearchSelect from "@/components/RemoteSearchSelect";
import Utils from "utils";
import RichTextEditor from "components/common/RichTextEditor";
import JSONEditor from "components/common/JSONEditor";
import ImageUpload from "components/common/ImageUpload";
import Enums from "enums";
import DatePicker from "components/common/DatePicker";
import Validator from "helpers/validator";

function JSONForm(props, ref) {
  const { data, onFinish, onFinishFailed } = props;
  const { layout = "vertical" } = data;
  const formRef = useRef();

  function renderComponent(item, name) {
    const { component } = item;
    const validate = () => formRef.current?.validateFields([name]);
    const setFieldValue = (value) => formRef.current?.setFieldValue(name, value);
    if (component === Enums.Components.Select) {
      if (item.searchApi) {
        return <RemoteSearchSelect {...item.props} api={item.searchApi} />;
      } else {
        return <Select {...item.props} />;
      }
    } else if (component === Enums.Components.Radio) {
      return <Radio.Group {...item.props} />;
    } else if (component === Enums.Components.Checkbox) {
      return <Checkbox.Group {...item.props} />;
    } else if (component === Enums.Components.Switch) {
      return <Switch {...item.props} />;
    } else if (component === Enums.Components.Tree) {
      let defaultExpandedKeys = [];
      if (item.props.defaultExpandedCheckedParents) {
        const treeData = Utils.buildTreeId({ treeData: item?.props?.treeData });
        defaultExpandedKeys = Utils.fillTreeKeysParents({
          treeData,
          keys: data?.initialValues?.[name],
        }).map((item) => item.key);
      }
      return <Tree {...item.props} defaultExpandedKeys={defaultExpandedKeys} />;
    } else if (component === Enums.Components.Textarea) {
      return <Input.TextArea {...item.props} />;
    } else if (component === Enums.Components.RichTextEditor) {
      return <RichTextEditor {...item.props} validate={validate} />;
    } else if (component === Enums.Components.JSONEditor) {
      return <JSONEditor {...item.props} validate={validate} />;
    } else if (component === Enums.Components.ImageUpload) {
      return <ImageUpload {...item.props} setFieldValue={setFieldValue} />;
    } else if (component === Enums.Components.Cascader) {
      return <Cascader {...item.props} />;
    } else if (component === Enums.Components.DatePicker) {
      return <DatePicker {...item.props} />;
    } else if (component === Enums.Components.RangePicker) {
      return <DatePicker.RangePicker {...item.props} />;
    } else if (component === Enums.Components.InputNumber) {
      return <InputNumber {...item.props} />;
    } else {
      return <Input {...item.props} />;
    }
  }

  function getFormRules(item) {
    const { component, rules = [] } = item;
    if ([Enums.Components.Checkbox, Enums.Components.Tree].includes(component)) {
      return rules?.map((rule) => {
        if (rule.required) {
          rule.validator = (rule, value) => {
            return new Promise((resolve, reject) => {
              if (Array.isArray(value) && value?.length > 0) {
                resolve();
              } else {
                reject(rule.message);
              }
            });
          };
        }
        return rule;
      });
    } else if ([Enums.Components.JSONEditor].includes(component)) {
      const { required, message } = rules?.[0] || {};
      return [
        {
          required: required,
          validateTrigger: ["onBlur"],
          validator: (rule, value) => {
            const promises = [];
            if (required) {
              promises.push(
                new Promise((resolve, reject) => {
                  if (!value?.trim?.()) {
                    reject(message);
                  } else {
                    resolve();
                  }
                })
              );
            }
            promises.push(
              new Promise((resolve, reject) => {
                try {
                  JSON.parse(value);
                  resolve();
                } catch (e) {
                  reject("Invalid JSON!");
                }
              })
            );
            return Promise.all(promises);
          },
        },
      ];
    } else if (component === Enums.Components.ImageUpload) {
      return rules.map((rule) => {
        if (rule.required) {
          const requiredMessage = rule.message;
          delete rule.message;
          rule.validator = (rule, fileList) => {
            const promises = [
              new Promise((resolve, reject) => {
                if (fileList?.length > 0) {
                  resolve();
                } else {
                  reject(requiredMessage);
                }
              }),
              new Promise((resolve, reject) => {
                const validFile = fileList.find((file) => file.status !== Enums.UploadFileStatus.Done);
                if (validFile) {
                  const message =
                    validFile.status === Enums.UploadFileStatus.Error
                      ? "Please delete the failed file"
                      : "Please wait for the file upload to complete";
                  reject(message);
                } else {
                  resolve();
                }
              }),
              new Promise((resolve, reject) => {
                const validFile = fileList.find((file) => !file.fid || !file.url);
                if (validFile) {
                  const field = !validFile.fid ? "fid" : "url";
                  const message = `The data field ${field} is missing, please try again or contact the developer`;
                  reject(message);
                } else {
                  resolve();
                }
              }),
            ];
            return Promise.all(promises);
          };
        }
        return rule;
      });
    } else {
      return rules?.map((rule) => {
        if (rule?.validatorName) {
          rule.validator = Validator[rule.validatorName];
        }
        return rule;
      });
    }
  }

  function getValuePropName(item) {
    const { component } = item;
    if (component === Enums.Components.Switch) {
      return "checked";
    } else if (component === Enums.Components.Tree) {
      return "checkedKeys";
    } else if (component === Enums.Components.ImageUpload) {
      return "fileList";
    } else {
      return "value";
    }
  }

  function getTrigger(item) {
    const { component } = item;
    if (component === Enums.Components.Tree) {
      return "onCheck";
    }
  }

  function handleValidateFailed(values) {
    onFinishFailed?.(values);
    setTimeout(() => {
      const element = document.querySelector(".ant-form-item-has-error");
      element?.scrollIntoView({ behavior: "smooth", block: "center" });
    }, 100);
  }

  useImperativeHandle(ref, () => {
    return formRef.current;
  });

  return (
    <Form ref={formRef} layout={layout} onFinish={onFinish} onFinishFailed={handleValidateFailed}>
      {Object.keys(data?.formItems || {})?.map((name, index) => {
        const item = data.formItems[name];
        const rules = getFormRules(item);
        const valuePropName = getValuePropName(item);
        const changeTrigger = getTrigger(item);
        return (
          <Form.Item
            key={index}
            name={name}
            label={item.label}
            rules={rules}
            valuePropName={valuePropName}
            trigger={changeTrigger}
          >
            {renderComponent(item, name)}
          </Form.Item>
        );
      })}
    </Form>
  );
}

JSONForm = forwardRef(JSONForm);

JSONForm.propTypes = {
  data: PropTypes.object,
  onFinish: PropTypes.func,
  onFinishFailed: PropTypes.func,
};

export default JSONForm;
