import { Modal, Typography, Button } from "antd";
import { ExportOutlined } from "@ant-design/icons";

import JSONEditor from "components/common/JSONEditor";
import Utils from "utils";
import Helper from "@/helpers";

import useToggle from "../../hooks/useToggle";

const { Text } = Typography;

const ExportModal = ({ open, data, onCancel }) => {
  const serializedValue = JSON.stringify(Utils.cloneDeep(data), null, 2);

  const onCopy = () => {
    Helper.openMessage({ type: "success", content: "复制成功" });
  };

  return (
    <Modal
      open={open}
      title="页面 JSON 结构"
      onCancel={onCancel}
      width={1000}
      destroyOnClose
      footer={[
        <Text
          key="copy"
          copyable={{
            text: serializedValue,
            icon: ["default", "success"].map((item) => (
              <Button key={item} type="primary">
                复制到剪贴板
              </Button>
            )),
            tooltips: false,
            onCopy,
          }}
        />,
      ]}
    >
      <JSONEditor value={serializedValue} height="700px" validate={() => {}} />
    </Modal>
  );
};

const ExportButton = ({ onExport }) => {
  return (
    <Button icon={<ExportOutlined />} onClick={onExport} iconPosition="end">
      导出
    </Button>
  );
};

const Export = ({ data }) => {
  const [open, { toggle: onToggle }] = useToggle();

  const onExport = () => {
    onToggle();
  };

  return (
    <>
      <ExportButton onExport={onExport} />
      <ExportModal open={open} data={data} onCancel={onToggle} />
    </>
  );
};

export default Export;
