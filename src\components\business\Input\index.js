import { useCallback } from "react";
import { Input as AntdInput } from "antd";

function Input({ autoFocus, isSelected, ...restProps }) {
  const ref = useCallback(
    (node) => {
      if (!node) return;

      if (autoFocus) {
        node?.focus();
      }
      if (isSelected) {
        node?.select();
      }
    },
    [autoFocus, isSelected]
  );

  return <AntdInput ref={ref} {...restProps} />;
}

export default Input;
