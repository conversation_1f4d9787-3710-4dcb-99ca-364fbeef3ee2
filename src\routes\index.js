import React, { lazy, Suspense } from "react";
import { Spin } from "antd";
import Layout from "components/Layout";
import Login from "../pages/login";
import Passport from "pages/passport";
import InterceptorComponent from "components/InterceptorComponent";
import RoutesContainer from "@/routes/container";

const QuickLoginRedirect = lazy(() => import("pages/quick-login-redirect"));
const NotFound = lazy(() => import("pages/404"));
const Index = lazy(() => import("pages/index"));
const List = lazy(() => import("pages/list"));
const Edit = lazy(() => import("pages/edit"));
const Detail = lazy(() => import("pages/detail"));
const SearchDetail = lazy(() => import("pages/search/detail"));
const EditPopup = lazy(() => import("pages/popup/edit-popup"));
const Test = lazy(() => import("pages/test"));
const PurchaseStockIn = lazy(() => import("pages/dms/purchase-stock-in"));
const QualityControl = lazy(() => import("pages/dms/quality-control"));
const Configuration = lazy(() => import("pages/configuration"));
const ShipOut = lazy(() => import("pages/dms/shipout"));
const OrderEditor = lazy(() => import("pages/dms/order/edit"));
const OtherPenaltyEdit = lazy(() => import("pages/dms/other-penalty/edit"));
const TicketPlay = lazy(() => import("pages/ticket/play"));
const TicketUpdate = lazy(() => import("pages/ticket/update"));
const ProductEdit = lazy(() => import("pages/pms/product/edit"));
const TemplatePropertyEdit = lazy(() => import("pages/pms/template-property/edit"));

const PodTemplates = lazy(() => import("pages/pod/templates"));
const PodEditTemplate = lazy(() => import("pages/pod/edit-template"));
const PodEditTemplateBg = lazy(() => import("pages/pod/edit-template-bg"));
const PodLibraries = lazy(() => import("pages/pod/libraries"));
const PodEditLibrary = lazy(() => import("pages/pod/edit-library"));
const PodEditCategory = lazy(() => import("pages/pod/edit-category"));
const PodOptionSets = lazy(() => import("pages/pod/option-sets"));
const PodEditOptionSet = lazy(() => import("pages/pod/edit-option-set"));
const PodIframeEditOptionSet = lazy(() => import("pages/pod/iframe-edit-option-set"));

const ThreeEditor = lazy(() => import("pages/three/editor"));
const ThreeGenerateProductImages = lazy(() => import("pages/three/generate-product-images"));
const ThreeMeshViewer = lazy(() => import("pages/three/mesh-viewer"));

const Crossword = lazy(() => import("pages/temp/crossword"));
const CrxImageCatcher = lazy(() => import("pages/temp/crx-image-catcher"));

const TestPod = lazy(() => import("pages/test/pod"));
const TestPodEditTemplate = lazy(() => import("pages/test/pod/edit-template"));
const TestMF = lazy(() => import("pages/test/mf"));
const Test3d = lazy(() => import("pages/test/3d"));

const Iframe = lazy(() => import("pages/iframe"));

const fallback = (
  <div className="root-fallback">
    <div className="root-fallback-wrapper">
      <Spin></Spin>
    </div>
  </div>
);

const lazyLoad = (children) => {
  return (
    <Suspense fallback={fallback}>
      <InterceptorComponent>{children}</InterceptorComponent>
    </Suspense>
  );
};

const loader = ({ title }) => {
  return () => (document.title = title);
};

const routes = [
  {
    element: <RoutesContainer />,
    children: [
      { path: "/login", element: lazyLoad(<Login />), loader: loader({ title: "登录" }) },
      { path: "/quick-login-redirect", element: lazyLoad(<QuickLoginRedirect />) },
      { path: "/passport", element: lazyLoad(<Passport />) },
      { path: "/test", element: lazyLoad(<Test />), loader: loader({ title: "test" }) },
      { path: "/test/pod", element: lazyLoad(<TestPod />) },
      { path: "/test/pod/edit-template", element: lazyLoad(<TestPodEditTemplate />) },
      { path: "/test/mf", element: lazyLoad(<TestMF />) },
      { path: "/test/3d", element: lazyLoad(<Test3d />) },
      { path: "/pod/iframe-edit-option-set", element: lazyLoad(<PodIframeEditOptionSet />) },
      { path: "/temp/crossword", element: lazyLoad(<Crossword />) },
      { path: "/temp/crx-image-catcher", element: lazyLoad(<CrxImageCatcher />) },
      {
        element: <Layout />,
        children: [
          { path: "/", element: lazyLoad(<Index />), loader: loader({ title: "首页" }) },
          { path: "/list/:url_key/*", element: lazyLoad(<List />) },
          { path: "/edit/:url_key/*", element: lazyLoad(<Edit />) },
          { path: "/detail/:url_key/*", element: lazyLoad(<Detail />) },
          { path: "/search/detail/:url_key/*", element: lazyLoad(<SearchDetail />) },
          { path: "/popup/edit-popup/:url_key/*", element: lazyLoad(<EditPopup />) },
          {
            path: "/dms/purchase-stock-in",
            element: lazyLoad(<PurchaseStockIn />),
            loader: loader({ title: "点数入仓" }),
          },
          { path: "/dms/quality-control", element: lazyLoad(<QualityControl />), loader: loader({ title: "质检" }) },
          { path: "/dms/shipOut", element: lazyLoad(<ShipOut />), loader: loader({ title: "发货" }) },
          {
            path: "/dms/order/detail",
            element: lazyLoad(<OrderEditor />),
            loader: loader({ title: "订单详情" }),
          },
          {
            path: "/dms/other-penalty/edit",
            element: lazyLoad(<OtherPenaltyEdit />),
            loader: loader({ title: "编辑其他罚款" }),
          },
          {
            path: "/configuration/:url_key/*",
            element: lazyLoad(<Configuration />),
            loader: loader({ title: "网站配置" }),
          },
          {
            path: "/ticket/ticket/play/*",
            element: lazyLoad(<TicketPlay />),
            loader: loader({ title: "ticket详情" }),
          },
          {
            path: "/ticket/ticket/update/*",
            element: lazyLoad(<TicketUpdate />),
            loader: loader({ title: "ticket详情" }),
          },
          {
            path: "/pms/template-property/edit",
            element: lazyLoad(<TemplatePropertyEdit />),
            loader: loader({ title: "设置版图属性" }),
          },
          { path: "/edit-popup/:url_key", element: lazyLoad(<EditPopup />) },
          { path: "/iframe/:url_key", element: lazyLoad(<Iframe />) },
          { path: "/pod/templates", element: lazyLoad(<PodTemplates />) },
          { path: "/pod/edit-template", element: lazyLoad(<PodEditTemplate />) },
          { path: "/pod/edit-template-bg", element: lazyLoad(<PodEditTemplateBg />) },
          { path: "/pod/libraries", element: lazyLoad(<PodLibraries />) },
          { path: "/pod/edit-library", element: lazyLoad(<PodEditLibrary />) },
          { path: "/pod/edit-category", element: lazyLoad(<PodEditCategory />) },
          { path: "/pod/option-sets", element: lazyLoad(<PodOptionSets />) },
          { path: "/pod/edit-option-set", element: lazyLoad(<PodEditOptionSet />) },
          {
            path: "/pms/product/edit",
            element: lazyLoad(<ProductEdit />),
            loader: loader({ title: "商品编辑" }),
          },
          { path: "/3d/editor", element: lazyLoad(<ThreeEditor />) },
          { path: "/3d/generate-product-images", element: lazyLoad(<ThreeGenerateProductImages />) },
          { path: "/3d/mesh-viewer", element: lazyLoad(<ThreeMeshViewer />) },
          { path: "*", element: lazyLoad(<NotFound />) },
        ],
      },
    ],
  },
];

export default routes;
