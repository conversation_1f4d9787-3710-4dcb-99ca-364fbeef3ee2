import React, { useState } from "react";

import BasicEditPage from "./components/BasicEditPage";
import RightPanel from "./components/RightPanel";
import Export from "./components/Export";
import LowCodeLayout from "./components/LowCodeLayout";
import BackPreviousButton from "@/pages/low-code/list-page/components/BackPreviousButton";

const DEFAULT_DATA = {
  breadcrumbs: [
    {
      title: "示例一",
      url: "/",
    },
    {
      title: "示例二",
      url: "/",
    },
  ],
  actions: [
    {
      title: "保存",
      props: {
        type: "primary",
      },
      command: {
        type: "submit",
        id: "form",
        data: {
          action: "save",
        },
      },
    },
  ],
  content: {
    component: "JSONComponents",
    props: {},
    type: "json",
    children: [],
  },
};

function LowCodeEditPage({ defaultData = DEFAULT_DATA }) {
  const [data, setData] = useState({ ...defaultData });

  const onSave = (values) => {
    setData((prevData) => ({ ...prevData, ...values }));
  };

  return (
    <LowCodeLayout
      Header={
        <div className="page-header">
          <div />
          <div className="page-header-actions">
            <Export data={data} />
            <BackPreviousButton />
          </div>
        </div>
      }
      Content={<BasicEditPage data={data} />}
      Sider={<RightPanel data={data} onBreadcrumbSave={onSave} onActionSave={onSave} onContentSave={onSave} />}
    />
  );
}

export default LowCodeEditPage;
