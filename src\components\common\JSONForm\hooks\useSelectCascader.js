import { useState } from "react";
import request from "@/fetchers/request";

function useSelectCascader(props) {
  const [cascaderOptions, setCascaderOptions] = useState({});

  async function handleSelectChange({ value, cascaderName, searchApi, form }) {
    if (searchApi) {
      const result = await request(searchApi, {
        method: "GET",
        params: { query: value },
      }).then((res) => res.data);
      if (result?.success) {
        const options = result?.data;
        setCascaderOptions((prev) => ({
          ...prev,
          [cascaderName]: options || [],
        }));
        form?.setFieldsValue({ [cascaderName]: undefined }); // 清空级联选择值
      }
    }
  }

  return { cascaderOptions, handleSelectChange };
}

export default useSelectCascader;
