import { Button, Input, Popconfirm, App } from "antd";
import { useState, useRef, forwardRef, useImperativeHandle } from "react";
import PropTypes from "prop-types";
import { Form, FormControl } from "@/components/react-form-x";
import { checkImageSize } from "@/pages/pod/common";

const SingleImageInput = forwardRef(({ onConfirm }, ref) => {
  const [value, setValue] = useState("");

  useImperativeHandle(
    ref,
    () => ({
      onConfirm: async () => {
        if (value) {
          await onConfirm?.(value);
        }
      },
    }),
    [value, onConfirm]
  );

  return (
    <Input
      value={value}
      onChange={(event) => {
        const nextValue = event.target.value;
        setValue(nextValue);
      }}
    />
  );
});

const MultiImageInput = forwardRef(({ onConfirm }, ref) => {
  const formRef = useRef();
  const { message } = App.useApp();

  useImperativeHandle(
    ref,
    () => ({
      onConfirm: async () => {
        const result = await formRef.current?.submit();
        if (result.success) {
          await checkImageSize(result.value.preview_image).catch((error) => {
            message.error(error.message || "添加图片时出现问题，请稍后重试");
            return Promise.reject(error);
          });
          await onConfirm?.({ ...result.value });
        } else {
          return Promise.reject(result.errors);
        }
      },
    }),
    [onConfirm, message]
  );

  return (
    <Form ref={formRef}>
      <div style={{ display: "flex", flexDirection: "column", rowGap: 20, paddingBottom: 10 }}>
        <FormControl
          name="prod_image"
          rule={{ required: true }}
          render={(props) => <Input {...props} placeholder="生产图" autoComplete="off" />}
        />
        <FormControl
          name="preview_image"
          rule={{ required: true }}
          render={(props) => <Input {...props} placeholder="预览图" autoComplete="off" />}
        />
      </div>
    </Form>
  );
});

function UpdateButton(props) {
  const { children, onConfirm, buttonProps, isMultiImageInput = false } = props;
  const ref = useRef();

  const renderTitle = () => {
    return isMultiImageInput ? (
      <MultiImageInput ref={ref} onConfirm={onConfirm} />
    ) : (
      <SingleImageInput ref={ref} onConfirm={onConfirm} />
    );
  };

  const handleConfirm = async () => {
    await ref.current?.onConfirm();
  };

  return (
    <>
      <Popconfirm title={renderTitle} icon={null} destroyTooltipOnHide onConfirm={handleConfirm}>
        <Button {...buttonProps}>{children}</Button>
      </Popconfirm>
    </>
  );
}

UpdateButton.propTypes = {
  buttonProps: PropTypes.object,
  onConfirm: PropTypes.func,
  isMultiImageInput: PropTypes.bool,
};

export default UpdateButton;
