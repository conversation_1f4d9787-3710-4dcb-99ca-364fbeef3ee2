.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;

  [class*="ant-upload"] {
    display: inline-block;
    width: 100%;
  }
}

.modal {
  width: 600px;

  .modalContent {
    .imageContainer {
      width: 560px;
      height: 560px;
      position: relative;

      .imageWrapper {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        img {
          max-width: 100%;
          max-height: 100%;
          width: auto;
          height: auto;
          object-fit: contain;
        }
      }

      .mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999;
        background-color: rgba(0, 0, 0, 0);
      }
    }

    .spotifyConfig {
      margin-top: 10px;

      .upload {
        margin-right: 10px;
      }
    }
  }

  [class~="cropper-face"] {
    background-repeat: no-repeat;
    background-size: 100% 100%;
    opacity: 1;
  }
}
