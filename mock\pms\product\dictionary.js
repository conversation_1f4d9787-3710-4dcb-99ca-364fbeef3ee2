module.exports = async (req, res) => {
  const { prefix = "" } = req.query;

  const data = {
    product_custom_option: [
      {
        value: "product_custom_option_stone_shape",
        label: "石头形状",
      },
      {
        value: "product_custom_option_metal_color",
        label: "电镀颜色",
      },
      {
        value: "product_custom_option_wordart_color",
        label: "艺术字颜色",
      },
      {
        value: "product_custom_option_wordart_font",
        label: "艺术字字体",
      },
      {
        value: "product_custom_option_font_size",
        label: "产品定制选项-字号",
      },
      {
        value: "product_custom_option_crystal",
        label: "是否",
      },
    ],
    product_custom_group_option: [
      {
        value: "product_custom_group_option_1",
        label: "姓名&生日石数量",
      },
      {
        value: "product_custom_group_option_3",
        label: "印刻数量",
      },
      {
        value: "product_custom_group_option_5",
        label: "姓名数量",
      },
      {
        value: "product_custom_group_option_6",
        label: "刻字数量",
      },
      {
        value: "product_custom_group_option_7",
        label: "拼图数量",
      },
      {
        value: "product_custom_group_option_stone",
        label: "石头数量",
      },
      {
        value: "product_custom_group_option_birthday_flowers",
        label: "生辰花数量",
      },
      {
        value: "product_custom_group_option_letter",
        label: "字母数量",
      },
    ],
    default: defaultData,
  };

  res.status(200).json({
    success: true,
    data: data[prefix] || data.default,
  });
};

const defaultData = [
  {
    value: "product_tag_style",
    label: "产品风格-列表页筛选项",
  },
  {
    value: "product_ring_size",
    label: "戒指尺码（用于次属性）",
  },
  {
    value: "product_tag_occasion",
    label: "佩戴场合",
  },
  {
    value: "product_tag_recipient",
    label: "适合人群",
  },
  {
    value: "sys_product_template",
    label: "产品详情页模板",
  },
  {
    value: "product_custom_option_stone_shape",
    label: "石头形状",
  },
  {
    value: "product_stone_color",
    label: "石头颜色（废弃，无需翻译）",
  },
  {
    value: "order_progress_state",
    label: "订单产品状态",
  },
  {
    value: "product_custom_option_metal_color",
    label: "电镀颜色",
  },
  {
    value: "product_custom_name_option",
    label: "定制选项字段名称",
  },
  {
    value: "product_custom_group_option_1",
    label: "姓名&生日石数量",
  },
  {
    value: "template_attr_group_dictionary_identifier",
    label: "版图属性分组",
  },
  {
    value: "product_custom_option_wordart_color",
    label: "艺术字颜色",
  },
  {
    value: "product_custom_option_wordart_font",
    label: "艺术字字体",
  },
  {
    value: "product_custom_option_font_size",
    label: "产品定制选项-字号",
  },
  {
    value: "product_custom_option_font_family",
    label: "产品定制选项-字体",
  },
  {
    value: "product_custom_tips_option",
    label: "输入提示文案",
  },
];
