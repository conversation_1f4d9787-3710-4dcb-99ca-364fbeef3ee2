import { useState } from "react";

import BackPreviousButton from "./components/BackPreviousButton";
import List from "@/pages/list";
import PropertyPanel from "./components/PropertyPanel";
import Export from "@/pages/low-code/edit-page/components/Export";
import LowCodeLayout from "@/pages/low-code/edit-page/components/LowCodeLayout";
import Utils from "@/utils";

function ListEditor() {
  const [pageData, setPageData] = useState({});

  return (
    <LowCodeLayout
      Header={
        <div className="page-header">
          <div />
          <div className="page-header-actions">
            <Export data={pageData} />
            <BackPreviousButton />
          </div>
        </div>
      }
      Content={<List isPreview previewData={Utils.cloneDeep(pageData)} />}
      Sider={<PropertyPanel pageData={pageData} onUpdatePageData={setPageData} />}
    ></LowCodeLayout>
  );
}

export default ListEditor;
