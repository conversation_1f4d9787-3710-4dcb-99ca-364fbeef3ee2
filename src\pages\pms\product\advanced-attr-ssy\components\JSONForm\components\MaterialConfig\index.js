import styles from "./index.module.scss";
import PropTypes from "prop-types";

import Enums from "@/pages/pms/product/advanced-attr-ssy/components/JSONForm/common/enums";
import Helper from "helpers";
import Fetchers from "fetchers";
import CommonEnums from "enums";
import Utils from "utils";

import { Card, Button, Checkbox, InputNumber, Radio, Space } from "antd";
import { useEffect, useRef } from "react";

function MaterialConfig(props) {
  const { value: materials = [], onChange, tables = [], isCustomPrice = false, form, params } = props;
  const table = tables[0] || {};
  const customList = ["925silver", "925silvergolden", "925silverrosegolden"];
  const paramsRef = useRef({});

  paramsRef.current = {
    materials,
    calculatePrices,
  };

  /**
   * 添加新材质组
   */
  function handleAdd() {
    const newMaterial = {
      info: {
        [Enums.InfoKeys.DefaultMaterialValue]: "10Kwhitegold",
      },
      data: {},
    };

    table?.columns.forEach((column, index) => {
      if (column) {
        const dataIndex = column?.dataIndex;
        const isActive = !customList.includes(dataIndex);

        newMaterial.data[dataIndex] = {
          index,
          active: isActive,
          price: 0,
        };
      }
    });

    const newMaterials = [...materials, newMaterial];
    onChange?.(newMaterials);

    if (!isCustomPrice && materials.length === 0) {
      calculatePrices({
        index: newMaterials.length - 1,
        materials: newMaterials,
        materialsData: [newMaterial],
      });
    }
  }

  /**
   * 删除材质组
   * @param {Number} index 材质组索引
   */
  function handleDelete(index) {
    const newMaterials = materials.filter((_, i) => i !== index);
    onChange?.(newMaterials);
  }

  /**
   * 更新材质激活状态
   * @param {Number} materialIndex 材质组索引
   * @param {String} materialKey 材质键名
   * @param {Boolean} checked 是否选中
   */
  function handleMaterialActiveChange(materialIndex, materialKey, checked) {
    const newMaterials = [...materials];
    newMaterials[materialIndex].data[materialKey].active = checked;
    onChange?.(newMaterials);
  }

  /**
   * 更新默认材质
   * @param {Number} materialIndex 材质组索引
   * @param {String} materialKey 材质键名
   */
  function handleDefaultMaterialChange(materialIndex, materialKey) {
    const newMaterials = [...materials];
    newMaterials[materialIndex].info[Enums.InfoKeys.DefaultMaterialValue] = materialKey;
    onChange?.(newMaterials);
  }

  /**
   * 更新材质价格
   * @param {Number} materialIndex 材质组索引
   * @param {String} materialKey 材质键名
   * @param {Number} price 价格
   */
  function handlePriceChange(materialIndex, materialKey, price) {
    const newMaterials = [...materials];
    newMaterials[materialIndex].data[materialKey].price = Math.round(price);
    onChange?.(newMaterials);
  }

  /**
   * 切换全选状态
   * @param {Number} materialIndex 材质组索引
   * @param {Boolean} checked 是否选中
   */
  function handleSelectAllChange(materialIndex, checked) {
    const newMaterials = [...materials];
    Object.keys(newMaterials[materialIndex].data).forEach((key) => {
      newMaterials[materialIndex].data[key].active = checked;
    });
    onChange?.(newMaterials);
  }

  async function calculatePrices({ materialsData, customPrice = isCustomPrice }) {
    if (customPrice || !materialsData) return;
    const product_id = form.getFieldValue("product_id") || params?.product_id;
    const stonesData = form.getFieldValue("gemstones");

    try {
      Helper.pageLoading(true);
      const result = await Fetchers.calculateStonePrice({
        data: { gemstones: stonesData, materials: materialsData, product_id },
      }).then((res) => res.data?.data);

      const { materials } = result;
      onChange?.(materials);
    } finally {
      Helper.pageLoading(false);
    }
  }

  useEffect(() => {
    function handleCalculatePrice() {
      const { materials, calculatePrices } = paramsRef.current;
      const newMaterials = [...materials];

      calculatePrices({ materialsData: newMaterials, customPrice: false });
    }
    Utils.addEventListener(CommonEnums.EventName.CalculateStonePrice, handleCalculatePrice);

    return function () {
      Utils.removeEventListener(CommonEnums.EventName.CalculateStonePrice, handleCalculatePrice);
    };
  }, []);

  return (
    <div className={styles.container}>
      {materials.map((material, index) => (
        <Card
          key={index}
          title={`M${index + 1}`}
          extra={
            <Button type="primary" danger onClick={() => handleDelete(index)}>
              删除
            </Button>
          }
          styles={{
            header: {
              backgroundColor: "#343a40",
              color: "#FFFFFF",
            },
          }}
          size="small"
        >
          <table className={styles.materialTable}>
            <thead>
              <tr>
                <th>材质</th>
                {table?.columns?.map((column) => (
                  <th key={column.dataIndex}>
                    <div className={styles.materialHeader}>
                      <Radio
                        checked={material.info[Enums.InfoKeys.DefaultMaterialValue] === column.dataIndex}
                        onChange={() => handleDefaultMaterialChange(index, column.dataIndex)}
                      >
                        <img className={styles.materialImage} src={column?.title} alt={column?.dataIndex} />
                      </Radio>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>
                  <Checkbox onChange={(e) => handleSelectAllChange(index, e.target.checked)}>全部启用</Checkbox>
                </td>
                {table?.columns?.map((column, columnIndex) => {
                  const dataIndex = column?.dataIndex;
                  const materialData = material.data[dataIndex];

                  return (
                    <td key={dataIndex || columnIndex}>
                      <Space direction="vertical">
                        <Checkbox
                          checked={materialData?.active}
                          onChange={(e) => handleMaterialActiveChange(index, dataIndex, e.target.checked)}
                        >
                          启用
                        </Checkbox>

                        {isCustomPrice || customList.includes(dataIndex) ? (
                          <InputNumber
                            size="small"
                            min={0}
                            precision={0}
                            value={materialData?.price || 0}
                            onChange={(value) => handlePriceChange(index, dataIndex, value)}
                          />
                        ) : (
                          <div>${materialData?.price || 0}</div>
                        )}
                      </Space>
                    </td>
                  );
                })}
              </tr>
            </tbody>
          </table>
        </Card>
      ))}

      <Button type="dashed" block onClick={handleAdd}>
        添加材质
      </Button>
    </div>
  );
}

MaterialConfig.propTypes = {
  value: PropTypes.array,
  onChange: PropTypes.func,
  tables: PropTypes.array,
  isCustomPrice: PropTypes.bool,
};

export default MaterialConfig;
