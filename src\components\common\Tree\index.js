import styles from "./index.module.scss";
import { Tree as AntdTree } from "antd";
import { DownOutlined } from "@ant-design/icons";
import { useState } from "react";
import Utils from "utils";
import Helper from "helpers";
import SubmitButton from "components/common/Button";
import axios from "@/fetchers/request";

function Tree(props) {
  const { data, onDrop, onSelect, ...others } = props;
  const [selectedNode, setSelectedNode] = useState(null);
  const [treeData, setTreeData] = useState(Utils.cloneDeep(data?.props?.treeData));

  async function handleAction({ node, command }) {
    await Helper.commandHandler({ key: node.key, command: command });
  }

  function renderTitle(node) {
    const isRoot = node?.key === treeData?.[0]?.key;
    return (
      <div className={styles.treeSpan}>
        <div className={styles.treeTitle}>{node.title}</div>
        {data?.actions?.map((action, index) => {
          const isAddAction = action?.key === "add";
          return isRoot && !isAddAction ? null : (
            <SubmitButton
              key={index}
              size="small"
              type="link"
              {...action.props}
              command={action.command}
              onClick={async (e) => {
                e.preventDefault();
                e.stopPropagation();
                await handleAction({ node, command: action.command });
              }}
            >
              {action.title}
            </SubmitButton>
          );
        })}
      </div>
    );
  }

  function handleSelect(selectedKeys, info) {
    setSelectedNode(info.node);
    onSelect?.(selectedKeys, info);
  }

  async function updateTreeOrder({ items }) {
    if (!data?.submit) return;
    const { url, ...options } = data?.submit?.request;
    await axios({
      url,
      method: "POST",
      ...options,
      data: { items },
    }).then((res) => res?.data || {});
  }

  function handleDrop(info) {
    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;
    const dropPos = info.node.pos.split("-");
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]); // top -1, bottom 1

    function isSameLevel(a, b) {
      const aLevel = a.props.pos.split("-").length;
      const bLevel = b.props.pos.split("-").length;
      return aLevel === bLevel;
    }

    function isSameParent(a, b) {
      const aLevel = a.props.pos.split("-");
      const bLevel = b.props.pos.split("-");

      aLevel.pop();
      bLevel.pop();

      return aLevel.join("") === bLevel.join("");
    }

    function isDropToFirst(a, b) {
      const aLevel = a.props.pos.split("-");
      const bLevel = b.props.pos.split("-");
      aLevel.pop();
      return aLevel.join("") === bLevel.join("");
    }

    const canDrop =
      isDropToFirst(info.dragNode, info.node) ||
      (isSameParent(info.dragNode, info.node) && isSameLevel(info.dragNode, info.node) && info.dropToGap);
    if (!canDrop) return;

    function loop(data, key, callback) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].key === key) {
          return callback(data[i], i, data);
        }
        if (data[i].children) {
          loop(data[i].children, key, callback);
        }
      }
    }

    const data = [...treeData];

    let dragObj;
    loop(data, dragKey, (item, index, arr) => {
      arr.splice(index, 1);
      dragObj = item;
    });

    if (!info.dropToGap) {
      loop(data, dropKey, (item) => {
        item.children = item.children || [];
        item.children.unshift(dragObj);
        updateTreeOrder({ items: item.children });
      });
    } else {
      let ar = [];
      let i;
      loop(data, dropKey, (_item, index, arr) => {
        ar = arr;
        i = index;
      });
      if (dropPosition === -1) {
        ar.splice(i, 0, dragObj);
      } else {
        ar.splice(i + 1, 0, dragObj);
      }

      updateTreeOrder({ items: ar });
    }

    setTreeData(data);
  }

  return (
    <AntdTree
      className={styles.tree}
      showLine
      blockNode
      selectable
      defaultExpandAll
      {...data?.props}
      {...others}
      switcherIcon={<DownOutlined />}
      onSelect={handleSelect}
      titleRender={renderTitle}
      onDrop={onDrop || handleDrop}
      treeData={treeData}
    />
  );
}

export default Tree;
