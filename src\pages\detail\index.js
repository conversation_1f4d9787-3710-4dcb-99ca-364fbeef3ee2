import Loading from "components/common/Loading";
import Fetchers from "@/fetchers";
import Helper from "@/helpers";
import { useLocation, useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import JSONComponents from "components/common/JSONComponent";

function DetailPage(props) {
  const location = useLocation();
  const params = useParams();
  const [data, setData] = useState({});
  const url_key = Helper.getUrlKey({ location, params });

  useEffect(() => {
    (async () => {
      try {
        const result = await Fetchers.getDetailPageData({ url_key }).then((res) => res.data);
        setData(result?.data);
      } finally {
      }
    })();
  }, [url_key]);

  return <>{data?.content ? <JSONComponents data={data?.content} /> : null}</>;
}

export default DetailPage;
