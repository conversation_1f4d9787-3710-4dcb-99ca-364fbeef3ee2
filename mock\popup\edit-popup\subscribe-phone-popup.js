const Api = require("../../../src/fetchers/api");

const contentValue = {
  style: { right: 0 },
  title: {
    text: "Enter your phone number to unlock $10 OFF",
    style: {},
  },
  form: {
    label: {
      text: "Phone Number",
      style: {},
    },
    input: {
      placeholder: "Phone Number",
      style: {},
    },
    privacy_policy_checkbox: {
      show: true,
      value: true,
      error_message: "Please check this box.",
    },
    tips: {
      text: "By subscribing, you accept receiving notifications via SMS. See our ",
      style: {},
    },
    privacy_policy: {
      text: "Privacy Policy.",
      href: "https://www.google.com",
      style: {
        color: "#FF9600",
        textDecoration: "underline",
      },
    },
    error_message: {
      text: "This is a required field",
      style: {},
    },
    subscribed_message: {
      text: "This phone number is already subscribed.",
      style: {},
    },
    button: {
      text: "SUBMIT",
      style: {},
    },
  },
  do_not_show_again: {
    text: "Don't show me again",
    style: {},
  },
  sideBar: {
    arrow: {
      image: {
        width: 11,
        height: 18,
        src: "https://static.bizseas.com/static/popup/sms-popup/pc/expand.png",
      },
    },
    icon: {
      image: {
        width: 20,
        height: 20,
        src: "https://static.bizseas.com/static/popup/sms-popup/pc/sms-telephone.png",
      },
    },
    title: {
      text: "GET $10 OFF NOW",
      style: {},
    },
    style: {},
  },
};

const steps = [
  {
    title: "基础设置",
    name: "template_info",
    forms: [
      {
        title: "模板设置",
        name: "template_config",
        formItems: [
          {
            key: "type",
            label: "弹窗类型",
            component: "Select",
            props: {
              options: [{ label: "订阅电话弹窗", value: "sms" }],
              disabled: true,
            },
          },
          {
            key: "images",
            label: "弹窗说明图",
            component: "ImageUpload",
            props: {
              listType: "picture-card",
              action: Api.uploadFile,
              multiple: true,
            },
          },
        ],
        props: {
          labelCol: {
            span: 6,
          },
          wrapperCol: {
            span: 18,
          },
          initialValues: {
            type: 1,
            images: [
              {
                uid: 1,
                fid: 1,
                name: "photo",
                status: "done",
                url: "https://res2022.jeulia.com/product/2/1/1000x1000/62fde0f091112.jpg",
              },
            ],
          },
        },
      },
      {
        title: "展示条件",
        name: "condition",
        formItems: [
          {
            key: "user_types",
            label: "人群",
            component: "Select",
            props: {
              mode: "multiple",
              options: [
                { label: "选项1", value: "1" },
                { label: "选项2", value: "2" },
                { label: "选项3", value: "3" },
                { label: "选项4", value: "4" },
                { label: "选项5", value: "5" },
              ],
            },
          },
          {
            key: "device",
            label: "设备",
            component: "Select",
            props: {
              mode: "multiple",
              options: [
                { label: "PC", value: "pc" },
                { label: "M", value: "mobile" },
              ],
            },
          },
          {
            key: "popup_pages",
            label: "展示页面",
            component: "Select",
            props: {
              mode: "multiple",
              options: [
                {
                  value: "home",
                  label: "首页",
                },
                {
                  value: "product",
                  label: "产品页",
                },
                {
                  value: "category",
                  label: "类目页",
                },
                {
                  value: "cart",
                  label: "购物车页",
                },
                {
                  value: "checkout",
                  label: "结算页",
                },
                {
                  value: "success",
                  label: "支付完成页",
                },
                {
                  value: "others",
                  label: "其他页面",
                },
              ],
            },
          },
          {
            key: "timing",
            label: "弹出时机",
            component: "Select",
            props: {
              options: [
                { label: "即时弹出", value: "immediately" },
                { label: "延时弹出", value: "delayed" },
                { label: "滚动弹出", value: "scroll" },
                { label: "不自动弹出", value: "none" },
              ],
            },
          },
          {
            key: "delay",
            label: "延时秒数",
            component: "Input",
          },
          {
            key: "scroll_distance",
            label: "滚动距离",
            component: "Input",
          },
          {
            key: "interval",
            label: "弹出频率（单位：小时）",
            component: "Input",
            rules: [{ required: true, message: "弹出频率是必填项！" }],
            props: {
              extra: "填写24，则表示弹窗关闭24小时后再弹",
            },
          },
        ],
        props: {
          labelCol: {
            span: 6,
          },
          wrapperCol: {
            span: 18,
          },
          initialValues: {
            // user_types: ["1", "2", "3"],
            device: ["pc", "mobile"],
            interval: 24,
            popup_pages: ["/edit-popup", "/category-"],
            timing: "scroll",
            delay: 0,
            scroll_distance: 100,
          },
        },
      },
    ],
  },
  {
    title: "弹窗主体",
    name: "content",
    forms: [
      {
        name: "main",
        formItems: [
          {
            key: "pc",
            label: "PC端内容",
            component: "JSONEditor",
            props: { disabled: false },
          },
          {
            key: "mobile",
            label: "M端内容",
            component: "JSONEditor",
            props: { disabled: false },
          },
        ],
        props: {
          labelCol: {
            span: 3,
          },
          wrapperCol: {
            span: 21,
          },
          initialValues: {
            pc: JSON.stringify(contentValue, null, 2),
            mobile: JSON.stringify(contentValue, null, 2),
          },
        },
      },
    ],
  },
  {
    title: "结果弹窗",
    name: "content",
    forms: [
      {
        name: "success",
        formItems: [
          {
            key: "pc",
            label: "PC端内容",
            component: "JSONEditor",
            props: { disabled: false },
          },
          {
            key: "mobile",
            label: "M端内容",
            component: "JSONEditor",
            props: { disabled: false },
          },
        ],
        props: {
          labelCol: {
            span: 3,
          },
          wrapperCol: {
            span: 21,
          },
          initialValues: {
            pc: JSON.stringify({
              icon: {
                image: {
                  width: 34,
                  height: 34,
                  src: "https://static.bizseas.com/static/popup/sms-popup/pc/subscribed-success.png",
                },
              },
              title: {
                text: "Thank you for your subscription!",
                style: {},
              },
              tips: {
                text: "You've received a $10 discount! use code: ",
                style: {},
              },
              code: {
                text: "SMS10",
                style: {},
              },
              message: {
                text: "We will send you a confirmation of your purchase via SMS.",
                style: {},
              },
            }),
            mobile: JSON.stringify(
              {
                icon: {
                  image: {
                    width: 28,
                    height: 28,
                    src: "https://static.bizseas.com/static/popup/sms-popup/mobile/success.png",
                  },
                },
                title: {
                  text: "Thank you for your subscription!",
                  style: {},
                },
                tips: {
                  text: "You've received a $10 discount! use code: ",
                  style: {},
                },
                code: {
                  text: "SMS10",
                  style: {},
                },
                message: {
                  text: "We will send you a confirmation of your purchase via SMS.",
                  style: {},
                },
              },
              null,
              2
            ),
          },
        },
      },
    ],
  },
  {
    title: "收起样式",
    name: "content",
    forms: [
      {
        name: "take_back",
        formItems: [
          {
            key: "pc",
            label: "PC端内容",
            component: "JSONEditor",
            props: { disabled: false },
          },
          {
            key: "mobile",
            label: "M端内容",
            component: "JSONEditor",
            props: { disabled: false },
          },
        ],
        props: {
          labelCol: {
            span: 3,
          },
          wrapperCol: {
            span: 21,
          },
          initialValues: {
            pc: JSON.stringify({
              icon: {
                image: {
                  width: 20,
                  height: 20,
                  src: "https://static.bizseas.com/static/popup/sms-popup/pc/telephone.png",
                },
              },
              style: {},
            }),
            mobile: JSON.stringify(
              {
                icon: {
                  image: {
                    width: 20,
                    height: 20,
                    src: "https://static.bizseas.com/static/popup/sms-popup/pc/telephone.png",
                  },
                },
                style: {},
              },
              null,
              2
            ),
          },
        },
      },
    ],
  },
];

module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      breadcrumbs: [],
      type: "SubscribePhonePopup",
      steps,
    },
    command: [],
  });
};
