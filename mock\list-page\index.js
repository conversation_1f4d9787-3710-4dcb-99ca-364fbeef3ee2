const orderList = require("./order-list");
const orderListHeader = require("./order-list-header");
const createOrder = require("./create-order");
const dynamicData = require("./dynamic-data");

module.exports = {
  "GET /rest/v1/order-list": async (req, res) => orderList(req, res),

  "GET /rest/v1/order-list/layout": async (req, res) => orderListHeader(req, res),

  "GET /rest/v1/list/order-list/create-order": async (req, res) => createOrder(req, res),

  "GET /rest/v1/order-list/dynamic-data": async (req, res) => dynamicData(req, res),
};
