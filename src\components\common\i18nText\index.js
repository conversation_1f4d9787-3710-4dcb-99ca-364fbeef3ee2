import { Fragment } from "react";
import { useI18n } from "@/context/I18nContext";
import PropTypes from "prop-types";

function I18nText({ params = {}, defaultText, children, ...restProps }) {
  const { t } = useI18n();

  <Fragment {...restProps}>{t(params) || defaultText}</Fragment>;
}

I18nText.propTypes = {
  params: PropTypes.object,
  defaultText: PropTypes.string,
  children: PropTypes.node,
};

export default I18nText;
