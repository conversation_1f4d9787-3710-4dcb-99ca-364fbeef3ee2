import styles from "./index.module.scss";
import { useMemo } from "react";
import { Empty } from "antd";
import { useDroppable } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import classNames from "classnames";

import ComponentItem from "./components/ComponentItem";
import { DroppableIds } from "../../enums";

function CanvasPanel({
  components = [],
  flattenedComponents = [],
  selectedComponentId,
  onSelect,
  onDelete,
  isOverDropZone,
  ...restProps
}) {
  const { attributes, listeners, setNodeRef, transform, transition } = useDroppable({
    id: DroppableIds.Canvas,
    data: {
      isContainer: true,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // 获取所有组件ID用于排序
  const sortableItems = useMemo(
    () => flattenedComponents.filter((component) => !component?.dragDisabled).map((component) => component.id),
    [flattenedComponents]
  );

  return (
    <SortableContext items={sortableItems} strategy={verticalListSortingStrategy}>
      <div
        ref={setNodeRef}
        className={classNames(styles.canvasPanel, { [styles.isOver]: isOverDropZone })}
        style={style}
        {...attributes}
        {...listeners}
      >
        {components.length === 0 ? (
          <div className={styles.empty}>
            <Empty description="拖拽组件到此处" />
          </div>
        ) : (
          <div className={styles.componentList}>
            {components.map((component, index) => (
              <ComponentItem
                key={component.id}
                index={index}
                component={component}
                isSelected={component.id === selectedComponentId}
                selectedComponentId={selectedComponentId}
                onSelect={onSelect}
                onDelete={onDelete}
                // onDelete={() => onDelete(component.id)}
                {...restProps}
              />
            ))}
          </div>
        )}
      </div>
    </SortableContext>
  );
}

export default CanvasPanel;
