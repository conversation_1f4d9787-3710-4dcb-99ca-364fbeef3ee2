import { Control<PERSON>rapper, FormControl, useForm } from "@/components/react-form-x";
import { Checkbox, Collapse, Input, Select } from "antd";
import styles from "./index.module.scss";
import { OptionType, renderI18nSelect } from "@/pages/pod/common";
import PropTypes from "prop-types";
import useForceUpdate from "@/hooks/useForceUpdate";
import { renderPrivateControls, renderSubCollapseItems } from "@/pages/pod/components/option-set-control-editor/common";

const optionTypeOptions = [
  { label: "Text Input", value: OptionType.TextInput },
  { label: "Dropdown", value: OptionType.Dropdown },
  { label: "Swatch", value: OptionType.Swatch },
  { label: "Image Upload", value: OptionType.ImageUpload },
];

function OptionSetControlEditor(props) {
  const { keyPath, control, optionSet, onLabelChange } = props;
  const forceUpdate = useForceUpdate();
  const optionType = control?.type;
  const form = useForm();

  return (
    <div className={styles.optionSetControlEditor}>
      <div style={{ display: "flex", gap: 16 }}>
        <div style={{ flex: 0.2 }}>
          <FormControl
            keyPath={keyPath}
            name="type"
            render={(props) => {
              return (
                <div>
                  <div>Type of Option: </div>
                  <div>
                    <ControlWrapper
                      {...props}
                      render={(props) => {
                        return (
                          <Select
                            {...props}
                            options={optionTypeOptions}
                            style={{ width: "100%" }}
                            onChange={(value) => {
                              props.onChange(null, { [props.name]: value });
                            }}
                          ></Select>
                        );
                      }}
                    ></ControlWrapper>
                  </div>
                </div>
              );
            }}
          ></FormControl>
        </div>
        <div style={{ flex: 0.6, selfAlign: "flex-end" }}>
          <FormControl
            keyPath={keyPath}
            name="label"
            render={(props) => {
              return (
                <div>
                  <div>Option Label: </div>
                  <div>
                    <ControlWrapper
                      {...props}
                      render={({ name, value, onChange }) => {
                        return renderI18nSelect({ form, keyPath, name, value, onChange });
                      }}
                    ></ControlWrapper>
                  </div>
                </div>
              );
            }}
          ></FormControl>
        </div>
        <div style={{ flex: 0.2 }} className={styles.checkboxRow}>
          <FormControl
            keyPath={keyPath}
            name="required"
            render={(props) => (
              <div>
                <div>&nbsp;</div>
                <div className={styles.controlBox}>
                  <ControlWrapper
                    {...props}
                    render={(props) => {
                      const { name, value, onChange } = props;
                      return (
                        <Checkbox
                          {...props}
                          checked={value}
                          onChange={(event) => {
                            onChange(null, { [name]: event.target.checked });
                          }}
                        >
                          Required
                        </Checkbox>
                      );
                    }}
                  ></ControlWrapper>
                </div>
              </div>
            )}
          ></FormControl>
        </div>
      </div>
      <div>
        <FormControl
          keyPath={keyPath}
          name="help_text"
          render={(props) => (
            <div>
              <div>Help Text (HTML is allowed):</div>
              <div>
                <ControlWrapper
                  {...props}
                  render={({ name, value, onChange }) => renderI18nSelect({ form, keyPath, name, value, onChange })}
                ></ControlWrapper>
              </div>
            </div>
          )}
        ></FormControl>
      </div>
      {renderPrivateControls({ optionType, keyPath, control })}
      <div>
        <FormControl
          keyPath={keyPath}
          name="hide_option"
          render={(props) => (
            <ControlWrapper
              {...props}
              render={(props) => {
                const { name, value, onChange } = props;
                return (
                  <Checkbox
                    {...props}
                    checked={value}
                    onChange={(event) => {
                      onChange(null, { [name]: event.target.checked });
                    }}
                  >
                    Hide Option
                  </Checkbox>
                );
              }}
            ></ControlWrapper>
          )}
        ></FormControl>
      </div>
      <div>
        <Collapse
          className={styles.innerCollapse}
          items={renderSubCollapseItems({ control, keyPath, optionSet, optionType, forceUpdate })}
        ></Collapse>
      </div>
    </div>
  );
}

OptionSetControlEditor.propTypes = {
  keyPath: PropTypes.array,
  control: PropTypes.object,
  optionSet: PropTypes.object,
  onLabelChange: PropTypes.func,
};

export default OptionSetControlEditor;
