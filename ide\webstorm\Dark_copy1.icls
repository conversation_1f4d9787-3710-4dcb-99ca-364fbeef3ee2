<scheme name="Dark copy1" version="142" parent_scheme="<PERSON><PERSON>">
  <metaInfo>
    <property name="ide">WebStorm</property>
    <property name="ideVersion">2024.2.3.0.0</property>
    <property name="originalScheme">Dark copy1</property>
  </metaInfo>
  <colors>
    <option name="ADDED_LINES_COLOR" value="549159" />
    <option name="ANNOTATIONS_COLOR" value="8d9199" />
    <option name="ANNOTATIONS_LAST_COMMIT_COLOR" value="ced0d6" />
    <option name="CARET_COLOR" value="ced0d6" />
    <option name="CARET_ROW_COLOR" value="26282e" />
    <option name="CONSOLE_BACKGROUND_KEY" value="1e1f22" />
    <option name="DELETED_LINES_COLOR" value="868a91" />
    <option name="DIFF_SEPARATORS_BACKGROUND" value="2b2d30" />
    <option name="DOCUMENTATION_COLOR" value="2b2d30" />
    <option name="DOC_COMMENT_GUIDE" value="394d3f" />
    <option name="DOC_COMMENT_LINK" value="3887a1" />
    <option name="ERROR_HINT" value="402929" />
    <option name="FILESTATUS_ADDED" value="73bd79" />
    <option name="FILESTATUS_COPIED" value="73bd79" />
    <option name="FILESTATUS_DELETED" value="6f737a" />
    <option name="FILESTATUS_IDEA_FILESTATUS_DELETED_FROM_FILE_SYSTEM" value="6f737a" />
    <option name="FILESTATUS_IDEA_FILESTATUS_IGNORED" value="d69a6b" />
    <option name="FILESTATUS_IDEA_FILESTATUS_MERGED_WITH_BOTH_CONFLICTS" value="de6a66" />
    <option name="FILESTATUS_IDEA_FILESTATUS_MERGED_WITH_CONFLICTS" value="de6a66" />
    <option name="FILESTATUS_IDEA_FILESTATUS_MERGED_WITH_PROPERTY_CONFLICTS" value="de6a66" />
    <option name="FILESTATUS_MERGED" value="cf84cf" />
    <option name="FILESTATUS_MODIFIED" value="70aeff" />
    <option name="FILESTATUS_NOT_CHANGED_IMMEDIATE" value="70aeff" />
    <option name="FILESTATUS_NOT_CHANGED_RECURSIVE" value="70aeff" />
    <option name="FILESTATUS_RENAMED" value="70aeff" />
    <option name="FILESTATUS_UNKNOWN" value="e88f89" />
    <option name="FILESTATUS_addedOutside" value="73bd79" />
    <option name="FILESTATUS_changelistConflict" value="de6a66" />
    <option name="FILESTATUS_modifiedOutside" value="70aeff" />
    <option name="FOLDED_TEXT_BORDER_COLOR" value="2b2d30" />
    <option name="IGNORED_ADDED_LINES_BORDER_COLOR" value="549159" />
    <option name="IGNORED_DELETED_LINES_BORDER_COLOR" value="868a91" />
    <option name="IGNORED_MODIFIED_LINES_BORDER_COLOR" value="375fad" />
    <option name="INDENT_GUIDE" value="313438" />
    <option name="INFORMATION_HINT" value="2b2d30" />
    <option name="INLINE_REFACTORING_SETTINGS_DEFAULT" value="393b40" />
    <option name="INLINE_REFACTORING_SETTINGS_FOCUSED" value="393b40" />
    <option name="INLINE_REFACTORING_SETTINGS_HOVERED" value="393b40" />
    <option name="LINE_NUMBERS_COLOR" value="4b5059" />
    <option name="LINE_NUMBER_ON_CARET_ROW_COLOR" value="a1a3ab" />
    <option name="LOOKUP_COLOR" value="2b2d30" />
    <option name="METHOD_SEPARATORS_COLOR" value="43454a" />
    <option name="MODIFIED_LINES_COLOR" value="375fad" />
    <option name="NOTIFICATION_BACKGROUND" value="25324d" />
    <option name="PROMOTION_PANE" value="25324d" />
    <option name="QUESTION_HINT" value="25324d" />
    <option name="RECENT_LOCATIONS_SELECTION" value="2b2d30" />
    <option name="RIGHT_MARGIN_COLOR" value="393b40" />
    <option name="SELECTED_INDENT_GUIDE" value="666870" />
    <option name="ScrollBar.Mac.hoverThumbColor" value="ffffff4d" />
    <option name="ScrollBar.Mac.thumbColor" value="ffffff26" />
    <option name="VCS_ANNOTATIONS_COLOR_1" value="2f5194" />
    <option name="VCS_ANNOTATIONS_COLOR_2" value="2d416b" />
    <option name="VCS_ANNOTATIONS_COLOR_3" value="283754" />
    <option name="VCS_ANNOTATIONS_COLOR_4" value="242d42" />
    <option name="VCS_ANNOTATIONS_COLOR_5" value="22252e" />
    <option name="VISUAL_INDENT_GUIDE" value="2b2d30" />
    <option name="WHITESPACES" value="6f737a" />
    <option name="WHITESPACES_MODIFIED_LINES_COLOR" value="52433d" />
  </colors>
  <attributes>
    <option name="ANNOTATION_ATTRIBUTE_NAME_ATTRIBUTES">
      <value>
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="ANNOTATION_NAME_ATTRIBUTES" baseAttributes="" />
    <option name="BAD_CHARACTER">
      <value>
        <option name="FOREGROUND" value="f75464" />
        <option name="EFFECT_TYPE" value="2" />
      </value>
    </option>
    <option name="BOOKMARKS_ATTRIBUTES">
      <value>
        <option name="ERROR_STRIPE_COLOR" value="f7e9c6" />
      </value>
    </option>
    <option name="BREADCRUMBS_CURRENT">
      <value>
        <option name="FOREGROUND" value="dfe1e5" />
        <option name="BACKGROUND" value="2b2d30" />
      </value>
    </option>
    <option name="BREADCRUMBS_DEFAULT">
      <value>
        <option name="FOREGROUND" value="9da0a8" />
      </value>
    </option>
    <option name="BREADCRUMBS_HOVERED">
      <value>
        <option name="FOREGROUND" value="dfe1e5" />
        <option name="BACKGROUND" value="2b2d30" />
      </value>
    </option>
    <option name="BREADCRUMBS_INACTIVE">
      <value>
        <option name="FOREGROUND" value="6f737a" />
      </value>
    </option>
    <option name="BREAKPOINT_ATTRIBUTES">
      <value>
        <option name="BACKGROUND" value="40252b" />
        <option name="ERROR_STRIPE_COLOR" value="8c5b65" />
      </value>
    </option>
    <option name="CODE_LENS_BORDER_COLOR">
      <value>
        <option name="EFFECT_COLOR" value="868a91" />
      </value>
    </option>
    <option name="CONSOLE_ERROR_OUTPUT">
      <value>
        <option name="FOREGROUND" value="f75464" />
      </value>
    </option>
    <option name="CONSOLE_NORMAL_OUTPUT">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
      </value>
    </option>
    <option name="CONSOLE_RANGE_TO_EXECUTE">
      <value>
        <option name="EFFECT_COLOR" value="3d7a49" />
      </value>
    </option>
    <option name="CONSOLE_SYSTEM_OUTPUT">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
      </value>
    </option>
    <option name="CONSOLE_USER_INPUT">
      <value>
        <option name="FOREGROUND" value="6aab73" />
        <option name="FONT_TYPE" value="2" />
      </value>
    </option>
    <option name="CSS.COLOR">
      <value>
        <option name="FOREGROUND" value="56a8f5" />
      </value>
    </option>
    <option name="CSS.IMPORTANT">
      <value>
        <option name="FOREGROUND" value="cf8e6d" />
        <option name="FONT_TYPE" value="1" />
      </value>
    </option>
    <option name="CSS.URL">
      <value>
        <option name="FOREGROUND" value="5c92ff" />
      </value>
    </option>
    <option name="CTRL_CLICKABLE">
      <value>
        <option name="FOREGROUND" value="548af7" />
        <option name="EFFECT_COLOR" value="548af7" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="CUSTOM_KEYWORD1_ATTRIBUTES">
      <value>
        <option name="FOREGROUND" value="cf8e6d" />
      </value>
    </option>
    <option name="CUSTOM_KEYWORD2_ATTRIBUTES">
      <value>
        <option name="FOREGROUND" value="c77dbb" />
      </value>
    </option>
    <option name="CUSTOM_KEYWORD3_ATTRIBUTES">
      <value>
        <option name="FOREGROUND" value="da19e" />
      </value>
    </option>
    <option name="CUSTOM_KEYWORD4_ATTRIBUTES">
      <value>
        <option name="FOREGROUND" value="b3ae60" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="CUSTOM_STRING_ATTRIBUTES" baseAttributes="DEFAULT_STRING" />
    <option name="CUSTOM_VALID_STRING_ESCAPE_ATTRIBUTES" baseAttributes="DEFAULT_VALID_STRING_ESCAPE" />
    <option name="DEBUGGER_INLINED_VALUES_EXECUTION_LINE">
      <value>
        <option name="FOREGROUND" value="849fbf" />
        <option name="FONT_TYPE" value="2" />
      </value>
    </option>
    <option name="DEBUGGER_INLINED_VALUES_MODIFIED">
      <value>
        <option name="FOREGROUND" value="b2ae60" />
        <option name="FONT_TYPE" value="2" />
      </value>
    </option>
    <option name="DEFAULT_BLOCK_COMMENT">
      <value>
        <option name="FOREGROUND" value="7a7e85" />
      </value>
    </option>
    <option name="DEFAULT_BRACES">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
      </value>
    </option>
    <option name="DEFAULT_BRACKETS">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
      </value>
    </option>
    <option name="DEFAULT_CLASS_REFERENCE">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
      </value>
    </option>
    <option name="DEFAULT_COMMA">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
      </value>
    </option>
    <option name="DEFAULT_CONSTANT">
      <value>
        <option name="FOREGROUND" value="c77dbb" />
        <option name="FONT_TYPE" value="2" />
      </value>
    </option>
    <option name="DEFAULT_DOC_COMMENT">
      <value>
        <option name="FOREGROUND" value="5f826b" />
        <option name="FONT_TYPE" value="2" />
      </value>
    </option>
    <option name="DEFAULT_DOC_COMMENT_TAG">
      <value>
        <option name="FOREGROUND" value="67a37c" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="DEFAULT_DOC_COMMENT_TAG_VALUE">
      <value>
        <option name="FOREGROUND" value="abadb3" />
      </value>
    </option>
    <option name="DEFAULT_DOC_MARKUP">
      <value>
        <option name="FOREGROUND" value="68a67e" />
      </value>
    </option>
    <option name="DEFAULT_DOT">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
      </value>
    </option>
    <option name="DEFAULT_FUNCTION_CALL" baseAttributes="DEFAULT_IDENTIFIER" />
    <option name="DEFAULT_FUNCTION_DECLARATION">
      <value>
        <option name="FOREGROUND" value="56a8f5" />
      </value>
    </option>
    <option name="DEFAULT_HIGHLIGHTED_REFERENCE">
      <value>
        <option name="EFFECT_COLOR" value="6b6c73" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="DEFAULT_IDENTIFIER">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
      </value>
    </option>
    <option name="DEFAULT_INSTANCE_FIELD">
      <value>
        <option name="FOREGROUND" value="c77dbb" />
      </value>
    </option>
    <option name="DEFAULT_INSTANCE_METHOD">
      <value>
        <option name="FOREGROUND" value="d5b778" />
      </value>
    </option>
    <option name="DEFAULT_INVALID_STRING_ESCAPE">
      <value>
        <option name="FOREGROUND" value="cf8e6d" />
        <option name="EFFECT_COLOR" value="fa6675" />
        <option name="EFFECT_TYPE" value="2" />
      </value>
    </option>
    <option name="DEFAULT_KEYWORD">
      <value>
        <option name="FOREGROUND" value="cf8e6d" />
      </value>
    </option>
    <option name="DEFAULT_LINE_COMMENT">
      <value>
        <option name="FOREGROUND" value="7a7e85" />
      </value>
    </option>
    <option name="DEFAULT_METADATA">
      <value>
        <option name="FOREGROUND" value="b3ae60" />
      </value>
    </option>
    <option name="DEFAULT_NUMBER">
      <value>
        <option name="FOREGROUND" value="2aacb8" />
      </value>
    </option>
    <option name="DEFAULT_OPERATION_SIGN">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
      </value>
    </option>
    <option name="DEFAULT_PARENTHS">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
      </value>
    </option>
    <option name="DEFAULT_REASSIGNED_LOCAL_VARIABLE">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
        <option name="EFFECT_COLOR" value="84868c" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="DEFAULT_REASSIGNED_PARAMETER">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
        <option name="EFFECT_COLOR" value="84868c" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="DEFAULT_SEMICOLON">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
      </value>
    </option>
    <option name="DEFAULT_STATIC_FIELD">
      <value>
        <option name="FOREGROUND" value="c77dbb" />
        <option name="FONT_TYPE" value="2" />
      </value>
    </option>
    <option name="DEFAULT_STATIC_METHOD">
      <value>
        <option name="FOREGROUND" value="d5b778" />
        <option name="FONT_TYPE" value="2" />
      </value>
    </option>
    <option name="DEFAULT_STRING">
      <value>
        <option name="FOREGROUND" value="6aab73" />
      </value>
    </option>
    <option name="DEFAULT_TAG">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
      </value>
    </option>
    <option name="DEFAULT_TEMPLATE_LANGUAGE_COLOR">
      <value>
        <option name="BACKGROUND" value="2b2d30" />
      </value>
    </option>
    <option name="DEFAULT_VALID_STRING_ESCAPE">
      <value>
        <option name="FOREGROUND" value="cf8e6d" />
      </value>
    </option>
    <option name="DEPRECATED_ATTRIBUTES">
      <value>
        <option name="EFFECT_COLOR" value="bcbec4" />
        <option name="EFFECT_TYPE" value="3" />
      </value>
    </option>
    <option name="DOC_CODE_BLOCK">
      <value>
        <option name="FOREGROUND" value="ced0d6" />
        <option name="BACKGROUND" value="26292c" />
        <option name="EFFECT_COLOR" value="393b40" />
      </value>
    </option>
    <option name="DOC_CODE_INLINE">
      <value>
        <option name="FOREGROUND" value="ced0d6" />
        <option name="BACKGROUND" value="343539" />
      </value>
    </option>
    <option name="DOC_TIPS_SHORTCUT">
      <value>
        <option name="FOREGROUND" value="ced0d6" />
        <option name="EFFECT_COLOR" value="6f737a" />
      </value>
    </option>
    <option name="ERRORS_ATTRIBUTES">
      <value>
        <option name="EFFECT_COLOR" value="fa6675" />
        <option name="ERROR_STRIPE_COLOR" value="d64d5b" />
        <option name="EFFECT_TYPE" value="2" />
      </value>
    </option>
    <option name="EXECUTIONPOINT_ATTRIBUTES">
      <value>
        <option name="BACKGROUND" value="2a5091" />
      </value>
    </option>
    <option name="FOLDED_TEXT_ATTRIBUTES">
      <value>
        <option name="FOREGROUND" value="868991" />
        <option name="BACKGROUND" value="393b40" />
      </value>
    </option>
    <option name="FOLLOWED_HYPERLINK_ATTRIBUTES">
      <value>
        <option name="FOREGROUND" value="b189f5" />
        <option name="EFFECT_COLOR" value="b189f5" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="GHERKIN_REGEXP_PARAMETER">
      <value>
        <option name="FOREGROUND" value="5c92ff" />
      </value>
    </option>
    <option name="HTML_ATTRIBUTE_VALUE" baseAttributes="DEFAULT_STRING" />
    <option name="HTML_CUSTOM_TAG_NAME">
      <value>
        <option name="FOREGROUND" value="d5b778" />
      </value>
    </option>
    <option name="HTML_ENTITY_REFERENCE">
      <value>
        <option name="FOREGROUND" value="56a8f5" />
      </value>
    </option>
    <option name="HTML_TAG">
      <value>
        <option name="FOREGROUND" value="d5b778" />
      </value>
    </option>
    <option name="HTML_TAG_NAME">
      <value>
        <option name="FOREGROUND" value="d5b778" />
      </value>
    </option>
    <option name="HYPERLINK_ATTRIBUTES">
      <value>
        <option name="FOREGROUND" value="548af7" />
        <option name="EFFECT_COLOR" value="548af7" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="IDENTIFIER_UNDER_CARET_ATTRIBUTES">
      <value>
        <option name="BACKGROUND" value="373b39" />
        <option name="ERROR_STRIPE_COLOR" value="5b786a" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="IMPLICIT_ANONYMOUS_CLASS_PARAMETER_ATTRIBUTES">
      <value>
        <option name="FOREGROUND" value="c77dbb" />
        <option name="EFFECT_COLOR" value="9e6294" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="INACTIVE_HYPERLINK_ATTRIBUTES">
      <value>
        <option name="EFFECT_COLOR" value="6b6c73" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="INFO_ATTRIBUTES">
      <value>
        <option name="EFFECT_COLOR" value="857042" />
        <option name="EFFECT_TYPE" value="2" />
      </value>
    </option>
    <option name="INJECTED_LANGUAGE_FRAGMENT">
      <value>
        <option name="BACKGROUND" value="293c40" />
      </value>
    </option>
    <option name="INLAY_DEFAULT">
      <value>
        <option name="FOREGROUND" value="868a91" />
        <option name="BACKGROUND" value="393b40" />
      </value>
    </option>
    <option name="INLAY_TEXT_WITHOUT_BACKGROUND">
      <value>
        <option name="FOREGROUND" value="727782" />
      </value>
    </option>
    <option name="INLINE_PARAMETER_HINT">
      <value>
        <option name="FOREGROUND" value="858a94" />
        <option name="BACKGROUND" value="393b40" />
      </value>
    </option>
    <option name="INLINE_PARAMETER_HINT_CURRENT">
      <value>
        <option name="FOREGROUND" value="83acfc" />
        <option name="BACKGROUND" value="35538f" />
      </value>
    </option>
    <option name="INLINE_PARAMETER_HINT_HIGHLIGHTED">
      <value>
        <option name="FOREGROUND" value="c9cbd6" />
        <option name="BACKGROUND" value="43454a" />
      </value>
    </option>
    <option name="JS.GLOBAL_FUNCTION">
      <value>
        <option name="FOREGROUND" value="e8bf6a" />
      </value>
    </option>
    <option name="JS.GLOBAL_VARIABLE">
      <value>
        <option name="FOREGROUND" value="c77dba" />
        <option name="FONT_TYPE" value="2" />
      </value>
    </option>
    <option name="JS.INSTANCE_MEMBER_FUNCTION">
      <value>
        <option name="FOREGROUND" value="e8bf6a" />
      </value>
    </option>
    <option name="JS.JSX_CLIENT_COMPONENT">
      <value>
        <option name="FOREGROUND" value="9c9cff" />
      </value>
    </option>
    <option name="JS.LOCAL_FUNCTION">
      <value>
        <option name="FOREGROUND" value="e8bf6a" />
      </value>
    </option>
    <option name="JS.REGEXP">
      <value>
        <option name="FOREGROUND" value="42c3d4" />
      </value>
    </option>
    <option name="JSP_DIRECTIVE_NAME">
      <value>
        <option name="FOREGROUND" value="cf8e6d" />
        <option name="FONT_TYPE" value="1" />
      </value>
    </option>
    <option name="KOTLIN_LABEL">
      <value>
        <option name="FOREGROUND" value="32b8af" />
      </value>
    </option>
    <option name="KOTLIN_MUTABLE_VARIABLE">
      <value>
        <option name="EFFECT_COLOR" value="84868c" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="KOTLIN_NAMED_ARGUMENT">
      <value>
        <option name="FOREGROUND" value="56c1d6" />
      </value>
    </option>
    <option name="KOTLIN_SMART_CAST_RECEIVER">
      <value>
        <option name="BACKGROUND" value="1a3b2d" />
      </value>
    </option>
    <option name="KOTLIN_SMART_CAST_VALUE">
      <value>
        <option name="BACKGROUND" value="1a3b2d" />
      </value>
    </option>
    <option name="KOTLIN_SMART_CONSTANT">
      <value>
        <option name="BACKGROUND" value="1a3b2d" />
      </value>
    </option>
    <option name="LINE_FULL_COVERAGE">
      <value>
        <option name="FOREGROUND" value="375239" />
        <option name="FONT_TYPE" value="1" />
      </value>
    </option>
    <option name="LINE_NONE_COVERAGE">
      <value>
        <option name="FOREGROUND" value="5e3838" />
        <option name="FONT_TYPE" value="1" />
      </value>
    </option>
    <option name="LINE_PARTIAL_COVERAGE">
      <value>
        <option name="FOREGROUND" value="5e4d33" />
        <option name="FONT_TYPE" value="1" />
      </value>
    </option>
    <option name="LIVE_TEMPLATE_ATTRIBUTES">
      <value>
        <option name="EFFECT_COLOR" value="467ff2" />
      </value>
    </option>
    <option name="LIVE_TEMPLATE_INACTIVE_SEGMENT">
      <value>
        <option name="EFFECT_COLOR" value="9da0a8" />
      </value>
    </option>
    <option name="LOG_ERROR_OUTPUT">
      <value>
        <option name="FOREGROUND" value="f75464" />
      </value>
    </option>
    <option name="LOG_INFO_OUTPUT">
      <value>
        <option name="FOREGROUND" value="e0bb65" />
      </value>
    </option>
    <option name="LOG_VERBOSE_OUTPUT">
      <value>
        <option name="FOREGROUND" value="56a8f5" />
      </value>
    </option>
    <option name="MARKDOWN_AUTO_LINK">
      <value>
        <option name="FOREGROUND" value="56a8f5" />
        <option name="EFFECT_COLOR" value="56a8f5" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="MARKDOWN_IMAGE">
      <value />
    </option>
    <option name="MARKDOWN_LINK_TEXT">
      <value>
        <option name="FOREGROUND" value="56a8f5" />
        <option name="EFFECT_COLOR" value="56a8f5" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="MARKED_FOR_REMOVAL_ATTRIBUTES">
      <value>
        <option name="EFFECT_COLOR" value="f75464" />
        <option name="EFFECT_TYPE" value="3" />
      </value>
    </option>
    <option name="MATCHED_BRACE_ATTRIBUTES">
      <value>
        <option name="BACKGROUND" value="43454a" />
        <option name="FONT_TYPE" value="1" />
      </value>
    </option>
    <option name="MATCHED_TAG_NAME">
      <value>
        <option name="BACKGROUND" value="3f4045" />
      </value>
    </option>
    <option name="NG.SIGNAL">
      <value>
        <option name="FOREGROUND" value="1db8ab" />
      </value>
    </option>
    <option name="NOT_TOP_FRAME_ATTRIBUTES">
      <value>
        <option name="BACKGROUND" value="273552" />
      </value>
    </option>
    <option name="NOT_USED_ELEMENT_ATTRIBUTES">
      <value>
        <option name="FOREGROUND" value="6f737a" />
      </value>
    </option>
    <option name="PROPERTIES.INVALID_STRING_ESCAPE">
      <value>
        <option name="FOREGROUND" value="f75464" />
        <option name="EFFECT_COLOR" value="fa6675" />
        <option name="EFFECT_TYPE" value="2" />
      </value>
    </option>
    <option name="PROPERTIES.KEY">
      <value>
        <option name="FOREGROUND" value="cf8e6d" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="QUTE_BACKGROUND">
      <value>
        <option name="BACKGROUND" value="27292b" />
      </value>
    </option>
    <option name="REGEXP.BRACES" baseAttributes="DEFAULT_BRACES" />
    <option name="REGEXP.BRACKETS" baseAttributes="DEFAULT_BRACKETS" />
    <option name="REGEXP.CHAR_CLASS">
      <value>
        <option name="FOREGROUND" value="b2ae60" />
        <option name="FONT_TYPE" value="1" />
      </value>
    </option>
    <option name="REGEXP.ESC_CHARACTER" baseAttributes="DEFAULT_VALID_STRING_ESCAPE" />
    <option name="REGEXP.META" baseAttributes="DEFAULT_KEYWORD" />
    <option name="REGEXP.PARENTHS" baseAttributes="DEFAULT_PARENTHS" />
    <option name="REGEXP.QUOTE_CHARACTER" baseAttributes="DEFAULT_VALID_STRING_ESCAPE" />
    <option name="REGEXP.REDUNDANT_ESCAPE">
      <value>
        <option name="FOREGROUND" value="6f7587" />
        <option name="FONT_TYPE" value="1" />
      </value>
    </option>
    <option name="RUNTIME_ERROR">
      <value>
        <option name="EFFECT_COLOR" value="f2c55c" />
        <option name="ERROR_STRIPE_COLOR" value="d64d5b" />
        <option name="EFFECT_TYPE" value="5" />
      </value>
    </option>
    <option name="SEARCH_RESULT_ATTRIBUTES">
      <value>
        <option name="BACKGROUND" value="2d543f" />
        <option name="ERROR_STRIPE_COLOR" value="42bd77" />
      </value>
    </option>
    <option name="Static method access">
      <value>
        <option name="FOREGROUND" value="c77dba" />
        <option name="FONT_TYPE" value="2" />
      </value>
    </option>
    <option name="Static property reference ID">
      <value>
        <option name="FOREGROUND" value="c77dba" />
        <option name="FONT_TYPE" value="2" />
      </value>
    </option>
    <option name="TEMPLATE_VARIABLE_ATTRIBUTES">
      <value>
        <option name="FOREGROUND" value="b189f5" />
      </value>
    </option>
    <option name="TEXT">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
        <option name="BACKGROUND" value="1e1f22" />
      </value>
    </option>
    <option name="TEXT_SEARCH_RESULT_ATTRIBUTES">
      <value>
        <option name="BACKGROUND" value="114957" />
        <option name="EFFECT_COLOR" value="165e70" />
        <option name="ERROR_STRIPE_COLOR" value="72d6d6" />
      </value>
    </option>
    <option name="TEXT_STYLE_ERROR">
      <value>
        <option name="EFFECT_COLOR" value="cf514e" />
        <option name="EFFECT_TYPE" value="5" />
      </value>
    </option>
    <option name="TEXT_STYLE_WARNING">
      <value>
        <option name="EFFECT_COLOR" value="ba9752" />
        <option name="EFFECT_TYPE" value="5" />
      </value>
    </option>
    <option name="TODO_DEFAULT_ATTRIBUTES">
      <value>
        <option name="FOREGROUND" value="8bb33d" />
        <option name="FONT_TYPE" value="2" />
        <option name="ERROR_STRIPE_COLOR" value="73ad2b" />
      </value>
    </option>
    <option name="TYPE_PARAMETER_NAME_ATTRIBUTES">
      <value>
        <option name="FOREGROUND" value="16baac" />
      </value>
    </option>
    <option name="TYPO">
      <value>
        <option name="EFFECT_COLOR" value="7ec482" />
        <option name="EFFECT_TYPE" value="2" />
      </value>
    </option>
    <option name="UNMATCHED_BRACE_ATTRIBUTES">
      <value>
        <option name="FOREGROUND" value="f75464" />
      </value>
    </option>
    <option name="Unresolved reference access">
      <value>
        <option name="FOREGROUND" value="757a85" />
        <option name="EFFECT_COLOR" value="6a707a" />
        <option name="EFFECT_TYPE" value="5" />
      </value>
    </option>
    <option name="WARNING_ATTRIBUTES">
      <value>
        <option name="EFFECT_COLOR" value="f2c55c" />
        <option name="ERROR_STRIPE_COLOR" value="c29e4a" />
        <option name="EFFECT_TYPE" value="2" />
      </value>
    </option>
    <option name="WRITE_IDENTIFIER_UNDER_CARET_ATTRIBUTES">
      <value>
        <option name="BACKGROUND" value="402f33" />
        <option name="ERROR_STRIPE_COLOR" value="ba6387" />
        <option name="EFFECT_TYPE" value="1" />
      </value>
    </option>
    <option name="WRITE_SEARCH_RESULT_ATTRIBUTES">
      <value>
        <option name="BACKGROUND" value="66313f" />
        <option name="ERROR_STRIPE_COLOR" value="fa7db1" />
      </value>
    </option>
    <option name="WRONG_REFERENCES_ATTRIBUTES">
      <value>
        <option name="FOREGROUND" value="f75464" />
      </value>
    </option>
    <option name="XML_ATTRIBUTE_NAME">
      <value>
        <option name="FOREGROUND" value="bcbec4" />
      </value>
    </option>
    <option name="XML_CUSTOM_TAG_NAME">
      <value>
        <option name="FOREGROUND" value="d5b778" />
      </value>
    </option>
    <option name="XML_ENTITY_REFERENCE">
      <value>
        <option name="FOREGROUND" value="56a8f5" />
      </value>
    </option>
    <option name="XML_PROLOGUE">
      <value>
        <option name="FOREGROUND" value="d5b778" />
      </value>
    </option>
    <option name="XML_TAG">
      <value>
        <option name="FOREGROUND" value="d5b778" />
      </value>
    </option>
    <option name="XML_TAG_NAME">
      <value>
        <option name="FOREGROUND" value="d5b778" />
      </value>
    </option>
  </attributes>
</scheme>