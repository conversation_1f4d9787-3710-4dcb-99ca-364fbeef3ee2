import React from "react";
import useS<PERSON> from "swr";
import { Spin, List, Typography, Button } from "antd";

import Api from "@/fetchers/api";
import Fetchers from "fetchers";
import Helper from "helpers";

const PodResources = ({ currentDownloadUrl = "", onUpdate }) => {
  const { data } = useSWR([Api.getPodResources], () => Fetchers.getPodResources().then((res) => res.data));
  const loading = !data;
  const list = data?.data ?? [];

  const onDelete = async (filename) => {
    const response = await Fetchers.deletePodResource({ filename }).then((res) => res.data);
    if (response.success) {
      Helper.openMessage({ type: "success", content: "删除成功" });
      onUpdate?.();
    }
  };

  return (
    <Spin spinning={loading}>
      <List
        header={<Typography.Title level={4}>POD 资源列表</Typography.Title>}
        dataSource={list}
        renderItem={(item) => {
          const downloadUrl = item?.downloadUrl ?? "";
          const filename = item?.name ?? "";

          return (
            <List.Item
              actions={[
                <a key="list-download" href={downloadUrl} download>
                  下载
                </a>,
                <Button
                  key="list-delete"
                  type="link"
                  onClick={() => {
                    onDelete(filename);
                  }}
                >
                  删除
                </Button>,
              ]}
            >
              <List.Item.Meta
                title={
                  <Typography.Text type={downloadUrl === currentDownloadUrl ? "success" : ""}>
                    {filename}
                  </Typography.Text>
                }
              />
            </List.Item>
          );
        }}
      />
    </Spin>
  );
};

export default PodResources;
