import styles from "./index.module.scss";
import { useState, useRef, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { Row, Col, Card } from "antd";
import EditPageHeader from "pages/edit/components/edit-page-header";
import Utils from "utils";
import Enums from "enums";
import Helper from "helpers";
import Fetchers from "@/fetchers";
import Loading from "components/common/Loading";
import TextEditable from "components/common/JSONComponent/components/TextEditable";
import JSONComponents from "components/common/JSONComponent";
import Form from "./components/form";
import EmailCard from "./components/email-card";
import NativeTable from "./components/native-table";
import Tags from "./components/Tags";
import { renderActions } from "../vars";

function TicketPlay(props) {
  const location = useLocation();
  const queryParams = Utils.getQueryParams(decodeURIComponent(location.search));
  const { id: ticket_id } = queryParams;
  const [data, setData] = useState({});
  const [incrementData, setIncrementData] = useState({});
  const [loading, setLoading] = useState(false);
  const [incrementLoading, setIncrementLoading] = useState(false);
  const paramsRef = useRef();

  paramsRef.current = {
    ...paramsRef.current,
    data,
    getData,
    getIncrementData,
  };

  async function getData(params) {
    try {
      setLoading(true);
      const result = await Fetchers.getTicketDetail({ params: { ...queryParams, ...params } }).then(
        (res) => res?.data?.data
      );
      setData(result);
      return result;
    } finally {
      setLoading(false);
    }
  }

  async function getIncrementData(params) {
    try {
      setIncrementLoading(true);
      const result = await Fetchers.getIncrementDetail({ params }).then((res) => res?.data?.data);
      setIncrementData(result);
    } finally {
      setIncrementLoading(false);
    }
  }

  function renderComponent(item) {
    const { component } = item;
    const componentType = component || "default";

    const handler = {
      [Enums.Components.TextEditable]: ({ item }) => {
        return <TextEditable data={item} {...item.props} />;
      },
      Form: ({ item }) => <Form data={item} ticketId={ticket_id} />,
      EmailCard: ({ item }) => <EmailCard data={item} updateData={getData} />,
      [Enums.Components.NativeTable]: ({ item }) => {
        return (
          <NativeTable
            data={item}
            ticketId={data?.content?.ticket_id}
            incrementId={data?.content?.increment_id}
            updateData={getIncrementData}
          />
        );
      },
      Tag: ({ item }) => <Tags data={item} />,
      default: () => <JSONComponents data={item} />,
    };

    return handler[componentType]?.({ item });
  }

  function renderCardExtra(actions) {
    return renderActions({
      actions: actions,
      onClick: async (item) => {
        await Helper.commandHandler({
          command: {
            ...item.command,
            request: { ...item?.command?.request, data: { ...item?.command?.request?.data } },
          },
        });
      },
    });
  }

  function renderContent(item, index) {
    if (!item) return null;

    const { component, props } = item;

    if (component === Enums.Components.Row) {
      return (
        <Row key={index} {...props}>
          {item?.children?.map((formItem, j) => {
            return renderContent(formItem, j);
          })}
        </Row>
      );
    } else if (component === Enums.Components.Col) {
      return (
        <Col key={index} {...props}>
          {item?.children?.map((formItem, j) => {
            return renderContent(formItem, j);
          })}
        </Col>
      );
    } else if (component === Enums.Components.Card) {
      return (
        <Card key={index} size="small" {...props} extra={renderCardExtra(props?.extra)}>
          {item?.children?.map((formItem, j) => {
            return renderContent(formItem, j);
          })}
        </Card>
      );
    } else {
      return <span key={index}>{renderComponent(item)}</span>;
    }
  }

  useEffect(() => {
    (async () => {
      const result = await paramsRef.current.getData();
      const { ticket_id, increment_id } = result?.content;
      paramsRef.current.getIncrementData({
        ticket_id: ticket_id,
        increment_id: increment_id,
      });
    })();
  }, []);

  useEffect(() => {
    function updateData() {
      const { getIncrementData, data } = paramsRef.current;
      const { ticket_id, increment_id } = data?.content;
      getIncrementData({ ticket_id, increment_id });
    }

    Utils.addEventListener(Enums.EventName.UpdatePageData, updateData);

    return function () {
      Utils.removeEventListener(Enums.EventName.UpdatePageData, updateData);
    };
  }, []);

  return (
    <div className={styles.container}>
      <EditPageHeader breadcrumbs={data?.breadcrumbs} actions={data?.actions} />
      <div className={styles.content}>
        <Row gutter={10}>
          <Col span={16}>
            <Loading loading={loading}>
              {data?.content?.children?.map((item, index) => {
                return renderContent(item, index);
              })}
            </Loading>
          </Col>

          <Col span={8}>
            <Loading loading={incrementLoading}>
              {incrementData?.content?.children?.map((item, index) => {
                return renderContent(item, index);
              })}
            </Loading>
          </Col>
        </Row>
      </div>
    </div>
  );
}

export default TicketPlay;
