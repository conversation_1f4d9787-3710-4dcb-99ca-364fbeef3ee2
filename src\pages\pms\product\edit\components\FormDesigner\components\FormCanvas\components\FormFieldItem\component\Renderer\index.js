import styles from "./index.module.scss";

import { useEffect, useState } from "react";

import {
  getComponentTitle,
  getComponentPlaceholder,
  getComponentConfigOptions,
} from "@/pages/pms/product/edit/components/FormDesigner/common/utils";

function Renderer({ widget }) {
  const [options, setOptions] = useState([]);
  const [placeholderOptions, setPlaceholderOptions] = useState([]);

  const previewComponent = widget?.component_config?.preview_component ?? (() => <div></div>);

  useEffect(() => {
    (async () => {
      if (widget?.title) {
        const titleIdentifier = widget?.component_config?.identifiers?.title;
        const placeholderIdentifier = widget?.component_config?.identifiers?.placeholder;
        if (titleIdentifier) {
          const options = await getComponentConfigOptions({ key: "title", identifier: titleIdentifier });
          setOptions(options);
        }

        if (placeholderIdentifier) {
          const options = await getComponentConfigOptions({ key: "placeholder", identifier: placeholderIdentifier });
          setPlaceholderOptions(options);
        }
      }
    })();
  }, [widget]);

  if (widget?.type === "ghost") {
    return <div className={styles.ghostLine}></div>;
  }

  return (
    <div className={styles.container}>
      <div>
        {/* {widget?.component_config?.title_label
          ? `${widget?.component_config?.title_label} - ${widget.widget_name}`
          : widget.widget_name} */}
        {getComponentTitle(widget, "title", options)}
      </div>
      <div className={styles.widget}>{previewComponent(getComponentPlaceholder(widget, placeholderOptions))}</div>
    </div>
  );
}

export default Renderer;
