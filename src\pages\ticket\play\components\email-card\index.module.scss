.container {
  width: 100%;

  .title {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    padding: 10px 0;

    .avatarWrapper {
      display: flex;
      align-items: center;
      flex-direction: column;
      gap: 5px;

      .avatar {
        width: 35px;
        height: 35px;

        border-radius: 50%;
        overflow: hidden;
      }
    }

    .info {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      flex-direction: column;
      font-weight: 400;
      font-size: 14px;

      .name {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 5px;
        font-weight: 600;
      }
    }
  }

  .content {
    width: 100%;
    display: inline-block;
    word-wrap: break-word;
    word-break: break-all;

    img {
      width: 100%;
    }
  }

  .attachmentWrapper {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    gap: 10px;
  }
}
