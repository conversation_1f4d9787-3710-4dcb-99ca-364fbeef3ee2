import styles from "./index.module.scss";
import { useState, useCallback } from "react";
import { Layout } from "antd";
import { DndContext, DragOverlay, PointerSensor, useSensors, useSensor, MeasuringStrategy } from "@dnd-kit/core";

import ComponentPanel from "./components/ComponentPanel";
import CanvasPanel from "./components/CanvasPanel";
import PropertyPanel from "./components/PropertyPanel";
import DragPreview from "./components/DragPreview";
import Toolbar from "./components/Toolbar";

import { removeComponentById, initializeComponents } from "./utils";
import customCollisionDetection from "./utils/customCollisionDetection";
import useDragAndDrop from "./hooks/useDragAndDrop";

const { Sider, Content } = Layout;

function LowCodeEditor({ initialComponents = [{}], onSave, showExportButton = true, showInnerSaveButton = false }) {
  const [components, setComponents] = useState(initializeComponents(initialComponents?.[0]?.formItems) ?? []);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [loadingComponent, setLoadingComponent] = useState(false);

  // 初始化拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // 使用拖拽钩子
  const {
    flattenedComponents = [],
    activeId,
    dragData,
    isOverDropZone,
    handleDragStart,
    handleDragMove,
    handleDragOver,
    handleDragEnd,
  } = useDragAndDrop({
    components,
    setComponents,
    setSelectedComponent: handleSelectedComponent,
  });

  const handleUpdateComponent = useCallback((component) => {
    // if (!selectedComponent) return;
    // setComponents((prev) =>
    //   prev.map((comp) => (comp.id === selectedComponent.id ? { ...comp, props: { ...comp.props, ...props } } : comp))
    // );

    // setSelectedComponent((prev) => ({ ...prev, props: { ...prev.props, ...props } }));

    setSelectedComponent(component);
    setComponents((prev) => {
      const updateComponentById = (items) => {
        return items.map((item) => {
          if (item.id === component.id) {
            return component;
          }

          const childrenName = item.component_config?.childrenName || "children";
          if (item[childrenName]?.length) {
            return {
              ...item,
              [childrenName]: updateComponentById(item[childrenName]),
            };
          }
          return item;
        });
      };

      const items = updateComponentById(prev);
      return items;

      // return { ...prev, items: updateComponentByKey(prev.items) };
    });
  }, []);

  function handleSelectedComponent(component) {
    setSelectedComponent(component);
    setLoadingComponent(true);
    setTimeout(() => {
      setLoadingComponent(false);
    }, 100);
  }

  const handleDeleteComponent = useCallback(
    (id) => {
      setComponents((prev) => removeComponentById(prev, id));

      if (selectedComponent && selectedComponent.id === id) {
        setSelectedComponent(null);
      }
    },
    [selectedComponent]
  );

  const handleSave = useCallback(
    (data) => {
      // const data = {
      //   children: sanitizeItems(components),
      // };
      onSave?.(data);
    },
    [onSave]
  );

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={customCollisionDetection}
      measuring={{
        droppable: {
          strategy: MeasuringStrategy.Always,
        },
      }}
      onDragStart={handleDragStart}
      onDragMove={handleDragMove}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
      autoScroll
    >
      <Layout className={styles.lowCodeEditor}>
        <Toolbar
          components={components}
          showExportButton={showExportButton}
          showInnerSaveButton={showInnerSaveButton}
          onSave={handleSave}
        />
        <Layout className={styles.main}>
          <Sider width={250} className={styles.sider}>
            <ComponentPanel />
          </Sider>

          <Content className={styles.content}>
            <CanvasPanel
              flattenedComponents={flattenedComponents}
              components={components}
              selectedComponentId={selectedComponent?.id}
              onSelect={handleSelectedComponent}
              onDelete={handleDeleteComponent}
              isOverDropZone={isOverDropZone}
            />
          </Content>

          <Sider width={400} className={styles.sider}>
            {!loadingComponent && (
              <PropertyPanel
                component={selectedComponent}
                flattenedComponents={flattenedComponents}
                onUpdateComponent={handleUpdateComponent}
              />
            )}
          </Sider>
        </Layout>

        <DragOverlay dropAnimation={null}>
          {activeId && (
            <DragPreview
              className={`${!isOverDropZone ? styles.noDrop : ""}`}
              activeId={activeId}
              dragData={dragData}
              isOverlay
            />
          )}
        </DragOverlay>
      </Layout>
    </DndContext>
  );
}

export default LowCodeEditor;
