const restockOrderCountInVerify = require("./restock-order-count-in-verify");
const restockOrderCountIn = require("./restock-order-count-in");
const restockOrderCountInConfirm = require("./restock-order-count-in-confirm");
const restockOrderItemPrintLabel = require("./restock-order-item-print-label");

module.exports = {
  "POST /rest/v1/stock/restock/order/count-in/verify": async (req, res) => restockOrderCountInVerify(req, res),

  "GET /rest/v1/stock/restock/order/count-in": async (req, res) => restockOrderCountIn(req, res),

  "POST /rest/v1/stock/restock/order/count-in/confirm": async (req, res) => restockOrderCountInConfirm(req, res),

  "POST /rest/v1/stock/restock/order/item/print-label": async (req, res) => restockOrderItemPrintLabel(req, res),
};
