import React, { useState } from "react";
import { Button, Form, InputNumber, Space, Card, Row, Col, Modal } from "antd";
import { PlusOutlined, DeleteOutlined, EditOutlined } from "@ant-design/icons";
import PropTypes from "prop-types";
import styles from "./index.module.scss";
import LowCodeEditor from "@/pages/low-code/editor/index";
import { componentMap } from "@/pages/low-code/editor/config/componentMap";
import { generateUniqueId } from "@/pages/low-code/editor/utils";

function RowColBuilder({ value, onChange, flattenedComponents }) {
  const [columns, setColumns] = useState(value ?? []);
  const [editorVisible, setEditorVisible] = useState(false);
  const [currentEditIndex, setCurrentEditIndex] = useState(null);
  const [currentComponents, setCurrentComponents] = useState(columns?.[currentEditIndex]?.children ?? []);

  const ColComponent = componentMap.Col;

  const handleAddColumn = () => {
    const newColumns = [
      ...columns,
      {
        // children: [],
        // component: "Col",
        // props: { span: 24, offset: 0 },
        ...ColComponent,
        id: generateUniqueId("Col"),
      },
    ];

    setColumns(newColumns);
    onChange?.(newColumns);
  };

  const handleRemoveColumn = (index) => {
    const newColumns = [...columns];
    newColumns.splice(index, 1);
    setColumns(newColumns);
    onChange?.(newColumns);
  };

  const handleColumnChange = (index, field, value) => {
    const newColumns = [...columns];
    newColumns[index] = {
      ...newColumns[index],
      props: {
        ...newColumns[index].props,
        [field]: value,
      },
    };
    setColumns(newColumns);
    onChange?.(newColumns);
  };

  const handleEditChildren = (index) => {
    setCurrentEditIndex(index);
    setCurrentComponents(columns[index]?.children || []);
    setEditorVisible(true);
  };

  const handleEditorSave = (data) => {
    if (currentEditIndex !== null && data?.children) {
      const newColumns = [...columns];
      newColumns[currentEditIndex] = {
        ...newColumns[currentEditIndex],
        children: data.children,
      };
      setColumns(newColumns);
      onChange?.(newColumns);
      setEditorVisible(false);
      setCurrentEditIndex(null);
    }
  };

  const handleEditorCancel = () => {
    setEditorVisible(false);
    setCurrentEditIndex(null);
  };

  return (
    <div className={styles.rowColBuilder}>
      <Card size="small" title="列配置" className={styles.previewCard}>
        <Row gutter={[8, 8]} className={styles.previewRow}>
          {columns.map((col, index) => (
            <Col
              key={index}
              span={col?.props?.span || 24}
              offset={col?.props?.offset || 0}
              className={styles.previewCol}
            >
              <div className={styles.colContent}>
                <div>列 {index + 1}</div>
                <div>span: {col?.props?.span}</div>
                {col?.props?.offset > 0 && <div>offset: {col?.props?.offset}</div>}
                <div className={styles.childrenCount}>
                  {col?.children && col?.children?.length > 0 ? `${col?.children?.length} 个组件` : "无组件"}
                </div>
                <div className={styles.actionButtons}>
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    onClick={() => handleEditChildren(index)}
                    className={styles.editBtn}
                  />
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleRemoveColumn(index)}
                    className={styles.removeBtn}
                  />
                </div>
              </div>
            </Col>
          ))}
        </Row>
      </Card>

      {/* <div className={styles.columnsConfig}>
        {columns.map((col, index) => (
          <Card key={index} size="small" title={`列 ${index + 1} 配置`} className={styles.columnCard}>
            <Space direction="vertical" style={{ width: "100%" }}>
              <Space>
                <Form.Item label="宽度(span)" style={{ marginBottom: 0 }}>
                  <InputNumber
                    min={1}
                    max={24}
                    value={col.props.span}
                    onChange={(value) => handleColumnChange(index, "span", value)}
                  />
                </Form.Item>
                <Form.Item label="偏移(offset)" style={{ marginBottom: 0 }}>
                  <InputNumber
                    min={0}
                    max={24}
                    value={col.props.offset}
                    onChange={(value) => handleColumnChange(index, "offset", value)}
                  />
                </Form.Item>
              </Space>
              <Space style={{ width: "100%", justifyContent: "space-between" }}>
                <Button type="primary" icon={<EditOutlined />} onClick={() => handleEditChildren(index)}>
                  编辑内容
                </Button>
                <Button type="text" danger icon={<DeleteOutlined />} onClick={() => handleRemoveColumn(index)} />
              </Space>
            </Space>
          </Card>
        ))}
      </div>

      <Button type="dashed" icon={<PlusOutlined />} onClick={handleAddColumn} style={{ width: "100%", marginTop: 16 }}>
        添加列
      </Button> */}

      <Modal
        title="编辑列内容"
        open={editorVisible}
        onCancel={handleEditorCancel}
        width="90%"
        style={{ top: 20, overflowY: "auto" }}
        footer={<></>}
        destroyOnClose={true}
      >
        <div className={styles.editorContainer}>
          <LowCodeEditor
            initialComponents={[{ formItems: currentComponents }]}
            onSave={handleEditorSave}
            onCancel={handleEditorCancel}
            showExportButton={false}
            showInnerSaveButton={true}
          />
        </div>
      </Modal>
    </div>
  );
}

RowColBuilder.propTypes = {
  value: PropTypes.array,
  onChange: PropTypes.func,
};

export default RowColBuilder;
