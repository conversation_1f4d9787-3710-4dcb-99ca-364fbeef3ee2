import styles from "./index.module.scss";
import Enums from "enums";
import { Form, Input, InputNumber, Select, Radio, Checkbox, Switch, Tree, TreeSelect, Cascader } from "antd";
import CheckboxSingle from "components/business/CheckboxSingle";
import DatePicker from "components/common/DatePicker";
import ImageUpload from "components/common/ImageUpload";
import EditTable from "components/common/EditTable";
import FileUpload from "components/common/FileUpload";
import CameraPhotoUpload from "components/common/CameraPhotoUpload";
import Transfer from "components/common/Transfer";
import ComponentContainer from "../ComponentContainer";
import ComponentItem from "../ComponentItem";

const Textarea = Input.TextArea;

function FormFieldItem({ component, isSelected, onSelect, onDelete, selectedComponentId }) {
  function renderComponent(component) {
    const componentData = { ...component, ...component?.props };

    const label = componentData?.label ?? component?.title;

    const baseComponent = {
      [Enums.Components.Input]: () => (
        <Form.Item label={label} key={componentData?.key}>
          <Input placeholder={componentData?.placeholder} />
        </Form.Item>
      ),
      [Enums.Components.InputNumber]: () => (
        <Form.Item label={label}>
          <InputNumber
            placeholder={componentData?.placeholder}
            min={componentData?.min}
            max={componentData?.max}
            style={{ width: "100%" }}
          />
        </Form.Item>
      ),
      [Enums.Components.Select]: () => (
        <Form.Item label={label}>
          <Select placeholder={componentData?.placeholder} options={componentData?.options} />
        </Form.Item>
      ),
      [Enums.Components.Radio]: () => (
        <Form.Item label={label}>
          <Radio.Group options={componentData?.options} />
        </Form.Item>
      ),
      [Enums.Components.CheckboxSingle]: () => (
        <Form.Item label={label}>
          <CheckboxSingle />
        </Form.Item>
      ),
      [Enums.Components.Checkbox]: () => (
        <Form.Item label={label}>
          <Checkbox.Group options={componentData?.options} />
        </Form.Item>
      ),
      [Enums.Components.Switch]: () => (
        <Form.Item label={label}>
          <Switch />
        </Form.Item>
      ),
      [Enums.Components.DatePicker]: () => (
        <Form.Item label={label}>
          <DatePicker placeholder={componentData.placeholder} style={{ width: "100%" }} />
        </Form.Item>
      ),
      [Enums.Components.Textarea]: () => (
        <Form.Item label={label}>
          <Textarea rows={componentData?.rows} />
        </Form.Item>
      ),
      [Enums.Components.RangePicker]: () => (
        <Form.Item label={label}>
          <DatePicker.RangePicker />
        </Form.Item>
      ),
      [Enums.Components.ImageUpload]: () => (
        <Form.Item label={label}>
          <ImageUpload listType="picture-card" />
        </Form.Item>
      ),
      [Enums.Components.EditTable]: () => (
        <Form.Item label={label}>
          <EditTable columns={componentData?.columns || []} />
        </Form.Item>
      ),
      [Enums.Components.FileUpload]: () => (
        <Form.Item label={label}>
          <FileUpload />
        </Form.Item>
      ),
      [Enums.Components.CameraPhotoUpload]: () => (
        <Form.Item label={label}>
          <CameraPhotoUpload />
        </Form.Item>
      ),
      [Enums.Components.Transfer]: () => (
        <Form.Item label={label}>
          <Transfer {...componentData} />
        </Form.Item>
      ),
      [Enums.Components.Tree]: () => (
        <Form.Item label={label}>
          <Tree {...componentData} />
        </Form.Item>
      ),
      [Enums.Components.TreeSelect]: () => (
        <Form.Item label={label}>
          <TreeSelect {...componentData} />
        </Form.Item>
      ),
      [Enums.Components.Cascader]: () => (
        <Form.Item label={label}>
          <Cascader {...componentData} />
        </Form.Item>
      ),

      // [Enums.Components.Row]: () => (
      //   <Form.Item label={label}>
      //     <Row gutter={componentData?.gutter} justify={componentData?.justify} align={componentData?.align}>
      //       {componentData?.children?.map((col, index) => {
      //         return (
      //           <Col span={col?.props?.span} offset={col?.props?.offset} key={col.id || `col-${index}`}>
      //             <Form.Item label={label}>
      //               <ComponentItem
      //                 key={col.id}
      //                 index={index}
      //                 component={col}
      //                 isSelected={col.id === selectedComponentId}
      //                 selectedComponentId={selectedComponentId}
      //                 onSelect={onSelect}
      //                 onDelete={onDelete}
      //               >
      //                 {/* <ComponentContainer
      //               component={col}
      //               parentKey={component.id}
      //               index={index}
      //               isSelected={col.id === selectedComponentId}
      //               selectedComponentId={selectedComponentId}
      //               onSelect={onSelect}
      //               onDelete={onDelete}
      //             /> */}
      //               </ComponentItem>

      //               {/* {col?.children?.map((item) => renderComponent(item))} */}
      //             </Form.Item>
      //           </Col>
      //         );
      //       })}
      //     </Row>
      //   </Form.Item>
      // ),
      ghost: () => <div className={styles.ghost}></div>,
      default: () => <Form.Item label={label} key={componentData?.key}></Form.Item>,
    };

    const componentType = component?.component || "default";
    return baseComponent[componentType]?.() || baseComponent.default();
  }

  return (
    <div className={styles.formFieldItem}>
      <Form layout="vertical">{renderComponent(component)}</Form>
    </div>
  );
}

export default FormFieldItem;
