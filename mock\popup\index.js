const popupTemplateList = require("./popup-template-list");
const popupManagementList = require("./popup-management-list");
const subscribePhonePopup = require("./edit-popup/subscribe-phone-popup");
const LuckyDrawSubscribePopup = require("./edit-popup/lucky-draw-subscribe-popup");

module.exports = {
  "GET /rest/v1/list/popup-template": async (req, res) => popupTemplateList(req, res),

  "GET /rest/v1/list/popup-management": async (req, res) => popupManagementList(req, res),
  // "GET /rest/v1/edit-popup/:url_key": async (req, res) => subscribePhonePopup(req, res),
  "POST /rest/v1/edit-popup/:url_key": async (req, res) => {
    res.status(200).json({
      status: "00",
      success: true,
      data: {},
      message: "操作成功",
    });
  },
  "GET /rest/v1/edit-popup/template": async (req, res) => {
    const { id } = req.query;
    const PopupMap = {
      1: subscribePhonePopup,
      2: LuckyDrawSubscribePopup,
    };
    const template = PopupMap[id];

    return template?.(req, res) || subscribePhonePopup(req, res);
  },
};
