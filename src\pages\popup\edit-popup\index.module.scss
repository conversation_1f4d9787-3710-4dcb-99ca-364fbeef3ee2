.container {
  .contentWrapper {
    padding: 10px 20px;

    .steps {
      width: 50%;
    }

    .content {
      margin-top: 30px;

      .stepsWrapper {
        height: calc(100vh - 210px);
        overflow-y: scroll;

        .contentItem {
          display: none;

          // .formCard,
          // .previewCard {
          //   height: calc(100vh - 250px);
          //   overflow-y: auto;
          // }

          .formCard {
            height: 100%;
            position: relative;

            .formItem {
              margin-bottom: 20px;

              .formItemTitle {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 15px;
              }
            }

            .actions {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              gap: 10px;
              position: sticky;
              bottom: 0;
              right: 0;
              z-index: 1000;
              background-color: #fff;
              padding: 5px 0 10px;
              box-shadow: 0 -1px 0 #ddd;
            }
          }

          .previewCard {
            position: relative;

            .preview {
              position: sticky;
            }
          }
        }

        .active {
          display: block;
        }
      }

      .previewCard {
        height: 100%;

        [class~="ant-card-body"] {
          height: 100%;
        }
      }
    }
  }
}
