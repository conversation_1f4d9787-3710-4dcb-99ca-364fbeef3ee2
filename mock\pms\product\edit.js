const Api = require("../../../src/fetchers/api");

module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      breadcrumbs: [
        {
          title: "商品管理",
          url: "/",
        },
        {
          title: "新建商品",
          url: "/",
        },
      ],
      actions: [
        {
          title: "保存",
          props: {
            type: "primary",
          },
          command: {
            type: "submit",
            id: "form",
          },
        },
        {
          title: "保存并发布",
          props: {
            type: "primary",
          },
          command: {
            type: "submit",
            id: "form",
            confirm: "确定保存并发布吗？",
            data: {
              action: "saveAndSubmit",
              status: "submitted",
            },
          },
        },
      ],
      content: {
        component: "JSONComponents",
        type: "json",
        children: [
          {
            component: "Form",
            type: "json",
            props: {
              id: "form",
              layout: "vertical",
              initialValues: {
                title: "test",
                brand_group: ["jack", "lucy"],
                treeSelect: ["parent-1-0", "leaf-1"],
                spu: "test",
                plate_number: "test",
                current_usd: 100,
                original_usd: 100,
                status: "on_sale",
                is_active: "on",
                is_in_stock: "on",
                sale_type: "suit",
                set_product_items: ["on_sale", "global_off_sale"],
                product_characteristic_images_1_sort: 1,
                product_characteristic_images: [
                  {
                    uid: 1,
                    fid: 1,
                    name: "photo.jpg",
                    size: 8777238,
                    status: "done",
                    url: "https://res2022.jeulia.com/product/2/1/1000x1000/62fde0f091112.jpg",
                    tags: ["渲染图"],
                  },
                ],
                formDesigner,
              },
            },
            submit: {
              request: {
                url: Api.customer,
              },
            },
            formItems: [
              {
                component: "Row",
                props: { gutter: [16, 32] },
                children: [
                  {
                    component: "Col",
                    props: { xs: { span: 16 }, lg: { span: 16 } },
                    children: [
                      {
                        component: "Card",
                        props: {
                          title: "商品信息",
                          bordered: true,
                          size: "small",
                          styles: { header: { backgroundColor: "#343a40", color: "#FFFFFF" } },
                        },
                        children: [
                          {
                            component: "LangTabs",
                          },
                          {
                            key: "title",
                            label: "Title",
                            component: "Input",
                            props: { placeholder: "Please input your value" },
                            rules: [{ required: true, message: "This is a required field" }],
                          },
                          {
                            key: "feed_title",
                            label: "Feed Title",
                            component: "Input",
                            props: { placeholder: "Please input your value" },
                          },
                          {
                            key: "fb_feed_title",
                            label: "FB Feed Title",
                            component: "Input",
                            props: { placeholder: "Please input your value" },
                          },
                          {
                            key: "description",
                            label: "Description",
                            component: "RichTextEditor",
                            props: { minHeight: 200, placeholder: "Enter your text", disabled: false },
                          },
                          {
                            key: "feature",
                            label: "Feature",
                            component: "RichTextEditor",
                            props: { minHeight: 200, placeholder: "Enter your text", disabled: false },
                          },
                          {
                            key: "product_characteristic_images",
                            label: "产品说明图(在轮播图中)",
                            component: "ImageUpload",
                            props: {
                              listType: "picture-card",
                              action: Api.uploadFile,
                              data: { disk: "s3-static" },
                              multiple: true,
                              isShowFileInfo: true,
                              actions: [
                                {
                                  title: "关联主属性",
                                  command: {
                                    type: "modal",
                                    closable: true,
                                    title: "关联主属性",
                                    props: {
                                      width: 800,
                                      maskClosable: true,
                                    },
                                    footer: [
                                      {
                                        title: "保存",
                                        props: {
                                          type: "primary",
                                        },
                                        command: {
                                          type: "submit",
                                          id: "form",
                                        },
                                      },
                                    ],
                                    content: {
                                      component: "JSONComponents",
                                      type: "api",
                                      props: {},
                                      fetcher: {
                                        request: {
                                          url: Api.getApiJsonComponents,
                                          data: {},
                                          method: "POST",
                                        },
                                      },
                                    },
                                  },
                                },
                                {
                                  title: "关联tag",
                                  command: {
                                    type: "modal",
                                    closable: true,
                                    title: "关联tag",
                                    props: {
                                      width: 800,
                                      maskClosable: true,
                                    },
                                    footer: [
                                      {
                                        title: "保存",
                                        props: {
                                          type: "primary",
                                        },
                                        command: {
                                          type: "submit",
                                          id: "form",
                                        },
                                      },
                                    ],
                                    content: {
                                      component: "JSONComponents",
                                      type: "api",
                                      props: {},
                                      fetcher: {
                                        request: {
                                          url: Api.getApiJsonComponents,
                                          data: {},
                                          method: "POST",
                                        },
                                      },
                                    },
                                  },
                                },
                              ],
                              extraFields: [
                                {
                                  key: "sort",
                                  label: "排序",
                                  component: "InputNumber",
                                  props: {
                                    min: 0,
                                    placeholder: "填写位置",
                                  },
                                },
                              ],
                            },
                          },
                          {
                            key: "product_characteristic_images_pc",
                            label: "产品特性图PC",
                            component: "ImageUpload",
                            props: {
                              listType: "picture-card",
                              action: Api.uploadFile,
                              data: { disk: "s3-static" },
                              multiple: true,
                            },
                          },
                          {
                            key: "product_characteristic_images_mobile",
                            label: "产品特性图Mobile",
                            component: "ImageUpload",
                            props: {
                              listType: "picture-card",
                              action: Api.uploadFile,
                              data: { disk: "s3-static" },
                              multiple: true,
                            },
                          },
                        ],
                      },
                      {
                        component: "Card",
                        props: {
                          title: "商品属性",
                          bordered: true,
                          size: "small",
                          styles: {
                            header: { backgroundColor: "#343a40", color: "#FFFFFF" },
                          },
                          style: { marginTop: "12px" },
                        },
                        children: [
                          {
                            key: "formDesigner",
                            component: "FormDesigner",
                            props: {
                              style: {
                                height: "600px",
                              },
                              master_property: {
                                // key: "master_attr_dictionary_identifier",
                                props: {
                                  disabled: false,
                                  showSearch: true,
                                  optionFilterProp: "label",
                                  optionsApi: Api.getDictionary,
                                },
                              },
                              second_property: {
                                // key: "second_attr_dictionary_identifier",
                                props: {
                                  disabled: false,
                                  showSearch: true,
                                  optionFilterProp: "label",
                                  optionsApi: Api.getDictionary,
                                },
                              },
                              save_template: {
                                key: "name",
                                request: {
                                  url: Api.getApiJsonComponents,
                                  method: "POST",
                                },
                              },
                              template_select: {
                                props: {
                                  options: [],
                                  allowClear: true,
                                  showSearch: true,
                                  optionFilterProp: "label",
                                  optionsApi: "http://192.168.2.110:8081/rest/v1/customize/template-options",
                                  addOptions: {
                                    title: "管理模板",
                                    command: {
                                      type: "message",
                                      config: {
                                        type: "success",
                                        content: "提示文案",
                                        duration: 3,
                                      },
                                    },
                                  },
                                },
                              },
                            },
                          },
                        ],
                      },
                    ],
                  },
                  {
                    component: "Col",
                    props: { xs: { span: 8 }, lg: { span: 8 } },
                    children: [
                      {
                        component: "Card",
                        props: {
                          title: "商品设置",
                          bordered: true,
                          size: "small",
                          styles: { header: { backgroundColor: "#343a40", color: "#FFFFFF" } },
                        },
                        children: [
                          {
                            key: "brand_group",
                            label: "品牌组",
                            component: "Select",
                            props: {
                              mode: "multiple",
                              showSearch: true,
                              options: [
                                {
                                  value: "jack",
                                  label: "杰克",
                                },
                                {
                                  value: "lucy",
                                  label: "路西",
                                },
                                {
                                  value: "tom",
                                  label: "汤姆",
                                },
                              ],
                              extra: "选择spu适用的品牌，可多选，只是当筛选spu的标签来用",
                            },
                            rules: [{ required: true, message: "此选项是必填项" }],
                          },
                          {
                            key: "treeSelect",
                            label: "产品类型",
                            component: "TreeSelect",
                            props: {
                              showSearch: true,
                              allowClear: true,
                              multiple: true,
                              treeDefaultExpandAll: true,
                              treeData: [
                                {
                                  value: "parent-1",
                                  title: "parent 1",
                                  children: [
                                    {
                                      value: "parent-1-0",
                                      title: "parent 1-0",
                                      children: [
                                        {
                                          value: "leaf-1",
                                          title: "leaf1",
                                        },
                                        {
                                          value: "leaf-2",
                                          title: "leaf2",
                                        },
                                      ],
                                    },
                                    {
                                      value: "parent 1-1",
                                      title: "parent 11",
                                      children: [
                                        {
                                          value: "leaf-3",
                                          title: "leaf3",
                                        },
                                      ],
                                    },
                                  ],
                                },
                              ],
                            },
                            rules: [{ required: true, message: "This is a required field" }],
                          },
                          {
                            key: "spu",
                            label: "SPU",
                            component: "Input",
                            props: { placeholder: "请输入SPU" },
                            rules: [{ required: true, message: "此选项是必填项" }],
                          },
                          {
                            key: "plate_number",
                            label: "版号",
                            component: "Select",
                            props: {
                              showSearch: true,
                              placeholder: "请输入版号",
                            },
                            rules: [{ required: true, message: "此选项是必填项" }],
                          },
                        ],
                      },
                      {
                        component: "Card",
                        props: {
                          bordered: true,
                          style: { marginTop: "12px" },
                        },
                        children: [
                          {
                            key: "current_usd",
                            label: "现价USD",
                            component: "InputNumber",
                            props: {
                              min: 0,
                              precision: 2,
                              style: { width: "100%" },
                            },
                            rules: [{ required: true, message: "This is a required field" }],
                          },
                          {
                            key: "original_usd",
                            label: "现价USD",
                            component: "InputNumber",
                            props: {
                              min: 0,
                              precision: 2,
                              style: { width: "100%" },
                            },
                            rules: [{ required: true, message: "This is a required field" }],
                          },
                        ],
                      },
                      {
                        component: "Card",
                        props: {
                          style: { marginTop: "12px" },
                        },
                        children: [
                          {
                            key: "status",
                            label: "上架状态",
                            component: "Select",
                            props: {
                              options: [
                                {
                                  label: "上架",
                                  value: "on_sale",
                                },
                                {
                                  label: "全局下架",
                                  value: "global_off_sale",
                                },
                              ],
                            },
                            rules: [{ required: true, message: "This is a required field" }],
                          },
                          {
                            key: "is_active",
                            label: "是否激活",
                            component: "Select",
                            props: {
                              options: [
                                {
                                  label: "是",
                                  value: "on",
                                },
                                {
                                  label: "否",
                                  value: "global_off_sale",
                                },
                              ],
                            },
                            rules: [{ required: true, message: "This is a required field" }],
                          },
                          {
                            key: "is_in_stock",
                            label: "是否有库存",
                            component: "Select",
                            props: {
                              options: [
                                {
                                  label: "是",
                                  value: "on",
                                },
                                {
                                  label: "否",
                                  value: "global_off_sale",
                                },
                              ],
                            },
                            rules: [{ required: true, message: "This is a required field" }],
                          },
                        ],
                      },
                      {
                        component: "Card",
                        props: {
                          style: { marginTop: "12px" },
                        },
                        children: [
                          {
                            key: "sale_type",
                            label: "销售方式",
                            component: "Select",
                            props: {
                              options: [
                                {
                                  label: "套装",
                                  value: "suit",
                                },
                                {
                                  label: "普通商品",
                                  value: "goods",
                                },
                              ],
                            },
                            rules: [{ required: true, message: "This is a required field" }],
                            showFieldsWhen: {
                              set_product_items: ["suit"],
                            },
                          },
                          {
                            key: "set_product_items",
                            label: "套装子商品",
                            component: "Select",
                            props: {
                              mode: "multiple",
                              options: [
                                {
                                  label: "套装",
                                  value: "on_sale",
                                },
                                {
                                  label: "普通商品",
                                  value: "global_off_sale",
                                },
                              ],
                            },
                            rules: [{ required: true, message: "This is a required field" }],
                          },
                        ],
                      },
                      {
                        component: "Card",
                        props: {
                          style: { marginTop: "12px" },
                        },
                        children: [
                          {
                            key: "youtube",
                            label: "Youtube 视频ID",
                            component: "Input",
                          },
                          {
                            key: "Vimeo",
                            label: "Vimeo",
                            component: "Input",
                          },
                          {
                            key: "is_360",
                            label: "开启360",
                            component: "Select",
                            props: {
                              options: [],
                            },
                          },
                          {
                            key: "imagick_version",
                            label: "Imagick Version",
                            component: "Select",
                          },
                        ],
                      },
                      {
                        component: "Card",
                        props: {
                          title: "商品标签",
                          style: { marginTop: "12px" },
                        },
                        children: [
                          {
                            key: "product_attributes",
                            label: "商品属性",
                            component: "Select",
                            props: {
                              mode: "multiple",
                              showSearch: true,
                            },
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                component: "Card",
                props: {
                  title: "商品图",
                  bordered: true,
                  size: "small",
                  styles: { header: { backgroundColor: "#343a40", color: "#FFFFFF" } },
                  style: { marginTop: "12px" },
                },
                children: [
                  {
                    key: "goods_images",
                    label: "渲染图&实拍图&试戴图",
                    component: "ImageUpload",
                    props: {
                      listType: "picture-card",
                      action: Api.uploadFile,
                      data: { disk: "s3-static" },
                      multiple: true,
                    },
                  },
                  {
                    key: "element_images",
                    label: "生产元素图",
                    component: "ImageUpload",
                    props: {
                      listType: "picture-card",
                      action: Api.uploadFile,
                      data: { disk: "s3-static" },
                      multiple: true,
                    },
                  },
                  {
                    key: "try_on_images",
                    label: "眼镜try on图",
                    component: "ImageUpload",
                    props: {
                      listType: "picture-card",
                      action: Api.uploadFile,
                      data: { disk: "s3-static" },
                      multiple: true,
                    },
                  },
                  {
                    key: "compression_images",
                    label: "定制产品压缩图包",
                    component: "ImageUpload",
                    props: {
                      listType: "picture-card",
                      action: Api.uploadFile,
                      data: { disk: "s3-static" },
                      multiple: true,
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
    },
    command: {},
  });
};

const formDesigner = {
  master_attr_dictionary_identifier: null, // "product_tag_recipient"
  second_attr_dictionary_identifier: null, // "product_tag_occasion"
  items: [
    {
      key: "Engraving5",
      font: { url: "https://images.drawelry.com/assets/fonts/Pacifico/Pacifico.woff2", size: 60, family: "pacifico" },
      icon: "el-icon-edit",
      name: "我是name",
      type: "text",
      price: { base_price: "100" },
      title: "engraving",
      drakey: "1736217251000_2249",
      required: true,
      ban_emoji: true,
      uppercase: true,
      emoji_list: [
        { label: "😃", value: "%F0%9F%98%83", checked: true },
        { label: "😁", value: "%F0%9F%98%81", checked: true },
        { label: "😆", value: "%F0%9F%98%86", checked: true },
        { label: "😅", value: "%F0%9F%98%85", checked: true },
        { label: "🤣", value: "%F0%9F%A4%A3", checked: true },
        { label: "😂", value: "%F0%9F%98%82", checked: true },
        { label: "🌙", value: "%F0%9F%8C%99", checked: true },
        { label: "🌞", value: "%F0%9F%8C%9E", checked: true },
        { label: "⭐", value: "%E2%AD%90", checked: true },
      ],
      isDisabled: false,
      max_length: 12,
      min_length: 4,
      placeholder: "please_enter_your_name_here",
      title_label: "Engraving",
      title_value: "engraving",
      widget_name: "单行刻字",
      uppercase_first: true,
      placeholder_label: "Please enter your name here",
      enable_font_options: false,
    },
    {
      type: "select",
      icon: "el-icon-arrow-down",
      placeholder: "-- Please select --",
      required: false,
      title: "",
      title_label: "",
      title_value: "",
      key: "select8",
      isDisabled: false,
      identifier: "product_custom_option_stone_shape",
      options: [
        { value: "asscher", id: "77", title: "正八宝塔", price: 11, cost: 22, checked: true },
        { value: "emerald", id: "78", title: "长方形", price: null, cost: "", checked: true },
        { value: "heart", id: "79", title: "心形", price: null, cost: 0, checked: true },
        { value: "marquise", id: "81", title: "马眼", price: null, cost: 0, checked: true },
        { value: "oval", id: "82", title: "蛋形", price: null, cost: 0, checked: true },
        { value: "pear", id: "83", title: "梨形", price: null, cost: 0, checked: true },
      ],
      expand: false,
      name: "",
      placeholder_label: "",
      widget_name: "下拉选项",
      drakey: "1736904049000_6554",
    },
    {
      type: "select_icon",
      icon: "el-icon-arrow-down",
      placeholder: "please_enter_your_name_here",
      required: true,
      title: "length",
      title_label: "Length",
      title_value: "length",
      key: "select9",
      isDisabled: false,
      identifier: "product_custom_option_stone_shape",
      options: [{ value: "emerald", id: "78", title: "长方形", price: 1, cost: 2, checked: true }],
      expand: false,
      name: "111",
      placeholder_label: "Please enter your name here",
      widget_name: "下拉选项(带图标)",
      drakey: "1736996863000_5592",
    },
  ],
};
