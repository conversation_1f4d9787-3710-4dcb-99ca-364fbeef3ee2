{"name": "react-erp", "version": "1.0.0", "private": true, "scripts": {"dev": "concurrently \"npm run start\" \"npm run mock\"", "start": "cross-env REACT_APP_RUNTIME_ENV=development REACT_APP_VERSION=development PORT=3001 BROWSER=none react-app-rewired start", "start:test": "cross-env REACT_APP_RUNTIME_ENV=test PORT=3001 BROWSER=none react-app-rewired start", "start:prod": "cross-env REACT_APP_RUNTIME_ENV=production PORT=3001 BROWSER=none react-app-rewired start", "mock": "cross-env MOCK_PORT=8081 node --watch mock/index.js", "mock:dms": "cross-env API_APP=dms MOCK_ENV=test MOCK_PORT=8081 node mock/proxy/index.js", "mock:pms": "cross-env API_APP=pms MOCK_ENV=test MOCK_PORT=8081 node mock/proxy/index.js", "build": "cross-env GENERATE_SOURCEMAP=false react-app-rewired build", "build:dev": "cross-env REACT_APP_RUNTIME_ENV=development GENERATE_SOURCEMAP=false react-app-rewired build", "build:test": "node deploy/build-zip.js", "build:prod": "cross-env REACT_APP_RUNTIME_ENV=production GENERATE_SOURCEMAP=false react-app-rewired build", "dms:test": "concurrently \"npm run start\" \"npm run mock:dms\"", "pms:test": "concurrently \"npm run start\" \"npm run mock:pms\"", "deploy": "node deploy/index.js", "rollback": "node deploy/rollback.js", "test": "react-app-rewired test", "eject": "react-app-rewired eject", "prepare": "husky install"}, "dependencies": {"@ant-design/icons": "5.2.6", "@ant-design/pro-components": "2.6.44", "@dnd-kit/core": "6.1.0", "@dnd-kit/modifiers": "7.0.0", "@dnd-kit/sortable": "8.0.0", "@dnd-kit/utilities": "3.2.2", "@module-federation/utilities": "3.0.5", "@testing-library/jest-dom": "5.17.0", "@testing-library/react": "13.4.0", "@testing-library/user-event": "13.5.0", "@wecom/jssdk": "1.4.5", "antd": "5.17.4", "axios": "1.7.9", "bignumber.js": "9.1.2", "classnames": "2.3.2", "cropperjs": "1.6.2", "crypto-js": "4.2.0", "dayjs": "1.11.10", "fabric": "5.3.0", "js-cookie": "3.0.1", "lodash.clonedeep": "4.5.0", "lodash.debounce": "4.0.8", "lodash.throttle": "4.1.1", "mobx": "6.10.2", "mobx-react-lite": "4.0.5", "moment": "2.29.4", "react": "18.2.0", "react-ace": "10.1.0", "react-dom": "18.2.0", "react-rnd": "10.4.14", "react-router-dom": "6.16.0", "react-scripts": "5.0.1", "sanitize-html": "2.14.0", "swr": "2.2.4", "three": "0.176.0", "uuid": "9.0.1", "web-vitals": "2.1.4", "ws": "8.18.0"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "7.21.11", "@module-federation/nextjs-mf": "8.1.3", "cors": "2.8.5", "cross-env": "7.0.3", "concurrently": "9.2.0", "express": "5.0.0", "husky": "8.0.3", "lint-staged": "15.2.0", "multer": "1.4.5-lts.1", "nodemon": "3.0.1", "prettier": "2.8.8", "react-app-rewired": "2.2.1", "sass": "1.68.0", "sharp": "0.33.3"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"no-empty-pattern": "off", "no-unused-vars": "off", "import/no-anonymous-default-export": "off", "no-dupe-keys": "error", "no-dupe-args": "error", "no-func-assign": "off", "no-loop-func": "off", "react/jsx-key": "error", "react-hooks/exhaustive-deps": "error"}, "globals": {"self": "readonly"}}, "eslintIgnore": ["**/*.min.js"], "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": [">0.2%", "not dead", "not op_mini all"]}}