import PropTypes from "prop-types";
import styles from "./index.module.scss";
import { forwardRef, useImperativeHandle, useState } from "react";
import classNames from "classnames";

function Swatch(props, ref) {
  const {
    options,
    value: propValue,
    defaultValue,
    onChange,
    imageProps,
    multiple,
    optionProps,
    readOnly,
    disabled,
    onClick,
    imageKey = "image",
  } = props;
  const [innerValue, setInnerValue] = useState(defaultValue);
  const isControlled = Object.hasOwnProperty.call(props, "value");
  const value = isControlled ? propValue : innerValue;

  useImperativeHandle(ref, () => {
    return {
      get value() {
        return value;
      },
      set value(nextValue) {
        setInnerValue(nextValue);
      },
    };
  });

  return (
    <div className={classNames(styles.swatch, { [styles.disabled]: disabled })}>
      {options?.map((option = {}, index) => {
        const checked = multiple ? value?.includes(option.value) : value === option.value;
        const image = option[imageKey] || option.image;
        return (
          <div
            {...optionProps}
            key={`${index}_${option.value}`}
            className={classNames(styles.option, { [styles.checked]: checked })}
            onClick={(event) => {
              onClick?.(event, option.value);
              if (readOnly || disabled) return;
              if (multiple) {
                const nextValue = checked ? value.filter((x) => x !== option.value) : [...(value || []), option.value];
                setInnerValue(nextValue);
                onChange?.(event, nextValue);
              } else {
                const nextValue = option.value;
                if (nextValue !== value) {
                  setInnerValue(nextValue);
                  onChange?.(event, nextValue);
                }
              }
            }}
          >
            {(() => {
              if (image) {
                return <img {...imageProps} src={image?.src} alt="" />;
              } else {
                return (
                  <span className={styles.label} style={{ width: imageProps?.width, height: imageProps?.height }}>
                    {option.label}
                  </span>
                );
              }
            })()}
          </div>
        );
      })}
    </div>
  );
}

Swatch = forwardRef(Swatch);

Swatch.propTypes = {
  options: PropTypes.array,
  value: PropTypes.any,
  defaultValue: PropTypes.any,
  onChange: PropTypes.func,
  imageProps: PropTypes.object,
  multiple: PropTypes.bool,
  readOnly: PropTypes.bool,
  disabled: PropTypes.bool,
  onClick: PropTypes.func,
  imageKey: PropTypes.string,
};

export default Swatch;
