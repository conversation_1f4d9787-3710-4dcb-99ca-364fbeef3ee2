module.exports = async (req, res) => {
  const gemstonesData = [
    {
      data: {
        diamond: {
          data: {
            diamond: {
              index: 0,
              active: false,
              disabled: false,
              price_list: { 0.2: 115, 0.3: 155, 0.5: 245, 0.7: 330, "1.0": 460, 1.2: 545 },
              stone_type: "gao",
            },
          },
        },
        genuine: {
          data: {
            pearl: {
              index: 16,
              active: false,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },

            chocolate: {
              index: 15,
              active: false,
              disabled: false,
              price_list: { 0.2: 30, 0.3: 30, 0.5: 30, 0.7: 30, "1.0": 30, 1.2: 40 },
              stone_type: "gao",
            },
            pinkpearl: {
              index: 18,
              active: false,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },
            blackpearl: {
              index: 17,
              active: false,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },
            moissanite: {
              index: 1,
              active: true,
              disabled: false,
              price_list: { 0.2: 70, 0.3: 90, 0.5: 130, 0.7: 170, "1.0": 230, 1.2: 270 },
              stone_type: "gao",
            },
            watermelon: {
              index: 14,
              active: false,
              disabled: false,
              price_list: { 0.2: 50, 0.3: 50, 0.5: 50, 0.7: 50, "1.0": 50, 1.2: 60 },
              stone_type: "gao",
            },
          },
        },
        simulated: {
          data: {
            pink: {
              index: 7,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },
            garnet: {
              index: 3,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "high_carbon",
            },
            crystal: {
              index: 2,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "high_carbon",
            },
            emerald: {
              index: 6,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },
            fuchsia: {
              index: 8,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },
            peridot: {
              index: 9,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },
            amethyst: {
              index: 4,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "high_carbon",
            },
            sapphire: {
              index: 10,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },
            bluetopaz: {
              index: 13,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },
            lightpink: {
              index: 21,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },
            aquamarine: {
              index: 5,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },
            fancyblack: {
              index: 11,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },
            orangerose: {
              index: 19,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },
            fancyyellow: {
              index: 12,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },
            padparadschared: {
              index: 20,
              active: true,
              disabled: false,
              price_list: { 0.2: 0, 0.3: 0, 0.5: 0, 0.7: 0, "1.0": 0, 1.2: 0 },
              stone_type: "gao",
            },
          },
        },
      },
      info: {
        num: 12,
        shape: "other_shape",
        stone_id: null,
        carat_list: ["0.2", "0.3", "0.5", "0.7", "1.0", "1.2"],
        stone_info: "石头信息",
        total_carat: 0.2,
        default_stone_type: "genuine",
        default_stone_value: "moissanite",
      },
    },
  ];

  const materialsData = [
    {
      data: {
        "10Kgold": { index: 4, price: 111, active: false },
        "14Kgold": { index: 7, price: 111, active: false },
        "18Kgold": { index: 10, price: 111, active: false },
        Platinum: { index: 12, price: 111, active: false },
        "925silver": { index: 0, price: 111, active: true },
        "10Krosegold": { index: 5, price: 111, active: false },
        "14Krosegold": { index: 8, price: 111, active: false },
        "18Krosegold": { index: 11, price: 111, active: false },
        "10Kwhitegold": { index: 3, price: 111, active: false },
        "14Kwhitegold": { index: 6, price: 111, active: false },
        "18Kwhitegold": { index: 9, price: 112, active: false },
        "925silvergolden": { index: 1, price: 112, active: true },
        "925silverrosegolden": { index: 2, price: 112, active: true },
      },
      info: { default_value: "925silver" },
    },
  ];

  res.status(200).json({
    status: "00",
    success: true,
    data: { gemstones: gemstonesData, materials: materialsData },
  });
};
