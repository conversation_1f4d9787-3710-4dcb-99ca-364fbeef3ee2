import { Tree } from "antd";
import { useState } from "react";

import Utils from "utils";

function TreePanel({ data = {}, onSelect, defaultSelectedKeys }) {
  const { tree: treeData = [] } = data;
  const [selectedKeys, setSelectedKeys] = useState(defaultSelectedKeys ?? []);

  const defaultExpandedKeys = Utils.flatTree({ treeData: treeData }).map((item) => item?.key);

  function handleSelect(selectedKeys, e) {
    if (!selectedKeys.length) return;

    const selectedNode = {
      id: selectedKeys[0],
      title: e.node.title,
      is_main_site: e.node?.is_main_site,
      is_follow_main_site: e.node?.is_follow_main_site,
      is_auto_sort: e.node?.is_auto_sort,
      end_index: e.node?.end_index,
    };

    setSelectedKeys(selectedKeys);
    onSelect?.(selectedNode);
  }

  return (
    <div>
      <Tree
        treeData={treeData}
        defaultExpandedKeys={defaultExpandedKeys}
        selectedKeys={selectedKeys}
        blockNode
        onSelect={handleSelect}
      />
    </div>
  );
}

export default TreePanel;
