import ConfigPrompt from "../ConfigPrompt";

const commandExamples = [
  {
    component: "redirect",
    title: "跳转页面",
    // description: "跳转页面",
    examples: [
      {
        title: "跳转页面",
        config: {
          target: "windowTop/windowSelf",
          type: "redirect",
          url: "/edit/order?id=1",
          customHeaders: {
            "X-My-Header": "CustomValue",
            "X-My-Header2": "CustomValue2",
          },
        },
      },
    ],
  },
  {
    component: "reload",
    title: "刷新页面",
    // description: "跳转页面",
    examples: [
      {
        title: "刷新页面",
        config: {
          type: "reload",
        },
      },
    ],
  },
  {
    component: "reload_table",
    title: "刷新table",
    // description: "跳转页面",
    examples: [
      {
        title: "刷新table",
        config: {
          type: "reload_table",
        },
      },
    ],
  },
  {
    component: "request",
    title: "请求",
    // description: "跳转页面",
    examples: [
      {
        title: "请求",
        config: {
          type: "request",
          showFullScreenLoading: false,
          request: {
            url: "/xxx",
            method: "GET",
            data: {
              action: "xxx",
            },
          },
          confirm: "确定保存吗？",
        },
      },
    ],
  },
  {
    component: "drawer",
    title: "打开抽屉",
    // description: "跳转页面",
    examples: [
      {
        title: "抽屉",
        config: {
          type: "drawer",
          title: "备注",
          props: {
            closable: true,
            maskClosable: true,
          },
          "extra/footer": [
            {
              title: "确定",
              props: {
                type: "primary",
              },
              command: {
                type: "submit",
                id: "form1",
              },
            },
          ],
          content: {
            component: "JSONComponents",
            type: "json/api",
            props: {},
            children: [
              {
                component: "Row",
                props: {
                  gutter: 16,
                },
                children: [
                  {
                    component: "Col",
                    props: {
                      xs: {
                        span: 24,
                      },
                      md: {
                        span: 24,
                      },
                    },
                    children: [
                      {
                        component: "Form",
                        type: "json",
                        props: {
                          layout: "vertical",
                          id: "form1",
                          initialValues: {
                            field1: 13123,
                          },
                          labelCol: {
                            span: 8,
                          },
                          wrapperCol: {
                            span: 16,
                          },
                        },
                        formItems: [
                          {
                            component: "Row",
                            props: {
                              gutter: 16,
                            },
                            children: [
                              {
                                component: "Col",
                                props: {
                                  span: 12,
                                },
                                children: [
                                  {
                                    key: "field1",
                                    label: "Field1",
                                    component: "Input",
                                    props: {
                                      extra: "提示文案",
                                    },
                                    rules: [
                                      {
                                        required: true,
                                        message: "This is a required field",
                                      },
                                    ],
                                  },
                                ],
                              },
                            ],
                          },
                        ],
                        submit: {
                          request: {
                            url: "http://192.168.2.110:8081/rest/v1/customer",
                            data: {
                              default: "test",
                            },
                          },
                        },
                      },
                    ],
                  },
                ],
              },
              {
                component: "Iframe",
                props: {
                  src: "https://www.jeulia.com/",
                },
              },
            ],
            fetcher: {
              request: {
                url: "",
                data: {},
              },
            },
          },
        },
      },
    ],
  },
  {
    component: "modal",
    title: "打开弹窗",
    // description: "跳转页面",
    examples: [
      {
        title: "弹窗",
        config: {
          type: "modal",
          title: "备注",
          props: {
            closable: true,
            maskClosable: true,
          },
          footer: [
            {
              title: "确定",
              props: {
                type: "primary",
              },
              command: {
                type: "submit",
                id: "form1",
              },
            },
          ],
          content: {
            component: "JSONComponents",
            type: "json/api",
            props: {},
            children: [
              {
                component: "Row",
                props: {
                  gutter: 16,
                },
                children: [
                  {
                    component: "Col",
                    props: {
                      xs: {
                        span: 24,
                      },
                      md: {
                        span: 24,
                      },
                    },
                    children: [
                      {
                        component: "Form",
                        type: "json",
                        props: {
                          layout: "vertical",
                          id: "form1",
                          initialValues: {
                            field1: 13123,
                          },
                          labelCol: {
                            span: 8,
                          },
                          wrapperCol: {
                            span: 16,
                          },
                        },
                        formItems: [
                          {
                            component: "Row",
                            props: {
                              gutter: 16,
                            },
                            children: [
                              {
                                component: "Col",
                                props: {
                                  span: 12,
                                },
                                children: [
                                  {
                                    key: "field1",
                                    label: "Field1",
                                    component: "Input",
                                    props: {
                                      extra: "提示文案",
                                    },
                                    rules: [
                                      {
                                        required: true,
                                        message: "This is a required field",
                                      },
                                    ],
                                  },
                                ],
                              },
                            ],
                          },
                        ],
                        submit: {
                          request: {
                            url: "http://192.168.2.110:8081/rest/v1/customer",
                            data: {
                              default: "test",
                            },
                          },
                        },
                      },
                    ],
                  },
                ],
              },
              {
                component: "Iframe",
                props: {
                  src: "https://www.jeulia.com/",
                },
              },
            ],
            fetcher: {
              request: {
                url: "",
                data: {},
              },
            },
          },
        },
      },
    ],
  },
  {
    component: "close_modal",
    title: "关闭弹窗/抽屉",
    // description: "跳转页面",
    examples: [
      {
        title: "关闭弹窗/抽屉",
        config: {
          type: "close_modal/close_drawer",
          target: "windowTop/windowSelf",
        },
      },
    ],
  },
  {
    component: "download",
    title: "请求接口下载",
    // description: "跳转页面",
    examples: [
      {
        title: "请求接口下载",
        config: {
          type: "download",
          url: "/xxx",
        },
      },
    ],
  },
  {
    component: "direct_download",
    title: "直接下载",
    // description: "跳转页面",
    examples: [
      {
        title: "直接下载",
        config: {
          type: "direct_download",
          url: "https://assets.cnzlerp.com/prod/download/template/import-shipping-no-tpl.xlsx",
        },
      },
    ],
  },
  {
    component: "message",
    title: "提示",
    // description: "跳转页面",
    examples: [
      {
        title: "提示",
        config: {
          type: "message",
          config: {
            type: "success",
            content: "提示文案",
            duration: 3,
          },
          command: {
            type: "reload_table",
          },
        },
      },
    ],
  },
  {
    component: "submit",
    title: "提交",
    // description: "跳转页面",
    examples: [
      {
        title: "提交",
        config: {
          type: "submit",
          id: "form (这里对应form的id)",
        },
      },
    ],
  },
  {
    component: "code_execution",
    title: "执行js",
    // description: "跳转页面",
    examples: [
      {
        title: "执行js",
        config: {
          type: "code_execution",
          code_string: '\n              console.log("菜单选项1");\n              alert("菜单选项1");\n            ',
        },
      },
    ],
  },
];

function CommandConfigPrompt() {
  return <ConfigPrompt data={commandExamples} />;
}

export default CommandConfigPrompt;
