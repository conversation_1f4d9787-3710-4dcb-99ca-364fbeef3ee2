import { ColorPicker as AntdColorPicker } from "antd";

function ColorPicker(props) {
  const { onChange, ...otherProps } = props;

  function convertToHex(value) {
    try {
      return `#${value.toHex()}`;
    } catch (error) {
      return "#000000";
    }
  }

  function handleChange(value) {
    const hexValue = convertToHex(value);
    onChange?.(hexValue);
  }

  return <AntdColorPicker {...otherProps} onChange={handleChange} />;
}

export default ColorPicker;
