import HtmlBlock from "components/common/HtmlBlock";
import { useEffect, useState } from "react";
import axios from "axios";
import PropTypes from "prop-types";
import styles from "./index.module.scss";
import SvgIcon from "components/common/SvgIcon";

function Icon(props) {
  const { src } = props;
  const [html, setHtml] = useState("");
  const isSvg = /\.svg$/.test(src);

  // useEffect(() => {
  //   (async () => {
  //     if (isSvg) {
  //       const result = await axios.get(src, { responseType: "text" });
  //       let svg = (result.data?.match(/<svg.*svg>/gi)[0] || "")
  //         .replace(/(width|height)=.[^'"]*./gi, "")
  //         .replace(/fill=.[^'"]*./gi, "");
  //       setHtml(svg);
  //     }
  //   })();
  // }, [src, isSvg]);

  if (isSvg) {
    return <SvgIcon {...props} src={src}></SvgIcon>;
  } else if (src) {
    return <img {...props} alt="" />;
  } else {
    return null;
  }
}

Icon.propTypes = {
  src: PropTypes.string,
};

export default Icon;
