import styles from "./index.module.scss";
import { useMemo } from "react";
import { SortableContext } from "@dnd-kit/sortable";

import SortableItem from "../SortableItem";

function SortableList({ items, selectedIds, count, onSelect }) {
  const itemIds = useMemo(() => {
    return items.filter((item) => !item.hidden).map((item) => item.id);
  }, [items]);

  return (
    <div className={styles.container}>
      <SortableContext items={itemIds}>
        <div className={styles.container}>
          <div className={styles.list}>
            {items
              ?.filter((i) => !i.hidden)
              ?.map((item, index) => (
                <SortableItem
                  key={item?.id || index}
                  id={item?.id}
                  item={item}
                  count={count}
                  onSelect={(e) => onSelect(e, item)}
                  selected={selectedIds.includes(item.id)}
                />
              ))}
          </div>
        </div>
      </SortableContext>
    </div>
  );
}

export default SortableList;
