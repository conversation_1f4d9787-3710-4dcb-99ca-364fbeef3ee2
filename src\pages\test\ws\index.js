import MyWebSocket from "@/websocket";
import { Button } from "antd";
import useWebSocketReceiver from "@/hooks/useWebSocketReceiver";
import Axios from "axios";

const axios = Axios.create({});

const StatusNames = {
  [MyWebSocket.DISCONNECTED]: "未连接",
  [MyWebSocket.CONNECTING]: "连接中",
  [MyWebSocket.CONNECTED]: "已连接",
  [MyWebSocket.RETRYING]: "重试中",
};

function TestWS() {
  const { status, data } = useWebSocketReceiver({ filter: { event: "downloadComplete" } });

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: `100vh`,
      }}
    >
      <div>
        <div>连接状态：{StatusNames[status]}</div>
        <div>下载完成：{data?.data?.event === "downloadComplete" ? "是" : "否"}</div>
      </div>
      {/*<div>
        <Button
          type="primary"
          onClick={async () => {
            axios.post(`http://localhost:8081/rest/v1/websocket/send`, { event: "downloadComplete" });
          }}
        >
          通知下载完成
        </Button>
      </div>*/}
    </div>
  );
}

export default TestWS;
