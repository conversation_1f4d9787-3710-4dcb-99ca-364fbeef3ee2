import React, { forwardRef, useEffect, useImperative<PERSON><PERSON><PERSON>, useRef, useCallback, useState } from "react";
import FormContext from "./context";
import PropTypes from "prop-types";
import FormEnums from "./enums";
import EventBus from "./event-bus";
import {
  debounce,
  findField,
  formControlsProps,
  formInstances,
  getFormControlProps,
  getFormRef,
  getObjectCursor,
  setFormFieldValue,
  updatedForms,
  classNames,
  isServer,
  getFormFieldValue,
  uniqueId,
} from "./common";

function Form(props, ref) {
  const {
    tag = "form",
    children,
    onReset,
    onSubmit,
    onChange,
    onValidateFailed,
    updateMode = "strict",
    ...otherProps
  } = props;
  const Tag = tag;
  const [formListenerReady, setFormListenerReady] = useState(false);
  const formRef = useRef();

  const paramsRef = useRef({
    formId: uniqueId(),
    formValues: {},
    valueFieldNames: {},
    defaultFormValues: {},
  });
  paramsRef.current = { ...paramsRef.current, tag, onChange, updateMode, handleSubmit, updateFormValue, handleReset };

  (() => {
    if (isServer()) return;
    const { formId } = paramsRef.current;
    if (!formInstances[formId]) {
      formInstances[formId] = Object.freeze({
        get element() {
          return formRef.current;
        },
        get formId() {
          return paramsRef.current.formId;
        },
        get value() {
          return paramsRef.current.formValues;
        },
        getFieldValue({ keyPath, name }) {
          const { formValues } = paramsRef.current;
          return getFormFieldValue({ formValues, keyPath, name });
        },
        setFieldValue({ keyPath, name, value }) {
          return setFieldValue({ keyPath, name, value });
        },
        updateValue: (partialData) => {
          return new Promise((resolve) => {
            const { formId } = paramsRef.current;
            updateFormValue(partialData);
            EventBus.dispatchEvent(FormEnums.Events.Message, {
              action: FormEnums.Actions.UpdateFormControl,
              payload: { formId, partialData, callback: debounce(resolve, 10) },
            });
            updatedForms[formId] = true;
          });
        },
        validate: validateForm,
        validateFields(fields = []) {
          return new Promise((resolve, reject) => {
            const { formId } = paramsRef.current;
            const validateResults = [];
            const handleComplete = debounce(() => {
              const results = fields.map((field) => {
                const result = findField({ fields: validateResults, name: field.name, keyPath: field.keyPath });
                return result ? result : { field, error: "not found" };
              });
              const errors = results.filter((x) => x.error);
              errors.length > 0
                ? reject(new Error(`[Form] validateFields error: ${JSON.stringify(errors)}`))
                : resolve(results);
            }, 10);
            EventBus.dispatchEvent(FormEnums.Events.Message, {
              action: FormEnums.Actions.ValidateFields,
              payload: {
                formId,
                fields,
                callback(result) {
                  if (result) {
                    validateResults.push(result);
                  }
                  handleComplete();
                },
              },
            });
          });
        },
        clearErrorMessage(fields) {
          const { formId } = paramsRef.current;
          EventBus.dispatchEvent(FormEnums.Events.Message, {
            action: FormEnums.Actions.ClearErrorMessage,
            payload: { formId, fields },
          });
        },
        async submit() {
          return await paramsRef.current.handleSubmit();
        },
        async reset() {
          return await paramsRef.current.handleReset();
        },
      });
    }
  })();

  const handleChange = useCallback((event, value, extra) => {
    const { onChange, updateFormValue } = paramsRef.current;
    const isCustomControl = typeof value === "object";
    const name = isCustomControl ? Object.keys(value)[0] : event?.target?.name;
    if (name) {
      const valueFieldName = getValueFieldName({ name });
      const nextValue = isCustomControl ? value : { [name]: event?.target?.[valueFieldName] };
      paramsRef.current.updateFormValue(nextValue, extra);
      onChange?.(event, nextValue, extra);
    }
  }, []);

  function getValueFieldName({ name }) {
    const { valueFieldNames } = paramsRef.current;
    return valueFieldNames[name] || "value";
  }

  function updateFormValue(partialData, extra) {
    Object.entries(partialData || {}).forEach(([name, value]) => {
      const formControlId = extra?.formControlId;
      if (formControlId) {
        const { keyPath } = getFormControlProps({ formControlId });
        setFieldValue({ keyPath, name, value, formControlId });
      } else {
        setFieldValue({ name, value });
      }
    });
  }

  function setFieldValue({ keyPath = [], name, value, formControlId }) {
    const { valueFieldNames, formValues, updateMode } = paramsRef.current;
    if (updateMode === "strict") {
      const validKeys = Object.keys(valueFieldNames);
      if (formControlId) {
        validKeys.push(getFormControlProps({ formControlId }).name);
      }
      if (keyPath?.length > 0) {
        validKeys.push(keyPath[0]);
      }
      setFormFieldValue({
        formValues,
        keyPath,
        setValue({ cursor }) {
          if (validKeys.includes(name)) {
            cursor[name] = value;
          }
        },
      });
    } else if (updateMode === "mutation") {
      setFormFieldValue({
        formValues,
        keyPath,
        setValue({ cursor }) {
          cursor[name] = value;
        },
      });
    }
  }

  function validateForm() {
    return new Promise((resolve, reject) => {
      if (Object.keys(formControlsProps).length > 0) {
        const { formId, formValues } = paramsRef.current;
        paramsRef.current.validateFormPromise = { resolve, reject };
        EventBus.dispatchEvent(FormEnums.Events.Message, {
          action: FormEnums.Actions.ValidateForm,
          payload: { formId, formValues },
        });
      } else {
        resolve();
      }
    });
  }

  function handleSubmit(event) {
    event?.preventDefault?.();
    const { formValues } = paramsRef.current;
    return validateForm()
      .then(() => {
        onSubmit?.(event, formValues);
        return { success: true, value: formValues };
      })
      .catch((errors) => {
        onValidateFailed?.(event, errors);
        return { success: false, errors };
      });
  }

  function clearGhostFormInstance() {
    Object.values(formInstances).forEach((item) => {
      if (!item.element) {
        delete formInstances[item.formId];
      }
    });
  }

  async function handleReset(event) {
    const { formId, defaultFormValues } = paramsRef.current;
    const form = getFormRef({ formId }).current;
    await form.updateValue(defaultFormValues);
    form.clearErrorMessage();
    onReset?.(event, defaultFormValues);
  }

  useImperativeHandle(
    ref,
    () => {
      const { formId } = paramsRef.current;
      return formInstances[formId];
    },
    []
  );

  useEffect(() => {
    const validateFields = {};
    const validateResults = {};
    const deletedFields = [];

    const handleUnmountComplete = debounce(() => {
      const { formValues } = paramsRef.current;
      const deletedKeyPath = {};
      deletedFields
        .filter((x) => x.keyPath.some((key) => typeof key === "number"))
        .forEach((field) => {
          const cursor = getObjectCursor({ object: formValues, keyPath: field.keyPath });
          if (Object.keys(cursor).length === 0) {
            const key = JSON.stringify(field.keyPath);
            deletedKeyPath[key] = true;
          }
        });
      Object.keys(deletedKeyPath).forEach((str) => {
        const keyPath = JSON.parse(str);
        const lastKey = keyPath[keyPath.length - 2];
        const parent = getObjectCursor({ object: formValues, keyPath: keyPath.slice(0, keyPath.length - 2) });
        parent[lastKey] = parent[lastKey].filter((x) => Object.keys(x).length > 0);
      });
      deletedFields.length = 0;
    }, 10);

    const handlers = {
      [FormEnums.Actions.InitFormControl]: async ({ payload }) => {
        const { name, value, defaultValue, valueField, keyPath, formControlId } = payload;
        if (!formControlsProps[formControlId]) {
          setFormFieldValue({ formValues: paramsRef.current.defaultFormValues, keyPath, name, value: defaultValue });
        }
        formControlsProps[formControlId] = payload;
        setFormFieldValue({ formValues: paramsRef.current.formValues, keyPath, name, value });
        paramsRef.current.valueFieldNames[name] = valueField;
        validateFields[formControlId] = false;
      },
      [FormEnums.Actions.ValidateFormCallback]: async ({ payload }) => {
        const { result, formControlId } = payload;
        validateFields[formControlId] = true;
        validateResults[formControlId] = result;
        if (Object.values(validateFields).every((x) => x)) {
          Object.keys(validateFields).forEach((key) => {
            validateFields[key] = false;
          });
          const { validateFormPromise } = paramsRef.current;
          if (Object.values(validateResults).every((result) => result.isValid)) {
            validateFormPromise.resolve(paramsRef.current.formValues);
          } else {
            validateFormPromise.reject(Object.values(validateResults).filter((x) => !x.isValid));
          }
        }
      },
      [FormEnums.Actions.UnmountFormControl]: async ({ payload }) => {
        const { formControlId, name } = payload;
        const { updateMode, formValues } = paramsRef.current;
        if (updateMode === "strict") {
          const { keyPath = [] } = getFormControlProps({ formControlId });
          setFormFieldValue({
            formValues,
            keyPath,
            name,
            setValue({ cursor }) {
              delete cursor[name];
              deletedFields.push({ name, keyPath });
            },
          });
          delete paramsRef.current.valueFieldNames[name];
        }
        delete validateFields[formControlId];
        delete validateResults[formControlId];
        delete formControlsProps[formControlId];
        handleUnmountComplete();
      },
    };

    function handleMessage(data) {
      const { action, payload = {} } = data;
      const { formId } = payload;
      const { formId: thisFormId } = paramsRef.current;
      if (formId === thisFormId) {
        handlers[action]?.({ payload });
      }
    }
    EventBus.addEventListener(FormEnums.Events.Message, handleMessage);
    setFormListenerReady(true);

    return function () {
      EventBus.removeEventListener(FormEnums.Events.Message, handleMessage);
      const { formId } = paramsRef.current;
      delete formInstances[formId];
      delete updatedForms[formId];
      Object.values(formControlsProps).forEach((item) => {
        if (item.formId === formId) {
          delete formControlsProps[item.formControlId];
        }
      });
      // 理论上useRef的初始化值应该只有一个，但实际情况会出现多个，会导致一个form出现多个formId，需要清除不存在的数据
      clearGhostFormInstance();
    };
  }, []);

  useEffect(() => {
    clearGhostFormInstance();
  }, []);

  useEffect(() => {
    const formEl = formRef.current;
    function handleFormClick(event) {
      const { tag, handleSubmit, handleReset } = paramsRef.current;
      if (tag.toLowerCase() !== "form") {
        if (event.target.closest(`button[type="submit"]`)) {
          event.stopPropagation();
          handleSubmit();
        } else if (event.target.closest(`button[type="reset"]`)) {
          event.stopPropagation();
          handleReset();
        }
      }
    }
    formEl?.addEventListener("click", handleFormClick);

    return function () {
      formEl?.removeEventListener("click", handleFormClick);
    };
  }, []);

  return (
    <FormContext.Provider
      value={{
        formId: paramsRef.current.formId,
        onFormChange: handleChange,
        formListenerReady,
      }}
    >
      <Tag
        {...otherProps}
        ref={formRef}
        className={classNames("form-x", props.className)}
        onReset={handleReset}
        onSubmit={handleSubmit}
      >
        {children}
      </Tag>
    </FormContext.Provider>
  );
}

Form = forwardRef(Form);

Form.propTypes = {
  onChange: PropTypes.func,
  onReset: PropTypes.func,
  onSubmit: PropTypes.func,
  onValidateFailed: PropTypes.func,
  style: PropTypes.object,
  tag: PropTypes.string,
  updateMode: PropTypes.oneOf(["strict", "mutation"]),
};

export default Form;
