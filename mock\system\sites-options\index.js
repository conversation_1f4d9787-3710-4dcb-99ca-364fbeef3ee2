module.exports = async (req, res) => {
  const scope = req.query.scope;

  const optionsMap = {
    "/list/order-list": [
      {
        value: 1,
        label: "DR品牌组",
        children: [
          {
            value: 2,
            label: "Drawelry",
          },
        ],
      },
      {
        value: 2,
        label: "Jeulia品牌组",
        children: [
          {
            value: 1,
            label: "Jeuli<PERSON>",
          },
        ],
      },
      {
        value: 3,
        label: "Aoolia品牌组",
        children: [
          {
            value: 6,
            label: "Aoolia",
          },
        ],
      },
      {
        value: 4,
        label: "SSY",
        children: [
          {
            value: 3,
            label: "Shesaidyes",
          },
        ],
      },
    ],
    "/list/popup-template": [
      {
        value: 1,
        label: "DR品牌组",
      },
      {
        value: 2,
        label: "Jeulia品牌组",
      },
      {
        value: 3,
        label: "Aoolia品牌组",
      },
      {
        value: 4,
        label: "SSY",
      },
    ],
  };

  const options = [
    {
      value: "jeulia",
      label: "<PERSON><PERSON><PERSON>",
      children: [
        { value: "jeulia-us", label: "JE-美国站" },
        { value: "jeulia-uk", label: "JE-英国站" },
        { value: "jeulia-fr", label: "JE-法国站" },
      ],
    },
    {
      value: "drawelry",
      label: "Drawelry",
      children: [
        { value: "drawelry-us", label: "DR-美国站" },
        { value: "drawelry-uk", label: "DR-英国站" },
        { value: "drawelry-fr", label: "DR-法国站" },
      ],
    },
    {
      value: "shesaidyes",
      label: "Shesaidyes",
      children: [{ value: "shesaidyes-us", label: "SSY-美国站" }],
    },
  ];

  res.status(200).json({
    success: true,
    data: optionsMap[scope] || options,
  });
};
