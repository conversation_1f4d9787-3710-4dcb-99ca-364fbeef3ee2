import { ControlWrapper, FormControl, useForm } from "@/components/react-form-x";
import PropTypes from "prop-types";
import { renderI18nSelect } from "@/pages/pod/common";

function DropdownPrivateControls(props) {
  const { keyPath } = props;
  const form = useForm();

  return (
    <>
      <div>
        <FormControl
          keyPath={keyPath}
          name="placeholder"
          render={(props) => (
            <div>
              <div>Placeholder:</div>
              <div>
                <ControlWrapper
                  {...props}
                  render={({ name, value, onChange }) => renderI18nSelect({ form, keyPath, name, value, onChange })}
                ></ControlWrapper>
              </div>
            </div>
          )}
        ></FormControl>
      </div>
    </>
  );
}

DropdownPrivateControls.propTypes = {
  keyPath: PropTypes.array,
};

export default DropdownPrivateControls;
