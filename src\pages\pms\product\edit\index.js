import styles from "./index.module.scss";
import { useLocation, useNavigate } from "react-router-dom";
import useS<PERSON> from "swr";
import classNames from "classnames";
import { <PERSON><PERSON>, Card } from "antd";

import Helper from "@/helpers";
import Utils from "@/utils";
import Fetchers from "@/fetchers";
import Breadcrumbs from "components/common/Breadcrumbs";
import SubmitButton from "components/common/Button";
import JSONComponent from "components/common/JSONComponent";
import JSONForm from "./components/JSONForm";

function ProductEdit() {
  const location = useLocation();
  const params = Utils.getQueryParams(decodeURIComponent(location.search));
  const navigate = useNavigate();

  const isInsideIframe = Helper.isInsideIframe();

  const { data = {} } = useSWR(params, async () => {
    const result = await Fetchers.getPmsProductEdit({ params }).then((res) => res.data?.data);
    return result;
  });

  function handleBack() {
    navigate(-1);
  }

  return (
    <div className={classNames(styles.productEditPage, { [styles.insideIframe]: isInsideIframe })}>
      <div className="page-header">
        {!isInsideIframe && <Breadcrumbs data={data?.breadcrumbs} />}
        <div className="page-header-actions">
          {!isInsideIframe && (
            <Button type="default" onClick={handleBack}>
              返回
            </Button>
          )}
          {data?.actions?.map((item, index) => {
            return (
              <SubmitButton
                key={index}
                type="primary"
                {...item.props}
                command={item.command}
                onClick={async () => {
                  await Helper.commandHandler({ command: item.command });
                }}
              >
                {item.title}
              </SubmitButton>
            );
          })}
        </div>
      </div>
      <div className={styles.content}>
        <Card size="small">
          {data?.content && (
            <JSONComponent
              data={data?.content}
              extendComponents={{
                Form: JSONForm,
              }}
            />
          )}
        </Card>
      </div>
    </div>
  );
}

export default ProductEdit;
