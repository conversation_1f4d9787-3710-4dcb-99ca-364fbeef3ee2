import { Select } from "antd";
import store from "@/stores";
import { observer } from "mobx-react-lite";
import useLanguage from "@/hooks/useLanguage";

function LangSelector() {
  const { systemInfo } = store;
  const { currentLanguage, changeLanguage } = useLanguage();

  if (!systemInfo?.language) return null;

  return (
    <Select size="small" value={currentLanguage} {...(systemInfo?.language?.props || {})} onChange={changeLanguage} />
  );
}

export default observer(LangSelector);
