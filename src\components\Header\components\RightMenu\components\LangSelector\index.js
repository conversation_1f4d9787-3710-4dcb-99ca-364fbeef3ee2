import { Select } from "antd";
import store from "@/stores";
import { observer } from "mobx-react-lite";
import { useI18n } from "@/context/I18nContext";

function LangSelector() {
  const { systemInfo } = store;
  const { currentLanguage, changeLanguage, t } = useI18n();

  if (!systemInfo?.language) return null;

  return (
    <Select size="small" value={currentLanguage} {...(systemInfo?.language?.props || {})} onChange={changeLanguage} />
  );
}

export default observer(LangSelector);
