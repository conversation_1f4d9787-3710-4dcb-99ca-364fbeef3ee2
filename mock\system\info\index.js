module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      name: "ERP管理系统",
      printer_service_enable: true,
      printer_service_secret: "54D95EFDD871831D8FC865526B6D75909D9:A9984F1619EFC5370C13557379858C88AB3",
      logo: "https://assets.cnzlerp.com/static/3/6/667d2c36d2b63.png",
      favicon: "https://site-deploy.cnzlerp.com/favicon.ico",
      user: {
        name: "yl",
      },
      page_info_enable: true,
      language: {
        props: {
          placeholder: "请选择语言",
          defaultValue: "zh-CN",
          options: [
            {
              label: "中文",
              value: "zh-CN",
            },
            {
              label: "英文",
              value: "en-US",
            },
          ],
        },
      },
      lang_tabs: {
        extraItems: [
          {
            key: "en",
            label: "英语",
            value: "en",
            icon: "/icons/flag-icon/us.svg",
          },
          { key: "en_GB", label: "英语(GB)", value: "en_GB", icon: "/icons/flag-icon/gb.svg" },
          { key: "en_NZ", label: "英语(NZ)", value: "en_NZ", icon: "/icons/flag-icon/nz.svg" },
          { key: "en_MY", label: "英语(MY)", value: "en_MY", icon: "/icons/flag-icon/my.svg" },
          { key: "en_ZA", label: "英语(ZA)", value: "en_ZA", icon: "/icons/flag-icon/za.svg" },
          { key: "en_SG", label: "英语(SG)", value: "en_SG", icon: "/icons/flag-icon/sg.svg" },
        ],
        items: [
          { key: "fr", label: "法语", value: "fr", icon: "/icons/flag-icon/fr.svg" },
          { key: "de", label: "德语", value: "de", icon: "/icons/flag-icon/de.svg" },
          { key: "zh", label: "中文", value: "zh", icon: "/icons/flag-icon/cn.svg" },
          { key: "it", label: "意语", value: "it", icon: "/icons/flag-icon/it.svg" },
          { key: "es", label: "西语", value: "es", icon: "/icons/flag-icon/es.svg" },
          { key: "ja", label: "日语", value: "ja", icon: "/icons/flag-icon/jp.svg" },
          { key: "pl", label: "波兰语", value: "pl", icon: "/icons/flag-icon/pl.svg" },
          { key: "sv", label: "瑞典语", value: "sv", icon: "/icons/flag-icon/sv.svg" },
          { key: "ar", label: "阿语", value: "ar", icon: "/icons/flag-icon/ar.svg" },
          { key: "pt", label: "葡萄牙语", value: "pt", icon: "/icons/flag-icon/pt.svg" },
          { key: "nl", label: "荷兰语", value: "nl", icon: "/icons/flag-icon/nl.svg" },
          { key: "no", label: "挪威语", value: "no", icon: "/icons/flag-icon/no.svg" },
        ],
      },
    },
  });
};
