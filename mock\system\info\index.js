module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      name: "ERP管理系统",
      printer_service_enable: true,
      printer_service_secret: "54D95EFDD871831D8FC865526B6D75909D9:A9984F1619EFC5370C13557379858C88AB3",
      logo: "https://assets.cnzlerp.com/static/3/6/667d2c36d2b63.png",
      favicon: "https://site-deploy.cnzlerp.com/favicon.ico",
      user: {
        name: "yl",
      },
      page_info_enable: true,
      language: {
        props: {
          placeholder: "请选择语言",
          defaultValue: "zh-CN",
          options: [
            {
              label: "中文",
              value: "zh-CN",
            },
            {
              label: "英文",
              value: "en-US",
            },
          ],
        },
      },
    },
  });
};
