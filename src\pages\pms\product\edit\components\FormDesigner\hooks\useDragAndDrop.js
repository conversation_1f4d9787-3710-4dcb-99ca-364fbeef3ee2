import { useState, useRef, useCallback, useMemo } from "react";
import { arrayMove } from "@dnd-kit/sortable";

import {
  generateKey,
  flattenTree,
  sanitizeItems,
  buildItems,
  removeComponent,
  moveComponent,
  isInGroup,
  cloneComponent,
} from "../common/utils";

import Enums from "../common/enums";
import { afterRender } from "functions";

function getDndData(prop) {
  return prop?.data?.current ?? {};
}

function createGhost({ key, isGroup = false, relKey }) {
  return {
    key,
    type: "ghost",
    isGroup,
    relKey,
  };
}

function useDragAndDrop({ widgetForm, setWidgetForm, setSelectedComponent, onChange }) {
  const [dragState, setDragState] = useState({
    activeId: null,
    dragData: null,
    isOverDropZone: false,
  });

  const ghostInsertedRef = useRef(false);
  const currentDragComponentRef = useRef(null);

  const flattenedItems = useMemo(() => flattenTree(widgetForm.items), [widgetForm]);

  // 判断是否在画布内
  const isOverFormCanvas = useCallback(
    (over) => {
      if (!over) return false;

      if (over.id === Enums.DroppableIds.FormCanvas) {
        return true;
      }

      return flattenedItems.some((item) => item.key === over.id);
    },
    [flattenedItems]
  );

  const handleDragStart = useCallback(
    (event) => {
      const { active } = event;
      const activeData = getDndData(active);

      setDragState((prev) => ({
        ...prev,
        activeId: active.id,
        dragData: activeData,
      }));

      if (activeData?.fromSidebar) {
        const { component } = activeData;
        currentDragComponentRef.current = {
          ...cloneComponent(component),
          key: generateKey({ prefix: component.key, items: widgetForm.items }),
        };
        return;
      }

      const { component } = activeData;

      currentDragComponentRef.current = component;

      // 递归查找并替换为 ghost
      const replaceWithGhost = (items) => {
        return items.map((item) => {
          // 如果找到了被拖拽的组件
          if (item.key === active.id) {
            return createGhost({
              key: active.id,
              isGroup: item.component_config?.isGroup,
            });
          }

          // 如果是组容器，递归查找其子项
          if (item.component_config?.isGroup) {
            const childrenName = item.component_config?.childrenName || "children";
            const updatedChildren = replaceWithGhost(item[childrenName] || []);

            // 如果子项有变化，说明找到并替换了目标组件
            if (updatedChildren.some((child) => child.type === "ghost")) {
              return {
                ...item,
                [childrenName]: updatedChildren,
              };
            }
          }

          return item;
        });
      };

      setWidgetForm((prev) => ({
        ...prev,
        items: replaceWithGhost(prev.items),
      }));
    },
    [widgetForm, setWidgetForm]
  );

  const handleDragMove = useCallback(
    (event) => {
      const { active, over, collisions } = event;
      if (!over) return;

      let hovered = null;
      if (collisions) {
        for (let i = collisions.length - 1; i >= 0; i--) {
          if (collisions[i]?.data?.hovered) {
            hovered = collisions[i];
            break;
          }
        }
      }

      const activeData = getDndData(active);
      const activeKey = activeData?.fromSidebar ? currentDragComponentRef.current?.key : active.id;

      if (activeKey === over.id) return;

      if (hovered?.data?.isActiveContainer) {
        // if (!over.data.current.isGroup) return;

        const containerData = hovered.data?.droppableContainer?.data?.current;
        if (!containerData?.component?.component_config?.isGroup) return;

        const componentConfig = containerData?.component?.component_config;
        const { childrenName = "children" } = componentConfig ?? {};

        const dropKey = hovered.id;

        const removeComponentAndGhost = (items) => {
          return items
            .map((item) => {
              // 移除ghost和原组件(不是从Sidebar拖拽来的)
              if (item.type === "ghost" || (!activeData?.fromSidebar && item.key === activeKey)) {
                return null;
              }
              if (item.component_config?.isGroup) {
                const itemChildrenName = item.component_config?.childrenName || "children";
                const children = item[itemChildrenName] || [];
                const filteredChildren = removeComponentAndGhost(children).filter(Boolean);
                return {
                  ...item,
                  [itemChildrenName]: filteredChildren,
                };
              }
              return item;
            })
            .filter(Boolean);
        };

        setWidgetForm((prev) => {
          const itemsWithoutComponent = removeComponentAndGhost(prev.items);
          const ghost = createGhost({
            key: activeKey,
            isGroup: currentDragComponentRef.current?.component_config?.isGroup,
            relKey: active.id,
          });

          // 在目标组容器中添加ghost
          const addGhostToTarget = (items) => {
            return items.map((item) => {
              if (item.key === dropKey) {
                const children = item[childrenName] || [];
                return {
                  ...item,
                  [childrenName]: [...children, ghost],
                };
              }
              if (item.component_config?.isGroup) {
                const itemChildrenName = item.component_config?.childrenName || "children";
                return {
                  ...item,
                  [itemChildrenName]: addGhostToTarget(item[itemChildrenName] || []),
                };
              }
              return item;
            });
          };

          return {
            ...prev,
            items: addGhostToTarget(itemsWithoutComponent),
          };
        });

        // const currentItems = widgetForm.items;
        // const newItems = buildItems({ items: widgetForm.items, activeKey, dropKey, childrenName });

        // if (JSON.stringify(currentItems) !== JSON.stringify(newItems)) {
        //   setWidgetForm((prev) => ({
        //     ...prev,
        //     items: newItems,
        //   }));
        // }
      } else if (isInGroup(widgetForm.items, activeKey)) {
        if (
          over?.id === Enums.DroppableIds.FormCanvas ||
          (!over?.data?.current?.isGroup && over?.data?.current?.index > -1)
        ) {
          const removeFromGroup = (items) => {
            return items.map((item) => {
              if (item.component_config?.isGroup) {
                const childrenName = item.component_config?.childrenName || "children";
                const children = item[childrenName] || [];

                const filteredChildren = children.filter((child) => child.key !== activeKey);

                if (filteredChildren.length !== children.length) {
                  return {
                    ...item,
                    [childrenName]: filteredChildren,
                  };
                }

                return {
                  ...item,
                  [childrenName]: removeFromGroup(children),
                };
              }
              return item;
            });
          };

          // 移动 ghost 到目标位置
          setWidgetForm((prev) => {
            const newItems = removeFromGroup(prev.items);
            const ghost = createGhost({
              key: activeKey,
              isGroup: currentDragComponentRef.current?.component_config?.isGroup,
              relKey: active.id,
            });

            let targetIndex;
            if (over?.data?.current?.index !== undefined) {
              targetIndex = over.data.current.index;
            } else {
              targetIndex = newItems.length;
            }

            return {
              ...prev,
              items: [...newItems.slice(0, targetIndex), ghost, ...newItems.slice(targetIndex, newItems.length)],
            };
          });
        }
      }
    },
    [widgetForm, setWidgetForm]
  );

  const handleDragOver = useCallback(
    (event) => {
      const { active, over } = event;
      const activeData = getDndData(active);
      const overData = getDndData(over);

      if (!isOverFormCanvas(over) && activeData?.fromSidebar) {
        // 拖拽到画布外，寻找ghost并删除
        setWidgetForm((prev) => {
          const newItems = activeData?.fromSidebar
            ? removeComponent({ items: prev.items, type: "ghost" })
            : prev.items.filter((item) => item.type !== "ghost");

          return {
            ...prev,
            items: newItems,
          };
        });

        ghostInsertedRef.current = false;
        return;
      }

      // 拖拽的是左边小组件
      if (activeData?.fromSidebar) {
        const isOver = isOverFormCanvas(over);
        setDragState((prev) => ({
          ...prev,
          isOverDropZone: isOver,
        }));

        // 插入ghost
        if (!ghostInsertedRef.current) {
          const ghost = createGhost({
            key: currentDragComponentRef.current.key,
            relKey: active.id,
            isGroup: activeData.component.component_config?.isGroup,
          });

          setWidgetForm((prev) => {
            let newItems;
            if (!prev.items.length) {
              newItems = [ghost];
            } else {
              const newIndex = overData.index > -1 ? overData.index : prev.items.length;

              newItems = [...prev.items.slice(0, newIndex), ghost, ...prev.items.slice(newIndex, prev.items.length)];
            }

            ghostInsertedRef.current = true;

            return {
              ...prev,
              items: newItems,
            };
          });
        } else {
          // 从左边拖拽来的，而不是canvas内排序
          if (currentDragComponentRef.current.key !== over.id) {
            const newItems = moveComponent({
              items: widgetForm.items,
              activeKey: currentDragComponentRef.current.key,
              overKey: over.id,
            });

            setWidgetForm((prev) => ({
              ...prev,
              items: newItems,
            }));
          }
        }
      }
    },
    [isOverFormCanvas, setWidgetForm, widgetForm]
  );

  const handleDragEnd = useCallback(
    (event) => {
      const { active, over } = event;
      const activeData = getDndData(active);

      if (!isOverFormCanvas(over) && activeData?.fromSidebar) {
        handleCleanUp();
        setWidgetForm((prev) => ({
          ...prev,
          items: prev.items.filter((item) => item.type !== "ghost"),
        }));
        return;
      }

      const nextComponent = currentDragComponentRef.current;
      if (nextComponent) {
        const overData = getDndData(over);
        setSelectedComponent(nextComponent);

        setWidgetForm((prev) => {
          // 递归查找 ghost 并获取其位置信息
          const findGhostPath = (items, path = []) => {
            for (let i = 0; i < items.length; i++) {
              const item = items[i];
              if (item.type === "ghost") {
                return { path, index: i };
              }

              if (item.component_config?.isGroup) {
                const childrenName = item.component_config?.childrenName || "children";
                const children = item[childrenName] || [];
                const result = findGhostPath(children, [...path, { item, prop: childrenName }]);
                if (result) return result;
              }
            }
            return null;
          };

          const replaceGhost = (items, ghostLocation) => {
            if (!ghostLocation) return items;

            const { path, index } = ghostLocation;

            if (path.length === 0) {
              const newItems = [...items.slice(0, index), nextComponent, ...items.slice(index + 1)];
              return activeData?.fromSidebar ? newItems : arrayMove(newItems, index, overData.index);
            }

            return items.map((item) => {
              const currentPath = path[0];
              if (item?.key === currentPath.item?.key) {
                const childrenName = currentPath.prop;
                const children = item[childrenName] || [];

                const updatedChildren = replaceGhost(children, {
                  path: path.slice(1),
                  index,
                });

                return {
                  ...item,
                  [childrenName]: updatedChildren,
                };
              }
              return item;
            });
          };

          const ghostLocation = findGhostPath(prev.items);
          const newItems = replaceGhost(prev.items, ghostLocation);

          if (!ghostLocation) return prev;

          afterRender(() => {
            const sanitizedItems = sanitizeItems(newItems);
            const data = { ...prev, items: sanitizedItems };
            onChange?.(data);
          });

          return { ...prev, items: replaceGhost(prev.items, ghostLocation) };
        });
      }

      handleCleanUp();
    },
    [isOverFormCanvas, setSelectedComponent, setWidgetForm, onChange]
  );

  const handleCleanUp = () => {
    ghostInsertedRef.current = false;
    currentDragComponentRef.current = null;

    setDragState((prev) => ({
      activeId: null,
      dragData: null,
      isOverDropZone: false,
    }));
  };

  return {
    ...dragState,
    flattenedItems,
    handleDragStart,
    handleDragMove,
    handleDragOver,
    handleDragEnd,
    handleCleanUp,
  };
}

export default useDragAndDrop;
