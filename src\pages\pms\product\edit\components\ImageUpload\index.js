import styles from "./index.module.scss";
import { useCallback } from "react";
import PropTypes from "prop-types";

import Helpers from "@/helpers";
import { Form, InputNumber } from "antd";
import CommonImageUpload from "components/common/ImageUpload";
import UploadItem from "components/common/ImageUpload/components/UploadItem";
import classNames from "classnames";

function ImageUpload(props) {
  const { item, setFieldValue, ...otherProps } = props;
  const { props: itemProps = {} } = item;

  const renderActions = useCallback((file, actions) => {
    if (!actions?.length) return null;

    return (
      <div className={classNames("ant-upload-list-item-actions", styles.actions)}>
        {actions.map((action) => {
          const { command } = action;
          return (
            <span
              className={styles.action}
              key={action.title}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                // TODO: 这里需要和后端确认需要传递的参数
                Helpers.commandHandler({ command, id: file.fid });
              }}
            >
              {action.title}
            </span>
          );
        })}
      </div>
    );
  }, []);

  const renderExtraFields = useCallback(
    (file, extraFields) => {
      if (!extraFields?.length) return null;

      // TODO: 优化listeners
      const onFocus = (e) => {
        e.preventDefault();
        e.stopPropagation();
        e.target.focus();
      };

      return (
        <div className={classNames("ant-upload-list-item-actions", styles.extraFields)}>
          {extraFields.map((field) => {
            const fieldName = `${item.key}_${file.fid}_${field.key}`;
            return (
              <Form.Item className={styles.extraField} key={field.key} name={fieldName} style={{ marginBottom: 0 }}>
                <InputNumber
                  key={fieldName}
                  size="small"
                  controls={false}
                  precision={0}
                  {...field.props}
                  style={{ width: "100%" }}
                  onClick={onFocus}
                  onFocus={onFocus}
                />
              </Form.Item>
            );
          })}
        </div>
      );
    },
    [item.key]
  );

  const renderTags = useCallback((file) => {
    if (!file?.tags?.length) return null;

    return (
      <div className={styles.tags}>
        {file.tags.map((tag) => (
          <span key={tag}>{tag}</span>
        ))}
      </div>
    );
  }, []);

  const renderUploadItem = useCallback(
    ({ item, file, isShowFileInfo }) => {
      return (
        <div className={styles.uploadItem}>
          <UploadItem item={item} file={file} isShowFileInfo={isShowFileInfo} />
          {renderTags(file)}
          {renderActions(file, itemProps?.actions)}
          {renderExtraFields(file, itemProps?.extraFields)}
        </div>
      );
    },
    [itemProps, renderActions, renderExtraFields, renderTags]
  );

  return (
    <CommonImageUpload
      className={styles.imageUpload}
      {...otherProps}
      {...itemProps}
      setFieldValue={setFieldValue}
      renderUploadItem={renderUploadItem}
    />
  );
}

ImageUpload.propTypes = {
  item: PropTypes.object,
  setFieldValue: PropTypes.func.isRequired,
};

export default ImageUpload;
