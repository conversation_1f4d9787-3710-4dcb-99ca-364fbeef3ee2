const FLAG_ICON_PATH = "/icons/flag-icon";

export function renderTab({ label, icon }) {
  return (
    <div className="lang-tabs-wrapper">
      <span
        className="lang-tabs-flag-icon"
        style={{
          backgroundImage: `url(${icon})`,
        }}
      ></span>
      <span>{label}</span>
    </div>
  );
}

function createLangConfig({ key, flag, label, value = key }) {
  return {
    key,
    flag,
    value,
    label: renderTab({ label, icon: `${FLAG_ICON_PATH}/${flag}.svg` }),
  };
}

export const operations = [
  createLangConfig({ key: "en", flag: "us", label: "英语" }),
  createLangConfig({ key: "en_GB", flag: "gb", label: "英语(GB)" }),
  createLangConfig({ key: "en_NZ", flag: "nz", label: "英语(NZ)" }),
  createLangConfig({ key: "en_MY", flag: "my", label: "英语(MY)" }),
  createLangConfig({ key: "en_ZA", flag: "za", label: "英语(ZA)" }),
  createLangConfig({ key: "en_SG", flag: "sg", label: "英语(SG)" }),
];

export const items = [
  createLangConfig({ key: "fr", flag: "fr", label: "法语" }),
  createLangConfig({ key: "de", flag: "de", label: "德语" }),
  createLangConfig({ key: "zh", flag: "cn", label: "中文" }),
  createLangConfig({ key: "it", flag: "it", label: "意语" }),
  createLangConfig({ key: "es", flag: "es", label: "西语" }),
  createLangConfig({ key: "ja", flag: "jp", label: "日语" }),
  createLangConfig({ key: "pl", flag: "pl", label: "波兰语" }),
  createLangConfig({ key: "sv", flag: "sv", label: "瑞典语" }),
  createLangConfig({ key: "ar", flag: "ar", label: "阿语" }),
  createLangConfig({ key: "pt", flag: "pt", label: "葡萄牙语" }),
  createLangConfig({ key: "nl", flag: "nl", label: "荷兰语" }),
  createLangConfig({ key: "no", flag: "no", label: "挪威语" }),
];
