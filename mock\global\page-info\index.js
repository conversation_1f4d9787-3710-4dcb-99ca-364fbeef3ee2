module.exports = async (req, res) => {
  const whiteList = ["/list/order-list", "/list/popup-template", "/iframe/iframe-page"];
  const pathname = req.query.pathname;
  const enable = !!whiteList.includes(pathname);

  res.status(200).json({
    success: true,
    data: {
      site_switcher: {
        enable,
        options_url: `http://*************:8081/rest/v1/sites/options?scope=${pathname}`,
        header_key: `Current_site${Math.random().toString().substring(2, 4)}`,
        default_value: null, // `[["jeulia", "jeulia-us"]]`
        props: { placeholder: "请选择站点", multiple: false }, // showCheckedStrategy: "SHOW_CHILD"
      },
    },
    command: [],
  });
};
