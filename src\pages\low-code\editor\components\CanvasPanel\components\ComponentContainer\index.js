import styles from "./index.module.scss";
import classNames from "classnames";
import { useMemo } from "react";

import Enums from "enums";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";

import { Empty, Row, Col, Form } from "antd";
import ComponentItem from "../ComponentItem";

function ComponentContainer({ component, isSelected, parentKey, index, onSelect, onDelete, selectedComponentId }) {
  const childrenName = component.component_config?.childrenName ?? "children";
  const children = useMemo(() => component[childrenName] ?? [], [component, childrenName]);
  const sortedItems = useMemo(() => children.map((component) => component.id), [children]);
  const isGhost = component?.type === "ghost";

  function renderComponent({ component, children }) {
    const componentData = { ...component, ...component?.props };

    const baseComponent = {
      [Enums.Components.Row]: () => (
        <Row gutter={componentData?.gutter} justify={componentData?.justify} align={componentData?.align}>
          {children}
        </Row>
      ),
      [Enums.Components.Col]: () => (
        <Col span={componentData?.span} offset={componentData?.offset}>
          {children}
        </Col>
      ),
    };

    const componentType = component?.component || "default";
    return baseComponent[componentType]?.() || baseComponent.default();
  }

  return (
    <div className={styles.container}>
      {component?.title}
      {children?.length > 0 ? (
        <SortableContext items={sortedItems} strategy={verticalListSortingStrategy}>
          {isGhost ? (
            <div className={styles.ghostLine}></div>
          ) : (
            renderComponent({
              component,
              children: (
                <div className={classNames(!isGhost && styles.containerWrapper, isSelected && styles.selected)}>
                  <div className={styles.containerContent} data-droppable-group={true} data-key={component.id}>
                    {children.map((item, index) => (
                      <ComponentItem
                        key={item.id}
                        parentKey={component.key}
                        index={index}
                        component={item}
                        isSelected={item.id === selectedComponentId}
                        selectedComponentId={selectedComponentId}
                        onSelect={onSelect}
                        onDelete={onDelete}
                      />
                    ))}
                  </div>
                </div>
              ),
            })
          )}
        </SortableContext>
      ) : (
        <div className={styles.empty}>
          <Empty description="拖拽组件到此处" />
        </div>
      )}
    </div>
  );
}

export default ComponentContainer;
