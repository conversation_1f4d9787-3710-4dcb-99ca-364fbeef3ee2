import { Checkbox, Input, Divider } from "antd";
import { forwardRef, useRef, useState } from "react";
import PropTypes from "prop-types";
import styles from "./index.module.scss";
import debounce from "lodash.debounce";
import Utils from "utils";
import { useTranslation } from "react-i18next";

function FilterCheckbox(props, ref) {
  const { searchable, checkedValue, options, onChange, onCheckAllChange, ...otherProps } = props;
  const [innerOptions, setInnerOptions] = useState(options);
  const [checkedList, setCheckedList] = useState(checkedValue || []);
  const indeterminate = checkedList.length > 0 && checkedList.length < options?.length;
  const checkAll = options?.length > 0 && options?.length === checkedList.length;
  const paramsRef = useRef({ isSearch: false });
  const { t } = useTranslation();

  paramsRef.current = { ...paramsRef.current };

  const handleInput = debounce((e) => {
    const keywords = e.target.value;
    if (keywords) {
      paramsRef.current.isSearch = true;
      setInnerOptions(options?.filter((item) => Utils.hasKeywords(item.label, keywords)));
    } else {
      paramsRef.current.isSearch = false;
      setInnerOptions(options);
    }
  }, 300);

  const handleCheckAllChange = (e) => {
    const values = options?.map((item) => item.value);
    const checkedValue = e.target.checked ? values : [];
    setCheckedList(checkedValue);
    onChange?.(checkedValue);
    onCheckAllChange?.(checkedValue);
  };

  return (
    <div className={styles.filterCheckbox}>
      <div style={{ marginBottom: 8 }}>
        <Input onInput={handleInput} />
      </div>
      <div>
        <Checkbox indeterminate={indeterminate} onChange={handleCheckAllChange} checked={checkAll}>
          {t("SelectAll")}
        </Checkbox>
        <Divider style={{ margin: "5px 0" }} dashed />
        <Checkbox.Group
          {...otherProps}
          options={innerOptions}
          onChange={(value) => {
            let values = value;
            if (paramsRef.current.isSearch) {
              values = (checkedValue || []).concat(value);
            }
            onChange?.(values);
            setCheckedList(values);
          }}
          onClick={async (e) => {
            if (
              paramsRef.current.isSearch &&
              Object.prototype.toString.call(e.target) === "[object HTMLInputElement]"
            ) {
              if (!e.target.checked) {
                const value = e.target.value;
                await Utils.sleep();
                const newValue = checkedValue?.filter((a) => a !== value);
                if (newValue) {
                  onChange?.(newValue);
                }
              }
            }
          }}
        />
      </div>
    </div>
  );
}

FilterCheckbox = forwardRef(FilterCheckbox);
FilterCheckbox.propTypes = {
  value: PropTypes.array,
  checkedValue: PropTypes.array,
  options: PropTypes.array,
  searchable: PropTypes.bool,
};

export default FilterCheckbox;
