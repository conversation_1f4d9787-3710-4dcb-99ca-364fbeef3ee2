import Fetchers from "@/fetchers";
import { Button, Input } from "antd";
import { useRef, useState } from "react";
import PropTypes from "prop-types";
import { Form, FormControl, ControlWrapper } from "@/components/react-form-x";
import I18nSelect from "@/pages/pod/components/i18n-select";

function UpdateLibraryItemForm(props) {
  const { data, onSuccess } = props;
  const [submitLoading, setSubmitLoading] = useState(false);
  const formRef = useRef();

  return (
    <Form
      ref={formRef}
      onSubmit={async (event, values) => {
        setSubmitLoading(true);
        Fetchers.updatePodLibraryItem({ id: data?.id, data: values })
          .then(() => {
            onSuccess?.();
          })
          .finally(() => {
            setSubmitLoading(false);
          });
      }}
    >
      <div style={{ display: "flex", flexDirection: "column", gap: 20 }}>
        <div>
          <FormControl
            name="option_name_i18n_key"
            rule={{ required: true }}
            render={(props) => (
              <ControlWrapper
                {...props}
                defaultValue={data?.option_name_i18n_key}
                render={({ name, value, onChange }) => (
                  <div>
                    <div>选项名称：</div>
                    <div>
                      <I18nSelect
                        value={value}
                        onChange={(value, option) => {
                          onChange(null, { [name]: option.value });
                        }}
                        style={{ width: `100%` }}
                      ></I18nSelect>
                    </div>
                  </div>
                )}
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
        <div style={{ display: "flex", justifyContent: "flex-end", gap: 8 }}>
          <Button type="primary" htmlType="submit" loading={submitLoading}>
            确定
          </Button>
        </div>
      </div>
    </Form>
  );
}

UpdateLibraryItemForm.propTypes = {
  data: PropTypes.object,
  onSuccess: PropTypes.func,
};

export default UpdateLibraryItemForm;
