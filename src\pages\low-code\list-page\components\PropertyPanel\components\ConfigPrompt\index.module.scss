.editableConfigEditor {
  display: inline-flex;
  align-items: center;

  .filterButton {
    color: #1890ff;
  }
}

.exampleItem {
  margin-bottom: 16px;

  .exampleHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .codeBlock {
    // background-color: #f5f5f5;
    border-radius: 4px;
    padding: 12px;

    pre {
      margin: 0;
      font-size: 12px;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
}
