import { Control<PERSON><PERSON><PERSON>, Form, FormControl } from "@/components/react-form-x";
import { <PERSON>ton, Checkbox, Select } from "antd";
import podStore from "@/pages/pod/stores";
import { toJS } from "mobx";
import { fabric } from "fabric";
import {} from "@/pages/pod/common/init-fabric-types";
import { FabricEvent, LayerType } from "@/pages/pod/common";
import { observer } from "mobx-react-lite";
import axios from "axios";
import Helper from "@/helpers";

const FilterType = {
  SingleColor: "single_color",
  Grayscale: "grayscale",
  FullColor: "full_color",
};

const FilterTypeValue = {
  BlackWhite: FilterType.SingleColor,
  Grayscale: FilterType.Grayscale,
};

function ImageBehavior(props) {
  const canvas = toJS(podStore.canvas);
  const filterValue = getCurrentFilter();

  function getCurrentFilter() {
    const objects = canvas.getActiveObjects();
    if (objects.every((a) => a.extraData.layerType === LayerType.ImagePlaceholder)) {
      const filters = objects.flatMap((obj) => obj.getImageObject().filters) || [];
      const set = new Set(filters);
      if (set.size === 0) {
        return FilterType.FullColor;
      } else if (set.size === 1) {
        const type = filters[0].type;
        return FilterTypeValue[type];
      }
    }
  }

  return (
    <Form>
      <div
        style={{
          display: "grid",
          gridTemplateColumns: `repeat(2, minmax(100px, 1fr)`,
          gridAutoRows: `minmax(32px, auto)`,
          gap: 10,
          alignItems: "center",
        }}
      >
        <div>
          <FormControl
            name="imageType"
            render={(props) => (
              <ControlWrapper
                {...props}
                defaultValue={filterValue}
                render={({ name, value, onChange }) => (
                  <div style={{ display: "flex", alignItems: "center", gap: 10 }}>
                    <div>Image Type:</div>
                    <div style={{ flex: 1 }}>
                      <Select
                        options={[
                          { label: "Single Color", value: FilterType.SingleColor },
                          { label: "Grayscale", value: FilterType.Grayscale },
                          { label: "Full Color", value: FilterType.FullColor },
                        ]}
                        value={value}
                        onChange={(value, option) => {
                          canvas.getActiveObjects().forEach((object) => {
                            const image = object.getImageObject();
                            if (value === FilterType.SingleColor) {
                              image.filters = [new fabric.Image.filters.BlackWhite({ threshold: 200 })];
                            } else if (value === FilterType.Grayscale) {
                              image.filters = [new fabric.Image.filters.Grayscale()];
                            } else {
                              image.filters = [];
                            }
                            image.applyFilters();
                          });
                          canvas.fire(FabricEvent.ObjectModified);
                          onChange(null, { [name]: value });
                        }}
                        style={{ width: `100%` }}
                      ></Select>
                    </div>
                  </div>
                )}
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
        <div>
          <div style={{ textAlign: "center" }}>
            <Button>Upload Mask</Button>
          </div>
        </div>
        <div>
          <Checkbox
            checked={podStore.activeObject?.extraData.objectFit === "cover"}
            onChange={(event) => {
              const activeObject = canvas.getActiveObject();
              activeObject.fitContent({ objectFit: event.target.checked ? "cover" : "contain" });
              podStore.setActiveObject({ ...activeObject });
              canvas.fire(FabricEvent.ObjectModified);
            }}
          >
            Fill Area
          </Checkbox>
        </div>
        <div>
          <Checkbox
            checked={podStore.activeObject?.src === podStore.activeObject?.extraData.backgroundRemovedSrc}
            onChange={async (event) => {
              const activeObject = canvas.getActiveObject();
              let nextSrc;
              Helper.pageLoading(true);
              if (event.target.checked) {
                if (!activeObject.extraData.backgroundRemovedSrc) {
                  const image = await Helper.createImage({
                    src: activeObject.src,
                    attrs: { crossOrigin: "anonymous" },
                  });
                  const tempCanvas = document.createElement("canvas");
                  tempCanvas.width = image.width;
                  tempCanvas.height = image.height;
                  const context = tempCanvas.getContext("2d");
                  context.drawImage(image, 0, 0);
                  const blob = await new Promise((resolve) => {
                    tempCanvas.toBlob(resolve);
                  });
                  const file = new File([blob], "image.png", { type: "image/png" });
                  const formData = new FormData();
                  formData.append("image", file);
                  formData.append("code", "background_remover");
                  const src = await axios
                    .post("https://sandbox2.jeulia.com/rest/v1/ai/cutout-pro", formData, {
                      headers: { "Content-Type": "multipart/form-data" },
                    })
                    .then((res) => res.data.data.host + res.data.data.path);
                  activeObject.extraData.originalSrc = activeObject.src;
                  activeObject.extraData.backgroundRemovedSrc = src;
                }
                nextSrc = activeObject.extraData.backgroundRemovedSrc;
              } else {
                nextSrc = activeObject.extraData.originalSrc;
              }
              await new Promise((resolve) => {
                activeObject.setSrc(nextSrc, resolve);
              });
              canvas.fire(FabricEvent.ObjectModified);
              Helper.pageLoading(false);
            }}
          >
            Remove background
          </Checkbox>
        </div>
      </div>
    </Form>
  );
}

ImageBehavior = observer(ImageBehavior);

export default ImageBehavior;
