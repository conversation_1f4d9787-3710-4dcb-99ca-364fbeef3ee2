import Utils from "@/utils";

const PodHelper = Object.freeze({
  resizeObject({ object, data, scale }) {
    if (!data) return;
    const { top, left, width, height } = PodHelper.getObjectRenderRect({
      top: data.object_props.top,
      left: data.object_props.left,
      width: data.object_props.width,
      height: data.object_props.height,
      scale,
    });
    const scaleX = width / object.width;
    const scaleY = height / object.height;
    const { angle = 0, originX = null, originY = null } = data.object_props || {};
    object.set({ top, left, scaleX, scaleY, angle, originX, originY });
  },

  getObjectRenderRect({ top: originTop, left: originLeft, width: originWidth, height: originHeight, scale = 1 }) {
    const { width, height } = Utils.getAspectScaleSize({
      width: originWidth,
      height: originHeight,
      targetSize: originWidth * scale,
      isWidth: true,
    });
    const { width: left, height: top } = Utils.getAspectScaleSize({
      width: originLeft,
      height: originTop,
      targetSize: originLeft * scale,
      isWidth: true,
    });
    return { top, left, width, height };
  },
});

export default PodHelper;
