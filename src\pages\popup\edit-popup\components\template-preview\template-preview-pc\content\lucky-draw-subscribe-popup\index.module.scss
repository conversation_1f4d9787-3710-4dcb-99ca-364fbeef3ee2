.container {
  position: relative;

  .open {
    width: 50px;
    height: 50px;
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: 60%;
    border-radius: 3px 3px 3px 3px;
    display: block;
    position: fixed;
    z-index: 1000;
    cursor: pointer;

    img {
      left: 50%;
      max-height: 50px;
      max-width: 50px;
      position: absolute;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .messageCopy {
    background: #43a047;
    border-radius: 3px;
    box-sizing: border-box;
    color: #fff;
    font-size: 14px;
    height: 42px;
    left: 0;
    line-height: 22px;
    margin: calc(50vh - 27px) calc(50vw - 70px);
    padding: 8px;
    position: fixed;
    text-align: center;
    top: 0;
    width: 140px;
    z-index: 2147483649;
  }

  .overlay {
    display: none;
    background-color: #000000a6;
    height: 100%;
    left: 0;
    opacity: 1;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 99999;

    &.opened {
      display: block;
    }
  }

  .contentWrapper {
    display: none;

    &.opened {
      display: block;
    }

    .wheelContainer {
      min-width: 450px;
      min-height: 270px;
      position: fixed;
      left: 50%;
      top: 50%;
      color: #0f1222;
      transform: translate(-50%, -50%);
      z-index: 99999;

      &.wheelLeft {
        width: 880px;
      }

      .wheelContent {
        max-height: 100vh;
        display: flex;
        justify-content: flex-start;
        word-wrap: break-word;
        background-color: #fff;
        // box-shadow: 0 6px 40px 0 #00000054;
        border-radius: 12px;
        overflow: hidden;
        position: relative;

        .close {
          width: 22px;
          height: 22px;
          cursor: pointer;
          position: absolute;
          right: 4px;
          top: 4px;
          z-index: 999;
          margin: 20px;
        }

        .backgroundImage,
        .successBackgroundImage {
          height: 100%;
          left: 0;
          overflow: hidden;
          position: absolute;
          top: 0;
          width: 100%;
          z-index: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .content,
        .success {
          align-items: center;
          // display: flex;
          flex-grow: 1;
          flex-shrink: 0;
          margin: 0;
          max-height: 100vh;
          overflow-y: auto;
          padding: 0;
          position: relative;
          text-align: center;
          width: 450px;
          z-index: 10;

          .countDown {
            display: flex;
            gap: 11px;
            align-items: center;
            justify-content: center;
            margin: 16px 0;
            font-size: 26px;
            font-weight: 600;

            & div {
              width: 36px;
              height: 36px;
              line-height: 36px;
              border-radius: 5px;
              color: #fff;
              text-align: center;
              font-size: 20px;
              background: #323232;
              overflow: hidden;
            }
          }
        }

        .content {
          &.left {
            display: flex;
            flex-direction: row;
            width: 880px;
          }

          .wheelImageWrapper {
            width: 100%;
            height: 387px;
            display: flex;
            justify-content: center;
            overflow: hidden;
            position: relative;

            .wheelImage {
              max-width: 100%;
              height: 100%;
              animation: StaticRotate 1.2s ease-in-out infinite alternate;
            }

            .point {
              left: 50%;
              position: absolute;
              top: 50%;
              transform: translate(-50%, -50%);
            }
          }

          .contentInner {
            width: 100%;
            padding: 50px 20px 30px;

            .title {
              font-size: 24px;
              font-weight: 700;
              text-align: center;
              color: #0f0f0f;
              line-height: 24px;
              margin-bottom: 18px;
            }

            .subTitle {
              font-size: 18px;
              font-weight: 400;
              text-align: center;
              color: #0f0f0f;
              line-height: 18px;
              margin-bottom: 18px;
            }

            .form {
              width: 100%;

              .formItem {
                display: flex;

                input {
                  width: 100%;
                  height: 42px;
                  background: #ffffff;
                  border: 1px solid #cccccc;
                  padding: 0 13px;
                  border-radius: 3px;
                  font-size: 16px;
                  color: #000;

                  &::placeholder {
                    color: #999999;
                  }

                  &:focus {
                    outline: none;
                  }
                }

                select {
                  height: 42px;
                  background: #ffffff;
                  border: 1px solid;
                  border-color: #cccccc;
                  border-radius: 3px;
                  padding: 0 20px 0 0px;

                  &:focus {
                    outline: none;
                  }
                }
              }

              &[class~="has-error"] {
                .formItem {
                  &[class~="has-error"] {
                    input {
                      border-color: red;
                    }
                  }
                }

                .errorMessage {
                  visibility: visible;
                  transform: scaleY(1);
                  pointer-events: none;
                }
              }

              .errorMessage {
                visibility: hidden;
                color: red;
                font-size: 12px;
                line-height: 20px;
                min-height: 20px;
                text-align: left;
              }

              .formItemWrapper {
                display: flex;
                gap: 10px;
              }

              .privacyPolicyLabel {
                display: block;
                text-align: left;
                user-select: none;

                .privacyPolicyCheckbox {
                  display: none;
                  vertical-align: middle;

                  &.show {
                    display: inline-block;
                  }
                }

                .privacy {
                  font-size: 14px;
                  font-weight: 400;
                  text-align: left;
                  line-height: 15px;
                }
              }

              .displayNone {
                display: none;
              }
            }

            .submitButton {
              width: 100%;
              height: 42px;
              line-height: 42px;
              font-size: 16px;
              font-weight: 700;
              color: #fff;
              background: #323232;
              border-radius: 3px;
              cursor: pointer;
              margin-top: 20px;
            }
          }
        }

        .success {
          .successContentInner {
            width: 100%;
            padding: 32px 23px;

            .title {
              font-size: 30px;
              font-weight: 700;
              text-align: center;
              color: #0f0f0f;
              line-height: 32px;
            }

            .couponMessage {
              font-size: 18px;
              font-weight: 400;
              text-align: center;
              color: #0f0f0f;
              line-height: 18px;
              margin: 18px 0 16px;
            }

            .successDesc {
              font-size: 20px;
              font-weight: 400;
              text-align: center;
              color: #0f0f0f;
              line-height: 20px;
            }

            .couponCodeWrapper {
              width: 100%;
              height: 68px;
              background: #ffffff;
              border: 1px solid #cbcbcb;
              border-radius: 3px;
              padding: 10px 0 10px 10px;

              .couponCodeContent {
                height: 100%;
                display: flex;
                border-radius: 3px;

                .couponCode {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 260px;
                  font-size: 20px;
                  font-weight: 700;
                  text-align: center;
                  color: #34271c;
                  line-height: 30px;
                  background: #d0d0d0;
                  border-radius: 3px;
                }

                .copy {
                  flex: 1;
                  display: flex;
                  align-items: center;
                  justify-content: center;

                  img {
                    width: 23px;
                    height: 23px;
                    cursor: pointer;
                  }
                }
              }
            }

            .shopNow {
              width: 100%;
              height: 42px;
              line-height: 42px;
              background: #323232;
              border-radius: 3px;
              font-size: 16px;
              font-weight: 700;
              text-align: center;
              color: #ffffff;
              cursor: pointer;
              margin-top: 20px;
            }
          }
        }
      }
    }
  }
}

.preview {
  .overlay {
    &.opened {
      display: none;
    }
  }

  .contentWrapper {
    .wheelContainer {
      width: 450px;
      position: relative;
      top: 0;
      transform: translate(-50%, 0);
    }
  }
}

@keyframes StaticRotate {
  0% {
    transform: rotate(-8deg);
  }

  100% {
    transform: rotate(8deg);
  }
}
