function getPageType(pathname) {
  const rules = [
    { pattern: /\/channel-.*/, type: "home" },
    { pattern: /\/checkout\/onepage\/success/, type: "success" },
    { pattern: /\/checkout\/cart.*/, type: "cart" },
    { pattern: /\/checkout/, type: "checkout" },
    { pattern: /\/(ads-|qatc-)?product-.*/, type: "product" },
    { pattern: /\/category-.*/, type: "category" },
    { pattern: /\/$/, type: "home" },
  ];
  return rules.find((rule) => rule.pattern.test(pathname))?.type || "others";
}

function usePopupShow({ React, preview, condition, additionalCheck }) {
  const [isShow, setIsShow] = React.useState(false);

  const handleShowChange = React.useCallback(() => {
    try {
      if (preview) {
        setIsShow(true);
        return;
      }

      if (!condition) {
        setIsShow(false);
        return;
      }

      const { popup_pages } = condition || {};

      let shouldShow = false;

      if (popup_pages) {
        const pathname = window.location.pathname;
        const pageType = getPageType(pathname);
        shouldShow = popup_pages.includes(pageType);
      }

      if (additionalCheck) {
        shouldShow = shouldShow && additionalCheck();
      }

      setIsShow(shouldShow);
    } catch (error) {
      setIsShow(false);
    }
  }, [preview, condition, additionalCheck]);

  React.useEffect(() => {
    handleShowChange();
  }, [handleShowChange]);

  return [isShow, setIsShow];
}

export default usePopupShow;
