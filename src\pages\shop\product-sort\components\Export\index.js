import { useState } from "react";
import { Modal, Typography, Button } from "antd";
import { ExportOutlined } from "@ant-design/icons";

import Utils from "utils";
import Helper from "@/helpers";

const { Text } = Typography;

const ExportModal = ({ open, data, onCancel }) => {
  const onCopy = () => {
    onCancel();
    Helper.openMessage({ type: "success", content: "复制成功" });
  };

  return (
    <Modal
      open={open}
      title="复制选中的SPU"
      onCancel={onCancel}
      width={1000}
      destroyOnClose
      footer={[
        <Text
          key="copy"
          copyable={{
            text: data,
            icon: ["default", "success"].map((item) => (
              <Button key={item} type="primary">
                复制到剪贴板
              </Button>
            )),
            tooltips: false,
            onCopy,
          }}
        />,
      ]}
    >
      <div>{data}</div>
    </Modal>
  );
};

const ExportButton = ({ onExport }) => {
  return (
    <Button type="link" onClick={onExport}>
      导出选中SPU
    </Button>
  );
};

const Export = ({ data }) => {
  const [open, setOpen] = useState(false);

  const onExport = () => {
    setOpen((open) => !open);
  };

  return (
    <>
      <ExportButton onExport={onExport} />
      <ExportModal open={open} data={data} onCancel={onExport} />
    </>
  );
};

export default Export;
