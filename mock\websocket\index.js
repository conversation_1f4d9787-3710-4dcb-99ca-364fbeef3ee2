const { WebSocket } = require("ws");
const Helper = require("../common/helper");

module.exports = {
  "GET /rest/v1/websocket/send": async (req, res) => {
    const data = req.query;
    const urlObj = new URL(`wss://site-deploy.cnzlerp.com/websocket`);
    const app = "erp";
    const user = Helper.uniqueId;
    const group = "backend";
    Object.entries({ app, group, user }).forEach(([key, value]) => {
      urlObj.searchParams.set(key, value);
    });
    const url = urlObj.toString();
    const ws = new WebSocket(url);
    await new Promise((resolve, reject) => {
      ws.onopen = resolve;
      ws.onerror = reject;
      ws.onclose = reject;
    });
    ws.send(
      JSON.stringify({
        action: "send",
        from: { app, user, group },
        to: { app, group: "frontend" },
        data: data,
      })
    );
    res.status(200).json({ success: true });
  },

  "POST /rest/v1/websocket/send": async (req, res) => {
    const data = req.body;
    const urlObj = new URL(`wss://site-deploy.cnzlerp.com/websocket`);
    const app = "erp";
    const user = Helper.uniqueId;
    const group = "backend";
    Object.entries({ app, group, user }).forEach(([key, value]) => {
      urlObj.searchParams.set(key, value);
    });
    const url = urlObj.toString();
    const ws = new WebSocket(url);
    await new Promise((resolve, reject) => {
      ws.onopen = resolve;
      ws.onerror = reject;
      ws.onclose = reject;
    });
    ws.send(
      JSON.stringify({
        action: "send",
        from: { app, user, group },
        to: { app, group: "frontend" },
        data: data,
      })
    );
    res.status(200).json({ success: true });
  },
};
