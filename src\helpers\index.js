import ReactDOM, { createRoot } from "react-dom/client";
import { Spin, message, Modal } from "antd";
import Enums from "@/enums";
import Utils from "utils";
import store from "@/stores";
import Cookies from "js-cookie";
import axios from "@/fetchers/request";
import debounce from "lodash.debounce";
import EnvHelper from "helpers/env-helper";
import { router } from "@/router";
import { importRemote } from "@module-federation/utilities";

const history = window;
const fetchLogout = () => {};

const Helper = Object.freeze({
  get uniqueId() {
    const hash = () => (+Math.random().toString().substring(2)).toString(16);
    return (hash() + hash() + hash() + hash()).substring(0, 40);
  },

  getUrlKey({ location, params }) {
    const { url_key = "", "*": asterisk } = params;
    return `${url_key}${asterisk ? `/${asterisk}` : ""}${location.search}` || "";
  },

  getPageUrl() {
    const location = history.location;
    return location.pathname + location.search;
  },

  pageLoading(value) {
    const elementId = "_AppPageLoading_";
    if (!document.getElementById(elementId)) {
      document.body.insertAdjacentHTML("afterbegin", `<div id="${elementId}" class="app-page-loading"></div>`);
      const root = createRoot(document.getElementById(elementId));
      root.render(<Spin />);
    }
    if (value) {
      document.getElementById(elementId).style.display = "flex";
    } else {
      document.getElementById(elementId).style.display = "none";
    }
  },

  setPathParams(url, params) {
    return url.replace(/:(\w+)/g, (searchedText, $1) => {
      return params?.[$1] || searchedText;
    });
  },

  async logout() {
    await fetchLogout();
    this.removeCookies([Enums.LocalStorageKey.Token]);
    localStorage.removeItem(Enums.LocalStorageKey.SystemInfo);
    sessionStorage.removeItem(Enums.SessionStorageKey.PathPageConfigs);
    localStorage.removeItem(Enums.SessionStorageKey.PathSiteValues);
    window.location.reload();
    // const { search, pathname } = history.location;
    // if (pathname !== "/user/login") {
    //   history.replace({
    //     pathname: "/user/login",
    //     search: Utils.toQueryParams({
    //       redirect: pathname + search,
    //     }),
    //   });
    // }
  },

  // 清除用户缓存
  clearSystemInfoCache() {
    localStorage.removeItem(Enums.LocalStorageKey.SystemInfo);
  },

  setCookies(values, { expires = 36500, domain = this.getMainDomain() } = {}) {
    Object.keys(values || {}).forEach((key) => {
      Cookies.set(key, values[key], { expires, domain });
    });
  },

  removeCookies(keys = [], { domain = this.getMainDomain() } = {}) {
    keys?.forEach((key) => {
      Cookies.remove(key, { domain });
    });
  },

  async loadFont(fontFamily, url, descriptors = {}) {
    const fontFace = new FontFace(fontFamily, `url(${url})`, descriptors);
    await fontFace.load();
    document.fonts.add(fontFace);
  },

  getDomain(url = window.location.href) {
    let domain = "";
    const fullUrl = Utils.isFullUrl(url) ? url : `http://localhost${url}`;
    const urlArr = fullUrl.split("/");
    if (urlArr[2]) {
      domain = urlArr[2];
    }
    return domain;
  },

  getMainDomain(url) {
    try {
      let key = `mh_${Math.random()}`;
      let keyR = new RegExp(`(^|;)\\s*${key}=12345`);
      let expiredTime = new Date(0);
      let domain = this.getDomain(url);
      let domainList = domain.split(".");

      let urlItems = [];
      urlItems.unshift(domainList.pop());
      while (domainList.length) {
        urlItems.unshift(domainList.pop());
        let mainHost = urlItems.join(".");
        let cookie = `${key}=${12345};domain=.${mainHost}`;

        document.cookie = cookie;

        if (keyR.test(document.cookie)) {
          document.cookie = `${cookie};expires=${expiredTime}`;
          return mainHost;
        }
      }
    } catch (error) {}
  },

  getCurrentBrand() {
    let brand = sessionStorage.getItem("brand");
    if (!brand) {
      brand = localStorage.getItem("brand");
    }
    return brand;
  },

  setCurrentBrand(brand) {
    sessionStorage.setItem("brand", brand);
    localStorage.setItem("brand", brand);
  },

  getCurrentPathSiteValues() {
    return Utils.JSON.parse(sessionStorage.getItem(Enums.SessionStorageKey.PathSiteValues));
  },

  setCurrentPathSiteValues(pathSiteValues) {
    sessionStorage.setItem(Enums.SessionStorageKey.PathSiteValues, pathSiteValues);
  },

  getPathPageConfigs() {
    return sessionStorage.getItem(Enums.SessionStorageKey.PathPageConfigs);
  },

  setPathPageConfigs(pathPageConfigs) {
    sessionStorage.setItem(Enums.SessionStorageKey.PathPageConfigs, pathPageConfigs);
  },

  async commandRequest({ request, values }) {
    const { url, ...options } = request;
    return await axios({
      url,
      method: "POST",
      ...options,
      data: { ...options.data, ...values },
    });
  },

  async requestSuccessCallback(command) {
    await this.commandHandler({ command });
  },

  async commandHandler({ command, ...others }) {
    if (!command) return;

    const handler = {
      [Enums.CommandType.Request]: async ({ command, ...others }) => {
        const { showFullScreenLoading = true } = command;

        const fetcher = async () => {
          if (showFullScreenLoading) {
            Helper.pageLoading(true);
          }
          try {
            const result = await this.commandRequest({
              request: { ...command.request, url: Utils.setQueryParams(command.request.url, others) },
              values: {},
            }).then((res) => res?.data);
            return result;
          } finally {
            Helper.pageLoading(false);
          }
        };
        if (command?.confirm) {
          return await new Promise((resolve) => {
            Helper.modal.confirm({
              title: command.confirm,
              onOk: () => fetcher(),
              afterClose: () => resolve(),
            });
          });
        } else {
          return await fetcher();
        }
      },
      [Enums.CommandType.Redirect]: ({ command, ...others }) => {
        const url = Utils.setQueryParams(command.url, { ...others });

        if (command.openInNewTab) {
          window.open(url, "_blank");
        } else {
          router.navigate(url);
        }
      },
      [Enums.CommandType.Modal]: ({ command, ...others }) => {
        Utils.dispatchEvent(Enums.EventName.SetCommandModal, { open: true, data: command, ...others });
      },
      [Enums.CommandType.ReloadTable]: ({ command, ...others }) => {
        Utils.dispatchEvent(Enums.EventName.ReloadTable);
      },
      [Enums.CommandType.Reload]: ({ command, ...others }) => {
        window.location.reload();
      },
      [Enums.CommandType.Download]: async ({ command, ...others }) => {
        const fetcher = async () => {
          Helper.pageLoading(true);
          try {
            const result = await this.commandRequest({
              request: { method: "GET", url: Utils.setQueryParams(command.url, others) },
              values: {},
            }).then((res) => res?.data);

            if (!result?.data || typeof result.data !== "string") {
              return;
            }

            const isRelativePath = !result.data.startsWith("http") && !result.data.startsWith("https");
            let url;

            if (isRelativePath) {
              url = new URL(result.data, window.location.href).href;
            } else {
              url = new URL(result.data).href;
            }

            if (url) {
              window.open(url, "_blank");
            }
          } finally {
            Helper.pageLoading(false);
          }
        };

        if (command?.confirm) {
          await new Promise((resolve) => {
            Helper.modal.confirm({
              title: command.confirm,
              onOk: () => fetcher(),
              afterClose: () => resolve(),
            });
          });
        } else {
          await fetcher();
        }
      },
      [Enums.CommandType.DirectDownload]: async ({ command, ...others }) => {
        const downloadUrl = Utils.setQueryParams(command.url, others);
        const downloadType = command.downloadType || "fetch";

        if (downloadType === "window") {
          window.open(downloadUrl);
          return;
        }
        try {
          const response = await fetch(downloadUrl);
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = downloadUrl.split("/").pop().split("?")[0];
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        } catch (error) {
          window.open(downloadUrl);
        }
      },
      [Enums.CommandType.Message]: ({ command, ...others }) => {
        Helper.openMessage(command?.config);
      },
      [Enums.CommandType.Submit]: async ({ command, ...others }) => {
        const { id: formId, data } = command;
        const submit = () => {
          if (formId) {
            Utils.dispatchEvent(Enums.EventName.FormSubmit, { formId, ...others, ...data });
          }
        };

        if (command?.confirm) {
          await new Promise((resolve) => {
            Helper.modal.confirm({
              title: command.confirm,
              onOk: () => submit(),
              afterClose: () => {
                Utils.dispatchEvent(Enums.EventName.SetSubmitButtonLoading, { loading: false });
                resolve();
              },
            });
          });
        } else {
          submit();
        }
      },
      [Enums.CommandType.CloseModal]: ({ command, ...others }) => {
        Utils.dispatchEvent(Enums.EventName.SetCommandModal, { open: false, data: {} });
      },
      [Enums.CommandType.Drawer]: ({ command, ...others }) => {
        Utils.dispatchEvent(Enums.EventName.SetCommandDrawer, { open: true, data: command, ...others });
      },
      [Enums.CommandType.CloseDrawer]: ({ command, ...others }) => {
        Utils.dispatchEvent(Enums.EventName.SetCommandDrawer, { open: false, data: {} });
      },
      [Enums.CommandType.CodeExecution]: ({ command, ...others }) => {
        try {
          (function () {
            Helper.pageLoading(true);
            const Fun = Function;
            const myFunction = new Fun(command?.code_string);
            myFunction();
          })();
        } finally {
          Helper.pageLoading(false);
        }
      },
      [Enums.CommandType.Notification]: ({ command, ...others }) => {
        Helper.openNotification(command?.config);
      },
      [Enums.CommandType.UpdatePageData]: ({ command, ...others }) => {
        Utils.dispatchEvent(Enums.EventName.UpdatePageData, { open: false, data: {} });
      },
    };

    const { target: commandTarget, ...commandContent } = command;
    if (commandTarget === Enums.CommandTarget.WindowTop) {
      window.top.postMessage({ action: "command", data: commandContent }, window.location.origin);
      return commandContent;
    }

    await handler[command?.type]?.({ command, ...others });

    // 可能有延迟执行需求
    if (command?.command) {
      debounce(() => {
        this.requestSuccessCallback(command?.command);
      }, 100)();
    }
  },

  isInsideIframe() {
    return window.top !== window.self;
  },

  async getRemoteComponentDependencies({
    componentModule = "./popups",
    dependenciesModule = "./dependencies",
    url = EnvHelper.RuntimeEnv === "production" ? "https://pwa.bizseas.com" : "https://test-mf.bizseas.com",
    remoteEntryFileName = "app-store/_next/static/chunks/remoteEntry.js",
    scope = "appStore",
  } = {}) {
    const module = await import("axios");
    const Axios = module.default;
    const versionSuffix = "/app-store/app.json";
    const version = await Axios.get(`${url}${versionSuffix}`)
      .then((res) => res.data?.version)
      .catch((error) => {
        console.log(error);
        return "";
      });
    const remoteEntryFileNameWithVersion = `${remoteEntryFileName}?v=${version}`;

    const [dependencies, popups] = await Promise.all([
      importRemote({
        url,
        remoteEntryFileName: remoteEntryFileNameWithVersion,
        scope,
        module: dependenciesModule,
        bustRemoteEntryCache: false,
      }),
      importRemote({
        url,
        remoteEntryFileName: remoteEntryFileNameWithVersion,
        scope,
        module: componentModule,
        bustRemoteEntryCache: false,
      }),
    ]);
    return { dependencies, popups };
  },
  openMessage({ type, content, ...others }) {
    Utils.dispatchEvent(Enums.EventName.AntdMessage, { type, content, ...others });
  },
  modal: {
    confirm(params) {
      Utils.dispatchEvent(Enums.EventName.AntdModal, "confirm", params);
    },
  },
  openNotification(config) {
    Utils.dispatchEvent(Enums.EventName.AntdNotification, config);
  },

  createImage({ src, attrs = {} }) {
    return new Promise((resolve, reject) => {
      const image = document.createElement("img");
      Object.entries(attrs).forEach(([key, value]) => {
        image[key] = value;
      });
      image.onload = () => {
        resolve(image);
      };
      image.onerror = (err) => {
        reject(err);
      };
      image.src = src;
    });
  },

  navigate({ url }) {
    Utils.dispatchEvent(Enums.EventName.GlobalNavigate, { url });
  },

  getBaseHeaders() {
    const token = Cookies.get()[Enums.CookieName.Token];
    const currentPath = window.location.pathname;
    const pageInfo = store.pathPageConfigs?.[currentPath];
    const urlParams = new URLSearchParams(window.location.search);
    const language = urlParams.get("language");
    let headers = {};

    // 处理website切换
    if (pageInfo?.site_switcher?.enable) {
      const website = store.pathSiteValues?.[currentPath];
      const headerKey = pageInfo?.site_switcher?.header_key;
      if (headerKey && website) {
        headers[headerKey] = website;
      }
    }

    if (language) {
      headers[Enums.HeaderName.Language] = language;
    }

    return {
      token,
      headers,
    };
  },

  getCommonHeaders() {
    const { token, headers } = this.getBaseHeaders();

    return {
      Authorization: `Bearer ${token}`,
      ...headers,
    };
  },
});

export default Helper;
