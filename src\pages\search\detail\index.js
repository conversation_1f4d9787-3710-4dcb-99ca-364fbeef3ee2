import styles from "./index.module.scss";

import { useEffect, useState, useRef } from "react";
import { useLocation, useParams } from "react-router-dom";

import Fetchers from "@/fetchers";
import Helpers from "@/helpers";

import JSONComponents from "components/common/JSONComponent";
import Breadcrumbs from "components/common/Breadcrumbs";
import QueryFilter from "components/common/JSONComponent/components/QueryFilter";

function SearchDetail() {
  const location = useLocation();
  const params = useParams();
  const [data, setData] = useState(null);
  const [formValues, setFormValues] = useState({});
  const url_key = Helpers.getUrlKey({ location, params });
  const isInsideIframe = Helpers.isInsideIframe();
  const paramsRef = useRef({ fetchData });

  async function fetchData(values = {}, submit = {}) {
    try {
      Helpers.pageLoading(true);
      const { method = "get" } = submit?.request || {};
      const isGet = method.toLowerCase() === "get";
      const result = await Fetchers.getSearchDetailPageData({
        url_key,
        method,
        data: isGet ? undefined : values,
        params: isGet ? values : undefined,
      });
      setFormValues(values);
      setData(result.data?.data);
    } finally {
      Helpers.pageLoading(false);
    }
  }

  async function handleFinish(values, { submit }) {
    fetchData(values, submit);
  }

  const extendComponents = {
    QueryFilter: (props) => {
      return (
        <QueryFilter
          {...props}
          onFinish={handleFinish}
          data={{
            ...props.data,
            props: {
              ...props.data?.props,
              initialValues: { ...props.data?.props?.initialValues, ...formValues },
            },
          }}
        />
      );
    },
  };

  useEffect(() => {
    const { fetchData } = paramsRef.current;
    fetchData();
  }, []);

  return data ? (
    <div className={styles.container}>
      {!isInsideIframe ? (
        <div className={styles.breadcrumbs}>
          <Breadcrumbs data={data?.breadcrumbs}></Breadcrumbs>
        </div>
      ) : null}
      <JSONComponents data={data?.content} extendComponents={extendComponents} />
    </div>
  ) : null;
}

export default SearchDetail;
