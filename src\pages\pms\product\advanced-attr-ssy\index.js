import styles from "./index.module.scss";
import classNames from "classnames";
import { lazy, Suspense } from "react";
import { useLocation, useNavigate } from "react-router-dom";

import useSWR from "swr";
import Fetchers from "@/fetchers";
import Helper from "@/helpers";
import Utils from "@/utils";

import { But<PERSON>, Card, Spin } from "antd";

const JSONComponents = lazy(() => import("components/common/JSONComponent"));
const Breadcrumbs = lazy(() => import("components/common/Breadcrumbs"));
const SubmitButton = lazy(() => import("components/common/Button"));
const Form = lazy(() => import("./components/JSONForm"));

function AdvancedAttrSsy() {
  const location = useLocation();
  const navigate = useNavigate();
  const params = Utils.getQueryParams(decodeURIComponent(location.search));
  const isInsideIframe = Helper.isInsideIframe();

  const extendComponents = {
    Form: (props) => <Form {...props} params={params} />,
  };

  const { data } = useSWR(params, async () => {
    try {
      Helper.pageLoading(true);
      return await Fetchers.getAdvancedAttrSsyDetail({ params }).then((res) => res?.data?.data);
    } finally {
      Helper.pageLoading(false);
    }
  });

  function handleBackClick() {
    navigate(-1);
  }

  return (
    <div className={styles.container}>
      <div className={classNames("page-header", { [styles.pageHeader]: isInsideIframe })}>
        {!isInsideIframe && (
          <Suspense fallback={<Spin />}>
            <Breadcrumbs data={data?.breadcrumbs}></Breadcrumbs>
          </Suspense>
        )}

        <div className="page-header-actions">
          {!isInsideIframe && (
            <Button type="default" onClick={handleBackClick}>
              返回
            </Button>
          )}
          {data?.actions?.map((item, index) => {
            return (
              <Suspense fallback={<Spin />} key={index}>
                <SubmitButton
                  type="primary"
                  {...item.props}
                  command={item.command}
                  onClick={async () => {
                    await Helper.commandHandler({ command: item.command });
                  }}
                >
                  {item.title}
                </SubmitButton>
              </Suspense>
            );
          })}
        </div>
      </div>
      <div className={styles.content}>
        <Card size="small">
          {data?.content && (
            <Suspense fallback={<Spin />}>
              <JSONComponents data={data?.content} extendComponents={extendComponents} />
            </Suspense>
          )}
        </Card>
      </div>
    </div>
  );
}

export default AdvancedAttrSsy;
