import styles from "../index.module.scss";
import { useRef, useCallback, useEffect } from "react";

import { Input } from "antd";
import FilterSelect from "@/pages/list/components/FilterSelect";

import Utils from "@/utils";

const { Search } = Input;

function useFilters({ searchParams, setSearchParams, paramsRef }) {
  const filterSearchRef = useRef(null);
  const filterSelectRef = useRef(null);

  function getFilterKey(dataIndex) {
    return `filter[${dataIndex}]`;
  }

  const createSearchFilter = useCallback(
    ({ column }) => {
      const { dataIndex, filter } = column;
      const filterKey = getFilterKey(dataIndex);
      const { getList, sorter, searchParams } = paramsRef.current;

      column.filterDropdown = ({ close }) => {
        return (
          <div className={styles.filterControl}>
            <Search
              allowClear
              {...filter?.props}
              ref={filterSearchRef}
              defaultValue={searchParams[filterKey] || ""}
              onSearch={(keywords) => {
                close();
                const newSearchParams = { ...searchParams };
                if (keywords) {
                  newSearchParams[filterKey] = keywords;
                } else {
                  delete newSearchParams[filterKey];
                }
                setSearchParams(newSearchParams);
                getList({ ...newSearchParams, ...sorter });
              }}
            ></Search>
          </div>
        );
      };
      column.onFilterDropdownOpenChange = async (visible) => {
        if (visible) {
          await Utils.sleep(100);
          filterSearchRef.current?.focus();
        }
      };
    },
    [paramsRef, setSearchParams]
  );

  const createSelectFilter = useCallback(
    ({ column }) => {
      column.filterDropdown = ({ close }) => {
        const { dataIndex, filter } = column;
        const { searchParams, sorter, getList } = paramsRef.current;
        const filterKey = getFilterKey(dataIndex);
        const filterKeyType = typeof filter?.props?.options?.[0]?.value;
        const selectedValue = filterKeyType === "number" ? searchParams[filterKey] * 1 : searchParams[filterKey];
        return (
          <div className={styles.filterControl}>
            <FilterSelect
              {...filter?.props}
              ref={filterSelectRef}
              value={selectedValue}
              searchable={filter?.searchable}
              searchApi={filter?.searchApi}
              onClick={(e, item) => {
                if (selectedValue !== item.value) {
                  close();
                  const filterKey = getFilterKey(dataIndex);
                  const newSearchParams = {
                    ...searchParams,
                    [filterKey]: item.value,
                  };
                  setSearchParams(newSearchParams);
                  getList({ ...newSearchParams, ...sorter });
                }
              }}
            />
          </div>
        );
      };
      column.onFilterDropdownOpenChange = async (visible) => {
        if (visible) {
          await Utils.sleep(100);
          filterSelectRef.current?.scrollToSelected();
        }
      };
    },
    [setSearchParams, paramsRef]
  );

  useEffect(() => {
    paramsRef.current = {
      ...paramsRef.current,
      searchParams,
    };
  }, [searchParams, paramsRef]);

  return { createSearchFilter, createSelectFilter };
}

export default useFilters;
