import { Space } from "antd";
import PropTypes from "prop-types";

const Compact = Space.Compact;

function SpaceWrapper({ children = [], ...restProps }) {
  if (!children || children.length === 0) {
    return null;
  }

  return (
    <Space {...restProps}>
      <Compact size="small" block>
        {children}
      </Compact>
    </Space>
  );
}

SpaceWrapper.propTypes = {
  children: PropTypes.array,
};

export default SpaceWrapper;
