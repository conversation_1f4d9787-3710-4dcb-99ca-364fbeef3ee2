import styles from "@/pages/pod/components/fabric-props-editor/index.module.scss";
import {
  DefaultPlaceholderImage,
  getSelectedLayout,
  layerItems,
} from "@/pages/pod/components/fabric-props-editor/common";
import { LayerType, createObjectFromJSON as commonCreateObjectFromJSON } from "@/pages/pod/common";
import { FabricObjectType } from "@/pages/pod/common/init-fabric-types";
import SvgIcon from "@/components/common/SvgIcon";
import classNames from "classnames";
import PropTypes from "prop-types";

function LayerItems(props) {
  const { canvas } = props;

  function createObjectFromJSON({ data, layerType }) {
    return commonCreateObjectFromJSON({ canvas, data, layerType });
  }

  return (
    <div className={styles.layerItems}>
      {layerItems.map((item, index) => {
        return (
          <div
            key={item.id}
            onClick={() => {
              let object;
              if (item.id === LayerType.DynamicImage) {
                object = createObjectFromJSON({
                  data: {
                    type: FabricObjectType.CustomImageBox,
                    src: DefaultPlaceholderImage[LayerType.DynamicImage],
                  },
                  layerType: LayerType.DynamicImage,
                });
              } else if (item.id === LayerType.ImagePlaceholder) {
                object = createObjectFromJSON({
                  data: {
                    type: FabricObjectType.CustomImageBox,
                    src: DefaultPlaceholderImage[LayerType.ImagePlaceholder],
                  },
                  layerType: LayerType.ImagePlaceholder,
                });
              } else if (item.id === LayerType.TextBox) {
                object = createObjectFromJSON({
                  data: { type: FabricObjectType.CustomTextBox },
                  layerType: LayerType.TextBox,
                });
              }
              if (object) {
                const selectedLayout = getSelectedLayout({ canvas }) || canvas.getActiveObjects()[0]?.extraData.layout;
                if (selectedLayout) {
                  object.extraData.layout = selectedLayout;
                }
                const needCenter = !canvas.getActiveObject();
                canvas.add(object);
                canvas.setActiveObject(object);
                if (needCenter) {
                  canvas.viewportCenterObject(object);
                }
              }
            }}
          >
            <SvgIcon src={item.icon} className={classNames(styles.icon, item.className)}></SvgIcon>
            <div className={styles.label}>{item.label}</div>
          </div>
        );
      })}
    </div>
  );
}

LayerItems.propTypes = {
  canvas: PropTypes.object,
  layerId: PropTypes.number,
};

export default LayerItems;
