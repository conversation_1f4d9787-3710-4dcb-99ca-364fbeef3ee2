import { Cascader } from "antd";
import { useCallback, useEffect, useState, useRef } from "react";
import { useLocation } from "react-router-dom";
import useSWR from "swr";
import { observer } from "mobx-react-lite";
import PropTypes from "prop-types";

import store from "@/stores";
import Helper from "helpers";
import request from "@/fetchers/request";
import Utils from "utils";

function SiteSwitcher({ siteSwitcher }) {
  const {
    enable = false,
    options_url: optionsUrl,
    props: siteSwitcherProps,
    default_value: defaultValue,
  } = siteSwitcher ?? {};
  const location = useLocation();
  const [value, setValue] = useState([]);

  const { data: siteOptions } = useSWR([location.pathname, optionsUrl], async () => {
    const result = await request({
      url: optionsUrl,
      method: "GET",
    });

    return result?.data?.data;
  });

  const updateSiteValue = useCallback(
    (value) => {
      const website = Utils.JSON.stringify(value);
      const currentPathSiteValues = Helper.getCurrentPathSiteValues();
      const newPathSiteValues = Utils.JSON.stringify({ ...currentPathSiteValues, [location.pathname]: website });

      Helper.setCurrentPathSiteValues(newPathSiteValues);
      store.setPathSiteValues(newPathSiteValues);
    },
    [location.pathname]
  );

  const handleChange = useCallback(
    (value) => {
      setValue(value);
      updateSiteValue(value);
      window.location.reload();
    },
    [updateSiteValue]
  );

  useEffect(() => {
    const currentPathSiteValues = Helper.getCurrentPathSiteValues();
    const savedValue = Utils.JSON.parse(currentPathSiteValues[location.pathname], []);

    // 如果有保存的值，使用保存的值，否则使用默认值
    setValue(savedValue ?? defaultValue);
  }, [location.pathname, defaultValue]);

  if (!enable) return null;

  return (
    siteOptions && (
      <Cascader
        allowClear={false}
        style={{ width: 200 }}
        maxTagCount="responsive"
        placeholder="请选择"
        {...siteSwitcherProps}
        value={value}
        options={siteOptions}
        onChange={handleChange}
      />
    )
  );
}

SiteSwitcher = observer(SiteSwitcher);

SiteSwitcher.propTypes = {
  siteSwitcher: PropTypes.object,
};

export default SiteSwitcher;
