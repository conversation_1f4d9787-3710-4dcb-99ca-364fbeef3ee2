import styles from "./index.module.scss";
import { Row, Col, Modal } from "antd";
import Enums from "enums";
import axios from "@/fetchers/request";
import HTMLBlock from "components/common/HtmlBlock";
import EditablePopover from "components/common/EditablePopover";
import { useRef, useState } from "react";

function PanelEditor(props) {
  const { data } = props;
  const [loading, setLoading] = useState(false);
  const popoverRef = useRef();

  async function commandRequest({ request, values }) {
    const { url, ...options } = request;
    return await axios({
      url,
      method: "POST",
      ...options,
      data: { ...options.data, ...values },
    });
  }

  function renderEditable(item, index) {
    if (!item?.editable) return null;
    const { editable } = item;

    async function onFinish(values, options) {
      try {
        popoverRef.current.hidden();
        setLoading(true);
        await commandRequest({
          request: editable?.request,
          values: { field: item?.key, value: values[item?.key] },
        });
        setLoading(false);
        if (options?.length > 0) {
          item.content = options?.find((a) => a.value === values[item?.key])?.label;
        } else {
          item.content = values[item?.key];
        }
      } catch (e) {
        setLoading(false);
      }
    }

    return (
      <EditablePopover
        ref={popoverRef}
        onFinish={onFinish}
        defaultValue={item?.content}
        field={item?.key}
        editable={editable}
      />
    );
  }

  function renderComponent(item, index) {
    const { component, props } = item;

    if (component === Enums.Components.Row) {
      return (
        <Row key={index} {...props}>
          {item?.children?.map((childrenItem, j) => {
            return renderComponent(childrenItem, j);
          })}
        </Row>
      );
    } else if (component === Enums.Components.Col) {
      return (
        <Col key={index} {...props}>
          {item?.children?.map((childrenItem, j) => {
            return renderComponent(childrenItem, j);
          })}
        </Col>
      );
    } else {
      return (
        <div className={styles.item} key={index}>
          <span className={styles.label}>{item?.label}：</span>
          <div className={styles.content}>
            <HTMLBlock tag="span" html={item?.content} />
            {renderEditable(item, index)}
          </div>
        </div>
      );
    }
  }

  if (!data) return null;

  return <div className={styles.container}>{data?.children?.map((item, index) => renderComponent(item, index))}</div>;
}

export default PanelEditor;
