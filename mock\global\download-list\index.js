module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    message: "",
    data: {
      total: 3,
      files: [
        {
          id: "1",
          name: "report-2023-10-01.csv",
          createdAt: "2023-10-01T12:00:00Z",
          downloadUrl: "https://example.com/files/report-2023-10-01.csv",
          fileType: "csv",
        },
        {
          id: "2",
          name: "summary-2023-10-01summary-2023-10-01summary-2023-10-01.xlsx",
          createdAt: "2023-10-01T13:00:00Z",
          downloadUrl: "https://example.com/files/summary-2023-10-01.xlsx",
          fileType: "xlsx",
        },
        {
          id: "3",
          name: "invoice-2023-10-01.pdf",
          createdAt: "2023-10-01T14:00:00Z",
          downloadUrl: "https://example.com/files/invoice-2023-10-01.pdf",
          fileType: "pdf",
        },
        {
          id: "4",
          name: "invoice-2023-10-01.pdf",
          createdAt: "2023-10-01T14:00:00Z",
          downloadUrl: "https://assets.cnzlerp.com/test/gpsr/og30434-c4.pdf?v=973993fed9380e56e5723bcc2551c982",
          fileType: "zip",
        },
      ],
    },
  });
};
