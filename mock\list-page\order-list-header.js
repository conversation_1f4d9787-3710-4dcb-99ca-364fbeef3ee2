const Api = require("../../src/fetchers/api");

module.exports = async (req, res) => {
  res.status(200).json({
    status: "00",
    success: true,
    data: {
      content: {
        component: "JSONComponents",
        type: "json",
        props: {},
        children: [
          {
            component: "QueryFilter",
            type: "json",
            filterKey: "filter", // 过滤器 key（QueryFilter组件专用）
            showButtons: false,
            props: {
              id: "layout_form",
              labelCol: {
                span: 3,
              },
              wrapperCol: {
                span: 21,
              },
            },
            formItems: [
              {
                component: "Row",
                props: { gutter: 16 },
                children: [
                  {
                    component: "Col",
                    props: {
                      xs: {
                        span: 6,
                      },
                      lg: {
                        span: 6,
                      },
                    },
                    children: [
                      // {
                      //   key: "order_number",
                      //   component: "Input",
                      //   props: {
                      //     placeholder: "请扫描商品上的条形码",
                      //     isPressEnterSubmit: true,
                      //     isAutoFocus: true,
                      //     isSelected: true,
                      //   },
                      //   rules: [{ required: true, message: "" }],
                      // },
                      {
                        key: "search",
                        component: "Search",
                        props: {
                          placeholder: "请扫描商品上的条形码",
                          enterButton: "Search",
                          isPressEnterSubmit: true,
                          isAutoFocus: true,
                          isSelected: true,
                        },
                        rules: [{ required: false, message: "" }],
                      },
                    ],
                  },
                ],
              },
            ],
            submit: {
              request: {
                url: Api.customer,
                method: "POST",
                data: {
                  default: "test",
                },
              },
              fieldsToClearOnSubmit: ["field1"],
            },
          },
        ],
      },
    },
  });
};
