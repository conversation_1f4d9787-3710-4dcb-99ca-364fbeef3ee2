import { useEffect, useState } from "react";

const localeCache = new Map();
const DEFAULT_LANGUAGE = "zh-CN";

const localeLoaderHandlers = {
  "zh-CN": () => import("antd/locale/zh_CN"),
  "en-US": () => import("antd/locale/en_US"),
  "fr-FR": () => import("antd/locale/fr_FR"),
  "de-DE": () => import("antd/locale/de_DE"),
  "es-ES": () => import("antd/locale/es_ES"),
  "ja-JP": () => import("antd/locale/ja_JP"),
  "it-IT": () => import("antd/locale/it_IT"),
};

export async function getAntdLocale(language = DEFAULT_LANGUAGE) {
  if (localeCache.has(language)) {
    return localeCache.get(language);
  }

  try {
    const loader = localeLoaderHandlers[language];
    if (!loader) {
      console.warn(`[Antd i18n] No locale loader for language ${language}`);
      return getAntdLocale(DEFAULT_LANGUAGE);
    }

    const { default: locale } = await loader();
    localeCache.set(language, locale);
    return locale;
  } catch (error) {
    console.warn("Failed to load antd locale:", error);
    return getAntdLocale(DEFAULT_LANGUAGE);
  }
}

export function useAntdLocale(language = DEFAULT_LANGUAGE) {
  const [locale, setLocale] = useState(null);

  useEffect(() => {
    async function loadLocale() {
      const loadedLocale = await getAntdLocale(language);
      setLocale(loadedLocale);
    }

    loadLocale();
  }, [language]);

  return [locale];
}
