import zhCN from "antd/locale/zh_CN";
import enUS from "antd/locale/en_US";
import frFR from "antd/locale/fr_FR";
import deDE from "antd/locale/de_DE";
import esES from "antd/locale/es_ES";
import jaJP from "antd/locale/ja_JP";
import itIT from "antd/locale/it_IT";

const antdLocalMap = {
  "zh-CN": zhCN,
  "en-US": enUS,
  "fr-FR": frFR,
  "de-DE": deDE,
  "es-ES": esES,
  "ja-JP": jaJP,
  "it-IT": itIT,
};

export function getAntdLocale(language) {
  return antdLocalMap[language] || zhCN;
}

export function normalizeLanguageCode(language) {
  const langMap = {
    en: "en-US",
    zh: "zh-CN",
    fr: "fr-FR",
    de: "de-DE",
    es: "es-ES",
    ja: "ja-JP",
    it: "it-IT",
  };

  return langMap[language] || language;
}
