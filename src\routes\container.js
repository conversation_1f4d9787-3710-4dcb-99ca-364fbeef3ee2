import { Outlet, useNavigate } from "react-router-dom";
import { useEffect, useRef } from "react";
import Utils from "@/utils";
import Enums from "@/enums";

function RoutesContainer() {
  const navigate = useNavigate();

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current, navigate };

  useEffect(() => {
    function handleGlobalNavigate({ url }) {
      const { navigate } = paramsRef.current;
      navigate(url);
    }
    Utils.addEventListener(Enums.EventName.GlobalNavigate, handleGlobalNavigate);

    return function () {
      Utils.removeEventListener(Enums.EventName.GlobalNavigate, handleGlobalNavigate);
    };
  }, []);

  return <Outlet></Outlet>;
}

export default RoutesContainer;
