import Utils from "@/utils";
import { useEffect, useRef, useState } from "react";
import Fetchers from "@/fetchers";
import { <PERSON><PERSON>, Mo<PERSON>, Popconfirm, Space } from "antd";
import styles from "./index.module.scss";
import classNames from "classnames";
import AddLibraryItemsForm from "@/pages/pod/components/add-library-items-form";
import UpdateLibraryItemForm from "@/pages/pod/components/update-library-item-form";
import TableHelper from "@/helpers/table-helper";
import LibraryItemsTable from "@/pages/pod/components/library-items-table";

function EditCategory() {
  const queryParams = Utils.getQueryParams(window.location.href);
  // const [library, setLibrary] = useState();
  const [tableData, setTableData] = useState();
  const [tableLoading, setTableLoading] = useState(false);
  const [addCategoryItemsOpen, setAddCategoryItemsOpen] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editData, setEditData] = useState();

  const library = tableData?.library;
  const category = tableData?.category;

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current, loadTableData, category };

  async function loadTableData({ query } = {}) {
    setTableLoading(true);
    await Fetchers.getPodLibraryCategory({
      ...query,
      id: queryParams.id,
    })
      .then(async (res) => {
        const result = res.data;
        if (result.success) {
          const tableData = result.data;
          TableHelper.createColumnsRender({ columns: tableData.tableProps.columns });
          tableData.tableProps.columns.push({
            width: 1,
            title: "",
            className: styles.operationColumn,
            render: (text, item, index) => {
              return (
                <div style={{ display: "flex", gap: 8 }}>
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => {
                      setEditData(item);
                      setEditModalOpen(true);
                    }}
                  >
                    编辑
                  </Button>
                  <Popconfirm
                    title="确定删除吗？"
                    placement="topRight"
                    onConfirm={() => {
                      Fetchers.deletePodLibraryItem({ id: item.id }).then(() => {
                        loadTableData();
                      });
                    }}
                  >
                    <Button type="primary" size="small">
                      删除
                    </Button>
                  </Popconfirm>
                </div>
              );
            },
          });
          setTableData(tableData);
        }
      })
      .finally(() => {
        setTableLoading(false);
      });
  }

  useEffect(() => {
    const { loadTableData } = paramsRef.current;
    loadTableData();
  }, [queryParams.type, queryParams.libraryId, queryParams.categoryId]);

  return (
    <>
      <div className={classNames("page", styles.page)}>
        <div style={{ marginBottom: 20 }}>
          <Space>
            <Button
              type="primary"
              onClick={() => {
                setAddCategoryItemsOpen(true);
              }}
            >
              添加图片
            </Button>
          </Space>
        </div>
        <div>
          <LibraryItemsTable
            tableData={tableData}
            setTableData={setTableData}
            tableLoading={tableLoading}
            setTableLoading={setTableLoading}
            loadTableData={loadTableData}
          ></LibraryItemsTable>
        </div>
      </div>
      <Modal
        open={addCategoryItemsOpen}
        onCancel={() => {
          setAddCategoryItemsOpen(false);
        }}
        footer={null}
        closable={!submitLoading}
        maskClosable={!submitLoading}
        destroyOnClose
        width={600}
      >
        <AddLibraryItemsForm
          libraryId={category?.library_id}
          libraryType={library?.type}
          categoryId={category?.id}
          onSubmit={() => {
            setSubmitLoading(true);
          }}
          onSuccess={() => {
            setAddCategoryItemsOpen(false);
            loadTableData();
          }}
          onFinally={() => {
            setSubmitLoading(false);
          }}
        ></AddLibraryItemsForm>
      </Modal>
      <Modal
        open={editModalOpen}
        onCancel={() => {
          setEditModalOpen(false);
        }}
        footer={null}
        destroyOnClose
      >
        <UpdateLibraryItemForm
          data={editData}
          onSuccess={() => {
            setEditModalOpen(false);
            loadTableData();
          }}
        ></UpdateLibraryItemForm>
      </Modal>
    </>
  );
}

export default EditCategory;
