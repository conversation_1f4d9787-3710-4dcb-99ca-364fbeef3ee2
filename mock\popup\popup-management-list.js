const Api = require("../../src/fetchers/api");

const headerActions = [
  {
    key: "65a24ce6d7f0b",
    title: "新增",
    label: "新增",
    enableByRowSelection: false,
    command: {
      type: "modal",
      title: "添加用户",
      closable: true,
      content: {
        type: "json",
        props: [],
        children: [
          {
            component: "Form",
            props: {
              layout: "horizontal",
              labelCol: {
                span: 6,
              },
              wrapperCol: {
                span: 18,
              },
              id: "form",
              initialValues: {},
            },
            layout: "vertical",
            formItems: [
              {
                component: "Row",
                props: {
                  gutter: 16,
                },
                children: [
                  {
                    component: "Col",
                    props: {
                      xs: {
                        span: 24,
                      },
                      lg: {
                        span: 12,
                      },
                    },
                    children: [
                      {
                        key: "login_type",
                        label: "Login Type",
                        component: "Select",
                        rules: [
                          {
                            required: true,
                          },
                        ],
                        props: {
                          placeholder: "必填字段1",
                          disabled: false,
                          extra: "凡是有竹乐企业微信的，统一选择企业微信登录",
                          options: [
                            {
                              value: "wxwork",
                              label: "企业微信",
                            },
                            {
                              value: "account",
                              label: "账号密码",
                            },
                            {
                              value: "sms",
                              label: "短信登录",
                            },
                          ],
                        },
                      },
                      {
                        key: "phone",
                        label: "Phone",
                        component: "Input",
                        rules: [
                          {
                            required: true,
                          },
                        ],
                        props: {
                          placeholder: "必填字段2",
                          disabled: false,
                        },
                      },
                      {
                        key: "name",
                        label: "Name",
                        component: "Input",
                        rules: [
                          {
                            required: true,
                          },
                        ],
                        props: {
                          placeholder: "必填字段3",
                          disabled: false,
                        },
                      },
                      {
                        key: "email",
                        label: "Email",
                        component: "Input",
                        rules: [
                          {
                            required: true,
                          },
                        ],
                        props: {
                          placeholder: "必填字段4",
                          disabled: false,
                        },
                      },
                    ],
                  },
                ],
              },
            ],
            submit: {
              request: {
                url: Api.customer,
                method: "post",
                data: [],
              },
            },
          },
        ],
      },
      footer: [
        {
          title: "提交",
          props: {
            type: "primary",
          },
          command: {
            type: "submit",
            id: "form",
          },
        },
      ],
    },
    props: [],
  },
  {
    title: "刷新table",
    props: {},
    command: {
      type: "message",
      config: {
        type: "success",
        content: "提示文案",
        duration: 3,
      },
      command: {
        type: "reload_table",
      },
    },
  },
];

module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      headerActions,
      toolbarActions: null,
      tableProps: {
        dataSource: [
          {
            id: "5",
            phone: "13572543260",
            username: "renk",
            email: "<EMAIL>",
            login_type: "企业微信",
            status: "激活",
          },
          {
            id: "6",
            phone: "15135210985",
            username: "yanglei",
            email: "<EMAIL>",
            login_type: "企业微信",
            status: "激活",
          },
          {
            id: "7",
            phone: "123123123123",
            username: "<EMAIL>",
            email: "<EMAIL>",
            login_type: "账号密码",
            status: "禁用",
          },
          {
            id: "8",
            phone: "15021955295",
            username: "yangsen",
            email: "<EMAIL>",
            login_type: "企业微信",
            status: "激活",
          },
        ],
        pagination: {
          current: 1,
          pageSize: 20,
          pageSizeOptions: [20, 50, 100, 200],
          showSizeChanger: true,
          count: 4,
          total: 4,
        },
        size: "small",
        rowSelection: true,
        bordered: true,
        columns: [
          {
            dataIndex: "id",
            title: "ID",
            width: 100,
            ellipsis: true,
            copyable: true,
            fixed: "",
            align: "left",
            value_type: "text",
            filter: {
              component: "Search",
            },
          },
          {
            dataIndex: "phone",
            title: "Phone",
            width: 100,
            ellipsis: true,
            copyable: true,
            fixed: "",
            align: "left",
            value_type: "text",
            filter: {
              component: "Search",
            },
          },
          {
            dataIndex: "username",
            title: "Username",
            width: 100,
            ellipsis: true,
            copyable: true,
            fixed: "",
            align: "left",
            value_type: "text",
            filter: {
              component: "Search",
            },
          },
          {
            dataIndex: "email",
            title: "Email",
            ellipsis: true,
            copyable: true,
            fixed: "",
            align: "left",
            value_type: "text",
            filter: {
              component: "Search",
            },
          },
          {
            dataIndex: "login_type",
            title: "登录方式",
            width: 100,
            ellipsis: true,
            copyable: true,
            fixed: "",
            align: "left",
            value_type: "text",
            filter: {
              component: "Checkbox",
              props: {
                options: [
                  {
                    value: "wxwork",
                    label: "企业微信",
                  },
                  {
                    value: "account",
                    label: "账号密码",
                  },
                  {
                    value: "sms",
                    label: "短信登录",
                  },
                ],
              },
            },
          },
          {
            dataIndex: "status",
            title: "状态",
            width: 100,
            ellipsis: true,
            copyable: true,
            fixed: "",
            align: "left",
            value_type: "text",
            filter: {
              component: "Checkbox",
              props: {
                options: [
                  {
                    value: 2,
                    label: "锁定",
                  },
                  {
                    value: 1,
                    label: "激活",
                  },
                  {
                    value: 0,
                    label: "禁用",
                  },
                ],
              },
            },
            editable: {
              component: "Select",
              request: {
                url: Api.customer,
                data: [],
              },
              props: {
                options: [
                  {
                    value: 2,
                    label: "锁定",
                  },
                  {
                    value: 1,
                    label: "激活",
                  },
                  {
                    value: 0,
                    label: "禁用",
                  },
                ],
              },
            },
          },
          {
            dataIndex: "operations",
            valueType: "operation",
            title: "操作",
            width: 80,
            fixed: "right",
            align: "center",
            operations: [
              {
                key: "65a24ce6d9156",
                title: "编辑",
                label: "编辑",
                enableByRowSelection: false,
                command: {
                  type: "redirect",
                  url: "/edit-popup/management",
                },
                props: [],
              },
            ],
          },
        ],
      },
    },
  });
};
