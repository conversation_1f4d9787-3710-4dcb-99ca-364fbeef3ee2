import { <PERSON><PERSON>, <PERSON>, Button } from "antd";
import PropTypes from "prop-types";

const DRAWER_WIDTH = 1200;

function StoneDrawer({ open, title, onClose, onSelect, children }) {
  return (
    <Drawer
      title={title}
      open={open}
      width={DRAWER_WIDTH}
      onClose={onClose}
      extra={
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button type="primary" onClick={onSelect}>
            选择
          </Button>
        </Space>
      }
    >
      {children}
    </Drawer>
  );
}

StoneDrawer.propTypes = {
  open: PropTypes.bool,
  title: PropTypes.string,
  onSelect: PropTypes.func,
  children: PropTypes.any,
};

export default StoneDrawer;
