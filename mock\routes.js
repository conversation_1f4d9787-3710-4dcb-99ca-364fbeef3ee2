const common = require("./common");
const listPageRoutes = require("./list-page/index");
const editPageRoutes = require("./edit");
const detailPageRoutes = require("./detail/index");
const productSpuRoutes = require("./product/spu");
const uploadRoutes = require("./upload");
const uploadFile = require("./upload-file");
const menu = require("./menu");
const orderModifyHistory = require("./order-modify-history");
const popup = require("./popup");
const serialRequest = require("./serial-request");
const order = require("./order");
const apiForm = require("./api-form");
const apiJsonComponents = require("./api-json-components");
const restock = require("./dms/restock/index");
const qualityControl = require("./dms/quality-control");
const configuration = require("./configuration");
const system = require("./system");
const shipOut = require("./dms/ship-out");
const dmsOrder = require("./dms/order/index");
const download = require("./download");
const global = require("./global/index");
const websocket = require("./websocket");
const pms = require("./pms");
const ticket = require("./ticket");
const podRoutes = require("./pod");
const search = require("./search");
const otherPenalty = require("./dms/other-penalty");
const iframe = require("./iframe");
const three = require("./three");

// const noTokenApis = ["/rest/v1/admin/user/login", "/rest/v1/upload", "/rest/v1/files/:fileId"];

const routes = {
  ...websocket,
  ...podRoutes,
  ...listPageRoutes,
  ...editPageRoutes,
  ...detailPageRoutes,
  ...productSpuRoutes,
  ...uploadRoutes,
  ...uploadFile,
  ...menu,
  ...orderModifyHistory,
  ...popup,
  ...serialRequest,
  ...order,
  ...apiForm,
  ...apiJsonComponents,
  ...restock,
  ...qualityControl,
  ...configuration,
  ...system,
  ...shipOut,
  ...dmsOrder,
  ...download,
  ...global,
  ...pms,
  ...ticket,
  ...search,
  ...otherPenalty,
  ...iframe,
  ...three,

  "GET /": async (req, res) => {
    res.status(200).json({ success: true, data: "Hello, world!" });
  },

  "POST /rest/v1/admin/user/login": (req, res) => {
    const { username, password } = req.body;
    console.log(`${username} login success!`);
    res.json({
      status: "00",
      success: true,
      data: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1aWQiOjYsImFjY2Vzc190b2tlbiI6IkRId0dnczJfbEVmdXNWLTZMX1NKaWxZaHBMOWhSbTNEIn0.2s6tVWOlMy7XI-_V96yDD21j3-KFwLjc_xG9otL_oUk",
    });
  },

  "POST /rest/v1/admin/user/login/get-token": (req, res) => {
    const { code } = req.body;
    console.log(`${code} login success!`);
    res.json({
      status: "00",
      success: true,
      data: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1aWQiOjYsImFjY2Vzc190b2tlbiI6IkRId0dnczJfbEVmdXNWLTZMX1NKaWxZaHBMOWhSbTNEIn0.2s6tVWOlMy7XI-_V96yDD21j3-KFwLjc_xG9otL_oUk",
    });
  },

  "GET /rest/v1/user": (req, res) => {
    res.json({ status: "00", data: [{ id: 1, name: "YangSen" }] });
  },

  "POST /rest/v1/customer": async (req, res) => {
    res.status(200).json({
      success: true,
      data: {},
      command: {
        type: "message",
        config: {
          type: "success",
          content: "提示文案",
          duration: 3,
        },
        command: {
          target: "windowTop",
          type: "close_modal",
        },
      },
    });
  },
};

module.exports = routes;
