import classNames from "classnames";
import styles from "@/pages/pod/components/option-set-control-editor/index.module.scss";
import { ControlWrapper, FormControl } from "@/components/react-form-x";
import { Button, Input, Popover, Radio, Select } from "antd";
import { OptionType } from "@/pages/pod/common";
import Swatch from "@/components/Swatch";
import { CaretDownOutlined, CloseOutlined } from "@ant-design/icons";
import { createIncrementId } from "@/pages/pod/components/option-set-control-editor/common";
import PropTypes from "prop-types";

const selectWidth = 160;

function Conditions(props) {
  const { control, keyPath, forceUpdate, optionSet } = props;

  return (
    <div className={classNames(styles.flexGrid, styles.conditions)}>
      {control?.conditions?.map((condition, index) => {
        const conditionKeyPath = [...keyPath, "conditions", index];
        return (
          <div key={condition.id} className={styles.item}>
            {condition.combination_operator ? (
              <div>
                <FormControl
                  keyPath={conditionKeyPath}
                  name="combination_operator"
                  render={(props) => {
                    return (
                      <ControlWrapper
                        {...props}
                        render={(props) => (
                          <Radio.Group
                            {...props}
                            options={[
                              { label: "AND", value: "and" },
                              { label: "OR", value: "or" },
                            ]}
                            optionType="button"
                            buttonStyle="solid"
                          ></Radio.Group>
                        )}
                      ></ControlWrapper>
                    );
                  }}
                ></FormControl>
              </div>
            ) : null}
            <div>
              <FormControl
                keyPath={conditionKeyPath}
                name="action"
                render={(props) => {
                  return (
                    <ControlWrapper
                      {...props}
                      render={(props) => {
                        const { name, onChange } = props;
                        return (
                          <Select
                            {...props}
                            options={[
                              { label: "Show", value: "show" },
                              { label: "Hide", value: "hide" },
                            ]}
                            style={{ width: "100%" }}
                            onChange={(value, option) => {
                              onChange(null, { [name]: value });
                            }}
                          ></Select>
                        );
                      }}
                    ></ControlWrapper>
                  );
                }}
              ></FormControl>
            </div>
            <div className={styles.connectText}>this field when</div>
            <div>
              <FormControl
                keyPath={conditionKeyPath}
                name="watch_control"
                render={(props) => {
                  return (
                    <ControlWrapper
                      {...props}
                      render={(props) => {
                        const { name, onChange } = props;
                        return (
                          <Select
                            {...props}
                            onChange={(value, option) => {
                              onChange(null, { [name]: value });
                              forceUpdate();
                            }}
                            options={optionSet?.controls?.map((item) => ({
                              label: item.label,
                              value: item.id,
                            }))}
                            style={{ width: selectWidth }}
                            showSearch
                            filterOption={(keyword, option) => option.label.toLowerCase().includes(keyword)}
                          ></Select>
                        );
                      }}
                    ></ControlWrapper>
                  );
                }}
              ></FormControl>
            </div>
            {(() => {
              const control = optionSet?.controls?.find((x) => x.id === condition?.watch_control);
              const type = control?.type;
              const options = [
                ...(control?.options || []).map((x) => ({
                  label: x.label,
                  value: x.id,
                  preview_image: x.thumb_image || x.preview_image,
                })),
                { label: "Any value", value: "any" },
              ];
              if ([OptionType.Swatch].includes(type)) {
                const selectedOptions = condition.desired_value
                  ?.map((id) => options.find((x) => x.id === id))
                  .filter((x) => x);
                return (
                  <>
                    <div className={styles.connectText}>is any of</div>
                    <Swatch readOnly options={selectedOptions} imageKey="preview_image"></Swatch>
                    <Popover
                      content={
                        <div style={{ width: 500, height: 300, overflow: "auto" }}>
                          <FormControl
                            keyPath={conditionKeyPath}
                            name="desired_value"
                            render={(props) => {
                              const { ref, name, onChange } = props;
                              return (
                                <Swatch
                                  {...props}
                                  ref={ref}
                                  options={options}
                                  imageKey="preview_image"
                                  multiple
                                  onChange={(event, value) => {
                                    let nextValue = value;
                                    if (ref.current?.value?.includes("any")) {
                                      nextValue = value.filter((x) => x !== "any");
                                    } else if (value.includes("any")) {
                                      nextValue = ["any"];
                                    }
                                    ref.current.value = nextValue;
                                    onChange?.(event, { [name]: nextValue });
                                    forceUpdate();
                                  }}
                                ></Swatch>
                              );
                            }}
                          ></FormControl>
                        </div>
                      }
                      trigger="click"
                      placement="bottom"
                      autoAdjustOverflow
                    >
                      <div className={styles.dropdownIcon}>
                        <div style={{ fontSize: 20, position: "relative", top: -1 }}>
                          <CaretDownOutlined />
                        </div>
                      </div>
                    </Popover>
                  </>
                );
              } else if ([OptionType.Dropdown].includes(type)) {
                return (
                  <>
                    <div className={styles.connectText}>is any of</div>
                    <FormControl
                      keyPath={conditionKeyPath}
                      name="desired_value"
                      render={(props) => {
                        return (
                          <ControlWrapper
                            {...props}
                            render={({ name, value, onChange }) => {
                              return (
                                <Select
                                  options={options}
                                  value={value}
                                  onChange={(value) => {
                                    onChange(null, { [name]: value });
                                  }}
                                  mode="multiple"
                                  style={{ width: selectWidth }}
                                ></Select>
                              );
                            }}
                          ></ControlWrapper>
                        );
                      }}
                    ></FormControl>
                  </>
                );
              } else if ([OptionType.TextInput].includes(type)) {
                return (
                  <>
                    <div>is</div>
                    <div>
                      <FormControl
                        keyPath={conditionKeyPath}
                        name="desired_value"
                        render={(props) => {
                          return (
                            <ControlWrapper
                              {...props}
                              render={(props) => {
                                return <Input {...props}></Input>;
                              }}
                            ></ControlWrapper>
                          );
                        }}
                      ></FormControl>
                    </div>
                  </>
                );
              }
            })()}
            <Button
              type="text"
              icon={<CloseOutlined></CloseOutlined>}
              onClick={() => {
                control.conditions = control.conditions.filter((x) => x.id !== condition.id);
                delete control.conditions[0]?.combination_operator;
                forceUpdate();
              }}
            ></Button>
          </div>
        );
      })}
      <div>
        <Button
          type="primary"
          onClick={() => {
            const condition = { id: createIncrementId({ items: control.conditions }), action: "show" };
            if (control.conditions?.length > 0) {
              condition.combination_operator = "and";
            }
            control.conditions.push(condition);
            forceUpdate();
          }}
        >
          Add Condition
        </Button>
      </div>
    </div>
  );
}

Conditions.propTypes = {
  control: PropTypes.object,
  keyPath: PropTypes.array,
  forceUpdate: PropTypes.func,
  optionSet: PropTypes.object,
};

export default Conditions;
