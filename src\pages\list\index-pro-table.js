import { useEffect, useRef, useState } from "react";
import { Link, useNavigate, useLocation, useParams } from "react-router-dom";
import {
  Button,
  Dropdown,
  Form,
  Input,
  Modal,
  Select,
  Space,
  Tag,
  message,
  Popover,
  Table,
  Image,
  Tooltip,
  Upload,
} from "antd";
import { ProConfigProvider, ProTable } from "@ant-design/pro-components";
import styles from "./index.module.scss";
import {
  SearchOutlined,
  CaretDownOutlined,
  EditOutlined,
  CheckOutlined,
  EyeOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  SyncOutlined,
  UnorderedListOutlined,
} from "@ant-design/icons";
import Utils from "@/utils";
import Enums from "@/enums";
import FilterSelect from "@/pages/list/components/FilterSelect";
import FilterTree from "@/pages/list/components/FilterTree";
import FilterCheckbox from "@/pages/list/components/FilterCheckbox";
import Helper from "@/helpers";
import { afterRender, Counter } from "functions";
import Breadcrumbs from "@/components/common/Breadcrumbs";
import JSONForm from "@/components/common/JSONForm";
import classNames from "classnames";
import Icon from "@/components/common/Icon";
import HtmlBlock from "@/components/common/HtmlBlock";
import axios from "@/fetchers/request";
import Fetchers from "@/fetchers";
import debounce from "lodash.debounce";

const { Search } = Input;
const Params = {
  selectedId: "",
  selectedOperation: null,
};

const axiosRequest = async (args) => {
  return await axios(args);
};
const fetchListPageData = async ({ url_key }) => {
  return await Fetchers.getListPageData({ url_key }).then((res) => res.data);
};
const saveTableColumnsWidth = () => {};
const getComputedStyle = (node) => {
  return window.getComputedStyle(node || document.createElement("div"));
};

function ListPage(props) {
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const [tableScrollHeight, setTableScrollHeight] = useState(0);
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(false);
  const [optModalOpen, setOptModalOpen] = useState(false);
  const [modalConfirmLoading, setModalConfirmLoading] = useState(false);
  const [columnModal, setColumnModal] = useState({ open: false, title: "" });
  const [columnModalTableLoading, setColumnModalTableLoading] = useState(false);
  const [columnModalTableData, setColumnModalTableData] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [commandModal, setCommandModal] = useState({ open: false, data: {} });
  const [commandModalConfirmLoading, setCommandModalConfirmLoading] = useState(false);
  const [buttonsLoading, setButtonsLoading] = useState({});
  const [tableFullscreen, setTableFullscreen] = useState(false);
  const [polling, setPolling] = useState(false);
  const filterSearchRef = useRef();
  const filterSelectRef = useRef();
  const filterCheckboxFormRef = useRef();
  const filterTreeFormRef = useRef();
  const filterTreeRef = useRef();
  const contentRef = useRef();
  const contentTopRef = useRef();
  const paramsRef = useRef({ filterTreeExpandedKeys: {} });
  const modalFormRef = useRef();
  const popoverContainerRef = useRef();
  const popoverRef = useRef();
  const operationRef = useRef();
  const actionRef = useRef();
  const commandFormRef = useRef();
  const filterParams = getFilterParams();
  const sortParams = getSortParams();
  paramsRef.current = {
    ...paramsRef.current,
    data,
    processFilterData,
    updateTableScrollHeight,
    sortParams,
    columnRender,
    fetchTableData,
    tableFullscreen,
  };

  function forceUpdateTable() {
    const { data } = paramsRef.current;
    data.tableProps.dataSource = Utils.cloneDeep(data.tableProps.dataSource);
    setData({ ...data });
  }

  function updateSearchParams({ searchParams }) {
    navigate({ pathname: location.pathname, search: decodeURIComponent(searchParams.toString()) });
  }

  function handleTableChange(pagination, filters, sorter) {
    const searchParams = new URLSearchParams(location.search);
    processSort({ searchParams, sorter });
    processPagination({ searchParams, pagination });
    updateSearchParams({ searchParams });
  }

  function processSort({ searchParams, sorter }) {
    const sorters = sorter?.length > 0 ? sorter : [sorter];
    [...searchParams.keys()].forEach((key) => {
      if (key.indexOf("sort[") > -1) {
        searchParams.delete(key);
      }
    });
    sorters.forEach((item) => {
      const key = getSortKey(item.field);
      const value = item.order;
      if (value) {
        searchParams.set(key, value);
      } else {
        searchParams.delete(key);
      }
    });
  }

  function processPagination({ searchParams, pagination }) {
    const { current: page, pageSize, pageSizeOptions } = pagination;
    const defaultPageSize = pageSizeOptions?.[0] || pageSize;
    if (page > 1) {
      searchParams.set("page", page);
    } else {
      searchParams.delete("page");
    }
    if (pageSize !== defaultPageSize) {
      searchParams.set("pageSize", pageSize);
    } else {
      searchParams.delete("pageSize");
    }
  }

  function getFilterKey(dataIndex) {
    return `filter[${dataIndex}]`;
  }

  function getSortKey(dataIndex) {
    return `sort[${dataIndex}]`;
  }

  function createSearchFilter({ column }) {
    const { dataIndex, filter } = column;
    column.filterDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
      return (
        <div className={styles.filterControl}>
          <Search
            {...filter?.props}
            ref={filterSearchRef}
            defaultValue={decodeURIComponent(filterParams[dataIndex] || "")}
            onSearch={(keywords) => {
              close();
              const searchParams = new URLSearchParams(location.search);
              const filterKey = getFilterKey(dataIndex);
              if (keywords) {
                searchParams.set(filterKey, keywords);
              } else {
                searchParams.delete(filterKey);
              }
              updateSearchParams({ searchParams });
            }}
          ></Search>
        </div>
      );
    };
    column.onFilterDropdownOpenChange = (visible) => {
      if (visible) {
        setTimeout(() => filterSearchRef.current?.focus(), 100);
      }
    };
  }

  function createSelectFilter({ column }) {
    column.filterDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
      const { dataIndex, filter } = column;
      const selectedValue = filterParams[dataIndex];
      return (
        <div className={styles.filterControl}>
          <FilterSelect
            {...filter?.props}
            ref={filterSelectRef}
            value={selectedValue}
            searchable={filter?.searchable}
            onClick={(e, item) => {
              if (selectedValue !== item.value) {
                close();
                const searchParams = new URLSearchParams(location.search);
                const filterKey = getFilterKey(dataIndex);
                searchParams.set(filterKey, item.value);
                updateSearchParams({ searchParams });
              }
            }}
          />
        </div>
      );
    };
    column.onFilterDropdownOpenChange = async (visible) => {
      if (visible) {
        await Utils.sleep(100);
        filterSelectRef.current?.scrollToSelected();
      }
    };
  }

  function createCheckboxFilter({ column }) {
    const { dataIndex, filter } = column;
    const getDefaultValue = () => {
      return decodeURIComponent(filterParams[dataIndex] || "")
        ?.split(",")
        .filter((item) => item);
    };
    column.filterDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
      const checkedValue = getDefaultValue();
      return (
        <div className={styles.filterControl}>
          <Form
            ref={filterCheckboxFormRef}
            initialValues={{ [dataIndex]: checkedValue }}
            onValuesChange={(changedValues) => {
              filterCheckboxFormRef.current?.setFieldsValue(changedValues);
            }}
            onFinish={(values) => {
              close();
              const selected = values[dataIndex];
              const searchParams = new URLSearchParams(location.search);
              const filterKey = getFilterKey(dataIndex);
              if (selected?.length > 0) {
                searchParams.set(filterKey, selected);
              } else {
                searchParams.delete(filterKey);
              }
              updateSearchParams({ searchParams });
            }}
          >
            <div className={styles.filterCheckbox}>
              <Form.Item name={dataIndex}>
                <FilterCheckbox {...filter?.props} checkedValue={checkedValue}></FilterCheckbox>
              </Form.Item>
            </div>
            <div className={styles.actions}>
              <Space>
                <Button
                  size="small"
                  onClick={() => {
                    filterCheckboxFormRef.current?.setFieldValue(dataIndex, []);
                  }}
                >
                  清除
                </Button>
                <Button size="small" type="primary" htmlType="submit">
                  确定
                </Button>
              </Space>
            </div>
          </Form>
        </div>
      );
    };
    column.onFilterDropdownOpenChange = (visible) => {
      if (visible) {
        filterCheckboxFormRef.current?.setFieldValue(dataIndex, getDefaultValue());
      }
    };
  }

  function createTreeFilter({ column }) {
    const { dataIndex, filter } = column;
    column.filterDropdown = ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => {
      const defaultValue = filterParams[dataIndex] ? decodeURIComponent(filterParams[dataIndex]).split(",") : [];
      return (
        <div className={styles.filterControl}>
          <Form
            ref={filterTreeFormRef}
            initialValues={{ [dataIndex]: defaultValue }}
            onValuesChange={(changedValues) => {
              filterTreeFormRef.current?.setFieldsValue(changedValues);
            }}
            onFinish={(values) => {
              close();
              const selected = values[dataIndex];
              const searchParams = new URLSearchParams(location.search);
              const filterKey = getFilterKey(dataIndex);
              if (selected?.length > 0) {
                searchParams.set(filterKey, selected);
              } else {
                searchParams.delete(filterKey);
              }
              updateSearchParams({ searchParams });
            }}
          >
            <div className={styles.filterCheckbox}>
              <Form.Item name={dataIndex}>
                <FilterTree {...filter?.props} ref={filterTreeRef} defaultExpandAll={filter?.defaultExpandAll} />
              </Form.Item>
            </div>
            <div className={styles.actions}>
              <Space>
                <Button
                  size="small"
                  onClick={() => {
                    filterTreeFormRef.current?.setFieldValue(dataIndex, []);
                  }}
                >
                  清除
                </Button>
                <Button size="small" type="primary" htmlType="submit">
                  确定
                </Button>
              </Space>
            </div>
          </Form>
        </div>
      );
    };
    column.onFilterDropdownOpenChange = (visible) => {
      if (visible) {
        filterTreeFormRef.current?.setFieldValue(dataIndex, filterParams[dataIndex]?.split(","));
        filterTreeRef.current?.setExpandedKeys(paramsRef.current.filterTreeExpandedKeys[dataIndex]);
      }
    };
  }

  function processFilterData({ column }) {
    const { filter } = column;
    const { component } = filter;
    if (component === Enums.Components.Select) {
      createSelectFilter({ column });
    } else if (component === Enums.Components.Checkbox) {
      createCheckboxFilter({ column });
    } else if (component === Enums.Components.Search) {
      createSearchFilter({ column });
    } else if (component === Enums.Components.Tree) {
      createTreeFilter({ column });
    }
    column.filterIcon = (filtered) => <SearchOutlined style={{ color: filtered ? "#1890ff" : null }} />;
  }

  function getFilterParams() {
    const queryParams = Utils.getQueryParams(decodeURIComponent(location.search));
    const filterParams = {};
    Object.keys(queryParams).forEach((key) => {
      const dataIndex = /^filter\[(\w+)\]/.exec(key)?.[1];
      if (dataIndex) {
        filterParams[dataIndex] = queryParams[key];
      }
    });
    return filterParams;
  }

  function getSortParams() {
    const queryParams = Utils.getQueryParams(decodeURIComponent(location.search));
    const sortParams = {};
    Object.keys(queryParams).forEach((key) => {
      const dataIndex = /^sort\[(\w+)\]/.exec(key)?.[1];
      if (dataIndex) {
        sortParams[dataIndex] = queryParams[key];
      }
    });
    return sortParams;
  }

  function clearFilters() {
    const searchParams = new URLSearchParams(decodeURIComponent(location.search));
    [...searchParams.keys()].forEach((key) => {
      if (key.indexOf("filter[") > -1) {
        searchParams.delete(key);
      }
    });
    searchParams.delete("page");
    updateSearchParams({ searchParams });
  }

  function getHeightWithMargin(node) {
    const style = node ? getComputedStyle(node) : {};
    return node ? node.offsetHeight + +style.marginTop.replace("px", "") + +style.marginBottom.replace("px", "") : 0;
  }

  function updateTableScrollHeight() {
    const { tableFullscreen } = paramsRef.current;
    const viewportHeight = window.innerHeight - 50; // 减去 page-header 高度
    const headerHeight = document.querySelector(".layout-header")?.offsetHeight || 50;
    const footerHeight = document.querySelector(`[class~="page-footer"]`)?.offsetHeight || 35;
    const { paddingTop, paddingBottom } = getComputedStyle(document.querySelector(".page-content"));
    const pageContentPadding = +paddingTop.replace("px", "") + +paddingBottom.replace("px", "");
    const contentHeight = viewportHeight - headerHeight - footerHeight - pageContentPadding;
    const contentTopHeight = getHeightWithMargin(contentTopRef.current);
    const tableHeaderHeight = contentRef.current?.querySelector(".ant-table-header")?.offsetHeight;
    const toolbarHeight = contentRef.current?.querySelector(".ant-pro-table-list-toolbar")?.offsetHeight;
    const paginationHeight = getHeightWithMargin(contentRef.current?.querySelector(".ant-table-pagination"));
    let scrollHeight;
    if (tableFullscreen) {
      const tableContainerPadding = 32;
      scrollHeight = window.innerHeight - tableContainerPadding - toolbarHeight - tableHeaderHeight - paginationHeight;
    } else {
      scrollHeight = contentHeight - contentTopHeight - tableHeaderHeight - toolbarHeight - paginationHeight;
    }
    setTableScrollHeight(scrollHeight);
  }

  function getFilterLabel({ column }) {
    if (column) {
      const { dataIndex, filter } = column;
      const { component, props = {} } = filter;
      const { options } = props;
      const value = decodeURIComponent(filterParams[dataIndex]);
      if (component === Enums.Components.Select) {
        return options?.find((item) => item.value === value)?.label;
      } else if (component === Enums.Components.Checkbox) {
        const values = value.split(",");
        return options
          ?.filter((item) => values.includes(item.value))
          .map((item) => item.label)
          .join(", ");
      } else if (component === Enums.Components.Tree) {
        const keys = value.split(",");
        const titles = [];
        Utils.forEachTree({
          treeData: filter?.props?.treeData,
          callback: (item) => {
            if (keys.includes(item.key)) {
              titles.push(item.title);
            }
          },
        });
        return titles.join(", ");
      }
      return value;
    }
  }

  function createOperations({ column, id }) {
    return column.operations.map((item, index) => {
      const { command } = item;
      const menu = {};
      const icon = item.icon ? <img className={styles.operationIcon} src={item.icon} alt="" /> : null;
      if (command.type === "redirect") {
        menu.label = (
          <Link to={Utils.setQueryParams(command.url, { id })} className={styles.operation}>
            {icon}
            {item.title}
          </Link>
        );
      } else if (command.type === "request") {
        const fetcher = async () => {
          const result = await commandRequest({ request: command.request, values: { id } });
          if (result?.status === Enums.ApiStatus.Success) {
            requestSuccessCallback(result);
          } else {
            message.error(result?.message);
          }
        };
        menu.label = (
          <div
            className={styles.operation}
            onClick={async () => {
              if (command.confirm) {
                Helper.modal.confirm({
                  content: command.confirm,
                  onOk: fetcher,
                });
              } else {
                await fetcher();
              }
            }}
          >
            {icon}
            {item.title}
          </div>
        );
      } else if (command.type === "modal") {
        menu.label = (
          <div
            className={styles.operation}
            onClick={() => {
              Params.selectedOperation = item;
              setOptModalOpen(true);
            }}
          >
            {icon}
            {item.title}
          </div>
        );
      } else {
        menu.label = (
          <div className={styles.operation}>
            {icon}
            {item.title}
          </div>
        );
      }
      return menu;
    });
  }

  function columnRender({ column, row, value, index, action }) {
    const { id } = row;
    const extraData = row[`${column?.dataIndex}_extra_data`];
    const style = extraData?.style;
    const icon = extraData?.icon;
    let tags = [];
    if (extraData?.tags) {
      tags = extraData?.tags?.map((item, index) => (
        <Tag key={index} color={item.color} style={item.style}>
          {item.text}
        </Tag>
      ));
    }
    if (column?.operations) {
      const items = createOperations({ column, id });
      return (
        <div ref={operationRef}>
          <Dropdown
            menu={{ items }}
            trigger={["click"]}
            getPopupContainer={() => operationRef.current}
            placement="bottomRight"
            overlayClassName={styles.tableRowOperationsOverlay}
          >
            <Button
              type="primary"
              size="small"
              onClick={() => {
                Params.selectedId = id;
              }}
            >
              <span>操作</span>
              <CaretDownOutlined style={{ margin: 0, fontSize: 10 }} />
            </Button>
          </Dropdown>
        </div>
      );
    } else if (column?.editable) {
      const { dataIndex } = column;
      const options = column?.editable?.props?.options || [];
      const hasOptions = options?.length > 0;
      const initialValue = hasOptions ? options?.find((a) => a.label === row[dataIndex])?.value : row[dataIndex];
      return (
        <div ref={popoverContainerRef}>
          <span>{value}</span>
          {tags}
          <Popover
            ref={popoverRef}
            content={
              <Form
                initialValues={{ [dataIndex]: initialValue }}
                onFinish={async (values) => {
                  try {
                    popoverRef.current?.setPopupVisible(false);
                    setLoading(true);
                    await commandRequest({
                      request: column.editable?.request,
                      values: { field: dataIndex, value: values[dataIndex], id },
                    });
                    setLoading(false);
                    if (hasOptions) {
                      row[dataIndex] = options?.find((a) => a.value === values[dataIndex])?.label;
                    } else {
                      row[dataIndex] = values[dataIndex];
                    }
                    forceUpdateTable();
                  } catch (e) {
                    // Modal.error({ title: "操作失败！" });
                  }
                }}
              >
                <Form.Item name={dataIndex} rules={[{ required: true }]}>
                  {formControlRender(column.editable)}
                </Form.Item>
                <Button htmlType="submit">
                  <CheckOutlined />
                </Button>
              </Form>
            }
            trigger={["click"]}
            overlayClassName={styles.editablePopover}
            destroyTooltipOnHide
            placement="right"
            getPopupContainer={() => popoverContainerRef.current}
          >
            <EditOutlined className={styles.editIcon} />
          </Popover>
        </div>
      );
    } else if (column?.modal) {
      return (
        <span
          className="primary-color cursor-pointer"
          style={style}
          onClick={async () => {
            setColumnModal({ open: true, title: column.modal?.title });
            setColumnModalTableData(null);
            setColumnModalTableLoading(true);
            const result = await commandRequest({ request: column.modal.request }).then((res) => res.data);
            if (result.status === Enums.ApiStatus.Success) {
              setColumnModalTableData(result.data);
            } else {
              message.error(result?.message);
            }
            setColumnModalTableLoading(false);
          }}
        >
          {value}
        </span>
      );
    } else if (column?.image) {
      return <Image src={value} width={column.image?.width} preview={{ mask: <EyeOutlined /> }} />;
    } else if (column?.link) {
      return (
        <Link to={column.link} className={styles.tableCellLink}>
          {value}
        </Link>
      );
    } else if (column?.valueType === "html") {
      return <HtmlBlock html={row[column.dataIndex]} />;
    } else {
      return (
        <div style={{ display: "flex", alignItems: "center" }}>
          <Icon {...icon} />
          <span className={styles.tableCellText} style={style}>
            {value}
          </span>
          {tags}
        </div>
      );
    }
  }

  function formControlRender(item) {
    const component = item.component;
    if (component === Enums.Components.Textarea) {
      return <Input.TextArea {...item.props} />;
    } else if (component === Enums.Components.Select) {
      return <Select {...item.props} />;
    } else {
      return <Input {...item.props} />;
    }
  }

  function initColumnsValue(columns) {
    columns?.forEach((column, index) => {
      const { filter, dataIndex } = column;
      const { processFilterData, sortParams, columnRender } = paramsRef.current;
      if (filter) {
        processFilterData({ column });
      }
      column.render = (value, row, index, action) => columnRender({ column, row, value, index, action });
      column.sortOrder = sortParams[dataIndex] || null;
      column.onHeaderCell = (column) => {
        return { column, index };
      };
      column.onCell = (row, index) => {
        const rowSpan = column.rowSpan?.[index];
        if (rowSpan > -1) {
          return { rowSpan };
        }
      };
    });
    return columns;
  }

  async function fetchTableData({ scrollToTop, hasLoading = true } = {}) {
    try {
      const antTableBody = contentRef.current.querySelector(".ant-table-body");
      if (antTableBody && scrollToTop) {
        antTableBody.scrollTop = 0;
      }
      if (hasLoading) {
        setLoading(true);
      }
      clearTableRowActive();
      const result = await fetchListPageData({ url_key: Helper.getUrlKey({ location, params }) });
      if (result?.data) {
        initColumnsValue(result?.data?.tableProps?.columns);
        setData(result.data);
      }
      return result;
    } finally {
      if (hasLoading) {
        setLoading(false);
      }
      setTimeout(updateTableScrollHeight);
    }
  }

  function resizableHeader(props) {
    const { column, index, children, ...otherProps } = props;
    const nthChildIndex = index + (data?.tableProps?.rowSelection ? 2 : 1);
    const selector = `.ant-table .ant-table-body table colgroup col:nth-child(${nthChildIndex})`;
    const col = typeof index === "number" ? contentRef.current?.querySelector(selector) : null;
    const params = {
      dragging: false,
      startX: 0,
      startWidth: col?.offsetWidth,
      width: col?.offsetWidth,
      parentElement: null,
    };
    const handleMouseMove = (e) => {
      const inc = e.clientX - params.startX;
      const width = params.startWidth + inc;
      params.width = width;
      if (col) {
        col.style.width = width + "px";
      }
    };
    const handleMouseUp = () => {
      params.dragging = false;
      params.parentElement.style.pointerEvents = "auto";
      contentRef.current.style.userSelect = "auto";
      if (data.tableProps.columns[index]) {
        setData((data) => {
          data.tableProps.columns[index].width = params.width;
          return { ...data };
        });
      }
      afterRender(handleResizeColumnDragEnd);
      document.body.removeEventListener("mousemove", handleMouseMove);
      document.body.removeEventListener("mouseup", handleMouseUp);
    };
    return (
      <th {...otherProps}>
        {children}
        <span
          className="react-resizable-handle"
          onMouseDown={(e) => {
            params.dragging = true;
            params.startX = e.clientX;
            params.parentElement = e.currentTarget.parentElement;
            params.parentElement.style.pointerEvents = "none";
            contentRef.current.style.userSelect = "none";
            document.body.addEventListener("mousemove", handleMouseMove);
            document.body.addEventListener("mouseup", handleMouseUp);
          }}
        ></span>
      </th>
    );
  }

  async function handleResizeColumnDragEnd() {
    await saveTableColumnsWidth({
      columns: data?.tableProps?.columns
        ?.filter((item) => item.dataIndex)
        .map((item) => ({ dataIndex: item.dataIndex, width: item.width })),
    });
  }

  async function handleCommand(command) {
    if (command?.type === "request") {
      const fetcher = async () => {
        const result = await commandRequest({
          request: command.request,
          values: { ids: selectedRowKeys },
        });
        if (result?.status === Enums.ApiStatus.Success) {
          closeCommandModal();
          await requestSuccessCallback(result);
        } else {
          message.error(result?.message);
        }
      };
      if (command.confirm) {
        await new Promise((resolve) => {
          Modal.confirm({
            title: command.confirm,
            onOk: () => fetcher(),
            afterClose: () => resolve(),
          });
        });
      } else {
        await fetcher();
      }
    } else if (command?.type === "redirect") {
      navigate(command.url);
    } else if (command?.type === "modal") {
      setCommandModal({ ...commandModal, open: true, data: command });
    } else if (command?.type === "reload") {
      setSelectedRowKeys([]);
      fetchTableData();
    } else if (command?.type === "download") {
      window.open(command.url);
    }
  }

  function closeCommandModal() {
    setCommandModal({ open: false, data: {} });
  }

  async function requestSuccessCallback(result) {
    if (result?.message) {
      message.success(result?.message);
    }
    await handleCommand(result?.data?.command);
  }

  function commandRequest({ request, values }) {
    const { url, ...options } = request;
    return axiosRequest({
      url,
      method: "POST",
      ...options,
      data: { ...options.data, ...values },
    });
  }

  function handleTableFullscreenClick() {
    setTableFullscreen(!tableFullscreen);
  }

  function handleTableContainerClick(e) {
    const { target } = e;
    const isTableBody = !!target.closest(`.ant-table-body`);
    const isPopover = !!target.closest(`.${styles.editablePopover}`);
    const isDropdown = !!target.closest(`.ant-dropdown`);
    if (isTableBody && !isPopover && !isDropdown) {
      clearTableRowActive();
      target.closest("tr")?.classList?.add(styles.tableRowActive);
    }
  }

  function clearTableRowActive() {
    contentRef.current
      ?.querySelectorAll(`.ant-table-body .${styles.tableRowActive}`)
      .forEach((node) => node.classList.remove(styles.tableRowActive));
  }

  function handleBatchActionClick({ command, buttonKey }) {
    const hasLoading = ["request"].includes(command?.type);
    hasLoading && setButtonsLoading((data) => ({ ...data, [buttonKey]: true }));
    handleCommand(command).finally(() => {
      hasLoading && setButtonsLoading((data) => ({ ...data, [buttonKey]: false }));
    });
  }

  useEffect(() => {
    (async () => {
      const { fetchTableData } = paramsRef.current;
      fetchTableData({ scrollToTop: true });
    })();
  }, [location, params]);

  useEffect(() => {
    if (!loading) {
      const { updateTableScrollHeight } = paramsRef.current;
      updateTableScrollHeight();
    }
  }, [location, params, loading]);

  useEffect(() => {
    function handleWindowResize() {
      const { updateTableScrollHeight } = paramsRef.current;
      setTimeout(updateTableScrollHeight, 300);
    }
    window.addEventListener("resize", handleWindowResize);

    return function () {
      window.removeEventListener("resize", handleWindowResize);
    };
  }, []);

  useEffect(() => {
    const debounceUpdateTableScrollHeight = debounce(
      () => {
        const { updateTableScrollHeight } = paramsRef.current;
        updateTableScrollHeight();
      },
      0,
      { leading: false, trailing: true }
    );
    const observer = new MutationObserver(() => {
      debounceUpdateTableScrollHeight();
    });
    observer.observe(contentRef.current, { childList: true, subtree: true });
  }, []);

  useEffect(() => {
    document.body.classList.add(styles.disablePageScroll);

    return function () {
      document.body.classList.remove(styles.disablePageScroll);
    };
  }, []);

  useEffect(() => {
    function handleKeyDown(e) {
      const { tableFullscreen } = paramsRef.current;
      if (tableFullscreen && e.code === "Escape") {
        setTableFullscreen(false);
      }
    }
    document.addEventListener("keydown", handleKeyDown);

    return function () {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  useEffect(() => {
    const { updateTableScrollHeight } = paramsRef.current;
    updateTableScrollHeight();
  }, [tableFullscreen]);

  useEffect(() => {
    if (data?.polling > 0) {
      const { fetchTableData } = paramsRef.current;
      paramsRef.current.counter = new Counter({
        interval: data.polling,
        onTick() {
          fetchTableData({ hasLoading: false });
        },
      });
    }

    return function () {
      paramsRef.current.counter?.destroy();
    };
  }, [data?.polling]);

  useEffect(() => {
    const { counter } = paramsRef.current;
    polling ? counter?.start() : counter?.stop();
  }, [polling]);

  return (
    <>
      <div ref={contentRef} className={styles.listPage}>
        <div className={styles.listPageWrapper}>
          <div className="page-header">
            <div>
              <Breadcrumbs data={data?.breadcrumbs}></Breadcrumbs>
            </div>
            <div className="page-header-actions">
              {data?.headerActions?.map((item, index) => {
                const buttonKey = `batchAction${index}`;
                const isDropdown = item.component === "Dropdown";
                const isUpload = item.component === "Upload";
                const button = (
                  <Button
                    type="primary"
                    {...item.props}
                    key={buttonKey}
                    disabled={item.enableByRowSelection && selectedRowKeys?.length === 0}
                    loading={buttonsLoading[buttonKey]}
                    onClick={() => {
                      if (!isDropdown && !isUpload) handleBatchActionClick({ command: item.command, buttonKey });
                    }}
                    style={{ display: "flex", alignItems: "center", gap: 2 }}
                  >
                    <Icon src={item.icon} style={{ width: 16, height: 16 }} />
                    {item.title}
                    {isDropdown ? <CaretDownOutlined className={styles.caretDown} /> : null}
                  </Button>
                );
                if (isDropdown) {
                  return (
                    <Dropdown
                      key={index}
                      trigger={["click"]}
                      {...item.dropdownProps}
                      menu={{
                        ...item.dropdownProps.menu,
                        onClick: ({ key }) => {
                          const menuItem = item.dropdownProps.menu.items.find((a) => a.key === key);
                          handleBatchActionClick({ command: menuItem.command, buttonKey });
                        },
                      }}
                    >
                      {button}
                    </Dropdown>
                  );
                } else if (isUpload) {
                  return (
                    <Upload
                      key={index}
                      {...item.uploadProps}
                      showUploadList={false}
                      onChange={({ file }) => {
                        if (file.status === "uploading") {
                          setButtonsLoading((data) => ({ ...data, [buttonKey]: true }));
                        }
                        if (["done", "error"].includes(file.status) || file.response) {
                          setButtonsLoading((data) => ({ ...data, [buttonKey]: false }));
                          if (file.status === "done") {
                            if (file.response.message) {
                              message.success(file.response.message);
                            }
                          } else {
                            message.error("上传失败！");
                          }
                          return handleCommand(file.response?.data?.command);
                        }
                      }}
                    >
                      {button}
                    </Upload>
                  );
                } else {
                  return button;
                }
              })}
            </div>
          </div>
          <div
            className={classNames(styles.tableContainer, {
              [styles.tableSmaller]: data?.tableProps?.size === "smaller",
              [styles.tableFullscreen]: tableFullscreen,
            })}
            onClick={handleTableContainerClick}
          >
            <ProConfigProvider>
              <ProTable
                {...data?.tableProps}
                actionRef={actionRef}
                loading={loading}
                onChange={handleTableChange}
                scroll={{ y: tableScrollHeight }}
                search={false}
                components={{ header: { cell: resizableHeader } }}
                tableAlertRender={false}
                rowSelection={
                  data?.tableProps?.rowSelection
                    ? {
                        selectedRowKeys,
                        onChange: (selectedRowKeys, selectedRows) => {
                          setSelectedRowKeys(selectedRowKeys);
                        },
                      }
                    : false
                }
                options={{
                  reload: false,
                  density: false,
                }}
                toolbar={{
                  subTitle: (
                    <div>
                      {Object.keys(filterParams).length > 0 ? (
                        <div className={styles.filterTags}>
                          <span style={{ lineHeight: "26px" }}>筛选条件：</span>
                          <span>
                            {Object.keys(filterParams).map((dataIndex, index) => {
                              const column = data?.tableProps?.columns?.find((item) => item.dataIndex === dataIndex);
                              const title = column?.title;
                              const label = getFilterLabel({ column });
                              return title ? (
                                <Tag
                                  key={dataIndex + index}
                                  closable
                                  onClose={() => {
                                    const searchParams = new URLSearchParams(location.search);
                                    const filterKey = getFilterKey(dataIndex);
                                    searchParams.delete(filterKey);
                                    updateSearchParams({ searchParams });
                                  }}
                                >
                                  {title}: {label}
                                </Tag>
                              ) : null;
                            })}
                          </span>
                          <Tag className={styles.clearAll} onClick={clearFilters}>
                            Clear All
                          </Tag>
                          <span className={styles.filterTotal}>共 {data?.tableProps?.pagination?.total || 0} 条</span>
                        </div>
                      ) : null}
                    </div>
                  ),
                  actions: (
                    <div className={styles.tableActions}>
                      {data?.toolbarActions?.length > 0 ? (
                        <div className={styles.tableActionsItem}>
                          <Dropdown
                            menu={{
                              items: data?.toolbarActions?.map((item) => ({
                                ...item,
                                icon: <Icon src={item.icon} style={{ width: 16, height: 16 }} />,
                              })),
                              onClick({ key }) {
                                const item = data?.toolbarActions?.find((item) => item.key === key);
                                handleCommand(item.command);
                              },
                            }}
                            trigger={["click"]}
                            placement="bottomRight"
                          >
                            <Tooltip title="更多操作">
                              <UnorderedListOutlined />
                            </Tooltip>
                          </Dropdown>
                        </div>
                      ) : null}
                      <div className={styles.tableActionsItem} onClick={async () => setPolling(!polling)}>
                        <Tooltip title={polling ? "停止" : "轮询"} spin={polling}>
                          <SyncOutlined spin={polling} />
                        </Tooltip>
                      </div>
                      <div className={styles.tableActionsItem} onClick={handleTableFullscreenClick}>
                        {!tableFullscreen ? (
                          <Tooltip key="fullscreen" title="全屏">
                            <FullscreenOutlined style={{ fontSize: 16 }} />
                          </Tooltip>
                        ) : (
                          <Tooltip key="exitFullscreen" title="退出全屏">
                            <FullscreenExitOutlined style={{ fontSize: 16 }} />
                          </Tooltip>
                        )}
                      </div>
                    </div>
                  ),
                }}
              ></ProTable>
            </ProConfigProvider>
          </div>
        </div>
      </div>
      <Modal
        open={optModalOpen}
        onOk={() => {
          modalFormRef.current?.submit();
        }}
        onCancel={() => {
          setOptModalOpen(false);
        }}
        title={Params.selectedOperation?.title}
        destroyOnClose
        confirmLoading={modalConfirmLoading}
      >
        <JSONForm
          ref={modalFormRef}
          data={Params.selectedOperation?.command?.content?.form}
          onFinish={async (values) => {
            setModalConfirmLoading(true);
            const request = Params.selectedOperation?.command?.content?.form?.request;
            const result = await commandRequest({
              request: request,
              values: { ...values, id: Params.selectedId },
            });
            if (result?.status === Enums.ApiStatus.Success) {
              setOptModalOpen(false);
              requestSuccessCallback(result);
            } else {
              message.error(result?.message);
            }
            setModalConfirmLoading(false);
          }}
        />
      </Modal>
      <Modal
        open={columnModal.open}
        onCancel={() => {
          setColumnModal((modal) => ({ ...modal, open: false }));
        }}
        title={columnModal.title}
        destroyOnClose
        footer={null}
      >
        <Table {...columnModalTableData} loading={columnModalTableLoading} />
      </Modal>
      <Modal
        open={commandModal.open}
        title={commandModal.data?.title}
        onCancel={closeCommandModal}
        destroyOnClose
        confirmLoading={commandModalConfirmLoading}
        closable={commandModal.data?.closable}
        maskClosable={commandModal.data?.closable}
        footer={
          commandModal.data?.buttons ? (
            commandModal.data?.buttons?.map((item) => {
              return (
                <Button
                  key="confirm"
                  type="primary"
                  {...item.props}
                  loading={commandModalConfirmLoading}
                  onClick={async () => {
                    setCommandModalConfirmLoading(true);
                    await handleCommand(item.command);
                    setCommandModalConfirmLoading(false);
                  }}
                >
                  {item.title}
                </Button>
              );
            })
          ) : (
            <Button
              key="confirm"
              type="primary"
              loading={commandModalConfirmLoading}
              onClick={() => commandFormRef.current?.submit()}
            >
              确定
            </Button>
          )
        }
      >
        {commandModal.data.content?.form ? (
          <JSONForm
            ref={commandFormRef}
            data={commandModal.data?.content?.form}
            onFinish={async (values) => {
              setCommandModalConfirmLoading(true);
              const request = commandModal.data?.content?.form?.request;
              const result = await commandRequest({ request, values });
              if (result?.status === Enums.ApiStatus.Success) {
                closeCommandModal();
                requestSuccessCallback(result);
              } else {
                message.error(result?.message);
              }
              setCommandModalConfirmLoading(false);
            }}
          />
        ) : (
          commandModal.data.content
        )}
      </Modal>
    </>
  );
}

export default ListPage;
