import styles from "@/pages/pod/components/option-set-control-editor/index.module.scss";
import { ControlWrapper, FormControl } from "@/components/react-form-x";
import { Button, InputNumber, Select } from "antd";
import { CustomFunctionType } from "@/pages/pod/common";
import { CloseOutlined } from "@ant-design/icons";
import { createIncrementId, customFunctionOptions } from "@/pages/pod/components/option-set-control-editor/common";
import PropTypes from "prop-types";

function CustomFunctions(props) {
  const { control, keyPath, forceUpdate } = props;

  return (
    <div className={styles.flexGrid}>
      {control?.functions?.map((item, index) => {
        const funcKeyPath = [...keyPath, "functions", index];
        return (
          <div key={item.id} className={styles.flexRow}>
            <div>
              <FormControl
                keyPath={funcKeyPath}
                name="type"
                render={(props) => {
                  return (
                    <div className={styles.controlBox}>
                      <div>Function:</div>
                      <div style={{ flex: 1 }}>
                        <ControlWrapper
                          {...props}
                          render={(props) => {
                            const { name, value, onChange } = props;
                            return (
                              <Select
                                value={value}
                                options={customFunctionOptions}
                                style={{ width: "100%" }}
                                onChange={(value) => {
                                  onChange(null, { [name]: value });
                                  forceUpdate();
                                }}
                                disabled={[CustomFunctionType.UploadImage].includes(value)}
                              ></Select>
                            );
                          }}
                        ></ControlWrapper>
                      </div>
                    </div>
                  );
                }}
              ></FormControl>
            </div>
            {(() => {
              const { type } = item;
              return (
                <div>
                  {(() => {
                    if (
                      [CustomFunctionType.Text, CustomFunctionType.TextColor, CustomFunctionType.FontType].includes(
                        type
                      )
                    ) {
                      return (
                        <FormControl
                          keyPath={funcKeyPath}
                          name="text_id"
                          render={(props) => {
                            return (
                              <div className={styles.controlBox}>
                                <div>Text Id:</div>
                                <div style={{ flex: 1 }}>
                                  <ControlWrapper
                                    {...props}
                                    render={(props) => {
                                      const { name, onChange } = props;
                                      return (
                                        <InputNumber
                                          {...props}
                                          onChange={(value) => {
                                            onChange(null, { [name]: value });
                                          }}
                                          style={{ width: "100%" }}
                                        ></InputNumber>
                                      );
                                    }}
                                  ></ControlWrapper>
                                </div>
                              </div>
                            );
                          }}
                        ></FormControl>
                      );
                    } else if (
                      [
                        CustomFunctionType.DynamicImage,
                        CustomFunctionType.ImageColor,
                        CustomFunctionType.UploadImage,
                      ].includes(type)
                    ) {
                      return (
                        <FormControl
                          keyPath={funcKeyPath}
                          name="image_id"
                          render={(props) => {
                            return (
                              <div className={styles.controlBox}>
                                <div>Image Id:</div>
                                <div style={{ flex: 1 }}>
                                  <ControlWrapper
                                    {...props}
                                    render={(props) => {
                                      const { name, onChange } = props;
                                      return (
                                        <InputNumber
                                          {...props}
                                          onChange={(value) => {
                                            onChange(null, { [name]: value });
                                          }}
                                          style={{ width: "100%" }}
                                        ></InputNumber>
                                      );
                                    }}
                                  ></ControlWrapper>
                                </div>
                              </div>
                            );
                          }}
                        ></FormControl>
                      );
                    }
                  })()}
                </div>
              );
            })()}
            <div style={{ flex: 0 }}>
              <Button
                icon={<CloseOutlined />}
                type="text"
                onClick={() => {
                  control.functions = control.functions.filter((x) => x.id !== item.id);
                  forceUpdate();
                }}
              ></Button>
            </div>
          </div>
        );
      })}
      <div>
        <Button
          type="primary"
          onClick={() => {
            control.functions.push({ id: createIncrementId({ items: control.functions }), type: "text" });
            forceUpdate();
          }}
        >
          Add Custom Function
        </Button>
      </div>
    </div>
  );
}

CustomFunctions.propTypes = {
  control: PropTypes.object,
  keyPath: PropTypes.array,
  forceUpdate: PropTypes.func,
};

export default CustomFunctions;
