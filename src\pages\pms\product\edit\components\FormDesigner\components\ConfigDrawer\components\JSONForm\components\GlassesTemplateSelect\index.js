import { useState, useEffect, useCallback } from "react";
import { Select as AntdSelect, Empty, Spin } from "antd";
import { observer } from "mobx-react-lite";

import request from "@/fetchers/request";
import store from "@/pages/pms/product/edit/components/FormDesigner/store";

function GlassesTemplateSelect(props) {
  const { optionsApi, id, ...restProps } = props;
  const [loading, setLoading] = useState(false);

  const getOptions = useCallback(async () => {
    try {
      if (optionsApi) {
        setLoading(true);
        const result = await request(optionsApi, {
          method: "GET",
        }).then((res) => res.data);
        store.setOptionsData({ id, options: result.data });
      }
    } finally {
      setLoading(false);
    }
  }, [id, optionsApi]);

  useEffect(() => {
    if (store.optionsData[id]) {
      return;
    }
    getOptions();
  }, [getOptions, id]);

  return (
    <AntdSelect
      options={store.optionsData?.[id] ?? []}
      showSearch
      placeholder="请选择"
      optionFilterProp="label"
      notFoundContent={
        <div style={{ display: "flex", justifyContent: "center" }}>
          {loading ? <Spin /> : <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
        </div>
      }
      {...restProps}
    />
  );
}

GlassesTemplateSelect = observer(GlassesTemplateSelect);

export default GlassesTemplateSelect;
