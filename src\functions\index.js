export function afterRender(callback) {
  Promise.resolve().then(callback)
}

export function Counter({ interval = 1000, onTick } = {}) {
  const that = this
  const worker = new Worker("/worker-timer.js")

  worker.onmessage = function (e) {
    onTick?.(e)
  }

  that.start = function () {
    worker.postMessage({ action: "start", interval })
    return that
  }

  that.stop = function () {
    worker.postMessage({ action: "stop" })
    return that
  }

  that.destroy = function () {
    worker.terminate()
  }
}
