import classNames from "classnames";
import { useNavigate } from "react-router-dom";
import PropTypes from "prop-types";
import { Button } from "antd";
import SubmitButton from "components/common/Button";
import Breadcrumbs from "components/common/Breadcrumbs";
import Helper from "helpers";
import styles from "./index.module.scss";

function EditPageHeader(props) {
  const { breadcrumbs, actions } = props;
  const navigate = useNavigate();
  const isInsideIframe = Helper.isInsideIframe();

  function handleBackClick() {
    navigate(-1);
  }

  async function handleSubmit() {
    // formRef.current?.submit();
    Helper.commandHandler({ command: { type: "submit", id: "form" } });
  }

  return (
    <div className={classNames("page-header", { [styles.pageHeader]: isInsideIframe })}>
      {!isInsideIframe ? (
        <div>
          <Breadcrumbs data={breadcrumbs}></Breadcrumbs>
        </div>
      ) : null}

      <div className="page-header-actions">
        {!isInsideIframe && (
          <Button type="default" onClick={handleBackClick}>
            返回
          </Button>
        )}

        {/* <SubmitButton type="primary" onClick={handleSubmit}>
          保存
        </SubmitButton> */}
        {actions?.map((item, index) => {
          return (
            <SubmitButton
              key={index}
              type="primary"
              {...item.props}
              command={item.command}
              onClick={async () => {
                await Helper.commandHandler({ command: item.command });
              }}
            >
              {item.title}
            </SubmitButton>
          );
        })}
      </div>
    </div>
  );
}

EditPageHeader.prototype = {
  breadcrumbs: PropTypes.array,
  actions: PropTypes.array,
};

export default EditPageHeader;
