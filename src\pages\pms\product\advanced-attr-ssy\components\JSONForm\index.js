import { useEffect, useState } from "react";
import { Form } from "antd";

import Enums from "enums";
import Utils from "utils";

import CommonJSONForm from "components/common/JSONForm";
import GemstoneConfig from "./components/GemstoneConfig";
import MaterialConfig from "./components/MaterialConfig";

function JSONForm(props) {
  const { params } = props;
  const [form] = Form.useForm();
  const [isCustomPrice, setIsCustomPrice] = useState(false); // 是否自定义价格

  const extendComponents = {
    GemstoneConfig: ({ item }) => (
      <GemstoneConfig {...item.props} isCustomPrice={isCustomPrice} form={form} params={params} />
    ),
    MaterialConfig: ({ item }) => (
      <MaterialConfig {...item.props} isCustomPrice={isCustomPrice} form={form} params={params} />
    ),
  };

  function handleValuesChange(changedValues, allValues) {
    if (changedValues.hasOwnProperty("custom_price")) {
      const isCustomPrice = changedValues.custom_price;

      setIsCustomPrice(changedValues.custom_price);
      if (!isCustomPrice) {
        Utils.dispatchEvent(Enums.EventName.CalculateStonePrice, { isCustomPrice: changedValues.custom_price });
      }
    }
  }

  useEffect(() => {
    // 初始化否自定义价格
    const values = form.getFieldsValue();
    if (values.custom_price) {
      setIsCustomPrice(true);
    }
  }, [form]);

  return (
    <CommonJSONForm form={form} {...props} onValuesChange={handleValuesChange} extendComponents={extendComponents} />
  );
}

export default JSONForm;
