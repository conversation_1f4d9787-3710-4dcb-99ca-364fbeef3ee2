const common = require("../common");
const fs = require("node:fs/promises");

const createBaseRepository = () => {
  return {
    table: {},
    autoIncrementId: 0,

    async init() {
      const text = await fs.readFile(this.filename, { encoding: "utf8" }).catch(() => "");
      this.table = common.JSON.parse(text, {});
      if (Object.keys(this.table).length > 0) {
        this.autoIncrementId = Math.max(...Object.keys(this.table).map((x) => +x));
      }
      return this;
    },

    getAutoIncrementId() {
      return ++this.autoIncrementId;
    },

    getIndex() {
      return Object.keys(this.table).length + 1;
    },

    async findAll() {
      const str = await fs.readFile(this.filename, { encoding: "utf8" }).catch(() => "");
      const table = common.JSON.parse(str, {});
      this.table = table;
      return Object.values(table);
    },

    async find(where) {
      let items = await this.findAll();
      Object.keys(where).forEach((key) => {
        items = items.filter((item) => {
          if (Array.isArray(where[key])) {
            return where[key].includes(item[key]);
          }
          return item[key] === where[key];
        });
      });
      return items;
    },

    async findOne(where) {
      const items = await this.findAll();
      return items.find((item) => Object.keys(where).every((key) => item[key] === where[key]));
    },

    async updateOne(where, props) {
      const item = await this.findOne(where);
      Object.entries(props).forEach(([key, value]) => {
        item[key] = value;
      });
      item.update_time = new Date();
      await this.save();
      return item;
    },

    async delete(id) {
      const success = await this.deleteWithoutSave(id);
      await this.save();
      return success;
    },

    async deleteWithoutSave(id) {
      return delete this.table[id];
    },

    async deleteMany(where) {
      const ids = await this.find(where)?.map?.((item) => item.id);
      return await this.deleteManyByIds(ids);
    },

    async deleteManyByIds(ids) {
      const results = await Promise.all((ids || [])?.map((id) => this.deleteWithoutSave(id)));
      await this.save();
      const deletedCount = results.filter((success) => success).length;
      return { deletedCount };
    },

    async clear() {
      this.table = {};
      await this.save();
    },

    async save() {
      await fs.writeFile(this.filename, "", { encoding: "utf8" });
      return await fs.writeFile(this.filename, JSON.stringify(this.table), { encoding: "utf8" });
    },
  };
};

const LibrariesRepository = (() => {
  const base = createBaseRepository();
  const instance = {
    ...base,

    filename: `${__dirname}/data/libraries.json`,

    async insert({ type, name, name_i18n_key }) {
      const id = this.getAutoIncrementId();
      const index = this.getIndex();
      const item = {
        id,
        index,
        type,
        name,
        name_i18n_key,
        list_type: "",
        create_time: new Date().toJSON(),
        update_time: new Date().toJSON(),
      };
      this.table[id] = item;
      await this.save();
      return item;
    },
  };

  instance.init();

  return instance;
})();

const CategoriesRepository = (() => {
  const base = createBaseRepository();
  const instance = {
    ...base,

    filename: `${__dirname}/data/categories.json`,

    async insert(data) {
      const id = this.getAutoIncrementId();
      const index = this.getIndex();
      const item = {
        ...data,
        id,
        create_time: new Date(),
        update_time: new Date(),
      };
      this.table[id] = item;
      await this.save();
      return item;
    },
  };

  instance.init();

  return instance;
})();

const ItemsRepository = (() => {
  const base = createBaseRepository();
  const instance = {
    ...base,

    filename: `${__dirname}/data/items.json`,

    async insertMany({ library_id, category_id, items }) {
      let index = 0;
      if (category_id) {
        index = (await this.find({ category_id })).length;
      } else if (library_id) {
        index = (await this.find({ library_id })).length;
      }
      index++;
      const newItems = items?.map?.((item, i) => {
        const id = this.getAutoIncrementId();
        return {
          ...item,
          id,
          index: index + i,
          library_id,
          category_id,
          // preview_image: item.preview_image,
          // prod_image: item.prod_image,
          // thumb_image: item.thumb_image,
          // color: item.color,
          // font_url: item.font_url,
          // font_family: item.font_family,
          // option_id: item.option_id,
          // option_name: item.option_name,
          create_time: new Date(),
          update_time: new Date(),
        };
      });
      newItems?.forEach?.((item) => {
        this.table[item.id] = item;
      });
      await this.save();
      return newItems;
    },

    async find(where) {
      const items = await base.find.call(this, where);
      return items.sort((a, b) => a.index - b.index);
    },
  };

  instance.init();

  return instance;
})();

const TemplatesRepository = (() => {
  const base = createBaseRepository();
  const instance = {
    ...base,

    filename: `${__dirname}/data/templates.json`,

    async insert(data) {
      const id = this.getAutoIncrementId();
      const item = {
        ...data,
        id,
        linked_to_product: false,
        create_time: new Date(),
        update_time: new Date(),
      };
      this.table[id] = item;
      await this.save();
      return item;
    },
  };

  instance.init();

  return instance;
})();

const OptionSetsRepository = (() => {
  const base = createBaseRepository();
  const instance = {
    ...base,

    filename: `${__dirname}/data/option-sets.json`,

    async insert({ controls, template_id, product_id, title, create_from_template }) {
      const id = this.getAutoIncrementId();
      const item = {
        id,
        title,
        controls,
        template_id,
        product_id,
        create_from_template,
        create_time: new Date(),
        update_time: new Date(),
      };
      this.table[id] = item;
      await this.save();
      return item;
    },
  };

  instance.init();

  return instance;
})();

async function updateLibraryCover({ item, cover }) {
  if (item?.category_id) {
    const [{ value: library }, { value: category }, { value: firstItem }] = await Promise.allSettled([
      LibrariesRepository.findOne({ id: item.library_id }),
      CategoriesRepository.findOne({ id: item.category_id }),
      ItemsRepository.findOne({ category_id: item.category_id }),
    ]);
    // library.cover = category.cover = cover || firstItem.preview_image;
    library.cover = cover || firstItem.preview_image;
    await Promise.allSettled([LibrariesRepository.save(), CategoriesRepository.save()]);
  } else if (item?.library_id) {
    const [{ value: library }, { value: firstItem }] = await Promise.allSettled([
      LibrariesRepository.findOne({ id: item.library_id }),
      ItemsRepository.findOne({ library_id: item.library_id }),
    ]);
    library.cover = cover || firstItem.preview_image;
    await LibrariesRepository.save();
  }
}

async function waiting() {
  await common.sleep(500);
}

function updateTableRow({ row, updateData }) {
  Object.entries(updateData).forEach(([key, value]) => {
    if (!updateExcludeKeys.includes(key)) {
      row[key] = value;
    }
  });
}

const updateExcludeKeys = ["id", "create_time", "update_time"];

const libraryColumns = {
  image: [
    { dataIndex: "index", title: "#", width: 80 },
    {
      dataIndex: "preview_image",
      title: "Preview",
      valueType: "image",
      imageProps: { style: { objectFit: "contain" } },
      width: `12%`,
    },
    {
      dataIndex: "prod_image",
      title: "Production",
      valueType: "image",
      imageProps: { style: { objectFit: "contain" } },
      width: `12%`,
    },
    {
      dataIndex: "thumb_image",
      title: "Thumbnail",
      valueType: "image",
      imageProps: { style: { objectFit: "contain" } },
      width: `12%`,
    },
    { dataIndex: "option_id", title: "Option", width: `15%` },
    { dataIndex: "option_name", title: "Name" },
  ],
  color: [
    { dataIndex: "index", title: "#", width: 80 },
    { dataIndex: "color", title: "Color", valueType: "color" },
    { dataIndex: "option_id", title: "Option" },
    { dataIndex: "option_name", title: "Name" },
  ],
  font: [
    { dataIndex: "index", title: "#", width: 80 },
    { dataIndex: "font_family", title: "Font Family" },
    { dataIndex: "option_id", title: "Option" },
    { dataIndex: "option_name", title: "Name" },
  ],
};

const i18nResource = {
  StreetSign: "Street Sign",
  Cats: "Cats",
  CatEyes: "Cat Eyes",
  "Option {{index}}": "Option {{index}}",
  LongHair: "Long Hair",
  ShortHair: "Short Hair",
  "Image Selection {{index}}": "Image Selection {{index}}",
  "Text {{index}}": "Text {{index}}",
  "Custom Option Name {{index}}": "Custom Option Name {{index}}",
  StreetSignTest: "Street Sign Test",
  CatsTest: "Cats Test",
  "Layout {{index}}": "Layout {{index}}",
  "Layout Test {{index}}": "Layout Test {{index}}",
  Doormat: "Doormat",
  "One cat doormat": "One cat doormat",
  "Two cat doormat": "Two cat doormat",
  "Three cat doormat": "Three cat doormat",
  "Cat's body": "Cat's body",
  "Cat's eyes": "Cat's eyes",
  "Cat's name": "Cat's name",
  "Cat {{index}}'s body": "Cat {{index}}'s body",
  "Cat {{index}}'s eyes": "Cat {{index}}'s eyes",
  "Cat {{index}}'s name": "Cat {{index}}'s name",
  "One cat layout": "One cat layout",
  "Two cats layout": "Two cats layout",
  "Three cats layout": "Three cats layout",
  "This field is optional": "This field is optional",
  "This is help text": "This is help text",
  "This is initial value": "This is initial value",
};

function t(key, params) {
  let translated = i18nResource[key] || key;
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      translated = translated.replace(new RegExp(`\{\{${key}\}\}`, "g"), value);
    });
  }
  return translated;
}

function forEachObject(obj, callback) {
  const isPlainObject = (o) => Object.prototype.toString.call(o) === "[object Object]";

  const stack = [];

  if (isPlainObject(obj) || Array.isArray(obj)) {
    stack.push([obj, ""]);
  }

  while (stack.length > 0) {
    let [currentObj, path] = stack.pop();

    if (Array.isArray(currentObj)) {
      currentObj.forEach((item, idx) => {
        if (Array.isArray(item) || isPlainObject(item)) {
          stack.push([item, `${path}[${idx}].`]);
        } else {
          callback({ currentObj: currentObj, key: idx, path: `${path}[${idx}]`, value: item });
        }
      });
    } else if (isPlainObject(currentObj)) {
      for (let key in currentObj) {
        if (Array.isArray(currentObj[key]) || isPlainObject(currentObj[key])) {
          stack.push([currentObj[key], `${path}${key}.`]);
        } else {
          callback({ currentObj: currentObj, key: key, path: `${path}${key}`, value: currentObj[key] });
        }
      }
    }
  }
}

function translateData(data) {
  if (typeof data === "object") {
    forEachObject(data, ({ currentObj, key }) => {
      const match = /(.*)_i18n_[a-z]+/i.exec(key);
      if (match) {
        const field = match[1];
        const translated = t(currentObj[`${field}_i18n_key`], currentObj[`${field}_i18n_params`]);
        if (translated) {
          currentObj[field] = translated;
        }
      }
    });
  }
  return data;
}

module.exports = {
  "GET /rest/v1/pod/libraries": async (req, res) => {
    await waiting();
    const { type } = req.query;
    const dataSource = await LibrariesRepository.find({ type });
    res.status(200).json({
      success: true,
      data: {
        tableProps: {
          columns: [
            { dataIndex: "id", title: "Id", width: 80 },
            {
              dataIndex: "cover",
              title: "Cover",
              valueType: "image",
              imageProps: { style: { objectFit: "contain" } },
              width: `20%`,
              editable: { request: { method: "PATCH", url: "http://localhost:8081/rest/v1/pod/libraries/:id" } },
            },
            {
              dataIndex: "name",
              title: "Name",
              valueType: "i18n_editable",
              editable: { request: { method: "PATCH", url: `http://localhost:8081/rest/v1/pod/libraries/:id` } },
            },
          ],
          dataSource: translateData(dataSource),
          bordered: false,
          rowKey: "id",
        },
      },
    });
  },

  "POST /rest/v1/pod/libraries": async (req, res) => {
    await waiting();
    const { type, name, name_i18n_key } = req.body;
    const item = await LibrariesRepository.insert({ type, name, name_i18n_key });
    res.status(200).json({ success: true, data: { item } });
  },

  "PATCH /rest/v1/pod/libraries/:id": async (req, res) => {
    await waiting();
    const { data } = req.body;
    const library_id = +req.pathParams.id;
    const library = await LibrariesRepository.findOne({ id: library_id });
    await updateTableRow({ row: library, updateData: data });
    await LibrariesRepository.save();
    res.status(200).json({ success: true, data: { item: library } });
  },

  "DELETE /rest/v1/pod/libraries/:id": async (req, res) => {
    await waiting();
    const { id } = req.pathParams;
    const library_id = common.toNumber(id);
    await Promise.all([
      ItemsRepository.deleteMany({ library_id }),
      CategoriesRepository.deleteMany({ library_id }),
      LibrariesRepository.delete(library_id),
    ]);
    res.status(200).json({ success: true, data: {} });
  },

  "GET /rest/v1/pod/libraries/:id": async (req, res) => {
    await waiting();
    const { id } = req.pathParams;
    const library_id = common.toNumber(id);
    const library = await LibrariesRepository.findOne({ id: library_id });
    const isCategoryList = library?.list_type === "category";
    const columns = isCategoryList
      ? [
          { dataIndex: "id", title: "#", width: 80 },
          {
            dataIndex: "cover",
            title: "Cover",
            valueType: "image",
            editable: { request: { method: "PATCH", url: `http://localhost:8081/rest/v1/pod/categories/:id` } },
          },
          {
            dataIndex: "name",
            title: "Category",
            valueType: "i18n_editable",
            editable: { request: { method: "PATCH", url: `http://localhost:8081/rest/v1/pod/categories/:id` } },
          },
        ]
      : libraryColumns[library?.type];
    const dataSource = isCategoryList
      ? await CategoriesRepository.find({ library_id })
      : await ItemsRepository.find({ library_id });
    res.status(200).json({
      success: true,
      data: {
        library,
        tableProps: {
          columns,
          dataSource: translateData(dataSource),
          bordered: false,
          rowKey: "id",
          pagination: false,
          size: "small",
        },
      },
    });
  },

  "POST /rest/v1/pod/categories": async (req, res) => {
    await waiting();
    const { library_id: libraryId, name, cover } = req.body;
    const library_id = common.toNumber(libraryId);
    const library = await LibrariesRepository.findOne({ id: library_id });
    if (library.list_type && library.list_type !== "category") {
      throw new Error("Library is not category list");
    }
    library.list_type = "category";
    await LibrariesRepository.save();
    const item = await CategoriesRepository.insert({ ...req.body, library_id });
    res.status(200).json({ success: true, data: { item } });
  },

  "DELETE /rest/v1/pod/categories/:id": async (req, res) => {
    await waiting();
    const { id } = req.pathParams;
    const category_id = common.toNumber(id);
    const category = await CategoriesRepository.findOne({ id: category_id });
    await CategoriesRepository.delete(category_id);
    const categories = await CategoriesRepository.find({ library_id: category.library_id });
    if (category && categories.length === 0) {
      await LibrariesRepository.updateOne({ id: category.library_id }, { list_type: "" });
    }
    await ItemsRepository.deleteMany({ category_id });
    res.status(200).json({ success: true, data: {} });
  },

  "PATCH /rest/v1/pod/categories/:id": async (req, res) => {
    await waiting();
    const id = common.toNumber(req.pathParams.id);
    const { data } = req.body;
    const category = await CategoriesRepository.findOne({ id });
    await updateTableRow({ row: category, updateData: data });
    await CategoriesRepository.save();
    res.status(200).json({ success: true, data: { item: category } });
  },

  "GET /rest/v1/pod/categories": async (req, res) => {
    await waiting();
    const { library_ids: libraryIds = "" } = req.query;
    const library_ids = libraryIds
      .split(",")
      .filter((x) => x !== "")
      .map((str) => common.toNumber(str));
    let categories = [];
    if (library_ids.length > 0) {
      categories = await CategoriesRepository.find({ library_id: library_ids });
    }
    res.status(200).json({ success: true, data: { items: categories } });
  },

  "GET /rest/v1/pod/categories/:id": async (req, res) => {
    await waiting();
    const { id } = req.pathParams;
    const category_id = common.toNumber(id);
    const category = await CategoriesRepository.findOne({ id: category_id });
    const library = await LibrariesRepository.findOne({ id: category.library_id });
    const dataSource = await ItemsRepository.find({ category_id: category_id });
    res.status(200).json({
      success: true,
      data: {
        library,
        category,
        tableProps: {
          columns: libraryColumns.image,
          dataSource: translateData(dataSource),
          bordered: false,
          rowKey: "id",
          size: "small",
        },
      },
    });
  },

  "POST /rest/v1/pod/items": async (req, res) => {
    await waiting();
    const { library_id: libraryId, category_id: categoryId, items, cover } = req.body;
    const library_id = common.toNumber(libraryId);
    const category_id = common.toNumber(categoryId);
    const insertedItems = await ItemsRepository.insertMany({ library_id, category_id, items });
    if (category_id) {
      // const category = await CategoriesRepository.findOne({ id: category_id });
      // category.cover = cover || insertedItems[0]?.preview_image;
      // await CategoriesRepository.save();
    } else if (library_id) {
      const library = await LibrariesRepository.findOne({ id: library_id });
      library.list_type = "items";
      await LibrariesRepository.save();
    }
    await updateLibraryCover({ item: insertedItems[0], cover });
    res.status(200).json({ success: true, data: { insertedCount: insertedItems.length } });
  },

  "GET /rest/v1/pod/items": async (req, res) => {
    await waiting();
    const { library_id: libraryId, category_id: categoryId, library_ids: libraryIds = "" } = req.query;
    const library_id = common.toNumber(libraryId);
    const category_id = common.toNumber(categoryId);
    const library_ids = libraryIds
      .split(",")
      .filter((x) => x !== "")
      .map((id) => common.toNumber(id));
    let items = [];
    if (library_ids?.length > 0) {
      items = await ItemsRepository.find({ library_id: library_ids });
    } else {
      if (category_id) {
        items = await ItemsRepository.find({ category_id });
      } else if (library_id) {
        items = await ItemsRepository.find({ library_id });
      }
    }
    items.sort((a, b) => a.index - b.index);
    res.status(200).json({ success: true, data: { items } });
  },

  "DELETE /rest/v1/pod/items/:id": async (req, res) => {
    await waiting();
    const { id } = req.pathParams;
    const item = await ItemsRepository.findOne({ id: +id });
    let result;
    if (item) {
      result = await ItemsRepository.delete(item.id);
      // await updateLibraryCover({ item });
    }
    const items = await ItemsRepository.find({ library_id: item.library_id });
    if (items.length === 0) {
      const library = await LibrariesRepository.findOne({ id: item.library_id });
      library.list_type = null;
      await LibrariesRepository.save();
    }
    res.status(200).json({ success: true, data: result });
  },

  "DELETE /rest/v1/pod/items": async (req, res) => {
    await waiting();
    const { ids } = req.body;
    const result = await ItemsRepository.deleteManyByIds(ids);
    res.status(200).json({ success: true, data: result });
  },

  "PATCH /rest/v1/pod/items/:id": async (req, res) => {
    await waiting();
    const { id } = req.pathParams;
    const { data } = req.body;
    const item = await ItemsRepository.findOne({ id: +id });
    await updateTableRow({ row: item, updateData: data });
    await ItemsRepository.save();
    await updateLibraryCover({ item });
    res.status(200).json({ success: true, data: { item } });
  },

  "PATCH /rest/v1/pod/items": async (req, res) => {
    await waiting();
    const {
      action,
      data: { sortedIds },
    } = req.body;
    if (action === "sort") {
      for (let i = 0; i < sortedIds.length; i++) {
        const id = sortedIds[i];
        await ItemsRepository.updateOne({ id }, { index: i + 1 });
      }
    }
    const items = await ItemsRepository.findAll();
    res.status(200).json({ success: true, data: { items } });
  },

  "POST /rest/v1/pod/templates": async (req, res) => {
    await waiting();
    const { data } = req.body;
    const item = await TemplatesRepository.insert(data);
    res.status(200).json({ success: true, data: { item } });
  },

  "GET /rest/v1/pod/templates": async (req, res) => {
    await waiting();
    const dataSource = await TemplatesRepository.findAll().then((items) =>
      items
        .sort((a, b) => new Date(b.create_time) - new Date(a.create_time))
        .map((item) => ({
          id: item.id,
          name: item.name,
          name_i18n_key: item.name_i18n_key,
          cover: item.cover,
          create_time: item.create_time,
          update_time: item.update_time,
          linked_to_product: item.linked_to_product,
        }))
    );
    res.status(200).json({
      success: true,
      data: {
        tableProps: {
          columns: [
            // {
            //   dataIndex: "name",
            //   title: "Template",
            //   width: "30%",
            //   valueType: "withImage",
            //   imageDataKey: "cover",
            //   imageProps: { style: { objectFit: "contain" } },
            //   editable: { request: { url: "http://localhost:8081/rest/v1/pod/templates/:id", method: "PATCH" } },
            // },
            {
              dataIndex: "cover",
              title: "",
              width: 50,
              valueType: "image",
              imageProps: { style: { objectFit: "contain" } },
            },
            {
              dataIndex: "name",
              title: "Template",
              width: `30%`,
              valueType: "i18n_editable",
              editable: { request: { method: "PATCH", url: `http://localhost:8081/rest/v1/pod/templates/:id` } },
            },
            { dataIndex: "create_time", title: "Created", valueType: "datetime", format: "YYYY-MM-DD HH:mm:ss" },
            { dataIndex: "update_time", title: "Last Modified", valueType: "datetime", format: "YYYY-MM-DD HH:mm:ss" },
            { dataIndex: "linked_to_product", title: "Linked to Product", valueType: "boolean" },
          ],
          dataSource: translateData(dataSource),
          rowKey: "id",
          size: "small",
        },
      },
    });
  },

  "DELETE /rest/v1/pod/templates/:id": async (req, res) => {
    await waiting();
    const { id } = req.pathParams;
    const success = await TemplatesRepository.delete(id);
    res.status(200).json({ success, data: {} });
  },

  "GET /rest/v1/pod/templates/:id": async (req, res) => {
    await waiting();
    const { id } = req.pathParams;
    const item = await TemplatesRepository.findOne({ id: +id });
    res.status(200).json({ success: true, data: { item } });
  },

  "PATCH /rest/v1/pod/templates/:id": async (req, res) => {
    await waiting();
    const { id } = req.pathParams;
    const { data } = req.body;
    const template = await TemplatesRepository.findOne({ id: +id });
    Object.entries(data).forEach(([key, value]) => {
      template[key] = value;
    });
    template.update_time = new Date();
    await TemplatesRepository.save();
    res.status(200).json({ success: true, data: { item: template } });
  },

  "POST /rest/v1/pod/option-sets": async (req, res) => {
    await waiting();
    const { template_id, product_id, controls, title, create_from_template } = req.body;
    const item = await OptionSetsRepository.insert({
      template_id,
      product_id,
      controls,
      title,
      create_from_template,
    });
    res.status(200).json({ success: true, data: { item } });
  },

  "PATCH /rest/v1/pod/option-sets/:id": async (req, res) => {
    await waiting();
    const { id } = req.pathParams;
    const { data } = req.body;
    const item = await OptionSetsRepository.findOne({ id: +id });
    if (item) {
      updateTableRow({ row: item, updateData: data });
      await OptionSetsRepository.save();
    }
    res.status(200).json({ success: true, data: { item } });
  },

  "GET /rest/v1/pod/option-sets/:id": async (req, res) => {
    await waiting();
    const { id } = req.pathParams;
    const item = await OptionSetsRepository.findOne({ id: +id });
    res.status(200).json({ success: true, data: { item: translateData(item) } });
  },

  "GET /rest/v1/pod/templates/:templateId/option-set": async (req, res) => {
    await waiting();
    const { templateId } = req.pathParams;
    const template_id = common.toNumber(templateId);
    const item = await OptionSetsRepository.findOne({ template_id, create_from_template: true });
    res.status(200).json({ success: true, data: { item } });
  },

  "GET /rest/v1/pod/option-sets": async (req, res) => {
    await waiting();
    const items = await OptionSetsRepository.findAll().then((items) =>
      items.map((item) => ({
        id: item.id,
        title: item.title,
        create_from_template: item.create_from_template,
        product_id: item.product_id,
        create_time: item.create_time,
        update_time: item.update_time,
      }))
    );
    res.status(200).json({
      success: true,
      data: {
        tableProps: {
          columns: [
            { dataIndex: "id", title: "ID", width: "5%" },
            { dataIndex: "title", title: "Title", width: "25%" },
            { dataIndex: "create_from_template", title: "Template Options", valueType: "boolean" },
            // { dataIndex: "linked_to_product", title: "Linked to Product", valueType: "boolean" },
            { dataIndex: "create_time", title: "Create Time", valueType: "datetime", format: "YYYY-MM-DD HH:mm:ss" },
            { dataIndex: "update_time", title: "Update Time", valueType: "datetime", format: "YYYY-MM-DD HH:mm:ss" },
          ],
          dataSource: items,
          rowKey: "id",
          size: "small",
        },
      },
    });
  },

  "DELETE /rest/v1/pod/option-sets/:id": async (req, res) => {
    await waiting();
    if (req.pathParams.id) {
      const id = +req.pathParams.id;
      await OptionSetsRepository.delete(id);
    }
    res.status(200).json({ success: true, data: { deletedCount: 1 } });
  },

  "GET /rest/v1/pod/i18n": async (req, res) => {
    await waiting();
    const { search = "" } = req.query;
    let items = [];
    if (search.trim().length > 0) {
      items = Object.entries(i18nResource)
        .map(([value, label]) => ({ label, value }))
        .filter((item) => item.label.toLowerCase().includes(search.toLowerCase()));
    }
    res.status(200).json({ success: true, data: { items } });
  },
};
