.lowCodeEditor {
  height: calc(100vh - 50px);
  display: flex;
  flex-direction: column;

  .main {
    flex: 1;
    overflow: hidden;
  }

  .sider {
    background-color: #fff;
    border-right: 1px solid #f0f0f0;
    overflow: auto;
  }

  .content {
    padding: 24px;
    background-color: #f5f5f5;
    overflow: auto;
  }

  .jsonPreview {
    max-height: 500px;
    overflow: auto;
    background-color: #f5f5f5;
    padding: 16px;
    border-radius: 4px;
    font-family: monospace;
  }

  .noDrop {
    opacity: 0.5;
    cursor: not-allowed;
  }
}
