const common = require("../common");
const createItem = ({ index, name, fullName }) => {
  return {
    id: index + 1,
    name: fullName || `${name} Library ${index + 1}`,
    created_at: new Date(),
    updated_at: new Date(),
  };
};

const createDataSource = ({ name }) => {
  const list = [];
  for (let i = 0; i < 5; i++) {
    list.push(createItem({ index: i, name }));
  }
  return list;
};

const dataSourceMap = {
  image: createDataSource({ name: "Image" }),
  font: createDataSource({ name: "Font" }),
  color: createDataSource({ name: "Color" }),
};

module.exports = {
  "GET /rest/v1/list/pod/libraries": async (req, res) => {
    await common.sleep(500);
    const { type } = req.query;
    res.status(200).json({
      success: true,
      data: {
        headerActions: [{ title: "Add Library" }],
        tableProps: {
          columns: [
            { key: "id", dataIndex: "id", title: "ID", width: 80 },
            { key: "name", dataIndex: "name", title: "Name" },
          ],
          dataSource: dataSourceMap[type],
          pagination: false,
          bordered: false,
          size: "medium",
        },
      },
    });
  },
};
