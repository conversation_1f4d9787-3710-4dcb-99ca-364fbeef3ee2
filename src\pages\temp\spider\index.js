import { useState } from "react";
import { Button, InputNumber, Form, Input, Select } from "antd";
import styles from "./index.module.scss";
import Fetchers from "fetchers";

const SpiderConfig = [
  {
    type: "teepublic",
    config: {
      title: ".m-design-details__title h1",
      imageUrl: ".jsProductMainImage",
      categoryProductItem: ".jsDesignContainer",
      tags: {
        selector: ".m-design__additional-info",
        title: ".m-design__subtitles",
        item: ".link__content",
      },
    },
  },
  {
    type: "redbubble",
    config: {
      title: "[class*='i_primaryContent'] h1",
      imageUrl: "[class*='Picture_picture'] img",
      categoryProductItem: '[data-testid="search-result-card"]',
      tags: {
        selector: `
          [data-testid="current-product-tags"],
          [data-testid="all-product-tags"],
          [data-testid="trending-tags"]
        `,
        title: "h3",
        item: "[class*='TagsList_tagsList'] a",
      },
    },
  },
];

const Spider = () => {
  const [loading, setLoading] = useState(false);
  const [spiderProgress, setSpiderProgress] = useState([]);

  const onFinish = async (values) => {
    setLoading(true);
    const ws = Fetchers.getSpiderDataExport();

    ws.onopen = () => {
      console.log("Connected to spider service");

      // 发送爬取请求
      ws.send(
        JSON.stringify({
          type: "start-crawl",
          params: {
            ...values,
            config: SpiderConfig.find((item) => item.type === values.config)?.config,
          },
        })
      );
    };

    ws.onmessage = (event) => {
      const isResult = event.data instanceof Blob;
      const message = isResult
        ? {
            type: "result",
            data: event.data,
          }
        : JSON.parse(event.data);

      switch (message.type) {
        case "progress":
          console.log(`Progress: ${message.data.message}`);
          setSpiderProgress((prev) => [...prev, message.data.message]);
          break;
        case "result":
          setSpiderProgress(["爬取完成, 自动下载excel文件..."]);
          const blob = new Blob([message.data], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = "products.xlsx";
          a.click();
          window.URL.revokeObjectURL(url);
          setLoading(false);
          break;
        case "error":
          setLoading(false);
          setSpiderProgress((prev) => [...prev, message.data]);
          break;
        default:
          break;
      }
    };

    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
    };

    ws.onclose = () => {
      console.log("Disconnected from spider service");
      setLoading(false);
    };
  };

  return (
    <div className={styles.spider}>
      <Form
        className={styles.form}
        name="basic"
        labelCol={{
          span: 8,
        }}
        wrapperCol={{
          span: 16,
        }}
        onFinish={onFinish}
        autoComplete="off"
      >
        <Form.Item
          label="选择抓取站点"
          name="config"
          rules={[
            {
              required: true,
              message: "请输入要抓取的站点",
            },
          ]}
        >
          <Select options={SpiderConfig.map((item) => ({ label: item.type, value: item.type }))} />
        </Form.Item>
        <Form.Item
          label="URL"
          name="url"
          rules={[
            {
              required: true,
              type: "url",
              message: "请输入要抓取的url, 如: https://www.teepublic.com/t-shirts",
            },
          ]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          label="Page"
          name="page"
          rules={[
            {
              required: true,
              message: "请输入要抓取到第几页",
            },
          ]}
          extra="需要从第一页到第几页, 如输入3, 则表示爬取第一页到第三页的商品数据"
        >
          <InputNumber />
        </Form.Item>

        <Form.Item label={null} className={styles.action}>
          <Button type="primary" htmlType="submit" loading={loading}>
            开始抓取
          </Button>
        </Form.Item>
      </Form>
      <div className={styles.progress}>
        {spiderProgress.map((item, index) => (
          <div key={index}>{item}</div>
        ))}
      </div>
    </div>
  );
};

export default Spider;
