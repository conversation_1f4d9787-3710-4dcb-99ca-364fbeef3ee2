import { Collapse as AntdCollapse } from "antd";
import JSONComponents from "components/common/JSONComponent";
import Label from "./components/label";

function Collapse(props) {
  const { data, items: propsItems = null } = props;
  const { items, ...otherProps } = data?.props;

  const panels = propsItems
    ? []
    : items?.map((item, index) => {
        return {
          ...item,
          label: <Label data={item} />,
          children: <JSONComponents data={item} />,
        };
      });

  return <AntdCollapse items={propsItems || panels} size="small" {...otherProps}></AntdCollapse>;
}

export default Collapse;
