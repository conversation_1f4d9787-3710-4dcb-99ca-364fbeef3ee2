const Api = require("../src/fetchers/api");

module.exports = {
  "GET /rest/v1/api-json-components": (req, res) => {
    res.status(200).json({
      status: "00",
      success: true,
      data: {
        content: {
          component: "JSONComponents",
          type: "json",
          props: {},
          children: [
            {
              component: "Text",
              content: "Text文案",
            },
            {
              component: "Form",
              type: "json",
              props: {
                id: "form1",
                initialValues: {},
                labelCol: {
                  span: 4,
                },
                wrapperCol: {
                  span: 20,
                },
              },
              formItems: [
                {
                  component: "Row",
                  props: { gutter: 16 },
                  children: [
                    {
                      component: "Col",
                      props: {
                        xs: {
                          span: 24,
                        },
                        lg: {
                          span: 24,
                        },
                      },
                      children: [
                        {
                          key: "field1",
                          label: "Field1",
                          component: "Input",
                          props: {},
                          rules: [{ required: true, message: "This is a required field" }],
                        },
                      ],
                    },
                    {
                      component: "Col",
                      props: {
                        xs: {
                          span: 24,
                        },
                        lg: {
                          span: 24,
                        },
                      },
                      children: [
                        {
                          key: "field2",
                          label: "Field2",
                          component: "Input",
                          props: {},
                          rules: [{ required: true, message: "This is a required field" }],
                        },
                      ],
                    },
                  ],
                },
              ],
              submit: {
                request: {
                  url: Api.customer,
                  data: {
                    default: "test",
                  },
                },
              },
            },
            {
              component: "Row",
              props: { gutter: 16 },
              children: [
                {
                  component: "Col",
                  props: {
                    xs: {
                      span: 24,
                    },
                    lg: {
                      span: 24,
                    },
                  },
                  children: [
                    {
                      component: "Text",
                      content: "Text文案2222",
                    },
                    {
                      component: "Form",
                      type: "json",
                      props: {
                        layout: "vertical",
                        id: "form2",
                        initialValues: { field1: "xxx" },
                        labelCol: {
                          span: 8,
                        },
                        wrapperCol: {
                          span: 16,
                        },
                      },
                      formItems: [
                        {
                          component: "Row",
                          props: { gutter: 16 },
                          children: [
                            {
                              component: "Col",
                              props: { span: 12 },
                              children: [
                                {
                                  key: "field1",
                                  label: "Field21",
                                  component: "Input",
                                  props: { extra: "提示文案" },
                                  rules: [{ required: true, message: "This is a required field" }],
                                },
                              ],
                            },
                            {
                              component: "Col",
                              props: { span: 12 },
                              children: [
                                {
                                  key: "field2",
                                  label: "Field22",
                                  component: "Input",
                                  props: {},
                                  rules: [{ required: true, message: "This is a required field" }],
                                },
                              ],
                            },
                          ],
                        },
                      ],
                      submit: {
                        request: {
                          url: Api.customer,
                          data: {
                            default: "test",
                          },
                        },
                      },
                    },
                  ],
                },
              ],
            },
            {
              component: "Iframe",
              props: {
                src: "/edit/order?a=1&id=1",
              },
            },
            {
              component: "Row",
              props: { gutter: 16 },
              children: [
                {
                  component: "Col",
                  props: {
                    xs: {
                      span: 24,
                    },
                    md: {
                      span: 24,
                    },
                  },
                  children: [
                    {
                      component: "Text",
                      content: "Text文案22",
                    },
                  ],
                },
              ],
            },
            {
              component: "Row",
              props: { gutter: 16 },
              children: [
                {
                  component: "Col",
                  props: {
                    xs: {
                      span: 24,
                    },
                    md: {
                      span: 24,
                    },
                  },
                  children: [
                    {
                      component: "Text",
                      content: "Text文案1111",
                    },
                  ],
                },
                {
                  component: "Col",
                  props: {
                    xs: {
                      span: 24,
                    },
                    md: {
                      span: 24,
                    },
                  },
                  children: [
                    {
                      component: "Text",
                      content: "Text文案2222",
                    },
                  ],
                },
                {
                  component: "Col",
                  props: {
                    xs: {
                      span: 24,
                    },
                    md: {
                      span: 24,
                    },
                  },
                  children: [
                    {
                      component: "Text",
                      content: "Text文案3333",
                    },
                  ],
                },
              ],
            },
          ],
        },
      },
    });
  },

  "POST /rest/v1/api-json-components": (req, res) => {
    res.status(200).json({
      status: "00",
      success: true,
      data: {
        content: {
          component: "JSONComponents",
          type: "json",
          props: {},
          children: [
            {
              component: "Form",
              type: "json",
              props: {
                layout: "vertical",
                id: "form",
                initialValues: { field1: 13123 },
                labelCol: {
                  span: 8,
                },
                wrapperCol: {
                  span: 16,
                },
              },
              formItems: [
                {
                  component: "Row",
                  props: { gutter: 16 },
                  children: [
                    {
                      component: "Col",
                      props: { span: 12 },
                      children: [
                        {
                          key: "field1",
                          label: "Field1",
                          component: "Input",
                          props: {},
                          rules: [{ required: true, message: "This is a required field" }],
                        },
                      ],
                    },
                    {
                      component: "Col",
                      props: { span: 12 },
                      children: [
                        {
                          key: "field2",
                          label: "Field2",
                          component: "Input",
                          props: {},
                          rules: [{ required: true, message: "This is a required field" }],
                        },
                      ],
                    },
                    {
                      component: "Col",
                      props: { span: 12 },
                      children: [
                        {
                          key: "field3",
                          label: "Field3",
                          component: "Input",
                          props: {},
                          rules: [{ required: true, message: "This is a required field" }],
                        },
                      ],
                    },
                    {
                      component: "Col",
                      props: { span: 12 },
                      children: [
                        {
                          key: "field4",
                          label: "Field4",
                          component: "Textarea",
                          props: { rows: 5 },
                          rules: [],
                        },
                      ],
                    },
                  ],
                },
              ],
              fetcher: {},
              submit: {
                request: {
                  url: Api.customer,
                  data: {},
                },
              },
            },
          ],
        },
      },
    });
  },

  "PUT /rest/v1/api-json-components": (req, res) => {
    res.status(200).json({
      status: "00",
      success: true,
      data: {
        content: {
          component: "JSONComponents",
          type: "json",
          props: {},
          children: [
            {
              component: "Form",
              type: "json",
              props: {
                layout: "vertical",
                id: "form",
                initialValues: { field1: "field1", field2: "field2", field3: "field3", field4: "field4" },
                labelCol: {
                  span: 8,
                },
                wrapperCol: {
                  span: 16,
                },
              },
              formItems: [
                {
                  component: "Row",
                  props: { gutter: 16 },
                  children: [
                    {
                      component: "Col",
                      props: { span: 12 },
                      children: [
                        {
                          key: "field1",
                          label: "Field1",
                          component: "Input",
                          props: {},
                          rules: [{ required: true, message: "This is a required field" }],
                        },
                      ],
                    },
                    {
                      component: "Col",
                      props: { span: 12 },
                      children: [
                        {
                          key: "field2",
                          label: "Field2",
                          component: "Input",
                          props: {},
                          rules: [{ required: true, message: "This is a required field" }],
                        },
                      ],
                    },
                    {
                      component: "Col",
                      props: { span: 12 },
                      children: [
                        {
                          key: "field3",
                          label: "Field3",
                          component: "Input",
                          props: {},
                          rules: [{ required: true, message: "This is a required field" }],
                        },
                      ],
                    },
                    {
                      component: "Col",
                      props: { span: 12 },
                      children: [
                        {
                          key: "field4",
                          label: "Field4",
                          component: "Textarea",
                          props: { rows: 5 },
                          rules: [],
                        },
                      ],
                    },
                  ],
                },
              ],
              fetcher: {},
              submit: {
                request: {
                  url: Api.customer,
                  data: {},
                },
              },
            },
          ],
        },
      },
    });
  },
};
