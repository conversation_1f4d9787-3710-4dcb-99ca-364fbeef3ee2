import { EditOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";
import { useEffect, useRef, useState } from "react";
import classNames from "classnames";
import PropTypes from "prop-types";

const ControlType = {
  Input: "input",
  Select: "select",
};

function Editable(props) {
  const { children, onSave, disableBlurSave, allowEmpty, renderControl } = props;
  const [isEditing, setIsEditing] = useState(false);
  const childrenRef = useRef();
  const controlRef = useRef();
  const hasRenderControl = !!renderControl;

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current };

  async function handleSave({ value: nextValue, option }) {
    // 乐观更新
    setIsEditing(false);
    if (!nextValue && !allowEmpty) {
      reset();
    } else {
      childrenRef.current.textContent = nextValue;
      if (onSave) {
        const { success = true } = (await onSave({ value: nextValue, option, childrenRef })) || {};
        if (!success) {
          reset();
        }
      }
    }
  }

  function reset() {
    const { prevValue } = paramsRef.current;
    childrenRef.current.textContent = prevValue;
  }

  useEffect(() => {
    if (!hasRenderControl) {
      const childrenStyle = getComputedStyle(childrenRef.current);
      ["fontFamily", "fontSize", "fontWeight", "lineHeight", "letterSpacing"].forEach((key) => {
        controlRef.current.style[key] = childrenStyle[key];
      });
    }
  }, [hasRenderControl]);

  return (
    <div className={classNames(styles.editableRow, { [styles.editing]: isEditing })}>
      <div className={styles.wrapper}>
        <div ref={childrenRef} className={styles.children}>
          {children}
        </div>
        <div
          className={styles.editIcon}
          onClick={(event) => {
            event.stopPropagation();
            const value = childrenRef.current?.textContent;
            paramsRef.current.prevValue = value;
            controlRef.current.value = value;
            setIsEditing(true);
            Promise.resolve().then(() => {
              controlRef.current?.focus();
            });
          }}
        >
          <EditOutlined />
        </div>
        <div className={styles.editControl}>
          {(() => {
            if (!renderControl) {
              return (
                <input
                  ref={controlRef}
                  type="text"
                  style={{ display: "block", width: `100%`, height: `100%`, boxSizing: "border-box" }}
                  onChange={(event) => {
                    childrenRef.current.textContent = event.target.value;
                  }}
                  onKeyDown={(event) => {
                    const key = event.key.toLowerCase();
                    if (key === "enter") {
                      paramsRef.current.noBlurSave = true;
                      handleSave({ value: event.target.value });
                    } else if (key === "escape") {
                      paramsRef.current.noBlurSave = true;
                      childrenRef.current.textContent = paramsRef.current.prevValue;
                      setIsEditing(false);
                    }
                  }}
                  onBlur={(event) => {
                    if (!disableBlurSave) {
                      if (!paramsRef.current.noBlurSave) {
                        handleSave({ value: event.target.value });
                      }
                      paramsRef.current.noBlurSave = false;
                    }
                  }}
                  onClick={(event) => {
                    event.stopPropagation();
                  }}
                />
              );
            } else {
              return renderControl({ controlRef, handleSave, setIsEditing });
            }
          })()}
        </div>
      </div>
    </div>
  );
}

Editable.propTypes = {
  beforeSave: PropTypes.func,
  onSave: PropTypes.func,
  disableBlurSave: PropTypes.bool,
  type: PropTypes.oneOf(["input", "select"]),
  onSearch: PropTypes.func,
  renderControl: PropTypes.func,
};

export default Editable;
