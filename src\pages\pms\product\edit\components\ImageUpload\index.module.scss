.imageUpload {
  [class~="ant-upload-list-item-container"],
  [class~="ant-upload-select"] {
    width: 120px !important;
    height: 120px !important;
  }

  [class~="ant-upload-list-item-container"] {
    height: auto !important;
  }
}

.uploadItem {
  .actions,
  .extraFields,
  .tags {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    position: relative !important;
    opacity: 1 !important;

    .action {
      cursor: pointer;
      color: #666;
      font-size: 12px;
      text-decoration: underline;
      padding: 0 2px;

      &:hover {
        color: #000;
      }
    }
  }

  .tags {
    column-gap: 5px;
  }
}
