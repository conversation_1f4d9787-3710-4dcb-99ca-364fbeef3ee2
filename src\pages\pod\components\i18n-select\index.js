import { Select } from "antd";
import { forwardRef, useImperativeHandle, useMemo, useRef, useState } from "react";
import debounce from "lodash.debounce";
import Fetchers from "@/fetchers";
import PropTypes from "prop-types";

function I18nSelect(props, ref) {
  const { defaultValue, value: propValue, onChange, allowClear = true } = props;
  const [stateValue, setStateValue] = useState(defaultValue);
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const selectRef = useRef(null);
  const isControlled = Object.hasOwnProperty.call(props, "value");
  const value = isControlled ? propValue : stateValue;

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current, value };

  const debounceSearch = useMemo(() => {
    return debounce((search) => {
      if (search) {
        setLoading(true);
        Fetchers.searchPodI18n({ search })
          .then((res) => {
            setOptions(res.data.data.items);
          })
          .finally(() => {
            setLoading(false);
          });
      } else {
        setOptions([]);
      }
    }, 300);
  }, []);

  useImperativeHandle(
    ref,
    () => {
      return {
        selectRef,
        get value() {
          return paramsRef.current.value;
        },
        set value(value) {
          setStateValue(value);
        },
        focus() {
          selectRef.current?.focus();
        },
      };
    },
    []
  );

  return (
    <Select
      {...props}
      ref={selectRef}
      value={value}
      onChange={(value = "", option = { label: "", value: "" }) => {
        if (isControlled) {
          onChange?.(value, option);
        } else {
          setStateValue(value);
        }
      }}
      options={options}
      showSearch={true}
      onSearch={debounceSearch}
      loading={loading}
      allowClear={allowClear}
      placeholder="搜索翻译文案"
    ></Select>
  );
}

I18nSelect = forwardRef(I18nSelect);

I18nSelect.propTypes = {
  value: PropTypes.any,
  onChange: PropTypes.func,
  onSelect: PropTypes.func,
  style: PropTypes.object,
  allowClear: PropTypes.bool,
};

export default I18nSelect;
