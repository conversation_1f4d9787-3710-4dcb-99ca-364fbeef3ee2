import { useEffect } from "react";
import { useLocation, matchRoutes } from "react-router-dom";
import routes from "routes";
import Enums from "enums";
import Cookies from "js-cookie";
import Fetchers from "fetchers";
import EnvHelper from "helpers/env-helper";
import Helper from "helpers";

const whiteList = ["/login", "/passport", "/quick-login-redirect"];

function InterceptorComponent(props) {
  const { children } = props;
  const location = useLocation();
  const matches = matchRoutes(routes, location);

  useEffect(() => {
    async function checkVersion() {
      const version = process.env.REACT_APP_VERSION;
      if (version && !EnvHelper.isDevelopment) {
        try {
          const currentVersion = await Fetchers.getVersion()
            .then((res) => res.data.version)
            .catch(() => version);
          if (version !== currentVersion) {
            Helper.modal.confirm({
              title: "更新提示",
              content: "检测到新版本，是否刷新页面？",
              onOk() {
                window.location.reload();
              },
            });
          }
        } catch (error) {}
      }
    }

    checkVersion();
  }, [location.pathname]);

  if (Array.isArray(matches)) {
    const token = Cookies.get()[Enums.CookieName.Token];
    if (!token && !whiteList.includes(location.pathname)) {
      window.location.href = `${EnvHelper.RedirectOrigin}/login?redirect=${window.location.href}`;
      return;
    }
  }

  return children;
}

export default InterceptorComponent;
