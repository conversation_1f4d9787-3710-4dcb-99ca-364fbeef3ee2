import styles from "./index.module.scss";
import PropTypes from "prop-types";
import { useRef } from "react";
import classNames from "classnames";

function ResponsiveBox(props) {
  const { children, width, height, style, className } = props;
  const containerRef = useRef();

  return (
    <div ref={containerRef} className={classNames(styles.responsiveBox, className)} style={style}>
      <div className={styles.wrapper} style={{ paddingTop: `${(height / width) * 100}%` }}>
        <div className={styles.content}>{children}</div>
      </div>
    </div>
  );
}

ResponsiveBox.propTypes = {
  width: PropTypes.number.isRequired,
  height: PropTypes.number.isRequired,
  style: PropTypes.object,
  className: PropTypes.string,
};

export default ResponsiveBox;
