import styles from "./index.module.scss";
import { Button } from "antd";
import Helper from "helpers";

function CardLabel({ data }) {
  const { title, extra } = data || {};

  function handleCommand({ command, ...others }) {
    Helper.commandHandler({ command, ...others });
  }

  return (
    <div className={styles.container}>
      <span>{title}</span>
      <span>
        {extra?.map((action, index) => {
          return (
            <Button
              key={index}
              type="link"
              size="small"
              {...action?.props}
              onClick={(e) => {
                e.stopPropagation();
                handleCommand({ command: action?.command });
              }}
            >
              {action?.label}
            </Button>
          );
        })}
      </span>
    </div>
  );
}

export default CardLabel;
