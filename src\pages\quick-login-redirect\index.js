import Loading from "components/common/Loading";
import { useState, useEffect, useRef } from "react";

import Utils from "utils";
import Fetchers from "fetchers";
import EnvHelper from "helpers/env-helper";
import Helper from "helpers";
import Enums from "enums";

const LOGIN_PATH = "/login";

function QuickLoginRedirect() {
  const [isLoading, setIsLoading] = useState(true);
  const paramsRef = useRef({ handleLogin });

  const { code, state: redirect } = Utils.toQueryParams(window.location.href);

  async function handleLogin(code, redirect) {
    try {
      const response = await Fetchers.getToken({ code });
      const token = response?.data?.data;

      if (!token) {
        throw new Error("Invalid token");
      }
      if (redirect) {
        const redirectUrl = new URL(redirect);
        window.location.href = `${redirectUrl.origin}/passport?redirect=${redirect}&token=${token}`;
      } else {
        Helper.setCookies({ [Enums.CookieName.Token]: token });
        window.location.href = "/";
      }
    } catch (error) {
      const loginUrl = `${EnvHelper.RedirectOrigin}${LOGIN_PATH}?redirect=${redirect}`;
      window.location.href = loginUrl;
    }
  }

  useEffect(() => {
    const { handleLogin } = paramsRef.current;

    if (!code) {
      window.location.href = redirect;
      return;
    }

    handleLogin(code, redirect);
  }, [code, redirect]);

  return (
    <Loading loading={isLoading}>
      <div style={{ width: "100vw", height: "100vh" }}></div>
    </Loading>
  );
}

export default QuickLoginRedirect;
