import { makeAutoObservable } from "mobx";
import Utils from "@/utils";

const podStore = makeAutoObservable({
  canvas: null,
  objects: [],
  activeObject: null,
  initialized: false,
  template: null,
  layouts: [],
  fontFamilyOptions: [
    { label: "Arial", value: "Arial" },
    { label: "Sans Serif", value: "sans serif" },
  ],

  setCanvas(canvas) {
    this.canvas = canvas;
  },

  setObjects(objects) {
    this.objects = objects;
  },

  setActiveObject(activeObject) {
    this.activeObject = activeObject;
  },

  init() {
    this.initialized = true;
  },

  setTemplate(template) {
    this.template = template;
  },

  setLayouts(layouts) {
    this.layouts = layouts;
  },

  clear() {
    this.canvas = null;
    this.objects = [];
    this.activeObject = null;
    this.initialized = false;
    this.template = null;
    this.layouts = [];
  },

  setFontFamilyOptions(options) {
    this.fontFamilyOptions = Utils.removeDuplicates(options, "value");
  },
});

export default podStore;
