const Api = require("../../../src/fetchers/api");

module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      breadcrumbs: [
        {
          title: "Ticket",
          url: "/",
        },
        {
          title: "Ticket 编辑",
          url: "/",
        },
      ],
      actions: [
        {
          title: "发送",
          props: {
            type: "primary",
          },
          command: {
            type: "submit",
            id: "update-form",
          },
        },
      ],
      content: {
        component: "Form",
        type: "json",
        props: {
          layout: "vertical",
          id: "update-form",
          initialValues: { field1: "field1", field2: "field2", field4: "field4" },
          labelCol: {
            span: 24,
          },
          wrapperCol: {
            span: 24,
          },
        },
        formItems: [
          {
            component: "Row",
            props: { gutter: 16 },
            children: [
              {
                component: "Col",
                props: { span: 8 },
                children: [
                  {
                    key: "brand_id",
                    label: "品牌名",
                    component: "Select",
                    props: {
                      options: [
                        { label: "选项1", value: "1" },
                        { label: "选项2", value: "2" },
                        { label: "选项3", value: "3" },
                        { label: "选项4", value: "4" },
                        { label: "选项5", value: "5" },
                      ],
                    },
                    rules: [{ required: true, message: "This is a required field" }],
                  },
                ],
              },
              {
                component: "Col",
                props: { span: 8 },
                children: [
                  {
                    key: "field1",
                    label: "Field1",
                    component: "Input",
                    props: {},
                    rules: [{ required: true, message: "This is a required field" }],
                  },
                ],
              },
              {
                component: "Col",
                props: { span: 8 },
                children: [
                  {
                    key: "target_lang",
                    label: "语言",
                    component: "Select",
                    props: {
                      options: [
                        { label: "选项1", value: "1" },
                        { label: "选项2", value: "2" },
                        { label: "选项3", value: "3" },
                        { label: "选项4", value: "4" },
                        { label: "选项5", value: "5" },
                      ],
                    },
                    rules: [{ required: true, message: "This is a required field" }],
                  },
                ],
              },
              {
                component: "Col",
                props: { span: 8 },
                children: [
                  {
                    key: "field2",
                    label: "Field2",
                    component: "Input",
                    props: {},
                    rules: [{ required: true, message: "This is a required field" }],
                  },
                ],
              },
              {
                component: "Col",
                props: { span: 8 },
                children: [
                  {
                    key: "field3",
                    label: "Field3",
                    component: "Input",
                    props: {},
                    rules: [{ required: true, message: "This is a required field" }],
                  },
                ],
              },
              {
                component: "Col",
                props: { span: 8 },
                children: [
                  {
                    key: "field4",
                    label: "Field4",
                    component: "Input",
                    props: {},
                    rules: [{ required: true, message: "This is a required field" }],
                  },
                ],
              },
              {
                component: "Col",
                props: { span: 24 },
                children: [
                  {
                    key: "remark",
                    label: "备注",
                    component: "Textarea",
                    props: { rows: 3 },
                    rules: [],
                  },
                ],
              },
              {
                component: "Col",
                props: { span: 24 },
                children: [
                  {
                    key: "select",
                    label: "",
                    component: "SelectTemplate",
                    props: {
                      placeholder: "请选择回复模版",
                      mode: "multiple",
                      options: [
                        {
                          label: "manager",
                          title: "manager",
                          options: [
                            {
                              label: "Jack",
                              value: "Jack",
                            },
                            {
                              label: "Lucy",
                              value: "Lucy",
                            },
                          ],
                        },
                        {
                          label: "manager2",
                          title: "manager2",
                          options: [
                            {
                              label: "Jack2",
                              value: "Jack2",
                            },
                            {
                              label: "Lucy2",
                              value: "Lucy2",
                            },
                          ],
                        },
                      ],
                    },
                    templateApi: "http://192.168.2.110:8081/rest/v1/ticket/ticket/ajax-template",
                    addonBefore: {
                      content: "回复模板",
                      props: {},
                    },
                    cascader: {
                      name: "richTextEditor",
                    },
                  },
                ],
              },
              {
                component: "Col",
                props: { span: 24 },
                children: [
                  {
                    key: "richTextEditor",
                    label: "邮件内容",
                    component: "RichTextEditorTemplate",
                    props: { minHeight: 300, placeholder: "Enter your text", disabled: false },
                    rules: [{ required: true, message: "This is a required field" }],
                  },
                ],
              },
              {
                component: "Col",
                props: { span: 24 },
                children: [
                  {
                    key: "fileUpload",
                    label: "上传文件",
                    component: "FileUpload",
                    props: {
                      listType: "picture-card",
                      action: Api.uploadFile,
                      data: { disk: "s3-static" },
                      multiple: true,
                      accept: ".jpg, .jpeg, .png, .mp4, .pdf",
                    },
                    rules: [{ required: true, message: "This is a required field" }],
                  },
                ],
              },
            ],
          },
        ],
        fetcher: {},
        submit: {
          request: {
            url: Api.customer,
            data: {},
          },
        },
      },
    },
    command: {},
  });
};
