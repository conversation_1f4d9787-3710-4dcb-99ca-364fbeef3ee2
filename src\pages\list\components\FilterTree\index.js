import { forwardRef, useImperativeHandle, useRef, useState } from "react";
import styles from "./index.module.scss";
import { Input, Tree } from "antd";
import PropTypes from "prop-types";
import debounce from "lodash.debounce";
import Utils from "utils";

function FilterTree(props, ref) {
  const { treeData, value, onChange, defaultExpandAll } = props;
  const flatTreeData = Utils.flatTree({ treeData: Utils.buildTreeId({ treeData }) });
  const defaultExpandedKeys = getDefaultExpandedKeys();
  const [innerTreeData, setInnerTreeData] = useState(Utils.buildTreeId({ treeData }));
  const [expandedKeys, setExpandedKeys] = useState(defaultExpandedKeys);
  const [checkStrictly, setCheckStrictly] = useState(false);
  const paramsRef = useRef({ prevExpandedKeys: defaultExpandedKeys });

  paramsRef.current = { ...paramsRef.current };

  const handleInput = debounce((e) => {
    const keywords = e.target.value;
    if (keywords) {
      const searchedTreeData = Utils.searchTree({ flatTreeData, keywords });
      setCheckStrictly(true);
      setInnerTreeData(searchedTreeData);
      const newExpandedKeys = [];
      paramsRef.current.prevExpandedKeys = Utils.cloneDeep(expandedKeys);
      Utils.forEachTree({
        treeData: searchedTreeData,
        callback: (item) => {
          newExpandedKeys.push(item.key);
        },
      });
      setExpandedKeys(newExpandedKeys);
    } else {
      setCheckStrictly(false);
      setInnerTreeData(treeData);
      setExpandedKeys(paramsRef.current.prevExpandedKeys);
    }
  }, 300);

  function getDefaultExpandedKeys() {
    const parents = (value?.checked || value)
      ?.map((key) => flatTreeData.find((item) => item.key === key))
      .map((item) => item.__parentId)
      .map((id) => flatTreeData.find((item) => item.__id === id))
      .filter((item) => item);
    return Utils.fillTreeNodesParents({ treeNodes: parents, flatTreeData }).map((item) => item.key);
  }

  useImperativeHandle(ref, () => {
    return {
      setExpandedKeys,
    };
  });

  return (
    <div className={styles.filterTree}>
      <div style={{ marginBottom: 8 }}>
        <Input onInput={handleInput} />
      </div>
      <div className={styles.tree}>
        <Tree
          treeData={innerTreeData}
          checkedKeys={value}
          checkable
          checkStrictly={checkStrictly}
          selectable={false}
          defaultExpandAll={defaultExpandAll}
          expandedKeys={expandedKeys}
          onCheck={(value) => {
            onChange?.(value?.checked || value);
          }}
          onExpand={(expandedKeys) => {
            setExpandedKeys(expandedKeys);
          }}
        />
      </div>
    </div>
  );
}

FilterTree = forwardRef(FilterTree);
FilterTree.propTypes = {
  treeData: PropTypes.array,
  value: PropTypes.any,
  onChange: PropTypes.func,
  defaultExpandAll: PropTypes.bool,
  onExpand: PropTypes.func,
};

export default FilterTree;
