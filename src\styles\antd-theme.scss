:root {
  --ant-primary-color: #1890ff;
  --ant-primary-color-hover: #40a9ff;
  --ant-primary-color-active: #096dd9;
  --ant-primary-color-outline: rgba(24, 144, 255, 0.2);
  --ant-primary-1: #e6f7ff;
  --ant-primary-2: #bae7ff;
  --ant-primary-3: #91d5ff;
  --ant-primary-4: #69c0ff;
  --ant-primary-5: #40a9ff;
  --ant-primary-6: #1890ff;
  --ant-primary-7: #096dd9;
  --ant-success-color: #52c41a;
  --ant-success-color-hover: #73d13d;
  --ant-success-color-active: #389e0d;
  --ant-success-color-outline: rgba(82, 196, 26, 0.2);
  --ant-error-color: #ff4d4f;
  --ant-error-color-hover: #ff7875;
  --ant-error-color-active: #d9363e;
  --ant-error-color-outline: rgba(255, 77, 79, 0.2);
  --ant-warning-color: #faad14;
  --ant-warning-color-hover: #ffc53d;
  --ant-warning-color-active: #d48806;
  --ant-warning-color-outline: rgba(250, 173, 20, 0.2);
  --ant-info-color: #1890ff;
}

body {
  .ant-btn {
    > * {
      display: inline-flex;
      align-items: center;
    }
  }

  .ant-pro-layout-container {
    .ant-pro-layout-header {
      backdrop-filter: blur(2px) !important;

      .ant-pro-global-header {
        margin: 0;

        .ant-pro-global-header-logo {
          height: auto;
          margin: 0;
          padding: 0 16px;

          h1 {
            margin: 0;
          }
        }
      }

      & + * {
        height: 100%;
      }
    }

    .ant-pro-layout-content {
      flex: 1;
    }
  }

  .ant-dropdown {
    filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));

    &.ant-dropdown-placement-bottomRight.ant-dropdown-show-arrow {
      > .ant-dropdown-arrow {
        right: 10px;
        box-shadow: none;
      }
    }
  }

  .ant-layout-content {
    padding: 0 !important;
    padding-block: 0 !important;
    padding-inline: 0 !important;
  }

  .ant-table {
    .ant-table-filter-column {
      align-items: center;
    }
  }

  .ant-table-filter-dropdown {
    min-width: 0;
  }

  .ant-pro-table {
    > .ant-pro-card {
      background-color: transparent;

      .ant-pro-card-body {
        padding-inline: 0;
        padding-block: 0;

        .ant-pro-table-list-toolbar {
          .ant-pro-table-list-toolbar-container {
            padding-top: 0;
            padding-bottom: 8px;
            padding-right: 8px;

            .ant-pro-table-list-toolbar-right {
              //justify-content: space-between;
              //width: 100%;
            }
          }
        }
      }
    }
  }

  .ant-checkbox.ant-checkbox-checked,
  .ant-tree-checkbox.ant-tree-checkbox-checked {
    .ant-checkbox-inner,
    .ant-tree-checkbox-inner {
      &:after {
        top: 7px;
        left: 3px;
        width: 6px;
        height: 12px;
      }
    }
  }

  .ant-layout-sider {
    //z-index: 2000;
  }

  .ant-menu-vertical .ant-menu-item:not(:last-child),
  .ant-menu-vertical-left .ant-menu-item:not(:last-child),
  .ant-menu-vertical-right .ant-menu-item:not(:last-child),
  .ant-menu-inline .ant-menu-item:not(:last-child) {
    margin-bottom: 4px;
  }

  .ant-tabs-card.ant-tabs-small > .ant-tabs-nav .ant-tabs-tab {
    padding: 6px;
  }

  .ant-tree .ant-tree-checkbox + span.ant-tree-node-selected {
    background-color: #badcf7;
  }

  .ant-input-group-addon {
    button {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .ant-checkbox + span {
    min-width: 70px;
  }

  // 左侧菜单
  .ant-menu .ant-menu-item .ant-menu-item-icon + span,
  .ant-menu .ant-menu-submenu-title .ant-menu-item-icon + span {
    margin-inline-start: 0;
  }

  .ant-menu-dark.ant-menu-submenu-popup > .ant-menu {
    background-color: #3975c6;
  }

  .ant-menu .ant-menu-item a::before {
    position: absolute;
    inset: 0;
    content: "";
  }

  .ant-image {
    .ant-image-img {
      width: 100%;
      height: 100%;
      //object-fit: cover;
    }
  }

  .ant-image-no-preview-text {
    .ant-image-mask-info {
      font-size: 0;

      .anticon {
        font-size: 14px;
        margin-inline: 0 !important;
      }
    }
  }

  .ant-image-responsive {
    .ant-image {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .ant-upload-list-item-actions {
    display: flex;
    align-items: center;
    justify-content: center;

    > a {
      display: flex;
    }

    .ant-btn {
      padding: 0;
      border: none;
    }
  }

  .ant-btn-success,
  .ant-btn-success:not(:disabled):not([class*="disabled"]):hover {
    background: var(--ant-success-color);
  }
}
