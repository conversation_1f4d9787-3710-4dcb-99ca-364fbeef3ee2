import { ControlWrapper, Form, FormControl } from "@/components/react-form-x";
import { Button, Input, InputNumber } from "antd";
import FormControlLabel from "@/pages/pod/components/form-control-label";
import { useState } from "react";
import Fetchers from "@/fetchers";

function AddFontItemForm(props) {
  const { libraryId, onSuccess } = props;
  const [submitLoading, setSubmitLoading] = useState(false);

  return (
    <Form
      onSubmit={(event, values) => {
        setSubmitLoading(true);
        Fetchers.addPodLibraryItems({ library_id: libraryId, items: [values] })
          .then(() => {
            onSuccess?.();
          })
          .finally(() => {
            setSubmitLoading(false);
          });
      }}
    >
      <div style={{ display: "flex", flexDirection: "column", rowGap: 24 }}>
        <div>
          <FormControl
            name="font_url"
            rule={{ required: true }}
            render={(props, { rule }) => (
              <ControlWrapper
                {...props}
                render={(props) => (
                  <FormControlLabel label="Font URL:" required={rule.required}>
                    <Input {...props}></Input>
                  </FormControlLabel>
                )}
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
        <div>
          <FormControl
            name="font_family"
            rule={{ required: true }}
            render={(props, { rule }) => (
              <ControlWrapper
                {...props}
                render={(props) => (
                  <FormControlLabel label="Font Family:" required={rule.required}>
                    <Input {...props}></Input>
                  </FormControlLabel>
                )}
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
        <div>
          <FormControl
            name="option_id"
            rule={{ required: true }}
            render={(props, { rule }) => (
              <ControlWrapper
                {...props}
                render={(props) => {
                  const { name, value, onChange } = props;
                  return (
                    <FormControlLabel label="Option Id:" required={rule.required}>
                      <InputNumber
                        value={value}
                        onChange={(nextValue) => {
                          onChange(null, { [name]: nextValue });
                        }}
                        style={{ width: `100%` }}
                      ></InputNumber>
                    </FormControlLabel>
                  );
                }}
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
        <div>
          <FormControl
            name="option_name"
            rule={{ required: true }}
            render={(props, { rule }) => (
              <ControlWrapper
                {...props}
                render={(props) => (
                  <FormControlLabel label="Option Name:" required={rule.required}>
                    <Input {...props}></Input>
                  </FormControlLabel>
                )}
              ></ControlWrapper>
            )}
          ></FormControl>
        </div>
        <div style={{ textAlign: "end" }}>
          <Button type="primary" htmlType="submit" loading={submitLoading}>
            Submit
          </Button>
        </div>
      </div>
    </Form>
  );
}

export default AddFontItemForm;
