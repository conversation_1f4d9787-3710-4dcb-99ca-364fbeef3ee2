const Api = require("../../src/fetchers/api");

module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      breadcrumbs: [
        {
          title: "系统配置",
          url: "/",
        },
        {
          title: "网站配置",
          url: "/",
        },
      ],
      menus: [
        {
          key: "general",
          label: "常用",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
        {
          key: "catalog",
          label: "商品",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
        {
          key: "promotion",
          label: "促销",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
        {
          key: "seo",
          label: "SEO",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
        {
          key: "customer",
          label: "用户",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
        {
          key: "email",
          label: "邮件模板",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
        {
          key: "edm",
          label: "邮件营销",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
        {
          key: "tracking",
          label: "用户追踪",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
        {
          key: "discount",
          label: "折扣规则",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
        {
          key: "sales",
          label: "购物车和结算页",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
        {
          key: "aftersales",
          label: "售后",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
        {
          key: "success",
          label: "订单成功页",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
        {
          key: "volcengine",
          label: "火山推荐",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
        {
          key: "payment",
          label: "支付方式",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
        {
          key: "system",
          label: "系统",
          icon_url: "https://static.bizseas.com/static/icon/file.svg",
        },
      ],
      actions: [
        {
          title: "保存",
          props: {
            type: "primary",
          },
          command: {
            type: "submit",
            id: "form",
          },
        },
      ],
      content: {
        component: "JSONComponents",
        type: "json",
        props: {},
        children: [
          {
            component: "Form",
            type: "json",
            props: {
              id: "form",
              initialValues: {
                logo_pc: [
                  {
                    uid: "1",
                    fid: "1",
                    name: "photo",
                    status: "done",
                    url: "https://test-res.jeulia.com/media/system/jeulia/2023/11/28/7/3/6565b931b8737.png?r=260x100",
                  },
                ],
                logo_mobile: [
                  {
                    uid: "2",
                    fid: "2",
                    name: "photo",
                    status: "done",
                    url: "https://test-res.jeulia.com/media/system/jeulia/2023/10/13/4/8/6528c16092484.svg?r=520x220",
                  },
                ],
                favicon: [
                  {
                    uid: "3",
                    fid: "3",
                    name: "photo",
                    status: "done",
                    url: "https://test-res.jeulia.com/media/system/jeulia/2023/08/09/5/0/64d2f4cb3f405.png?r=32x32",
                  },
                ],
              },
              layout: "vertical",
            },
            formItems: [
              {
                component: "Collapse",
                props: {
                  defaultActiveKey: ["1", "2"],
                  ghost: false,
                  styles: { background: "#fff" },
                  items: [
                    {
                      key: "1",
                      label: "图标&文件",
                      children: [
                        {
                          key: "imageUpload",
                          label: "上传图片",
                          component: "ImageUpload",
                          props: {
                            listType: "picture-card",
                            action: Api.uploadFile,
                            data: { disk: "s3-static" },
                            multiple: true,
                          },
                          rules: [{ required: true, message: "This is a required field" }],
                        },
                        {
                          key: "logo_pc",
                          label: "Logo(PC)",
                          component: "ImageUpload",
                          props: {
                            listType: "picture-card",
                            action: Api.uploadFile,
                            data: { disk: "s3-static" },
                            maxCount: 1,
                          },
                          rules: [{ required: true, message: "This is a required field" }],
                        },
                        {
                          key: "logo_mobile",
                          label: "Logo(Mobile)",
                          component: "ImageUpload",
                          props: {
                            listType: "picture-card",
                            action: Api.uploadFile,
                            data: { disk: "s3-static" },
                            maxCount: 1,
                          },
                          rules: [{ required: true, message: "This is a required field" }],
                        },
                        {
                          key: "favicon",
                          label: "Favicon",
                          component: "ImageUpload",
                          props: {
                            listType: "picture-card",
                            action: Api.uploadFile,
                            data: { disk: "s3-static" },
                            maxCount: 1,
                          },
                          rules: [{ required: true, message: "This is a required field" }],
                        },
                      ],
                    },
                    {
                      key: "2",
                      label: "CMS",
                      children: [
                        {
                          component: "Select",
                          key: "homepage",
                          label: "首页",
                          props: {
                            placeholder: "请选择首页",
                            options: [
                              {
                                label: "首页1",
                                value: "1",
                              },
                              {
                                label: "首页2",
                                value: "2",
                              },
                              {
                                label: "首页3",
                                value: "3",
                              },
                            ],
                          },
                          rules: [{ required: true, message: "This is a required field" }],
                        },
                      ],
                    },
                  ],
                },
              },
            ],
            submit: {
              request: {
                url: Api.customer,
                data: {
                  default: "test",
                },
              },
            },
          },
        ],
      },
    },
  });
};
