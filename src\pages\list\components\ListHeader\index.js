import { useLocation, useParams } from "react-router-dom";
import useS<PERSON> from "swr";
import Fetchers from "fetchers";
import { useState, forwardRef } from "react";
import styles from "./index.module.scss";
import Utils from "utils";
import Helper from "helpers";
import Loading from "components/common/Loading";
import JSONComponents from "components/common/JSONComponent";

function ListHeader(props, ref) {
  const location = useLocation();
  const params = useParams();
  const url_key = Helper.getUrlKey({ location, params });
  const queryParams = Utils.getQueryParams(decodeURIComponent(location.search));
  const { layout } = queryParams;
  const [loading, setLoading] = useState(false);

  const { data } = useSWR([url_key], async () => {
    try {
      if (layout === "with_header") {
        setLoading(true);
        // `${params?.url_key}/layout${location.search}`
        const { url_key = "", "*": asterisk } = params;
        const result = await Fetchers.getListPageHeader({
          url_key: `${url_key}${asterisk ? `/${asterisk}` : ""}/layout${location.search}`,
        }).then((res) => res.data?.data);
        setLoading(false);
        return result;
      } else {
        return {};
      }
    } finally {
      setLoading(false);
    }
  });

  if (layout !== "with_header") {
    return null;
  }

  return (
    <Loading loading={loading}>
      <div ref={ref} className={styles.container}>
        <JSONComponents data={data?.content} />
      </div>
    </Loading>
  );
}

ListHeader = forwardRef(ListHeader);

export default ListHeader;
