import styles from "./index.module.scss";
import { Modal, Button, Form, Input, Tabs, Carousel, Image } from "antd";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import Select from "components/common/Select";
import JSONComponents from "components/common/JSONComponent";
import Loading from "components/common/Loading";
import { useRef, useState } from "react";
import Fetchers from "fetchers";

const optionsApi = "/rest/v1/global/taxonomy/search?identifier=defective_tag";
const iframeSrc = "/edit/global/taxonomy/edit?identifier=defective_tag";

function ProductDetailModal(props) {
  const { open, onOk, onCancel, loading, data, onRepair, repairOpen, onRepairOk, onRepairCancel } = props;
  const [repairModalTitle, setRepairModalTitle] = useState("返修");
  const [confirmedRework, setConfirmedRework] = useState(true);
  const [printCardLoading, setPrintCardLoading] = useState(false);

  const formRef = useRef();

  function handleRepairCancel() {
    onRepairCancel?.();
    // setConfirmedRework(false);
  }

  async function handleSubmit() {
    try {
      const values = await formRef.current.validateFields();
      const result = await Fetchers.shipOrderRepair({
        data: {
          item_id: data?.item_id,
          ...values,
        },
      }).then((res) => res?.data);
      if (!result?.success) {
        return;
      }
      onOk?.();
      // onCancel?.();
    } catch (errorInfo) {}
  }

  async function handlePrintCard() {
    try {
      setPrintCardLoading(true);
      await Fetchers.orderPrintCard({ params: { item_id: data?.item_id } }).then((res) => res?.data);
    } finally {
      setPrintCardLoading(false);
    }
  }

  function Footer() {
    return (
      <>
        <Button onClick={handlePrintCard} loading={printCardLoading}>
          打印贺卡
        </Button>
        <Button onClick={onRepair}>返修</Button>
        <Button type="primary" onClick={onOk}>
          合格
        </Button>
      </>
    );
  }

  function RepairFooter() {
    return (
      <div className={styles.repairFooterWrapper}>
        {!confirmedRework ? (
          <>
            <Button
              onClick={() => {
                setConfirmedRework(true);
                setRepairModalTitle("返修");
              }}
            >
              仍然返修
            </Button>
            <Button type="primary" onClick={onRepairOk}>
              确定
            </Button>
          </>
        ) : (
          <>
            <Button onClick={handleRepairCancel}>取消</Button>
            <Button type="primary" onClick={handleSubmit}>
              确定
            </Button>
          </>
        )}
      </div>
    );
  }

  function SlickButtonFix({ currentSlide, slideCount, children, ...otherProps }) {
    return <span {...otherProps}>{children}</span>;
  }

  return (
    <Modal
      className={styles.modal}
      width="1500px"
      open={open}
      onOk={onOk}
      onCancel={onCancel}
      destroyOnClose
      maskClosable={false}
      centered
      title="商品详情"
      closeIcon={true}
      cancelText="返修"
      okText="合格"
      footer={<Footer />}
    >
      <Loading loading={loading}>
        <div className={styles.container}>
          <div className={styles.gallery}>
            <Tabs
              tabPosition="left"
              type="card"
              items={data?.gallery?.map((item, index) => {
                return {
                  label: item?.label,
                  key: index,
                  children: (
                    <div className={styles.carouselWrapper}>
                      <Carousel
                        arrows
                        infinite={false}
                        prevArrow={
                          <SlickButtonFix>
                            <LeftOutlined />
                          </SlickButtonFix>
                        }
                        nextArrow={
                          <SlickButtonFix>
                            <RightOutlined />
                          </SlickButtonFix>
                        }
                      >
                        {item?.images?.map((image, index) => (
                          <Image {...image} width={500} height={500} key={index} preview={false} />
                        ))}
                      </Carousel>
                    </div>
                  ),
                };
              })}
            />
          </div>
          <div className={styles.content}>
            <JSONComponents data={data?.content} />
          </div>
        </div>
      </Loading>

      <Modal
        title={repairModalTitle}
        width="600px"
        open={repairOpen}
        onCancel={handleRepairCancel}
        centered
        footer={<RepairFooter />}
      >
        <div className={styles.repair}>
          {!confirmedRework ? (
            <span className={styles.promptContent}>该商品有可配货库存，你可以直接换一件货，是否先跳过此单验货?</span>
          ) : (
            <Form className={styles.repairForm} ref={formRef} layout="vertical">
              <Form.Item
                name="taxonomy_ids"
                label="原因"
                rules={[
                  {
                    required: true,
                    message: "请选择原因",
                  },
                ]}
              >
                <Select
                  mode="multiple"
                  addOptions={{
                    title: "维护次品原因",
                    command: {
                      type: "modal",
                      closable: true,
                      title: "维护次品原因",
                      props: {
                        width: 1000,
                      },
                      content: {
                        component: "JSONComponents",
                        type: "json",
                        props: {},
                        children: [
                          {
                            component: "Iframe",
                            props: {
                              src: iframeSrc,
                            },
                          },
                        ],
                      },
                    },
                  }}
                  placeholder="次品原因"
                  optionsApi={optionsApi}
                />
              </Form.Item>
              <Form.Item name="note" label="备注">
                <Input.TextArea rows={5} />
              </Form.Item>
            </Form>
          )}
        </div>
      </Modal>
    </Modal>
  );
}

export default ProductDetailModal;
