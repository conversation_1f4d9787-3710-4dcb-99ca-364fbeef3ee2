.optionSetControlEditor {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.checkboxRow {
  display: flex;
  flex-direction: column;

  [class~="form-x-control"] {
    flex: 1;

    > div {
      height: 100%;

      > div {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }
  }

  .controlBox {
    flex: 1;
    display: flex;
    align-items: center;
  }
}

.innerCollapse {
  [class~="ant-collapse-header"] {
    //background-color: var(--ant-primary-color);
    //color: #fff !important;
  }
}

.flexGrid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.flexRow {
  display: flex;
  gap: 16px;
  align-items: center;

  > div {
    flex: 0.3;
    max-width: 270px;
  }
}

.controlBox {
  display: flex;
  align-items: center;
  gap: 8px;
}

.connectText {
  white-space: nowrap;
}

.conditions {
  .item {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;

    [class~="ant-radio-group"] {
      white-space: nowrap;
    }
  }
}

.dropdownIcon {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;
  user-select: none;
}
