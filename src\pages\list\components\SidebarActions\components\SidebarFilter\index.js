import styles from "./index.scss";
import Enums from "enums";
import { forwardRef, useImperativeHandle, useRef, useState, useEffect, lazy, Suspense } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Collapse, Form, Input, Select, Checkbox, Radio } from "antd";
import DatePicker from "components/common/DatePicker";
import AddonAfterText from "./components/AddonAfterText";
import SpaceWrapper from "@/components/business/SpaceWrapper";

import Utils from "utils";
import { afterRender } from "functions";
import debounce from "lodash.debounce";

const delay = 500;

function SidebarFilter(props, ref) {
  const { data, onChange, isFlattenQueryParams } = props;
  const { batchFilters } = data;
  const location = useLocation();
  const navigate = useNavigate();
  const filterParams = getFilterParams();
  const formRef = useRef();
  const [values] = useState({ ...data?.props?.initialValues, ...filterParams });

  useImperativeHandle(ref, () => {
    return {
      get form() {
        return formRef.current;
      },
      clearFilters,
    };
  });

  function findFilterByKey(key) {
    // 检查 batchFilters
    const flatBatchFilters = Utils.flatTree({ treeData: batchFilters });
    const batchFilter = flatBatchFilters?.find((item) => item.key === key);
    if (batchFilter) return batchFilter;

    // 检查 groupFilters
    const groupFilter = data?.groupFilters?.items?.reduce((found, group) => {
      if (found) return found;
      return group.children?.find((item) => item.key === key);
    }, null);

    return groupFilter;
  }

  function isMultiSelectComponent(filter) {
    return (
      (filter?.component === Enums.Components.Select && filter?.props?.mode === "multiple") ||
      filter?.component === Enums.Components.Checkbox
    );
  }

  function getFilterParams() {
    const queryParams = Utils.getQueryParams(decodeURIComponent(location.search));
    const filterParams = {};
    Object.keys(queryParams).forEach((key) => {
      const dataIndex = /^sidebar_filter\[(.+)\]/.exec(key)?.[1];
      if (dataIndex) {
        const filter = findFilterByKey(dataIndex);
        const isMultiSelect = isMultiSelectComponent(filter);
        const filterKeyType = typeof filter?.props?.options?.[0]?.value;

        // 如果是多选 Select，将逗号分隔的字符串转换为数组
        const value = isMultiSelect ? queryParams[key]?.split(",") : queryParams[key];
        if (filterKeyType === "number") {
          filterParams[dataIndex] = isMultiSelect && value ? value.split(",").map((item) => +item) : +value;
        } else {
          filterParams[dataIndex] = isMultiSelect && value ? value.split(",") : value;
        }
      }
    });
    return filterParams;
  }

  function clearFilters() {
    const searchParams = new URLSearchParams(decodeURIComponent(location.search));
    [...searchParams.keys()].forEach((key) => {
      if (key.startsWith("sidebar_filter[")) {
        searchParams.delete(key);
      }
    });
    searchParams.delete("page");
    updateSearchParams({ searchParams });
    formRef.current?.resetFields();
  }

  function updateSearchParams({ searchParams }) {
    navigate({ pathname: location.pathname, search: decodeURIComponent(searchParams.toString()) });
  }

  function getFilterKey(dataIndex) {
    return `sidebar_filter[${dataIndex}]`;
  }

  function getSearchParams({ changedValues, values }) {
    const searchParams = new URLSearchParams(location.search);
    const searchKey = isFlattenQueryParams
      ? Object.keys(changedValues)[0]
      : getFilterKey(Object.keys(changedValues)[0]);
    const searchValue = Object.values(changedValues)[0];
    // 如果有 addonAfter 则同时更新 addonAfter 的值
    const addonAfter = batchFilters.filter((item) => item.key === Object.keys(changedValues)[0])?.[0]?.addonAfter;
    const addonAfterSearchKey = addonAfter?.key ? getFilterKey(addonAfter?.key) : null;

    if (searchValue && Object.prototype.toString.call(searchValue) !== "[object Object]") {
      searchParams.set(searchKey, searchValue);
      if (addonAfterSearchKey) {
        searchParams.set(addonAfterSearchKey, values[addonAfter?.key]);
      }
    } else {
      searchParams.delete(searchKey);
      if (addonAfterSearchKey) {
        searchParams.delete(addonAfterSearchKey);
      }
    }
    searchParams.delete("page"); // 删除页码
    return searchParams;
  }

  function onValuesChange(changedValues, values) {
    const searchParams = getSearchParams({ changedValues, values });

    updateSearchParams({ searchParams });
    formRef.current?.setFieldsValue(changedValues);
    onChange?.(changedValues, values);
  }

  function handleFilter({ field }) {
    const changedValues = formRef.current?.getFieldValue(field);
    const values = formRef.current?.getFieldsValue();
    onValuesChange({ [field]: changedValues }, values);
  }

  function renderFormItem(item) {
    const { component } = item;

    if (component === Enums.Components.Text) {
      return <AddonAfterText data={item} />;
    } else if (component === Enums.Components.Select) {
      return <Select {...item?.props} />;
    } else if (component === Enums.Components.Input) {
      return <Input {...item?.props} {...inputProps(item)} />;
    } else if (component === Enums.Components.Checkbox) {
      return <Checkbox.Group {...item?.props} />;
    } else if (component === Enums.Components.Radio) {
      return <Radio.Group {...item?.props} />;
    } else if (component === Enums.Components.RangePicker) {
      return <DatePicker.RangePicker {...item.props} />;
    } else if (component === Enums.Components.SpaceWrapper) {
      const { children, ...restProps } = item;
      const spaceChildren = children.map((child) => renderFormItem(child));
      return <SpaceWrapper {...restProps} children={spaceChildren} />;
    }

    return null;
  }

  function renderComponent(item, index) {
    const { component, props } = item;

    if (component === Enums.Components.SpaceWrapper) {
      const { children } = item;
      const renderedChildren = children.map((child) => renderComponent(child));
      return (
        <SpaceWrapper key={index} {...props}>
          {renderedChildren}
        </SpaceWrapper>
      );
    } else {
      const { label, key: name } = item;
      return (
        <Form.Item key={name} name={name} label={label}>
          {renderFormItem(item)}
        </Form.Item>
      );
    }
  }

  function inputProps(item) {
    const field = item?.addonAfter?.key;

    return {
      addonAfter: item?.addonAfter ? (
        <Form.Item key={field} name={field}>
          {renderComponent(item?.addonAfter)}
        </Form.Item>
      ) : null,
      onPressEnter: () => handleFilter({ field }),
      ...item?.props,
    };
  }

  function renderGroupFilter() {
    const collapseItems = Utils.cloneDeep(data?.groupFilters?.items);
    const activeKey = collapseItems?.map((item) => item?.key);
    const items = collapseItems?.map((item) => {
      item.children = <div>{item?.children?.map((formItem, index) => renderComponent(formItem, index))}</div>;
      return item;
    });

    return (
      <Collapse
        collapsible="header"
        expandIconPosition="end"
        size="small"
        {...data?.groupFilters?.props}
        items={items}
        defaultActiveKey={activeKey}
      ></Collapse>
    );
  }

  useEffect(() => {
    afterRender(() => {
      formRef.current?.setFieldsValue(values);
    });
  }, [values]);

  return (
    <div className={styles.sidebarFilter}>
      <Form
        {...data?.props}
        ref={formRef}
        name="sidebarFilterForm"
        size="small"
        onValuesChange={debounce(
          (changedValue, values) => {
            onValuesChange(changedValue, values);
          },
          [delay]
        )}
        scrollToFirstError
      >
        <div className={styles.batchFilters}>{batchFilters?.map((item, index) => renderComponent(item, index))}</div>
        {data?.groupFilters?.items?.length > 0 && <div className={styles.groupFilters}>{renderGroupFilter()}</div>}
      </Form>
    </div>
  );
}

SidebarFilter = forwardRef(SidebarFilter);

export default SidebarFilter;
