const Api = require("../../../src/fetchers/api");

module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      basic_info: {
        component: "Row",
        props: { gutter: [16, 6] },
        children: [
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "code",
                label: "编号",
                component: "Text",
                props: {},
                content: "JE20230719210",
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "order_no",
                label: "订单号",
                component: "Text",
                props: {},
                content: "JEJP030964",
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "order_status",
                label: "订单状态",
                component: "Text",
                props: {},
                content:
                  "<span style='color:red;padding: 5px 10px;border:1px solid red;border-radius: 10px;'>待发货</span>",
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "create_time",
                label: "创建时间",
                component: "Text",
                props: {},
                content: "2023-07-19 22:04:41",
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "pay_time",
                label: "支付时间",
                component: "Text",
                props: {},
                content: "2023-07-19 22:04:41",
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "pay_status",
                label: "支付状态",
                component: "Text",
                props: {},
                content: "已支付",
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "pay_type",
                label: "付款方式",
                component: "Text",
                props: {},
                content: "adyen_scheme",
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "total_amount",
                label: "总金额(退款后剩余)/ 最初支付",
                component: "Text",
                props: {},
                content: "11781(JPY) / 11781(JPY)",
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "warehouse",
                label: "仓库",
                component: "Text",
                props: {},
                content: "广州仓",
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "logistics",
                label: "物流方式",
                component: "Text",
                props: {},
                content: "LTW （standard）",
                editable: {
                  component: "Select",
                  props: {
                    // mode: "multiple", // 多选
                  },
                  // 支持远程搜索
                  searchApi: Api.searchProductSpu,
                  request: {
                    url: Api.order,
                    data: { id: 1 },
                  },
                },
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "logistics_number",
                label: "物流单号",
                component: "Text",
                props: {},
                content: "518797228110",
                editable: { component: "Input", props: {}, request: { url: Api.order, data: { id: 1 } } },
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "order_site",
                label: "订单站点",
                component: "Text",
                props: {},
                content: "JE-日本站",
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "freight_insurance",
                label: "运费险",
                component: "Text",
                props: {},
                content: "0(JPY)",
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
          },
          {
            component: "Col",
            props: { span: 8 },
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "customer_level",
                label: "客户等级(当前等级/下单时等级) ",
                component: "Text",
                props: {},
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "remote_payment",
                label: "偏远费/偏远费优惠",
                component: "Text",
                props: {},
                content: "0(JPY) /0(JPY)",
              },
            ],
          },
          {
            component: "Col",
            props: { span: 8 },
            children: [
              {
                key: "tax_type",
                label: "消费税类型",
                component: "Text",
                props: {},
                content: "0(JPY)",
              },
            ],
          },
        ],
      },
      address: {
        component: "JSONComponents",
        type: "json",
        children: [
          {
            component: "NativeTable",
            props: {},
            children: [
              [
                {
                  tag: "th",
                  valueType: "text",
                  value: "地址",
                  props: {
                    style: {
                      width: "50px",
                    },
                  },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "详情",
                  props: {
                    style: {
                      width: "50px",
                    },
                  },
                },
              ],
            ],
          },
        ],
        actions: [
          {
            label: "修改地址",
            command: {
              type: "modal",
              closable: true,
              title: "",
              props: {
                width: "800px",
                maskClosable: true,
                closeIcon: null,
              },
              content: {
                component: "JSONComponents",
                type: "json",
                children: [
                  {
                    component: "Card",
                    props: {
                      title: "产品信息",
                      styles: {
                        header: {
                          backgroundColor: "#343a40",
                          color: "#FFFFFF",
                        },
                      },
                    },
                    children: [
                      {
                        component: "NativeTable",
                        props: [],
                        children: [
                          [
                            {
                              tag: "th",
                              props: {
                                style: {
                                  width: "100px",
                                },
                              },
                              value: "SPU",
                            },
                            {
                              tag: "td",
                              value: "SG30009-C3",
                            },
                            {
                              tag: "th",
                              props: {
                                style: {
                                  width: "100px",
                                },
                              },
                              value: "SKU",
                            },
                            {
                              tag: "td",
                              value: 1094074,
                            },
                          ],
                          [
                            {
                              tag: "th",
                              value: "颜色|材质",
                            },
                            {
                              tag: "td",
                              value: "",
                            },
                            {
                              tag: "th",
                              value: "尺码",
                            },
                            {
                              tag: "td",
                              value: "宽",
                            },
                          ],
                          [
                            {
                              tag: "th",
                              value: "产品图",
                            },
                            {
                              tag: "td",
                              valueType: "image",
                              props: {
                                colSpan: 3,
                              },
                              value: [
                                {
                                  src: "https://assets.cnzlerp.com/product/uploads/product/5/2/63fec52465d25.jpg",
                                  width: 100,
                                  height: 100,
                                },
                                {
                                  src: "https://assets.cnzlerp.com/product/uploads/product/c/e/63fec524baeec.jpg",
                                  width: 100,
                                  height: 100,
                                },
                                {
                                  src: "https://assets.cnzlerp.com/product/uploads/product/a/e/63fec524cd4ea.jpg",
                                  width: 100,
                                  height: 100,
                                },
                              ],
                            },
                          ],
                          [
                            {
                              tag: "th",
                              value: "最终效果图",
                            },
                            {
                              tag: "td",
                              valueType: "image",
                              value: [
                                {
                                  src: "https://res2022.aoolia.com/product/a/e/63fec524cd4ea.jpg",
                                  width: 100,
                                  height: 100,
                                },
                              ],
                            },
                            {
                              tag: "th",
                              value: "生产元素图",
                            },
                            {
                              tag: "td",
                              valueType: "image",
                              value: [],
                            },
                          ],
                        ],
                      },
                    ],
                  },
                  {
                    component: "Card",
                    props: {
                      title: "定制信息",
                      styles: {
                        header: {
                          backgroundColor: "#343a40",
                          color: "#FFFFFF",
                        },
                      },
                    },
                    children: [
                      {
                        component: "NativeTable",
                        props: [],
                        children: [
                          [
                            {
                              tag: "th",
                              props: {
                                style: {
                                  width: "100px",
                                },
                              },
                              value: "尺码",
                            },
                            {
                              tag: "td",
                              value: "宽",
                            },
                          ],
                        ],
                      },
                    ],
                  },
                ],
              },
            },
          },
        ],
      },
      customs_declaration: {
        component: "JSONComponents",
        type: "json",
        children: [
          {
            component: "Form",
            type: "json",
            formItems: [
              {
                component: "Row",
                children: [
                  {
                    component: "Col",
                    props: { span: 8 },
                    children: [
                      {
                        key: "text",
                        label: "当前报关方式",
                        component: "Text",
                        props: {},
                        content: "类目报关",
                      },
                    ],
                  },
                  {
                    component: "Col",
                    props: { span: 8 },
                    children: [
                      {
                        key: "text",
                        label: "申报总价",
                        component: "Text",
                        props: {},
                        content: "20（$)",
                      },
                    ],
                  },
                  {
                    component: "Col",
                    props: { span: 8 },
                    children: [
                      {
                        key: "text",
                        label: "申报总重量",
                        component: "Text",
                        props: {},
                        content: "20（KG)",
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            component: "NativeTable",
            props: {},
            children: [
              [
                {
                  tag: "th",
                  valueType: "text",
                  value: "spu",
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "sku",
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "商品码",
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "产品分类",
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "商品名称",
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "申报品名",
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "申报品名（中文）",
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "申报单价",
                },
              ],
            ],
          },
        ],
      },
      product_info: {
        component: "JSONComponents",
        type: "json",
        children: [
          {
            component: "NativeTable",
            props: {},
            children: [
              [
                {
                  tag: "th",
                  valueType: "text",
                  value: "商品",
                  props: { style: { background: "#f3f3f3" } },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "spu",
                  props: { style: { background: "#f3f3f3" } },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "sku",
                  props: { style: { background: "#f3f3f3" } },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "商品码",
                  props: { style: { background: "#f3f3f3" } },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "数量",
                  props: { style: { background: "#f3f3f3" } },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "主属性",
                  props: { style: { background: "#f3f3f3" } },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "次属性",
                  props: { style: { background: "#f3f3f3" } },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "刻字",
                  props: { style: { background: "#f3f3f3" } },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "合成图",
                  props: { style: { background: "#f3f3f3" } },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "tag",
                  props: { style: { background: "#f3f3f3" } },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "操作",
                  props: { style: { background: "#f3f3f3" } },
                },
              ],
              [
                {
                  tag: "td",
                  valueType: "image",
                  value: [
                    {
                      width: 50,
                      height: 50,
                      src: "https://res2022.shesaidyes.com/uploads/produce-image/order/1286564/stone_setting_fids/2/3/660d16799b332.jpg",
                    },
                  ],
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "002",
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "1223",
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "123456",
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "3",
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "1",
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "1",
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "1",
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "1",
                },
                {
                  tag: "td",
                  valueType: "tag",
                  value: [
                    {
                      text: "加急",
                      props: {
                        color: "orange",
                      },
                    },
                    {
                      text: "高级定制",
                      props: {
                        color: "red",
                      },
                    },
                  ],
                },
                {
                  tag: "td",
                  valueType: "command",
                  value: "查看详情",
                  command: {
                    type: "message",
                    config: {
                      type: "success",
                      content: "提示文案",
                      duration: 3,
                    },
                  },
                },
              ],
            ],
          },
        ],
      },
      remark: {
        component: "JSONComponents",
        type: "json",
        children: [
          {
            component: "NativeTable",
            props: {},
            children: [
              [
                {
                  tag: "th",
                  valueType: "text",
                  value: "备注人",
                  props: { style: { width: "100px", background: "#f3f3f3" } },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "备注时间",
                  props: { style: { width: "200px", background: "#f3f3f3" } },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "备注内容",
                  props: { style: { background: "#f3f3f3" } },
                },
              ],
              [
                {
                  tag: "td",
                  valueType: "text",
                  value: "杨欢欢",
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "2023-07-14 10:39:45",
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "退回单号，9500112839263199601898，核实换货信息+地址。    ",
                },
              ],
            ],
          },
        ],
        actions: [
          {
            label: "edit",
            command: {
              type: "message",
              config: {
                type: "success",
                content: "这里是编辑command",
                duration: 3,
              },
            },
          },
        ],
      },
      log: {
        component: "JSONComponents",
        type: "json",
        children: [
          {
            component: "NativeTable",
            props: {},
            children: [
              [
                {
                  tag: "th",
                  valueType: "text",
                  value: "操作日志",
                  props: { style: { width: "100px", background: "#f3f3f3" } },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "操作时间",
                  props: { style: { width: "200px", background: "#f3f3f3" } },
                },
                {
                  tag: "th",
                  valueType: "text",
                  value: "内容",
                  props: { style: { background: "#f3f3f3" } },
                },
              ],
              [
                {
                  tag: "td",
                  valueType: "text",
                  value: "杨欢欢",
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "2023-07-14 10:39:45",
                },
                {
                  tag: "td",
                  valueType: "text",
                  value: "test内容",
                },
              ],
            ],
          },
        ],
      },
      tags: [
        { text: "加急", color: "error" },
        { text: "风控", style: { color: "#fff", backgroundColor: "#f00", borderColor: "#f00" } },
        { text: "加急", color: "error" },
        { text: "风控", style: { color: "#fff", backgroundColor: "#f00", borderColor: "#f00" } },
        { text: "加急", color: "error" },
        { text: "风控", style: { color: "#fff", backgroundColor: "#f00", borderColor: "#f00" } },
      ],
      actions: [
        {
          label: "取消面单",
          command: {
            type: "modal",
            closable: true,
            title: "Api Components",
            props: {
              width: 1000,
            },
            footer: [
              {
                title: "确定",
                props: { type: "primary" },
                command: {
                  type: "submit",
                  id: "form1",
                },
              },
            ],
            content: {
              component: "JSONComponents",
              type: "api",
              props: {},
              fetcher: {
                request: {
                  url: Api.getApiJsonComponents,
                  data: {},
                },
              },
            },
          },
        },
        {
          label: "生成面单",
          command: {
            type: "modal",
            closable: true,
            title: "Api Components",
            props: {
              width: 1000,
            },
            footer: [
              {
                title: "确定",
                props: { type: "primary" },
                command: {
                  type: "submit",
                  id: "form1",
                },
              },
            ],
            content: {
              component: "JSONComponents",
              type: "api",
              props: {},
              fetcher: {
                request: {
                  url: Api.getApiJsonComponents,
                  data: {},
                },
              },
            },
          },
        },
        {
          label: "撤销发货",
          command: {
            type: "modal",
            closable: true,
            title: "Api Components",
            props: {
              width: 1000,
            },
            footer: [
              {
                title: "确定",
                props: { type: "primary" },
                command: {
                  type: "submit",
                  id: "form1",
                },
              },
            ],
            content: {
              component: "JSONComponents",
              type: "api",
              props: {},
              fetcher: {
                request: {
                  url: Api.getApiJsonComponents,
                  data: {},
                },
              },
            },
          },
        },
        {
          label: "添加标签",
          command: {
            type: "modal",
            closable: true,
            title: "Api Components",
            props: {
              width: 1000,
            },
            footer: [
              {
                title: "确定",
                props: { type: "primary" },
                command: {
                  type: "submit",
                  id: "form1",
                },
              },
            ],
            content: {
              component: "JSONComponents",
              type: "api",
              props: {},
              fetcher: {
                request: {
                  url: Api.getApiJsonComponents,
                  data: {},
                },
              },
            },
          },
        },
      ],
    },
  });
};
