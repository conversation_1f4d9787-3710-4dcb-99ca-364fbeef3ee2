import { App, Image, Upload } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import Utils from "utils";
import Enums from "enums";
import SortableItem from "@/components/common/SortableItem";
import { closestCenter, DndContext, PointerSensor, useSensor, useSensors } from "@dnd-kit/core";
import { arrayMove, SortableContext } from "@dnd-kit/sortable";
import axios from "fetchers/request";
import PropTypes from "prop-types";
import Fetchers from "@/fetchers";
import Loading from "@/components/common/Loading";
import classNames from "classnames";

function ImageUpload(props, ref) {
  const {
    defaultFileList = [],
    fileList: propFileList = [],
    onChange,
    maxCount = props.multiple ? 100 : 1,
    multiple = props.maxCount > 1,
    action = "http://localhost:8081/rest/v1/upload",
    beforeUpload,
    width,
    height,
    itemRender,
    disabled,
  } = props;
  const [innerFileList, setInnerFileList] = useState(defaultFileList);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);
  const [items, setItems] = useState([]);
  const sensors = useSensors(useSensor(PointerSensor));
  const { modal } = App.useApp();

  const isControlled = Utils.hasOwnProperty(props, "fileList");
  const fileList = isControlled ? propFileList : innerFileList;

  function handleChange(file, fileList) {
    onChange?.(file, fileList);
    setInnerFileList(fileList);
  }

  async function handlePreview(file) {
    if (!file.url && !file.preview) {
      file.preview = await Utils.readAsDataUrl(file.originFileObj);
    }
    setPreviewOpen(true);
    setPreviewImage(file.url || file.preview);
  }

  async function handleRemove(file) {
    const { fid, url } = file;
    if (action) {
      await new Promise((resolve, reject) => {
        modal.confirm({
          title: "确定删除吗？",
          onOk: async () => {
            return await axios({
              url: action,
              method: "DELETE",
              data: { fid, url },
            })
              .then(resolve)
              .catch(reject);
          },
        });
      });
    }
    file.status = Enums.UploadFileStatus.Removed;
    const nextFileList = fileList.filter((item) => item.uid !== file.uid);
    handleChange(file, nextFileList);
  }

  function handleDragEnd(event) {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const nextItems = arrayMove(items, oldIndex, newIndex);
        const nextFileList = arrayMove(fileList, oldIndex, newIndex);
        setItems(nextItems);
        handleChange(
          nextFileList.find((item) => item.id === over.id),
          nextFileList
        );
        return nextItems;
      });
    }
  }

  useImperativeHandle(ref, () => {
    return {
      get fileList() {
        return fileList;
      },
      set fileList(fileList) {
        setInnerFileList(fileList);
      },
    };
  });

  return (
    <div
      className={classNames(styles.imageUpload, { [styles.disabled]: disabled })}
      style={{ "--upload-image-v2-width": `${width}px`, "--upload-image-v2-height": `${height}px` }}
    >
      <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <SortableContext items={items}>
          <Upload
            {...props}
            listType="picture-card"
            disabled={disabled}
            beforeUpload={(file, fileList) => {
              if (action) {
                if (beforeUpload) {
                  return beforeUpload(file, fileList);
                } else {
                  Fetchers.uploadFile({ file }).then((res) => {
                    file.status = Enums.UploadFileStatus.Done;
                    file.url = res.data.data.host + res.data.data.image.src;
                    file.response = res.data;
                    handleChange(file, fileList);
                  });
                  return false;
                }
              } else {
                return false;
              }
            }}
            fileList={fileList}
            onChange={({ file, fileList }) => {
              if (file.status === Enums.UploadFileStatus.Done && file.response.success) {
                const src = file.response?.data?.host + file.response?.data?.image?.src;
                file.fid = file.uid;
                file.url = src;
              }
              handleChange(file, fileList);
            }}
            onPreview={handlePreview}
            onRemove={handleRemove}
            itemRender={
              itemRender ||
              function (item, file, fileList, actions) {
                return (
                  <SortableItem id={file.uid}>
                    <Loading loading={file.status === Enums.UploadFileStatus.Uploading}>{item}</Loading>
                  </SortableItem>
                );
              }
            }
          >
            {(() => {
              if (multiple) {
                if (fileList?.length < maxCount) {
                  return true;
                }
              } else {
                if (fileList?.length < 1) {
                  return true;
                }
              }
            })() ? (
              <div className={styles.uploadButton}>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>Upload</div>
              </div>
            ) : null}
          </Upload>
        </SortableContext>
      </DndContext>
      <Image
        style={{ display: "none" }}
        preview={{
          visible: previewOpen,
          onVisibleChange: () => {
            setPreviewOpen(false);
          },
          src: previewImage,
          scaleStep: 0.15,
        }}
      />
    </div>
  );
}

ImageUpload = forwardRef(ImageUpload);

ImageUpload.propTypes = {
  fileList: PropTypes.array,
  onChange: PropTypes.func,
  // listType: PropTypes.oneOf(["text", "picture", "picture-card", "picture-circle"]),
  multiple: PropTypes.bool,
  maxCount: PropTypes.number,
  action: PropTypes.string,
  beforeUpload: PropTypes.func,
  itemRender: PropTypes.func,
  disabled: PropTypes.bool,
};

export default ImageUpload;
