import { Fragment } from "react";
import { But<PERSON>, Space, Modal, Form, Input, Flex, Divider } from "antd";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";

import JSONEditor from "components/common/JSONEditor";
import LowCodeEditor from "@/pages/low-code/editor";
import Utils from "utils";

import useToggle from "../../hooks/useToggle";

import styles from "./index.module.scss";

const EditFormModal = ({ data, name, open, onCancel, onFinish, confirmLoading, children }) => {
  const [form] = Form.useForm();

  const initialValues = { [name]: data };

  return (
    <Modal
      open={open}
      destroyOnClose
      closable={false}
      okText="保存"
      okButtonProps={{ autoFocus: true, htmlType: "submit", loading: confirmLoading }}
      onCancel={onCancel}
      modalRender={(dom) => (
        <Form
          layout="vertical"
          form={form}
          name={`edit_page_${name}`}
          initialValues={initialValues}
          onFinish={onFinish}
          autoComplete="off"
        >
          {dom}
        </Form>
      )}
    >
      {typeof children === "function" ? children({ form, name }) : children}
    </Modal>
  );
};

const BreadcrumbsButton = ({ data, onSave }) => {
  const [open, { toggle: onToggle }] = useToggle();
  const [confirmLoading, { toggle: toggleConfirmLoading }] = useToggle();

  const onFinish = async (values) => {
    toggleConfirmLoading();
    await Utils.sleep(300);
    onSave?.(values);
    toggleConfirmLoading();
    onToggle();
  };

  return (
    <>
      <Button type="primary" onClick={onToggle}>
        编辑面包屑
      </Button>
      <EditFormModal
        data={data}
        open={open}
        name="breadcrumbs"
        confirmLoading={confirmLoading}
        onCancel={onToggle}
        onFinish={onFinish}
      >
        {({ name }) => (
          <Form.List name={name}>
            {(fields, { add, remove }) => {
              return (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Space key={key} style={{ display: "flex", marginBottom: 8 }} align="baseline">
                      <Form.Item {...restField} name={[name, "title"]} rules={[{ required: true }]}>
                        <Input placeholder="Title" />
                      </Form.Item>
                      <Form.Item {...restField} name={[name, "url"]} rules={[{ required: true }]}>
                        <Input placeholder="Url" />
                      </Form.Item>
                      <MinusCircleOutlined onClick={() => remove(name)} />
                    </Space>
                  ))}
                  <Form.Item>
                    <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                      添加面包屑
                    </Button>
                  </Form.Item>
                </>
              );
            }}
          </Form.List>
        )}
      </EditFormModal>
    </>
  );
};

const JSON_SERIALIZE_FIELDS = ["props", "command"];

const prepareFormData = (data, fields = JSON_SERIALIZE_FIELDS) => {
  return (data ?? []).map((item) => {
    const processed = { ...item };

    fields.forEach((field) => {
      if (Utils.hasOwnProperty(item, field)) {
        processed[field] = JSON.stringify(item[field], null, 2);
      }
    });

    return processed;
  });
};

const parseFormData = (data, fields = JSON_SERIALIZE_FIELDS) => {
  return (data ?? []).map((item) => {
    const processed = { ...item };

    fields.forEach((field) => {
      if (Utils.hasOwnProperty(item, field)) {
        processed[field] = Utils.JSON.parse(item[field]);
      }
    });

    return processed;
  });
};

const ActionsButton = ({ data, onSave }) => {
  const [open, { toggle: onToggle }] = useToggle();
  const [confirmLoading, { toggle: toggleConfirmLoading }] = useToggle();
  const name = "actions";

  const onFinish = async (values) => {
    toggleConfirmLoading();
    const parsedValues = {
      ...values,
      [name]: parseFormData(values[name]),
    };

    await Utils.sleep(300);
    onSave?.(parsedValues);
    toggleConfirmLoading();
    onToggle();
  };

  return (
    <>
      <Button type="primary" onClick={onToggle}>
        编辑操作项
      </Button>
      <EditFormModal
        data={prepareFormData(data)}
        open={open}
        name={name}
        confirmLoading={confirmLoading}
        onCancel={onToggle}
        onFinish={onFinish}
      >
        {({ form, name }) => (
          <Form.List name={name}>
            {(fields, { add, remove }) => {
              return (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Fragment key={key}>
                      <Flex align="center" gap="middle">
                        <Space size="small" direction="vertical" style={{ width: "100%" }}>
                          <Form.Item
                            {...restField}
                            label="Title"
                            name={[name, "title"]}
                            rules={[{ required: true, message: "请输入 title" }]}
                          >
                            <Input placeholder="Title" />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            label="Props"
                            name={[name, "props"]}
                            rules={[{ required: true, message: "请输入 props" }]}
                          >
                            <JSONEditor
                              height="150px"
                              validate={() => {
                                form.validateFields([name, "props"]);
                              }}
                            />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            label="Command"
                            name={[name, "command"]}
                            rules={[{ required: true, message: "请输入 command" }]}
                          >
                            <JSONEditor
                              height="150px"
                              validate={() => {
                                form.validateFields([name, "command"]);
                              }}
                            />
                          </Form.Item>
                        </Space>
                        <MinusCircleOutlined onClick={() => remove(name)} />
                      </Flex>
                      <Divider size="small" />
                    </Fragment>
                  ))}
                  <Form.Item>
                    <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                      添加操作项
                    </Button>
                  </Form.Item>
                </>
              );
            }}
          </Form.List>
        )}
      </EditFormModal>
    </>
  );
};

const ContentButton = ({ data, onSave }) => {
  const [open, { toggle: onToggle }] = useToggle();

  const initialComponents = data?.children ?? [];

  const handleSave = (value) => {
    onSave?.({
      content: {
        ...data,
        ...value,
      },
    });
    onToggle();
  };

  return (
    <>
      <Button type="primary" onClick={onToggle}>
        编辑内容
      </Button>
      <Modal open={open} width={1400} destroyOnClose onCancel={onToggle} footer={null}>
        <LowCodeEditor initialComponents={initialComponents} showExportButton={false} onSave={handleSave} />
      </Modal>
    </>
  );
};

function RightPanel({ data, onBreadcrumbSave, onActionSave, onContentSave }) {
  return (
    <div className={styles.container}>
      <Space direction="vertical" size="middle">
        <BreadcrumbsButton data={data?.breadcrumbs} onSave={onBreadcrumbSave} />
        <ActionsButton data={data?.actions} onSave={onActionSave} />
        <ContentButton data={data?.content} onSave={onContentSave} />
      </Space>
    </div>
  );
}

export default RightPanel;
