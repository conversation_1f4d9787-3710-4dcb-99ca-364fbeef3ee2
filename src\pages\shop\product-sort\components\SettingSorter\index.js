import { Drawer, Form, Button, Space, Switch, Input<PERSON><PERSON>ber, Tooltip } from "antd";
import { SettingOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import Fetchers from "fetchers";

const DRAWER_WIDTH = 500;

function SettingDrawer({ open, onToggle, initialValues, isMainSite = false, categoryId, refreshData }) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  async function handleSubmit() {
    try {
      setLoading(true);
      const values = form.getFieldsValue();
      const result = await Fetchers.updateShopProductSortSetting({ data: { id: categoryId, ...values } }).then(
        (res) => res.data
      );
      if (result.success) {
        onToggle?.();
        window.location.reload();
      }
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [form, initialValues]);

  return (
    <Drawer
      title="商品排序设置"
      open={open}
      onClose={onToggle}
      width={DRAWER_WIDTH}
      destroyOnClose
      extra={
        <Space>
          <Button onClick={onToggle}>取消</Button>
          <Button type="primary" loading={loading} onClick={handleSubmit}>
            保存
          </Button>
        </Space>
      }
    >
      <div>
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          {!isMainSite ? (
            <Form.Item name="is_follow_main_site" label="跟随主站排序">
              <Switch />
            </Form.Item>
          ) : null}

          <Form.Item
            shouldUpdate={(prev, curr) =>
              prev.is_follow_main_site !== curr.is_follow_main_site || prev.is_auto_sort !== curr.is_auto_sort
            }
          >
            {({ getFieldValue }) => {
              const isFollowMainSite = getFieldValue("is_follow_main_site");
              const isAutoSort = getFieldValue("is_auto_sort");

              return (
                <>
                  {!isFollowMainSite ? (
                    <Form.Item name="is_auto_sort" label="开启商品自动排序">
                      <Switch />
                    </Form.Item>
                  ) : null}
                  {isAutoSort && !isFollowMainSite ? (
                    <Form.Item name="top_num" label="排序截止位置">
                      <InputNumber style={{ width: "100%" }} />
                    </Form.Item>
                  ) : null}
                </>
              );
            }}
          </Form.Item>
        </Form>
      </div>
    </Drawer>
  );
}

function SettingSorter({ data, categoryId, refreshData }) {
  const [open, setOpen] = useState(false);
  const initialValues = {
    is_follow_main_site: data?.is_follow_main_site,
    is_auto_sort: data?.is_auto_sort,
    top_num: data?.top_num,
  };

  function handleToggle() {
    setOpen(!open);
  }

  return (
    <>
      <Tooltip title="商品排序设置">
        <Button type="primary" shape="circle" icon={<SettingOutlined />} onClick={handleToggle} />
      </Tooltip>
      <SettingDrawer
        open={open}
        onToggle={handleToggle}
        initialValues={initialValues}
        isMainSite={data?.is_main_site}
        categoryId={categoryId}
        refreshData={refreshData}
      />
    </>
  );
}

export default SettingSorter;
