import styles from "./index.module.scss";
import { useState, useEffect } from "react";
import { Form, Input, Select, Button, Space, Modal } from "antd";
import { PlusOutlined, DeleteOutlined, EditOutlined } from "@ant-design/icons";
import JSONEditor from "components/common/JSONEditor";
import CommandConfigPrompt from "../CommandConfigPrompt";

function ToolbarActionsEditor({ value = [], onChange }) {
  const [actions, setActions] = useState(value);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentAction, setCurrentAction] = useState(null);
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [form] = Form.useForm();

  useEffect(() => {
    setActions(value);
  }, [value]);

  const showModal = (action, index) => {
    setCurrentAction(action);
    setCurrentIndex(index);
    form.setFieldsValue(action);
    setModalVisible(true);
  };

  const handleAdd = () => {
    const newAction = {
      title: "",
      icon: "https://images.aoolia.com/static/icons/edit.svg",
      key: "",
      props: {},
      command: {},
    };
    showModal(newAction, -1);
  };

  const handleEdit = (index) => {
    showModal({ ...actions[index] }, index);
  };

  const handleRemove = (index) => {
    const newActions = [...actions];
    newActions.splice(index, 1);
    setActions(newActions);
    onChange(newActions);
  };

  const handleModalCancel = () => {
    setModalVisible(false);
    form.resetFields();
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      const newActions = [...actions];

      if (currentIndex === -1) {
        // 添加新操作
        newActions.push(values);
      } else {
        // 更新现有操作
        newActions[currentIndex] = values;
      }

      setActions(newActions);
      onChange(newActions);
      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error("表单验证失败:", error);
    }
  };

  return (
    <div className={styles.toolbarActionsEditor}>
      <div className={styles.actionsList}>
        {actions.map((action, index) => (
          <div key={index} className={styles.actionItem}>
            <span className={styles.actionTitle}>
              {action.title || `${action.component || "工具栏项"} ${index + 1}`}
            </span>
            <Space>
              <Button type="text" icon={<EditOutlined />} onClick={() => handleEdit(index)} />
              <Button type="text" danger icon={<DeleteOutlined />} onClick={() => handleRemove(index)} />
            </Space>
          </div>
        ))}
      </div>

      <Button type="primary" onClick={handleAdd} block icon={<PlusOutlined />} className={styles.addButton}>
        添加工具栏项
      </Button>

      <Modal
        title={currentIndex === -1 ? "添加工具栏项" : "编辑工具栏项"}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
      >
        <Form form={form} layout="vertical">
          <Form.Item label="标题" name="title" rules={[{ required: true, message: "请输入标题" }]}>
            <Input placeholder="请输入标题" />
          </Form.Item>

          <Form.Item label="键名" name="key" rules={[{ required: true, message: "请输入键名" }]}>
            <Input placeholder="请输入键名" />
          </Form.Item>

          <Form.Item label="Icon" name="icon">
            <Input placeholder="请输入图标地址" />
          </Form.Item>

          <Form.Item
            label="组件属性"
            name="props"
            getValueFromEvent={(value) => {
              try {
                return JSON.parse(value);
              } catch (e) {
                return {};
              }
            }}
            getValueProps={(value) => ({
              value: JSON.stringify(value || {}, null, 2),
            })}
          >
            <JSONEditor height="150px" validate={() => {}} />
          </Form.Item>

          <Form.Item
            label={
              <>
                命令配置
                <CommandConfigPrompt />
              </>
            }
            name="command"
            rules={[{ required: true, message: "请配置命令" }]}
            getValueFromEvent={(value) => {
              try {
                return JSON.parse(value);
              } catch (e) {
                return {};
              }
            }}
            getValueProps={(value) => ({
              value: JSON.stringify(value || {}, null, 2),
            })}
          >
            <JSONEditor height="150px" validate={() => {}} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}

export default ToolbarActionsEditor;
