import styles from "./index.module.scss";
import { useEffect, useMemo, useRef } from "react";
import { fabric } from "fabric";
import {} from "@/pages/pod/common/init-fabric-types";
import ResponsiveBox from "@/components/ResponsiveBox";
import PropTypes from "prop-types";

function FabricCanvas(props) {
  const { width = 656, height = 426 } = props;
  const canvasRef = useRef();
  const boxRef = useRef();

  const canvas = useMemo(() => {
    return <canvas ref={canvasRef} width={0} height={0}></canvas>;
  }, []);

  useEffect(() => {
    const params = {};
    (async () => {
      const canvas = new fabric.Canvas(canvasRef.current, { width: 0, height: 0 });

      params.handleWindowResize = () => {
        const rect = boxRef.current?.getBoundingClientRect();
        canvas.setDimensions({ width: rect.width, height: rect.height });
      };
      params.handleWindowResize();
      window.addEventListener("resize", params.handleWindowResize);
    })();

    return function () {
      window.removeEventListener("resize", params.handleWindowResize);
    };
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <div ref={boxRef} className={styles.responsiveBox}>
          <ResponsiveBox width={width} height={height}></ResponsiveBox>
        </div>
        <div className={styles.canvasBox}>{canvas}</div>
      </div>
    </div>
  );
}

FabricCanvas.propTypes = {
  width: PropTypes.number,
  height: PropTypes.number,
  jsonData: PropTypes.object,
};

export default FabricCanvas;
