import styles from "./index.module.scss";
import { Form, Col, Row, Input, Button, Divider, message, Modal, Tabs } from "antd";
import EditTable from "components/common/EditTable";
import { useEffect, useReducer, useRef, useState } from "react";
import Fetchers from "fetchers";
import Helper from "helpers";
import {
  tableProps,
  initialValues,
  filterStatusReducer,
  defaultFilterStatus,
  editTableOperations,
} from "./common/vars";

const { Search } = Input;

function PurchaseStockIn(props) {
  const [tableLoading, setTableLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [scannedQty, setScannedQty] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [filterStatus, dispatch] = useReducer(filterStatusReducer, defaultFilterStatus);
  const [columns, setColumns] = useState([]);
  const defaultActiveKey = "batch_stock_in";
  const [activeKey, setActiveKey] = useState(defaultActiveKey);
  const formRef = useRef();
  const purchaseOrderNoRef = useRef();
  const skuCodeRef = useRef();
  const qtyRef = useRef();
  const goodsCodeRef = useRef();
  const isBatchControl = activeKey === "batch_stock_in";

  const tabItems = [
    {
      key: "batch_stock_in",
      label: "按批次点数入库",
      children: renderBatchStockInChildren(),
    },
    {
      key: "sku_stock_in",
      label: "按商品码点数入库",
      children: renderSkuStockInChildren(),
    },
  ];

  function renderBatchStockInChildren() {
    return (
      <>
        <Form.Item name="order_no" label="采购单号">
          <Search
            ref={purchaseOrderNoRef}
            placeholder="--输入采购单号--"
            onSearch={onPurchaseOrdersSearch}
            readOnly={filterStatus.order_no.readOnly}
          />
        </Form.Item>
        <Form.Item name="sku" label="商品码或sku码">
          <Search
            ref={skuCodeRef}
            placeholder="--输入商品码或sku码--"
            onSearch={onSkuSearch}
            readOnly={isBatchControl && filterStatus.sku.readOnly}
          />
        </Form.Item>
        <Form.Item name="qty" label="数量">
          <Search ref={qtyRef} placeholder="--输入数量--" onSearch={onNumSearch} readOnly={filterStatus.qty.readOnly} />
        </Form.Item>
      </>
    );
  }

  function renderSkuStockInChildren() {
    return (
      <Form.Item name="sku" label="商品码">
        <Search ref={goodsCodeRef} placeholder="--输入商品码--" onSearch={onGoodsCodeSearch} />
      </Form.Item>
    );
  }

  async function onFinish(values) {
    if (!values?.list?.length) {
      return Helper.openMessage({ type: "warning", content: "请扫描完商品再确认收货" });
    }
    try {
      setSubmitLoading(true);
      const result = await Fetchers.restockOrderCountInConfirm({
        data: { order_no: values.order_no, items: values.list },
      }).then((res) => res?.data);
      if (!result?.success) return;
      setSubmitLoading(false);
      handleReset();
    } finally {
      setSubmitLoading(false);
    }
  }

  async function handleReset() {
    formRef.current.resetFields();
    dispatch(defaultFilterStatus);
    setScannedQty(0);
    handleFocusOrderNo();
  }

  const handleFocusOrderNo = () => {
    setTimeout(() => {
      purchaseOrderNoRef.current?.focus();
    }, 10);
  };

  const handleFocusSku = () => {
    setTimeout(() => {
      skuCodeRef.current?.focus();
    }, 10);
  };

  const handleFocusGoodsCode = () => {
    setTimeout(() => {
      goodsCodeRef.current?.focus();
    }, 10);
  };

  async function onPurchaseOrdersSearch(value, e, info) {
    e.preventDefault();
    try {
      if (!value) {
        return Helper.openMessage({ type: "warning", content: "采购单号不能为空" });
      }
      Helper.pageLoading(true);
      const result = await restockOrderCountInVerify({ order_no: value });
      if (!result?.success) return;
      dispatch({ order_no: { readOnly: true }, sku: { readOnly: false } });
      handleFocusSku();
    } finally {
      Helper.pageLoading(false);
    }
  }

  async function onSkuSearch(value, e, info) {
    e.preventDefault();
    try {
      if (!value) {
        return Helper.openMessage({ type: "warning", content: "商品码或sku码不能为空" });
      }
      Helper.pageLoading(true);
      const values = formRef.current.getFieldsValue();
      const { order_no } = values;
      const result = await restockOrderCountInVerify({ order_no, sku: value });
      if (!result?.success) return;
      dispatch({ sku: { readOnly: true }, qty: { readOnly: false } });
      formRef.current.setFieldValue("qty", null);
      qtyRef.current.focus();
    } finally {
      Helper.pageLoading(false);
    }
  }

  async function onGoodsCodeSearch(value, e, info) {
    e.preventDefault();
    try {
      if (!value) {
        Helper.openMessage({ type: "warning", content: "商品码不能为空" });
        return;
      }
      Helper.pageLoading(true);
      const verify = await restockOrderCountInVerify({ sku: value });
      if (!verify?.success) return;

      setTableLoading(true);
      const values = formRef.current.getFieldsValue();
      const { list = [] } = values;
      const { columns, items } = await fetchTableData({ sku: value });
      const item = list.find((item) => item.id === items[0].id);
      if (item) return;
      let newItems = [...list, ...items];
      formRef.current.setFieldValue("list", []);
      formRef.current.setFieldValue("sku", "");
      setTimeout(() => {
        formRef.current.setFieldValue("list", newItems);
      }, 100);
      setColumns(columns);
      handleSetScannedQty(newItems);
    } finally {
      setTableLoading(false);
      Helper.pageLoading(false);
    }
  }

  function mergeItems(items, list, qty) {
    const listItemMap = {};
    list.forEach((item) => {
      listItemMap[item.id] = item;
    });
    return items.reduce(
      (acc, item) => {
        const { id } = item;
        const listItem = listItemMap[id];

        if (listItem) {
          const updatedItem = { ...item, qty: Number(qty) + Number(listItem.qty) };
          const index = acc.findIndex((i) => i.id === listItem.id);
          if (index !== -1) {
            acc[index] = updatedItem;
          } else {
            acc.push(updatedItem);
          }
        } else {
          acc.push({ ...item, qty: Number(qty) });
        }
        return acc;
      },
      [...list]
    );
  }

  async function onNumSearch(value, e, info) {
    e.preventDefault();
    if (!value || !isNumber(value) || !Number.isInteger(Number(value))) {
      return Helper.openMessage({ type: "warning", content: "请输入整数" });
    }

    if (Number(value) === 0 || value < 0) {
      return Helper.openMessage({ type: "warning", content: "数量不能为0" });
    }
    try {
      Helper.pageLoading(true);
      setTableLoading(true);
      const values = formRef.current.getFieldsValue();
      const { order_no, sku, qty, list = [] } = values;
      const result = await fetchTableData({ order_no, sku, qty });
      const { columns, items } = result;
      let newItems = [...list];
      // 如果存在相同sku，则合并数量
      newItems = mergeItems(items, newItems, qty);
      formRef.current.setFieldValue("list", []);
      formRef.current.setFieldValue("sku", "");
      setTimeout(() => {
        formRef.current.setFieldValue("list", newItems);
      }, 100);
      setColumns(columns);
      handleFocusSku();
      dispatch({ sku: { readOnly: false }, qty: { readOnly: true } });
      setTableLoading(false);
      handleSetScannedQty(newItems);
    } finally {
      setTableLoading(false);
      Helper.pageLoading(false);
    }
  }

  async function fetchTableData(values) {
    return await Fetchers.getRestockOrderCountIn({ params: values }).then((res) => res?.data?.data);
  }

  async function restockOrderCountInVerify(values) {
    return await Fetchers.restockOrderCountInVerify({ data: values }).then((res) => res?.data);
  }

  function rowSelectionChange({ selectedRowKeys, selectedRows }) {
    setSelectedRowKeys(selectedRowKeys);
  }

  function isNumber(value) {
    return !Number.isNaN(Number(value));
  }

  async function handleAddNotInItems() {
    try {
      setTableLoading(true);
      const values = formRef.current.getFieldsValue();
      const { order_no } = values;
      if (!order_no) {
        return Helper.openMessage({ type: "warning", content: "采购单号不能为空" });
      }
      const result = await fetchTableData({ order_no });
      const { columns, items } = result;

      formRef.current.setFieldValue("list", []);
      formRef.current.setFieldValue("sku", "");
      setTimeout(() => {
        formRef.current.setFieldValue("list", items);
      }, 100);
      dispatch({ order_no: { readOnly: true }, sku: { readOnly: false } });
      handleFocusSku();
      setColumns(columns);
      setTableLoading(false);
      handleSetScannedQty(items);
    } finally {
      setTableLoading(false);
    }
  }

  function handleSetScannedQty(list) {
    let qty = 0;
    if (list?.length > 0) {
      list.forEach((item) => {
        qty += Number(item.qty);
      });
    }
    setScannedQty(qty);
    return qty;
  }

  function handleEditTableChange(data) {
    handleSetScannedQty(data);
  }

  function handleTabsChange(key) {
    setActiveKey(key);
    handleReset();
    if (key === "batch_stock_in") {
      if (!filterStatus.order_no.readOnly) {
        handleFocusOrderNo();
      } else if (!filterStatus.sku.readOnly) {
        handleFocusSku();
      }
    } else {
      handleFocusGoodsCode();
    }
  }

  useEffect(() => {
    handleFocusOrderNo();
  }, []);

  return (
    <div className={styles.container}>
      <Form ref={formRef} initialValues={initialValues} onFinish={onFinish} layout="vertical" autoComplete="off">
        <Row gutter={16}>
          <Col span={6}>
            <div className={styles.panel}>
              {/* <div className={styles.title}>采购单 | 返修单</div> */}
              <Tabs defaultActiveKey={defaultActiveKey} items={tabItems} onChange={handleTabsChange} />
              <div className={styles.actions}>
                <Button
                  onClick={() => {
                    new Promise((resolve) => {
                      Helper.modal.confirm({
                        title: "请确认 ?",
                        content: "重置后需要重新扫描当前信息，是否重置？",
                        onOk: () => {
                          handleReset();
                        },
                        afterClose: () => resolve(),
                      });
                    });
                  }}
                >
                  重置
                </Button>
                <Button type="primary" htmlType="submit" loading={submitLoading}>
                  确认收货
                </Button>
              </div>
            </div>
          </Col>
          <Col span={18}>
            <div className={styles.panel}>
              <div className={styles.scan}>
                <span>已扫描数量:</span>
                <span className={styles.scanNum}>{scannedQty}</span>
              </div>
              <Divider plain></Divider>
              <div>
                {/* <Form.Item name="remark" label="备注">
                  <Input placeholder="--输入备注--" onPressEnter={(e) => e.preventDefault()} />
                </Form.Item>
                <Divider plain></Divider> */}
                <div style={{ display: "flex", alignItems: "center" }}>
                  <span>进仓信息</span>
                  <span>
                    {isBatchControl ? (
                      <Button
                        type="link"
                        onClick={() => {
                          handleAddNotInItems();
                        }}
                      >
                        一键加载未入库数
                      </Button>
                    ) : null}
                    {/* <Button type="link">商品条码打印</Button>
                        <Button type="link">标记次品原因</Button> */}
                  </span>
                </div>
                {columns?.length > 0 ? (
                  <Form.Item name="list">
                    <EditTable
                      {...tableProps}
                      columns={columns}
                      operations={editTableOperations}
                      loading={tableLoading}
                      isShowAddRow={false}
                      rowSelectionChange={rowSelectionChange}
                      onChange={handleEditTableChange}
                    />
                  </Form.Item>
                ) : null}
              </div>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
}

export default PurchaseStockIn;
