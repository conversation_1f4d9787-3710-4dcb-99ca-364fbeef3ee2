import { useRef } from "react";
import CommonJSONForm from "components/common/JSONForm";
import DatePicker from "components/common/DatePicker";
import Fetcher from "@/fetchers";
import Helper from "@/helpers";

const { RangePicker } = DatePicker;

const RESET_FIELDS = {
  overdue_day: null,
  fine_amount: null,
};

const FIELDS_TRIGGER_RESET = ["overdue_rule_id", "brand_ids"];

function JSONForm(props) {
  const { data } = props;
  const formRef = useRef(null);

  const extendComponents = {
    PenaltyTimePicker: ({ item }) => <RangePicker {...item.props} onChange={handlePenaltyTimePickerChange} />,
  };

  async function handlePenaltyTimePickerChange(value) {
    if (!value) return;
    const formData = formRef.current.getFieldsValue();
    const automaticCalculateData = await getAutomaticCalculateData(formData);
    formRef.current.setFieldsValue(automaticCalculateData);
  }

  async function getAutomaticCalculateData(formData) {
    const { fine_num } = formData;
    if (fine_num === undefined || fine_num === null || fine_num === "") {
      Helper.openMessage({ type: "error", content: "请输入罚款件数" });
      return;
    }

    try {
      return await Fetcher.getOtherPenaltyChangeDate({ data: formData }).then((res) => res.data?.data);
    } catch (error) {
      return RESET_FIELDS;
    }
  }

  function onValuesChange(changedFields, allFields) {
    const currentField = Object.keys(changedFields)[0];
    if (FIELDS_TRIGGER_RESET.includes(currentField)) {
      formRef.current.setFieldsValue({
        ...allFields,
        ...RESET_FIELDS,
      });
    }
  }

  return (
    <CommonJSONForm ref={formRef} data={data} extendComponents={extendComponents} onValuesChange={onValuesChange} />
  );
}

export default JSONForm;
