module.exports = {
  "GET /rest/v1/order/modify/history": (req, res) => {
    const dataSource = [];
    const Status = ["pending", "processing", "shipped"];
    for (let i = 0; i < Math.floor(Math.random() * (6 - 2)) + 1; i++) {
      dataSource.push({
        id: i + 1,
        status: Status[Math.floor(Math.random() * (2 - 0))],
        update_at: `2023-01-0${i + 1} ${Math.floor(Math.random() * (20 - 10))}:00:00`,
        user: "admin",
      });
    }
    res.status(200).json({
      status: "00",
      success: true,
      data: {
        content: {
          component: "JSONComponents",
          type: "json",
          props: {},
          children: [
            {
              component: "Table",
              props: {
                columns: [
                  { dataIndex: "status", title: "状态" },
                  { dataIndex: "update_at", title: "变更时间" },
                  { dataIndex: "user", title: "操作人" },
                ],
                dataSource,
                rowKey: "id",
                pagination: false,
                size: "small",
              },
            },
          ],
        },
        // columns: [
        //   { dataIndex: "status", title: "状态" },
        //   { dataIndex: "update_at", title: "变更时间" },
        //   { dataIndex: "user", title: "操作人" },
        // ],
        // dataSource,
        // rowKey: "id",
        // pagination: false,
        // size: "small",
      },
    });
  },
};
