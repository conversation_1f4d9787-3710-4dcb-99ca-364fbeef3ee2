.container {
  .label {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 3px;

    .icon {
      font-size: 18px;
    }
  }

  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--theme-border-color);

    .count {
      font-size: 12px;
      padding: 1px 3px;
      color: #fff;
      background-color: #6c757d;
      border-radius: 5px;
    }
  }
}
