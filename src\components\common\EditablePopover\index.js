import styles from "./index.module.scss";
import PropTypes from "prop-types";
import Enums from "enums";
import { forwardRef, useState, useRef } from "react";
import Popover from "components/common/Popover";
import { Form, Button, Input, Select, InputNumber } from "antd";
import RemoteSearchSelect from "@/components/common/RemoteSearchSelect";
import ExternalPageEditor from "@/components/business/ExternalPageEditor";
import { CheckOutlined, EditOutlined } from "@ant-design/icons";

function EditablePopover(props, ref) {
  const {
    children,
    onFinish,
    field,
    defaultValue,
    editable,
    isShowSubmitButton = true,
    extraSubmitData,
    rowData,
  } = props;
  const { component: componentType } = editable;
  const [options, setOptions] = useState(editable?.props?.options || []);
  const initialValues = { [field]: getInitialValue() };
  const formRef = useRef();

  function getInitialValue() {
    if (componentType === Enums.Components.Select) {
      return editable?.props?.options?.find((option) => option.label === defaultValue)?.value || defaultValue;
    } else {
      return defaultValue;
    }
  }

  function handleOptionsChange(options) {
    setOptions(options);
    if (componentType === Enums.Components.Select && editable.searchApi) {
      const newValue = options?.find((option) => option.label === defaultValue)?.value || defaultValue;
      formRef.current.setFieldsValue({ [field]: newValue });
    }
  }

  function renderFormControl() {
    if (componentType === Enums.Components.InputNumber) {
      return <InputNumber {...editable?.props} />;
    }
    if (componentType === Enums.Components.ExternalPageEditor) {
      return (
        <ExternalPageEditor
          {...editable.props}
          extraSubmitData={extraSubmitData}
          targetUrl={rowData?.targetUrl}
          defaultValue={rowData?.defaultValue}
        />
      );
    } else if (componentType === Enums.Components.Textarea) {
      return <Input.TextArea allowClear {...editable.props} />;
    } else if (componentType === Enums.Components.Select) {
      if (editable.searchApi) {
        const optionsValues = options?.map((option) => option.value);
        return (
          <RemoteSearchSelect
            {...editable?.props}
            api={editable?.searchApi}
            isGetDefaultOptions={editable?.isGetDefaultOptions}
            defaultOptions={options}
            onOptionsChange={handleOptionsChange}
            onChange={(value, option) => {
              if (options?.length === 0) {
                setOptions([option]);
              } else if (!optionsValues.includes(value)) {
                setOptions([...options, option]);
              }
            }}
          />
        );
      } else {
        return <Select {...editable?.props} />;
      }
    } else {
      return <Input allowClear {...editable?.props} />;
    }
  }

  function handleFinish(values) {
    onFinish?.(values, options);
  }

  return (
    <Popover
      ref={ref}
      content={
        <Form ref={formRef} initialValues={initialValues} onFinish={handleFinish}>
          <Form.Item name={field} rules={editable?.rules || [{ required: true }]}>
            {renderFormControl()}
          </Form.Item>
          {isShowSubmitButton && (
            <Button htmlType="submit">
              <CheckOutlined />
            </Button>
          )}
        </Form>
      }
    >
      {children ? <span className={styles.editIcon}>{children}</span> : <EditOutlined className={styles.editIcon} />}
    </Popover>
  );
}

EditablePopover = forwardRef(EditablePopover);

EditablePopover.propTypes = {
  onFinish: PropTypes.func,
  defaultValue: PropTypes.string,
  field: PropTypes.string,
  editable: PropTypes.object,
};

export default EditablePopover;
