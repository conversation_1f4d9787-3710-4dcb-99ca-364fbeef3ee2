import styles from "./index.module.scss";
import { forwardRef } from "react";
import classNames from "classnames";
import { Image } from "antd";

function Item({ item, isDragging, style, count, onSelect, selected, ...restProps }, ref) {
  const inlineStyles = {
    transform: isDragging ? "scale(1.05)" : "scale(1)",
    borderColor: isDragging || selected ? "#1890ff" : "",
    ...style,
  };

  return (
    <div
      ref={ref}
      className={classNames({ [styles.item]: true, [styles.dragging]: isDragging })}
      style={inlineStyles}
      onClick={onSelect}
      {...restProps}
    >
      <Image src={item?.image?.src} width={90} height={90} preview={false} />
      <div>{item?.spu}</div>
      <div>{item?.price}</div>

      {isDragging && count > 1 && (
        <div className={styles.count}>
          <p>{count}</p>
        </div>
      )}
    </div>
  );
}

export default forwardRef(Item);
