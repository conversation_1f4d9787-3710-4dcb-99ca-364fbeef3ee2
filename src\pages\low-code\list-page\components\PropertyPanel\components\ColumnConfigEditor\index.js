import styles from "./index.module.scss";
import { useState, useEffect, forwardRef, useImperativeHandle } from "react";
import { Form, Input, InputNumber, Select, Button, Space, Tabs, Row, Col } from "antd";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import JSONEditor from "components/common/JSONEditor";
import ConfigPrompt from "../ConfigPrompt";

// 所有筛选类型的示例配置
const filterExamples = [
  {
    component: "Select",
    title: "下拉选择",
    description: "适用于从预定义选项中选择一个值",
    examples: [
      {
        title: "基础下拉选择",
        config: {
          component: "Select",
          props: {
            placeholder: "请选择",
            options: [
              { label: "选项1", value: "1" },
              { label: "选项2", value: "2" },
            ],
            allowClear: true,
          },
        },
      },
      {
        title: "可搜索下拉选择",
        config: {
          component: "Select",
          props: {
            placeholder: "请选择",
            options: [
              { label: "选项1", value: "1" },
              { label: "选项2", value: "2" },
            ],
            allowClear: true,
          },
          searchable: true,
        },
      },
      {
        title: "远程搜索下拉选择",
        config: {
          component: "Select",
          props: {
            placeholder: "请选择",
            allowClear: true,
          },
          searchable: true,
          searchApi: "/api/options",
        },
      },
    ],
  },
  {
    component: "Checkbox",
    title: "多选框",
    description: "适用于从预定义选项中选择多个值",
    examples: [
      {
        title: "基础多选框",
        config: {
          component: "Checkbox",
          props: {
            options: [
              { label: "选项1", value: "1" },
              { label: "选项2", value: "2" },
              { label: "选项3", value: "3" },
            ],
          },
        },
      },
    ],
  },
  {
    component: "Input",
    title: "搜索框",
    description: "适用于文本搜索",
    examples: [
      {
        title: "基础搜索框",
        config: {
          component: "Search",
          props: {
            placeholder: "请输入关键词",
          },
        },
      },
    ],
  },
  {
    component: "TreeSelect",
    title: "树形选择",
    description: "适用于层级数据的选择",
    examples: [
      {
        title: "基础树形选择",
        config: {
          component: "Tree",
          props: {
            placeholder: "请选择",
            treeData: [
              {
                title: "父节点1",
                value: "parent1",
                children: [
                  { title: "子节点1", value: "child1" },
                  { title: "子节点2", value: "child2" },
                ],
              },
            ],
          },
        },
      },
    ],
  },
  {
    component: "RangePicker",
    title: "日期范围",
    description: "适用于日期范围筛选",
    examples: [
      {
        title: "创建日期",
        config: {
          component: "RangePicker",
        },
      },
    ],
  },
];

// 所有编辑类型的示例配置
const editableExamples = [
  {
    component: "Input",
    title: "文本输入框",
    description: "适用于编辑文本内容",
    examples: [
      {
        title: "基础文本输入",
        config: {
          component: "Input",
          props: {
            placeholder: "请输入",
          },
          request: { url: "/api", data: { action: "edit" } },
          rules: [{ required: true, message: "请输入内容" }],
        },
      },
      {
        title: "带验证规则的文本输入",
        config: {
          component: "Input",
          props: {
            placeholder: "请输入",
          },
          request: { url: "/api", data: { action: "edit" } },
          rules: [
            { required: true, message: "请输入内容" },
            { max: 50, message: "最多输入50个字符" },
          ],
        },
      },
    ],
  },
  {
    component: "Select",
    title: "下拉选择框",
    description: "适用于从预定义选项中选择",
    examples: [
      {
        title: "基础下拉选择",
        config: {
          component: "Select",
          props: {
            placeholder: "请选择",
            options: [
              { label: "选项1", value: "1" },
              { label: "选项2", value: "2" },
            ],
          },
          rules: [{ required: true, message: "请选择一个选项" }],
        },
      },
      {
        title: "远程数据下拉选择",
        config: {
          component: "Select",
          props: {
            placeholder: "请选择",
          },
          searchApi: "/searchApi",
          isGetDefaultOptions: true,
          request: { url: "/api", data: { action: "edit" } },
          rules: [{ required: true, message: "请选择一个选项" }],
        },
      },
    ],
  },
];

function ColumnConfigEditor({ value = [], onChange }, ref) {
  const [columns, setColumns] = useState(value);
  const [activeKey, setActiveKey] = useState("0");
  const [form] = Form.useForm();

  const handleAdd = () => {
    const newColumns = [...columns];
    newColumns.push({
      dataIndex: "",
      title: "",
      width: 100,
      ellipsis: false,
      copyable: false,
      sorter: false,
    });
    setColumns(newColumns);
    onChange(newColumns);
    setActiveKey(String(newColumns.length - 1));
  };

  const handleRemove = (index) => {
    const newColumns = [...columns];
    newColumns.splice(index, 1);
    setColumns(newColumns);
    onChange(newColumns);
    if (Number(activeKey) === index) {
      setActiveKey(String(Math.max(0, index - 1)));
    } else if (Number(activeKey) > index) {
      setActiveKey(String(Number(activeKey) - 1));
    }
  };

  const handleFieldChange = (index, field, value) => {
    const newColumns = [...columns];

    if (field === "filter") {
      try {
        newColumns[index].filter = JSON.parse(value);
      } catch (e) {
        return;
      }
    } else if (field === "editable") {
      try {
        newColumns[index].editable = JSON.parse(value);
      } catch (e) {
        return;
      }
    } else {
      // 处理嵌套字段，如 "props.placeholder"
      if (field.includes(".")) {
        const [parent, child] = field.split(".");
        if (!newColumns[index][parent]) {
          newColumns[index][parent] = {};
        }
        newColumns[index][parent][child] = value;
      } else {
        newColumns[index][field] = value;
      }
    }

    setColumns(newColumns);
    onChange(newColumns);
  };

  useImperativeHandle(ref, () => form);

  useEffect(() => {
    setColumns(value);
  }, [value]);

  return (
    <div className={styles.columnConfigEditor}>
      <div className={styles.columnTabs}>
        <Tabs
          activeKey={activeKey}
          onChange={setActiveKey}
          type="card"
          tabPosition="left"
          className={styles.tabs}
          tabBarExtraContent={
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd} className={styles.addButton}>
              添加列
            </Button>
          }
          items={columns.map((column, index) => ({
            key: String(index),
            label: column.title || `列 ${index + 1}`,
            children: (
              <div key={index} className={styles.tabContent}>
                <div className={styles.tabHeader}>
                  <Space>
                    <Button type="text" danger icon={<DeleteOutlined />} onClick={() => handleRemove(index)} />
                  </Space>
                </div>

                <Form form={form} layout="vertical">
                  <div className={styles.formWrapper}>
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item label="字段名">
                          <Input
                            value={column.dataIndex}
                            onChange={(e) => handleFieldChange(index, "dataIndex", e.target.value)}
                            placeholder="请输入字段名"
                          />
                        </Form.Item>

                        <Form.Item label="表头名称">
                          <Input
                            value={column.title}
                            onChange={(e) => handleFieldChange(index, "title", e.target.value)}
                            placeholder="请输入表头名称"
                          />
                        </Form.Item>

                        <Form.Item label="宽度">
                          <InputNumber
                            value={column.width}
                            onChange={(value) => handleFieldChange(index, "width", value)}
                            placeholder="请输入宽度"
                            min={50}
                            style={{ width: "100%" }}
                          />
                        </Form.Item>

                        <Form.Item label="固定位置">
                          <Select
                            value={column.fixed || ""}
                            onChange={(value) => handleFieldChange(index, "fixed", value)}
                            options={[
                              { label: "不固定", value: "" },
                              { label: "左侧", value: "left" },
                              { label: "右侧", value: "right" },
                            ]}
                          />
                        </Form.Item>

                        <Form.Item label="展示类型">
                          <Select
                            value={column.valueType || "text"}
                            onChange={(value) => handleFieldChange(index, "valueType", value)}
                            options={[
                              { label: "文本", value: "text" },
                              { label: "图片", value: "image" },
                              { label: "链接", value: "link" },
                              { label: "HTML", value: "html" },
                              { label: "命令", value: "command" },
                              { label: "枚举", value: "enum" },
                              { label: "日期", value: "date" },
                              { label: "日期时间", value: "datetime" },
                              { label: "金额", value: "money" },
                              { label: "标签", value: "tag" },
                              { label: "进度条", value: "progress" },
                              { label: "状态", value: "status" },
                            ]}
                          />
                        </Form.Item>
                        <Form.Item label="是否省略">
                          <Select
                            value={column.ellipsis}
                            onChange={(value) => handleFieldChange(index, "ellipsis", value)}
                            options={[
                              { label: "是", value: true },
                              { label: "否", value: false },
                            ]}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item label="是否可复制">
                          <Select
                            value={column.copyable}
                            onChange={(value) => handleFieldChange(index, "copyable", value)}
                            options={[
                              { label: "是", value: true },
                              { label: "否", value: false },
                            ]}
                          />
                        </Form.Item>

                        <Form.Item label="是否排序">
                          <Select
                            value={column.sorter}
                            onChange={(value) => handleFieldChange(index, "sorter", value)}
                            options={[
                              { label: "是", value: true },
                              { label: "否", value: false },
                            ]}
                          />
                        </Form.Item>
                        <Form.Item
                          label={
                            <>
                              筛选配置
                              <ConfigPrompt data={filterExamples} />
                            </>
                          }
                        >
                          <Space direction="vertical" style={{ width: "100%" }}>
                            <div className={styles.jsonPreview}>
                              <JSONEditor
                                value={JSON.stringify(column.filter || {}, null, 2)}
                                height="150px"
                                onChange={(value) => {
                                  try {
                                    handleFieldChange(index, "filter", value);
                                  } catch (e) {
                                    // 解析错误时不更新
                                  }
                                }}
                                validate={() => {}}
                              />
                            </div>
                          </Space>
                        </Form.Item>

                        <Form.Item
                          label={
                            <>
                              编辑配置
                              <ConfigPrompt data={editableExamples} />
                            </>
                          }
                        >
                          <JSONEditor
                            value={JSON.stringify(column.editable || {}, null, 2)}
                            height="150px"
                            onChange={(value) => handleFieldChange(index, "editable", value)}
                            validate={() => {}}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </div>
                </Form>
              </div>
            ),
          }))}
        />
      </div>
    </div>
  );
}

export default forwardRef(ColumnConfigEditor);
