import { forwardRef } from "react";
import { Card, Collapse } from "antd";
import Enums from "enums";
import Utils from "utils";
import styles from "./index.module.scss";
import Icon from "components/common/Icon";

function SidebarList(props, ref) {
  const { data } = props;

  function renderCollapseItem(item) {
    return item?.children?.map((child, index) => (
      <a key={index} href={child?.url}>
        <div className={styles.item}>
          <span className={styles.text}>{child?.text}</span>
          <span className={styles.count}>{child?.count}</span>
        </div>
      </a>
    ));
  }

  function renderCollapseLabel(item) {
    const { icon, text, check_icon } = item?.label || {};
    return (
      <div className={styles.label}>
        {icon ? <Icon className={styles.icon} src={icon?.src} style={icon?.style} /> : null}
        <span>{text}</span>
        {check_icon ? <Icon className={styles.icon} src={check_icon?.src} style={check_icon?.style} /> : null}
      </div>
    );
  }

  function renderComponent(items) {
    return items?.map((item, index) => {
      if (item?.component === Enums.Components.Card) {
        return (
          <Card key={index} {...item.props}>
            {renderComponent(item?.children)}
          </Card>
        );
      } else if (item?.component === Enums.Components.Collapse) {
        const collapseItems = Utils.cloneDeep(item?.props?.items);
        const defaultActiveKey = collapseItems?.map((item) => item?.key);

        const items = collapseItems?.map((collapseItem) => ({
          ...collapseItem,
          label: renderCollapseLabel(collapseItem),
          children: renderCollapseItem(collapseItem),
        }));

        return (
          <Collapse
            key={index}
            collapsible="header"
            expandIconPosition="end"
            defaultActiveKey={defaultActiveKey}
            size="small"
            {...item?.props}
            items={items}
          />
        );
      }

      return "";
    });
  }

  return <div className={styles.container}>{renderComponent(data?.content?.children)}</div>;
}

SidebarList = forwardRef(SidebarList);

export default SidebarList;
