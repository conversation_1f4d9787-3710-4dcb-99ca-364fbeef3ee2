import { Control<PERSON>rapper, FormControl, useForm } from "@/components/react-form-x";
import { Checkbox, Col, Input, InputNumber, Row, Select } from "antd";
import PropTypes from "prop-types";
import { useState } from "react";
import { renderI18nSelect } from "@/pages/pod/common";

function TextInputPrivateControls(props) {
  const { keyPath, control } = props;
  const [isTextarea, setIsTextarea] = useState(control?.is_textarea);
  const [isCustomRestriction, setIsCustomRestriction] = useState(control?.restriction === "custom");
  const form = useForm();

  return (
    <>
      <Row gutter={16}>
        <Col span={12}>
          <FormControl
            keyPath={keyPath}
            name="placeholder"
            render={(props) => (
              <div>
                <div>Placeholder:</div>
                <div>
                  <ControlWrapper
                    {...props}
                    render={({ name, value, onChange }) => renderI18nSelect({ form, keyPath, name, value, onChange })}
                  ></ControlWrapper>
                </div>
              </div>
            )}
          ></FormControl>
        </Col>
        <Col span={12}>
          <FormControl
            keyPath={keyPath}
            name="initial_value"
            render={(props) => (
              <div>
                <div>Initial Value:</div>
                <div>
                  <ControlWrapper
                    {...props}
                    render={({ name, value, onChange }) => renderI18nSelect({ form, keyPath, name, value, onChange })}
                  ></ControlWrapper>
                </div>
              </div>
            )}
          ></FormControl>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={6}>
          <FormControl
            keyPath={keyPath}
            name="max_length"
            render={(props) => (
              <div>
                <div>Max Length:</div>
                <div>
                  <ControlWrapper
                    {...props}
                    render={(props) => {
                      const { name, value, onChange } = props;
                      return (
                        <InputNumber
                          value={value}
                          onChange={(value) => {
                            onChange(null, { [name]: value });
                          }}
                          style={{ width: `100%` }}
                        ></InputNumber>
                      );
                    }}
                  ></ControlWrapper>
                </div>
              </div>
            )}
          ></FormControl>
        </Col>
        <Col span={6}>
          <FormControl
            keyPath={keyPath}
            name="is_textarea"
            render={(props) => (
              <div>
                <div>&nbsp;</div>
                <div style={{ display: "flex", alignItems: "center", height: 30 }}>
                  <ControlWrapper
                    {...props}
                    render={(props) => {
                      const { name, value, onChange } = props;
                      return (
                        <Checkbox
                          checked={value}
                          onChange={(event) => {
                            const checked = event.target.checked;
                            onChange(null, { [name]: checked });
                            setIsTextarea(checked);
                          }}
                        >
                          is textarea
                        </Checkbox>
                      );
                    }}
                  ></ControlWrapper>
                </div>
              </div>
            )}
          ></FormControl>
        </Col>
        {isTextarea ? (
          <>
            <Col span={6}>
              <FormControl
                keyPath={keyPath}
                name="max_lines"
                render={(props) => (
                  <div>
                    <div>Max number of lines:</div>
                    <div>
                      <ControlWrapper
                        {...props}
                        render={(props) => {
                          const { name, value, onChange } = props;
                          return (
                            <InputNumber
                              value={value}
                              onChange={(value) => {
                                onChange(null, { [name]: value });
                              }}
                              style={{ width: `100%` }}
                            ></InputNumber>
                          );
                        }}
                      ></ControlWrapper>
                    </div>
                  </div>
                )}
              ></FormControl>
            </Col>
            <Col span={6}>
              <FormControl
                keyPath={keyPath}
                name="max_chars_per_lines"
                render={(props) => (
                  <div>
                    <div>Max characters per lines:</div>
                    <div style={{ display: "flex", alignItems: "center", height: 30 }}>
                      <ControlWrapper
                        {...props}
                        render={(props) => {
                          const { name, value, onChange } = props;
                          return (
                            <InputNumber
                              value={value}
                              onChange={(value) => {
                                onChange(null, { [name]: value });
                              }}
                              style={{ width: `100%` }}
                            ></InputNumber>
                          );
                        }}
                      ></ControlWrapper>
                    </div>
                  </div>
                )}
              ></FormControl>
            </Col>
          </>
        ) : null}
      </Row>
      <Row gutter={16}>
        <Col span={6}>
          <FormControl
            keyPath={keyPath}
            name="restriction"
            render={(props) => (
              <div>
                <div>Restrict Input:</div>
                <div>
                  <ControlWrapper
                    {...props}
                    render={(props) => {
                      const { name, value, onChange } = props;
                      return (
                        <Select
                          defaultValue=""
                          value={value}
                          onChange={(value, option) => {
                            onChange(null, { [name]: value });
                            setIsCustomRestriction(value === "custom");
                          }}
                          options={[
                            { label: "No Restriction", value: "" },
                            { label: "Only numbers (with spaces)", value: "numbers_with_spaces" },
                            { label: "Only numbers (no spaces)", value: "only_numbers" },
                            { label: "Only letters (with spaces)", value: "letters_with_spaces" },
                            { label: "Only letters (no spaces)", value: "only_letters" },
                            { label: "Custom", value: "custom" },
                          ]}
                          style={{ width: `100%` }}
                        ></Select>
                      );
                    }}
                  ></ControlWrapper>
                </div>
              </div>
            )}
          ></FormControl>
        </Col>
        {isCustomRestriction ? (
          <Col span={6}>
            <FormControl
              keyPath={keyPath}
              name="restriction_regex"
              render={(props) => {
                return (
                  <div>
                    <div>Regular expression:</div>
                    <div>
                      <ControlWrapper {...props} render={(props) => <Input {...props}></Input>}></ControlWrapper>
                    </div>
                  </div>
                );
              }}
            ></FormControl>
          </Col>
        ) : null}
        <Col span={6}>
          <FormControl
            keyPath={keyPath}
            name="capitalization"
            render={(props) => (
              <div>
                <div>Capitalization (Letter Case):</div>
                <div>
                  <ControlWrapper
                    {...props}
                    render={(props) => {
                      const { name, value, onChange } = props;
                      return (
                        <Select
                          defaultValue=""
                          value={value}
                          onChange={(value, option) => {
                            onChange(null, { [name]: value });
                          }}
                          options={[
                            { label: "Original case", value: "" },
                            { label: "All uppercase", value: "upper_case" },
                            { label: "All lowercase", value: "lower_case" },
                          ]}
                          style={{ width: `100%` }}
                        ></Select>
                      );
                    }}
                  ></ControlWrapper>
                </div>
              </div>
            )}
          ></FormControl>
        </Col>
      </Row>
    </>
  );
}

TextInputPrivateControls.propTypes = {
  keyPath: PropTypes.array,
  control: PropTypes.object,
};

export default TextInputPrivateControls;
