import { App, But<PERSON>, Form, Input, Modal, Popconfirm, Select, Table } from "antd";
import { useEffect, useRef, useState } from "react";
import Axios from "axios";
import { createCustomToken } from "@/fetchers/custom-token";
import useCustomToken from "@/hooks/useCustomToken";
import WebSocketClient from "@/utils/WebSocketClient";
import styles from "./index.module.scss";
import { flushSync } from "react-dom";
import Utils from "@/utils";

const axios = Axios.create({});

axios.interceptors.request.use(async (config) => {
  const token = await createCustomToken();
  config.headers.Authorization = `Bearer ${token}`;
  return config;
});

const Fetchers = Object.freeze({
  get prefix() {
    const debug = window.location.href.includes("debug=true");
    const protocol = debug ? window.location.protocol : "https:";
    const host = debug ? "localhost:8085" : "pwa.bizseas.com";
    return `${protocol}//${host}`;
  },

  get appId() {
    return "crx-image-catcher";
  },

  async getConfig() {
    return await axios.get(`${this.prefix}/fdc/rest/v1/config/${this.appId}`);
  },

  async updateConfig({ jsonPatch }) {
    return await axios.patch(`${this.prefix}/fdc/rest/v1/config/${this.appId}`, {
      jsonPatch: jsonPatch,
    });
  },

  async setIPWhiteList({ list }) {
    return await this.updateConfig({ jsonPatch: [{ op: "updateOne", path: "data.ipWhiteList", value: list }] });
  },

  async getUsers() {
    return await this.getConfig().then((res) => Object.values(res.data.data.data.users));
  },

  async setUser({ id, value }) {
    return await this.updateConfig({ jsonPatch: [{ op: "updateOne", path: `data.users.${id}`, value }] });
  },

  async setStatus({ id, status }) {
    return await this.updateConfig({
      jsonPatch: [{ op: "updateOne", path: `data.users.${id}.status`, value: status }],
    });
  },

  async authorize({ id }) {
    return await this.setStatus({ id, status: "authorized" });
  },

  async disable({ id }) {
    return await this.setStatus({ id, status: "disabled" });
  },

  async delete({ id }) {
    if (!id) return Promise.reject("id is required");
    const data = await this.getConfig().then((res) => res.data.data.data);
    delete data.users[id];
    return await this.updateConfig({ jsonPatch: [{ op: "updateOne", path: "data.users", value: data.users }] });
  },
});

let ws;
const sendToApp = "crx-image-catcher";
const ipWhiteListDivider = "|";

function CrxImageCatcher() {
  const [tableData, setTableData] = useState([]);
  const [tableLoading, setTableLoading] = useState(true);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editSubmitLoading, setEditSubmitLoading] = useState(false);
  const [ipWhiteListText, setIpWhiteListText] = useState("");
  const [saveIpWhiteListLoading, setSaveIpWhiteListLoading] = useState(false);

  const editFormRef = useRef();

  const { message: toast } = App.useApp();

  useCustomToken();

  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current, loadTableData, tableData };

  async function loadTableData() {
    await Fetchers.getConfig().then((res) => {
      const config = res.data.data.data;
      const users = Object.values(config.users);
      setIpWhiteListText(config.ipWhiteList.join(` ${ipWhiteListDivider} `));
      setTableData(users);
      setTableLoading(false);
    });
  }

  function parseIPWhiteList(text) {
    return text
      .replace(/\s+/g, "")
      .split(ipWhiteListDivider)
      .filter((x) => x);
  }

  useEffect(() => {
    paramsRef.current.loadTableData();
  }, []);

  useEffect(() => {
    ws = new WebSocketClient("wss://message.cnzlerp.com/websocket", {
      WebSocket: window.WebSocket,
      params: { app: "crx-image-catcher", user: "manager" },
      retryAttempts: 1000,
    });
    ws.on("message", async (event) => {
      const message = event.data.toString();
      const { data } = JSON.parse(message);
      if (data.type === "auth") {
        const { loadTableData, tableData } = paramsRef.current;
        if (!tableData.some((x) => x.id === data.body.id)) {
          await Fetchers.setUser({ id: data.body.id, value: data.body });
        }
        ws.json({
          action: "send",
          to: { app: sendToApp, user: data.body.id },
          data: { type: "authDataSaved" },
        });
        loadTableData();
      }
    });
  }, []);

  return (
    <>
      <div className={styles.page}>
        <div>
          <div style={{ display: "flex", alignItems: "center", gap: 10 }}>
            <div>IP白名单</div>
            <Input
              value={ipWhiteListText}
              style={{ flex: 1 }}
              onChange={(event) => {
                setIpWhiteListText(event.target.value);
              }}
            ></Input>
            <div>
              <Button
                type="primary"
                loading={saveIpWhiteListLoading}
                onClick={() => {
                  setSaveIpWhiteListLoading(true);
                  const list = parseIPWhiteList(ipWhiteListText);
                  Fetchers.setIPWhiteList({ list })
                    .then(() => {
                      toast.success(`保存成功`);
                      return loadTableData();
                    })
                    .catch(() => {
                      toast.error(`保存失败`);
                    })
                    .finally(() => {
                      setSaveIpWhiteListLoading(false);
                    });
                }}
              >
                保存
              </Button>
            </div>
          </div>
        </div>
        <Table
          columns={[
            { dataIndex: "id", title: "ID", width: "34%" },
            { dataIndex: "ip", title: "IP地址", width: "20%" },
            { dataIndex: "username", title: "姓名", width: "20%" },
            { dataIndex: "status", title: "状态", width: "20%" },
            {
              width: "0%",
              render: (value, item, index) => {
                return (
                  <div className={styles.operations}>
                    <Popconfirm
                      title="确定授权吗？"
                      trigger="click"
                      placement="bottomRight"
                      onConfirm={async () => {
                        await Fetchers.authorize({ id: item.id })
                          .then(() => {
                            toast.success(`授权成功`);
                            loadTableData();
                            ws.json({
                              action: "send",
                              to: { app: sendToApp, user: item.id },
                              data: { type: "reload" },
                            });
                          })
                          .catch(() => {
                            toast.error(`授权失败`);
                          });
                      }}
                    >
                      <Button type="primary" size="small">
                        授权
                      </Button>
                    </Popconfirm>
                    <Popconfirm
                      title="确定禁用吗？"
                      trigger="click"
                      placement="bottomRight"
                      onConfirm={async () => {
                        await Fetchers.disable({ id: item.id })
                          .then(() => {
                            toast.success(`禁用成功`);
                            loadTableData();
                            ws.json({
                              action: "send",
                              to: { app: sendToApp, user: item.id },
                              data: { type: "reload" },
                            });
                          })
                          .catch(() => {
                            toast.error(`禁用失败`);
                          });
                      }}
                    >
                      <Button type="primary" size="small" danger>
                        禁用
                      </Button>
                    </Popconfirm>
                    <Button
                      type="primary"
                      size="small"
                      onClick={async () => {
                        flushSync(() => {
                          setEditModalOpen(true);
                        });
                        await Utils.sleep();
                        editFormRef.current?.setFieldsValue(item);
                      }}
                    >
                      编辑
                    </Button>
                    <Popconfirm
                      title="确定删除吗？"
                      trigger="click"
                      placement="bottomRight"
                      onConfirm={async () => {
                        const id = item?.id;
                        await Fetchers.delete({ id })
                          .then(() => {
                            toast.success(`删除成功`);
                            loadTableData();
                            ws.json({
                              action: "send",
                              to: { app: sendToApp, user: id },
                              data: { type: "reload" },
                            });
                          })
                          .catch(() => {
                            toast.error(`删除失败`);
                          });
                      }}
                    >
                      <Button type="primary" size="small" danger>
                        删除
                      </Button>
                    </Popconfirm>
                  </div>
                );
              },
            },
          ]}
          dataSource={tableData}
          rowKey="id"
          loading={tableLoading}
          pagination={{ hideOnSinglePage: true }}
        ></Table>
      </div>
      <Modal
        open={editModalOpen}
        onCancel={() => {
          setEditModalOpen(false);
        }}
        title="编辑"
        footer={null}
        destroyOnClose
      >
        <div>
          <Form
            ref={editFormRef}
            layout="vertical"
            onFinish={async (values) => {
              setEditSubmitLoading(true);
              await Fetchers.setUser({ id: values.id, value: values })
                .then(() => {
                  toast.success(`编辑成功`);
                  loadTableData();
                })
                .catch(() => {
                  toast.error(`编辑失败`);
                })
                .finally(() => {
                  setEditSubmitLoading(false);
                  setEditModalOpen(false);
                });
            }}
          >
            <Form.Item name="id" label="ID" rules={[{ required: true }]}>
              <Input disabled></Input>
            </Form.Item>
            <Form.Item name="ip" label="IP地址" rules={[{ required: true }]}>
              <Input disabled></Input>
            </Form.Item>
            <Form.Item name="username" label="姓名" rules={[{ required: true }]}>
              <Input></Input>
            </Form.Item>
            <Form.Item name="status" label="状态" rules={[{ required: false }]}>
              <Input></Input>
            </Form.Item>
            <div style={{ display: "flex", alignItems: "center", justifyContent: "flex-end" }}>
              <Button type="primary" htmlType="submit" loading={editSubmitLoading}>
                确定
              </Button>
            </div>
          </Form>
        </div>
      </Modal>
    </>
  );
}

export default CrxImageCatcher;
