.container {
  width: 100%;
  height: 100%;
  padding: 10px;

  .panel {
    width: 100%;
    height: calc(100vh - 100px);
    padding: 0 10px 10px;
    border: 1px solid #f0f0f0;
    overflow-y: scroll;
    position: relative;
    background-color: #fff;

    h2 {
      position: sticky;
      top: 0;
      left: 0;
      margin: 0;
      padding: 10px;
      background-color: #fff;
      z-index: 2;
    }

    .totalWrapper {
      .totalLabel {
        font-size: 25px;
        font-weight: 700;
        margin: 10px 0;
      }

      .total {
        color: red;
        font-size: 25px;
        font-weight: 700;
      }
    }

    .panelItem {
      margin-bottom: 24px;

      .address {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 5px;
      }

      .remarkLabel {
        font-size: 20px;
        font-weight: 600;
        color: red;
      }
    }

    .productItemWrapper {
      width: 100%;

      .productItem {
        padding: 10px;

        &:not(:last-child) {
          margin-bottom: 20px;
        }

        .productItemInfo {
          display: flex;
          gap: 5px;

          .productImage {
            cursor: pointer;
          }

          .customProps {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 5px;

            .customPropsItem {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              flex-wrap: wrap;
            }
          }

          .productItemActions {
            display: flex;
            align-items: center;
            justify-content: center;
            .reexamine {
              button {
                font-size: 20px;
              }
            }
          }

          .productQty {
            color: red;
            font-size: 20px;
            font-weight: 600;
          }
        }

        .cardTitle,
        .cardContent {
          &:not(:last-child) {
            margin-top: 10px;
          }
        }
      }
    }
  }

  .unverified {
    .productItemActive {
      background-color: rgba(250, 205, 145, 0.3);
    }
  }
}

.viewLabeModal {
  .viewLabeContainer {
    width: 100%;
    background-color: rgba(0, 0, 0, 0.5);

    .imageItem {
      width: 100%;
      height: 90vh;
      text-align: center;
    }
  }
}
