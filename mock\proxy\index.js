const express = require("express");
const cors = require("cors");
const multer = require("multer");
const Axios = require("axios");

const axios = Axios.create({});

// 测试环境的主机映射
const hostMap = {
  dms: {
    test: { ApiHost: "https://test-dms-erp6.cnzlerp.com" },
    production: { ApiHost: "https://dms-erp6.cnzlerp.com" },
  },
  pms: {
    test: {
      ApiHost: "https://test-pms-erp6.cnzlerp.com",
    },
    production: {
      ApiHost: "https://pms-erp6.cnzlerp.com",
    },
  },
  ams: {
    test: {
      ApiHost: "https://test-ams-erp6.cnzlerp.com",
    },
    production: {
      ApiHost: "https://ams-erp6.cnzlerp.com",
    },
  },
};

async function main() {
  const port = process.env.MOCK_PORT || 8080;
  const app = process.env.API_APP || "dms";
  const env = process.env.MOCK_ENV || "test";
  const appHostMap = hostMap[app] || hostMap.dms;
  const host = appHostMap[env]?.ApiHost || appHostMap.test.ApiHost;

  const server = express();

  const uploader = multer({ storage: multer.memoryStorage() });

  // for parsing application/json
  server.use(express.json({ limit: "1000mb" }));
  // for parsing application/x-www-form-urlencoded
  server.use(express.urlencoded({ limit: "1000mb", extended: true }));
  // for cors
  server.use(cors());

  server.all(/(.*)/, async (req, res, next) => {
    let data = req.body;
    let headers = { Authorization: req.headers["authorization"] };
    let apiUrl = req.url;

    const editPattern = /^(\/rest\/v1\/edit\/)/;

    if (editPattern.test(apiUrl)) {
      apiUrl = apiUrl.replace(editPattern, "/rest/v1/");
    }

    // 处理文件上传
    if (apiUrl.includes("/rest/v1/upload")) {
      await new Promise((resolve, reject) => {
        uploader.single("file")(req, res, (err) => {
          err ? reject(err) : resolve(req);
        });
      });
      headers["Content-Type"] = "multipart/form-data;charset=UTF-8";
      data = new FormData();
      data.append("file", req.file);
      Object.entries(req.body).forEach(([key, value]) => {
        data.append(key, value);
      });
    }

    const url = `${host}${apiUrl}`;
    console.log(`[API Proxy] ${req.method.toUpperCase()} ${url} host=> ${host}`);

    try {
      const response = await axios({
        method: req.method.toUpperCase(),
        url: url,
        params: req.query,
        data: data,
        headers: headers,
      });
      res.status(response.status).json(response.data);
    } catch (err) {
      res.status(err?.response?.status || 500).json(err);
    }
  });

  const httpServer = server.listen(port, "0.0.0.0", () => {
    console.log(`API proxy server started: http://localhost:${port} -> ${host}`);
  });

  process.on("SIGUSR2", async () => {
    await new Promise((resolve) => {
      httpServer.close(resolve);
    });
    process.kill(process.pid, "SIGTERM");
  });
}

main().catch((err) => {
  console.error(err);
});
