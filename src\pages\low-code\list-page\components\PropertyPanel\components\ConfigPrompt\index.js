import styles from "./index.module.scss";
import { useState } from "react";
import { Mo<PERSON>, <PERSON>bs, Button, Space, Typography, Divider } from "antd";
import { CopyOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import JSONEditor from "components/common/JSONEditor";

const { Text, Paragraph } = Typography;

function EditableConfigPrompt({ data }) {
  const [visible, setVisible] = useState(false);
  const [activeKey, setActiveKey] = useState(data[0]?.component || data[0]?.type);

  const tabItems = data.map((category) => ({
    key: category?.component || category?.type,
    label: category.title,
    children: (
      <div tab={category.title} key={category.component}>
        <Paragraph>{category.description}</Paragraph>

        {category.examples.map((example, index) => (
          <div key={index} className={styles.exampleItem}>
            <div className={styles.exampleHeader}>
              <h4>{example.title}</h4>
              <Space>
                <Text
                  key={index}
                  copyable={{
                    text: JSON.stringify(example.config, null, 2),
                    icon: <CopyOutlined />,
                    tooltips: false,
                  }}
                />
              </Space>
            </div>
            <div className={styles.codeBlock}>
              {/* <pre>{JSON.stringify(example.config, null, 2)}</pre> */}
              <JSONEditor value={JSON.stringify(example.config, null, 2)} />
            </div>
            {index < category.examples.length - 1 && <Divider />}
          </div>
        ))}
      </div>
    ),
  }));

  const handleCancel = () => {
    setVisible(false);
  };

  return (
    <div className={styles.editableConfigEditor}>
      <Button
        className={styles.filterButton}
        type="text"
        icon={<QuestionCircleOutlined />}
        onClick={() => setVisible(true)}
        title="示例"
      ></Button>
      <Modal
        title="示例"
        open={visible}
        onCancel={handleCancel}
        width={1000}
        style={{ top: 50 }}
        footer={null}
        destroyOnClose
      >
        <Tabs activeKey={activeKey} onChange={setActiveKey} items={tabItems} />
      </Modal>
    </div>
  );
}

export default EditableConfigPrompt;
