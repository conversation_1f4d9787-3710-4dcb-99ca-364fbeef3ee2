import styles from "./index.module.scss";
import Utils from "@/utils";

function UploadItem(props) {
  const { item, isShowFileInfo, file } = props;
  const { name, size } = { ...file, ...file?.image };

  return (
    <div className={styles.uploadItem}>
      {item}
      {isShowFileInfo && (
        <div className={styles.fileInfo}>
          {name && <div className={styles.fileName}>{name}</div>}
          {size && <div className={styles.fileSize}>{Utils.humanize.bytes.stringify(size)}</div>}
        </div>
      )}
    </div>
  );
}

export default UploadItem;
