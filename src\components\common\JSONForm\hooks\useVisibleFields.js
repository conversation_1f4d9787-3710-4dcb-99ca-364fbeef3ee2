import { useState, useCallback } from "react";
import Enums from "@/enums";

function useVisibleFields({ formData = {} }) {
  const updateVisibility = useCallback(({ showFieldsWhen, value }) => {
    if (!showFieldsWhen) return;

    const updates = {};
    Object.entries(showFieldsWhen).forEach(([fieldKey, conditions]) => {
      updates[fieldKey] = Array.isArray(value) ? value.some((v) => conditions.includes(v)) : conditions.includes(value);
    });

    return updates;
  }, []);

  const [visibleFields, setVisibleFields] = useState(() => initializeVisibleFields(formData));

  function getValueByPath(obj, path) {
    if (!obj || !path) return undefined;
    if (typeof path === "string") return obj[path];

    return path.reduce((current, key) => {
      return current ? current[key] : undefined;
    }, obj);
  }

  function initializeVisibleFields(formData) {
    if (!formData?.formItems) return {};
    let initialVisibility = {};

    function traverseItems(formItems) {
      formItems.forEach((item) => {
        // 处理子项
        if (item?.children) {
          traverseItems(item.children);
        }

        // 处理折叠面板
        if (item.component === Enums.Components.Collapse) {
          item?.props?.items?.forEach((panel) => {
            if (panel?.children) {
              traverseItems(panel.children);
            }
          });
        }

        // 处理字段可见
        if (item?.showFieldsWhen) {
          const initialValue = getValueByPath(formData?.props?.initialValues, item.key);

          if (initialValue !== undefined) {
            const updates = updateVisibility({
              showFieldsWhen: item.showFieldsWhen,
              value: initialValue,
            });
            initialVisibility = { ...initialVisibility, ...updates };
          }
        }
      });
    }

    traverseItems(formData.formItems);
    return initialVisibility;
  }

  function handleFieldVisibility({ value, item, form }) {
    if (item?.showFieldsWhen) {
      const updates = updateVisibility({ showFieldsWhen: item.showFieldsWhen, value });

      const previousVisibility = { ...visibleFields };

      // 清除不显示字段的值
      Object.entries(updates).forEach(([fieldKey, isVisible]) => {
        if (!isVisible && previousVisibility[fieldKey] !== false) {
          form?.setFieldValue(fieldKey, undefined);
        }
      });

      setVisibleFields((prevFields) => ({ ...prevFields, ...updates }));
    }
  }

  return { visibleFields, handleFieldVisibility };
}

export default useVisibleFields;
