module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      columns: [
        {
          dataIndex: "material",
          title: "材质",
          width: 80,
          filter: {
            component: "Select",
            searchable: true,
            props: {
              options: [
                {
                  label: "锆石",
                  value: "锆石",
                },
                {
                  label: "刚玉",
                  value: "刚玉",
                },
                {
                  label: "尖晶",
                  value: "尖晶",
                },
                {
                  label: "纳米",
                  value: "纳米",
                },
                {
                  label: "水晶",
                  value: "水晶",
                },
                {
                  label: "天然粉晶",
                  value: "天然粉晶",
                },
                {
                  label: "珍珠",
                  value: "珍珠",
                },
              ],
            },
          },
        },
        {
          dataIndex: "color",
          title: "色系",
          width: 80,
          filter: {
            component: "Select",
            searchable: true,
            props: {
              options: [
                { label: "钻石白", value: "钻石白" },
                { label: "粉红色", value: "粉红色" },
              ],
            },
          },
        },
        {
          dataIndex: "shape",
          title: "形状",
          width: 80,
          filter: {
            component: "Select",
            searchable: true,
            props: {
              options: [
                { label: "正八宝塔", value: "正八宝塔" },
                { label: "长方形", value: "长方形" },
              ],
            },
          },
        },
        {
          dataIndex: "specifications",
          title: "规格",
          width: 80,
          sorter: {
            multiple: 1,
          },
          filter: { component: "Search" },
        },
        {
          dataIndex: "cutting",
          title: "切割",
          width: 80,
          filter: {
            component: "Select",
            searchable: true,
            props: {
              options: [
                { label: "车花（八心八箭）", value: "车花（八心八箭）" },
                { label: "车平", value: "车平" },
              ],
            },
          },
        },
        {
          dataIndex: "carat_weight",
          title: "克拉数",
          width: 80,
          sorter: {
            multiple: 3,
          },
          filter: { component: "Search" },
        },
        { dataIndex: "fids", title: "图片", width: 100, valueType: "image" },
      ],
      dataSource: [
        {
          id: 2944,
          template_id: 43660,
          component_id: 2944,
          template_no: "",
          amount: 3,
          type: "side_stone",
          custom_value: "",
          created_at: 1741683244,
          updated_at: 1741683244,
          material: "高碳钻",
          color: "A50(中海蓝 Aqua Blue)",
          color_value: "a50",
          shape_value: "cushion1",
          shape: "肥长方",
          specifications: "8*10",
          cutting: "冰花切",
          weight: "0.80000",
          carat_weight: "4.00000",
          fids: [
            {
              width: 50,
              height: 50,
              src: "https://test-res.jeulia.com/product/0/7/800x800/6571369a28570.jpg",
            },
          ],
        },
        {
          id: 2930,
          template_id: 43660,
          component_id: 2930,
          template_no: "",
          amount: 4,
          type: "side_stone",
          custom_value: "",
          created_at: 1741683244,
          updated_at: 1741683244,
          material: "高碳钻",
          color: "A59（皇家蓝，Royal Blue）",
          shape: "肥长方",
          color_value: "a59",
          shape_value: "cushion1",
          specifications: "7*9",
          cutting: "冰花切",
          weight: "0.60000",
          carat_weight: "3.00000",
          fids: [
            {
              width: 50,
              height: 50,
              src: "https://test-res.jeulia.com/product/0/7/800x800/6571369a28570.jpg",
            },
          ],
        },
        {
          id: 2786,
          template_id: 43660,
          component_id: 2786,
          template_no: "",
          amount: 5,
          type: "side_stone",
          custom_value: "",
          created_at: 1741683244,
          updated_at: 1741683244,
          material: "尖晶",
          color: "113#蓝",
          shape: "心形",
          color_value: "sapphireblue113",
          shape_value: "heart",
          specifications: "5*5",
          cutting: "车花（八心八箭）",
          weight: "0.10600",
          carat_weight: "0.53000",
          fids: [
            {
              width: 50,
              height: 50,
              src: "https://test-res.jeulia.com/product/0/7/800x800/6571369a28570.jpg",
            },
            {
              width: 50,
              height: 50,
              src: "https://test-je5-oms.cnzlerp.com/uploads/product/e/9/6015357862b9e.jpg",
            },
          ],
        },
      ],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 3,
        position: ["bottomRight"],
        showQuickJumper: false,
        showSizeChanger: true,
        size: "small",
      },
    },
  });
};
