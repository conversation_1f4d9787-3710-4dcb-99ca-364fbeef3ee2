const Api = require("../../../src/fetchers/api");

module.exports = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      breadcrumbs: [],
      actions: [],
      content: {
        ticket_id: 213123,
        increment_id: 1122,
        children: [
          {
            component: "Row",
            props: {
              gutter: [0, 10],
            },
            children: [
              {
                component: "Col",
                props: { span: 24 },
                children: [
                  {
                    component: "Card",
                    props: { bordered: true },
                    children: [
                      {
                        component: "Row",
                        props: {
                          style: { marginBottom: "10px" },
                        },
                        children: [
                          {
                            component: "Col",
                            props: { flex: 1 },
                            children: [
                              {
                                component: "TextEditable",
                                content: [
                                  {
                                    text: "Send to 【",
                                    props: {
                                      tag: "span",
                                    },
                                  },
                                  {
                                    text: "<EMAIL>",
                                    props: {
                                      tag: "span",
                                    },
                                    key: "email",
                                    editable: {
                                      component: "Input",
                                      props: {},
                                      request: {
                                        url: "http://192.168.2.110:8081/rest/v1/order",
                                        data: {
                                          id: 1,
                                        },
                                      },
                                    },
                                  },
                                  {
                                    text: "】 via 【<EMAIL>】",
                                  },
                                ],
                              },
                            ],
                          },
                          {
                            component: "Col",
                            children: [
                              {
                                component: "TextEditable",
                                content: [
                                  {
                                    text: "Ticket Status:【",
                                    props: {
                                      tag: "span",
                                    },
                                  },
                                  {
                                    text: "Pending",
                                    props: {
                                      tag: "span",
                                    },
                                    key: "status",
                                    editable: {
                                      component: "Select",
                                      props: {
                                        options: [
                                          {
                                            label: "Pending",
                                            value: "pending",
                                          },
                                          {
                                            label: "Open",
                                            value: "open",
                                          },
                                        ],
                                      },
                                      request: {
                                        url: "http://192.168.2.110:8081/rest/v1/order",
                                        data: {
                                          id: 1,
                                        },
                                      },
                                    },
                                  },
                                  {
                                    text: "】",
                                  },
                                ],
                              },
                            ],
                          },
                        ],
                      },
                      {
                        component: "Form",
                        props: {
                          id: "form",
                          initialValues: { comment: "Regarding your order JE010582 from Jeulia", target_lang: "en" },
                          layout: "vertical",
                          labelCol: {
                            span: 24,
                          },
                          wrapperCol: {
                            span: 24,
                          },
                        },
                        formItems: [
                          {
                            key: "comment",
                            label: "",
                            component: "Input",
                            props: { placeholder: "Subject 必填填写(order# 订单号)" },
                            rules: [{ required: true, message: "This is a required field" }],
                          },
                          {
                            key: "target_lang",
                            label: "语言必选 翻译为",
                            component: "Select",
                            props: {
                              options: [
                                { label: "英语", value: "en" },
                                { label: "法语", value: "fr" },
                                { label: "中文", value: "zh" },
                              ],
                            },
                            cascader: {
                              name: "select",
                              searchApi: `${Api.searchProductSpu}?id=1`,
                            },
                            rules: [{ required: true, message: "This is a required field" }],
                          },
                          {
                            key: "select",
                            label: "",
                            component: "SelectTemplate",
                            props: {
                              placeholder: "请选择回复模版",
                              mode: "multiple",
                              options: [
                                {
                                  label: "manager",
                                  title: "manager",
                                  options: [
                                    {
                                      label: "Jack",
                                      value: "Jack",
                                    },
                                    {
                                      label: "Lucy",
                                      value: "Lucy",
                                    },
                                  ],
                                },
                                {
                                  label: "manager2",
                                  title: "manager2",
                                  options: [
                                    {
                                      label: "Jack2",
                                      value: "Jack2",
                                    },
                                    {
                                      label: "Lucy2",
                                      value: "Lucy2",
                                    },
                                  ],
                                },
                              ],
                              optionsApi:
                                "http://192.168.2.110:8081/rest/v1/global/taxonomy/search?identifier=defective_tag",
                            },
                            templateApi: "http://192.168.2.110:8081/rest/v1/ticket/ticket/ajax-template",
                            addonBefore: {
                              content: "回复模板",
                              props: {},
                            },
                            cascader: {
                              name: "richTextEditor",
                            },
                          },
                          {
                            key: "richTextEditor",
                            label: "富文本编辑器",
                            component: "RichTextEditorTemplate",
                            props: { minHeight: 300, placeholder: "Enter your text", disabled: false },
                            rules: [{ required: true, message: "This is a required field" }],
                          },
                          {
                            key: "fileUpload",
                            label: "上传文件",
                            component: "FileUpload",
                            props: {
                              listType: "picture-card",
                              action: Api.uploadFile,
                              data: { disk: "s3-static" },
                              multiple: true,
                              accept: ".jpg, .jpeg, .png, .mp4, .pdf",
                            },
                            rules: [{ required: true, message: "This is a required field" }],
                          },
                        ],
                        actions: [
                          {
                            title: "删除",
                            props: {
                              type: "primary",
                              danger: true,
                            },
                            command: {
                              type: "request",
                              request: {
                                url: Api.order,
                                data: {
                                  action: "batchDelete",
                                },
                                method: "POST",
                              },
                              confirm: "确定删除吗？",
                            },
                          },
                          {
                            title: "推迟处理",
                            props: {
                              type: "primary",
                              style: {
                                background: "#ffc107",
                              },
                            },
                            type: "submit",
                            command: {
                              type: "request",
                              request: {
                                url: Api.order,
                                data: {
                                  action: "batchDelete",
                                },
                              },
                            },
                          },
                          {
                            title: "推迟处理",
                            props: {
                              type: "primary",
                              danger: true,
                            },
                            type: "submit",
                            command: {},
                          },
                          {
                            title: "推迟处理",
                            props: {
                              type: "primary",
                              danger: true,
                              icon: "https://static.bizseas.com/static/icon/file.svg",
                            },
                            command: {},
                            type: "submit",
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                component: "Col",
                props: { span: 24 },
                children: [
                  {
                    component: "Card",
                    props: { bordered: true },
                    children: [
                      {
                        component: "Row",
                        props: { style: { marginBottom: 10 } },
                        children: [
                          {
                            component: "Col",
                            props: { span: 4 },
                          },
                          {
                            component: "Col",
                            props: { span: 20 },
                            children: [
                              {
                                component: "EmailCard",
                                content: {
                                  comment_id: 2323,
                                  external_id: 182728,
                                  profile_image: "https://picsum.photos/300/300",
                                  emoji: "https://test-je5-oms.cnzlerp.com/emoji/beaming-face-with-smiling-eyes.png",
                                  emoji_radio: {
                                    props: {
                                      options: [
                                        {
                                          label: "https://test-je5-oms.cnzlerp.com/emoji/slightly-smiling-face.png",
                                          value: "1",
                                        },
                                        {
                                          label:
                                            "https://test-je5-oms.cnzlerp.com/emoji/beaming-face-with-smiling-eyes.png",
                                          value: "2",
                                        },
                                        {
                                          label: "https://test-je5-oms.cnzlerp.com/emoji/frowning-face.png",
                                          value: "3",
                                        },
                                        {
                                          label: "https://test-je5-oms.cnzlerp.com/emoji/loudly-crying-face.png",
                                          value: "4",
                                        },
                                        {
                                          label: "https://test-je5-oms.cnzlerp.com/emoji/pouting-face.png",
                                          value: "5",
                                        },
                                      ],
                                    },
                                    command: {
                                      type: "request",
                                      request: {
                                        url: Api.order,
                                        data: {
                                          action: "batchDelete",
                                        },
                                      },
                                    },
                                    refresh: {
                                      params: {
                                        action: "emoji",
                                      },
                                    },
                                  },
                                  tags: [
                                    {
                                      text: "成功",
                                      props: {
                                        color: "green",
                                      },
                                    },
                                  ],
                                  actions: [
                                    {
                                      title: "重发邮件",
                                      props: {
                                        type: "link",
                                      },
                                      command: {
                                        type: "request",
                                        request: {
                                          url: Api.order,
                                          data: {
                                            action: "batchDelete",
                                          },
                                        },
                                      },
                                      refresh: {
                                        params: {
                                          action: "regain",
                                        },
                                      },
                                    },
                                  ],
                                  requester: "John Doe",
                                  datetime: "2024-09-03 18:19:30",
                                  subject: "[Re: Order Priority Confirmation from Jeulia!]",
                                  content: `<p>Hi&nbsp;{customer_name},</p>
  
                                      <p>&nbsp;</p>
  
                                      <p>The reason your payment was refused was due to &quot;Suspected&nbsp;fraud&quot;.</p>
  
                                      <p>&nbsp;</p>
  
                                      <p>Suspected&nbsp;fraud&nbsp;is&nbsp;one&nbsp;of&nbsp;the&nbsp;most&nbsp;common&nbsp;reasons&nbsp;for&nbsp;your&nbsp;card&nbsp;to&nbsp;be&nbsp;declined.&nbsp;Credit&nbsp;card&nbsp;companies&nbsp;are&nbsp;usually&nbsp;hypervigilant&nbsp;when&nbsp;it&nbsp;comes&nbsp;to&nbsp;detecting&nbsp;suspicious&nbsp;activity,&nbsp;including&nbsp;unusually&nbsp;large&nbsp;purchases&nbsp;that&nbsp;are&nbsp;inconsistent&nbsp;with&nbsp;your&nbsp;spending&nbsp;habits&nbsp;and&nbsp;transactions&nbsp;that&nbsp;are&nbsp;made&nbsp;far&nbsp;away&nbsp;from&nbsp;your&nbsp;usual&nbsp;location.<br />
                                      <br />
                                      While&nbsp;fraud&nbsp;detection&nbsp;can&nbsp;help&nbsp;to&nbsp;catch&nbsp;and&nbsp;stop&nbsp;instances&nbsp;of&nbsp;illegal&nbsp;card&nbsp;activity,&nbsp;it&nbsp;can&nbsp;also&nbsp;sometimes&nbsp;lead&nbsp;to&nbsp;credit&nbsp;card&nbsp;companies&nbsp;flagging&nbsp;legitimate&nbsp;charges.<br />
                                      <br />
                                      If&nbsp;your&nbsp;card&nbsp;has&nbsp;been&nbsp;frozen&nbsp;due&nbsp;to&nbsp;a&nbsp;case&nbsp;of&nbsp;mistaken&nbsp;fraud&nbsp;detection,&nbsp;you&nbsp;can&nbsp;call&nbsp;the&nbsp;credit&nbsp;card&nbsp;company&nbsp;to&nbsp;verify&nbsp;that&nbsp;you&#39;ve&nbsp;authorized&nbsp;the&nbsp;online&nbsp;transaction&nbsp;in&nbsp;question.</p>
  
                                      <p>&nbsp;</p>
  
                                      <p>Thank you,&nbsp;</p>
  
                                      <p>{agent_name},</p>
  
                                      <p>The&nbsp;{brand_name}&nbsp;Customer&nbsp;Satisfaction&nbsp;Team</p>`,
                                  translate_content: `<p>Hi&nbsp;{customer_name},</p>
  
                                      <p>&nbsp;</p>
  
                                      <p>The reason your payment was refused was due to &quot;Suspected&nbsp;fraud&quot;.</p>
  
                                      <p>&nbsp;</p>
  
                                      <p>Suspected&nbsp;fraud&nbsp;is&nbsp;one&nbsp;of&nbsp;the&nbsp;most&nbsp;common&nbsp;reasons&nbsp;for&nbsp;your&nbsp;card&nbsp;to&nbsp;be&nbsp;declined.&nbsp;Credit&nbsp;card&nbsp;companies&nbsp;are&nbsp;usually&nbsp;hypervigilant&nbsp;when&nbsp;it&nbsp;comes&nbsp;to&nbsp;detecting&nbsp;suspicious&nbsp;activity,&nbsp;including&nbsp;unusually&nbsp;large&nbsp;purchases&nbsp;that&nbsp;are&nbsp;inconsistent&nbsp;with&nbsp;your&nbsp;spending&nbsp;habits&nbsp;and&nbsp;transactions&nbsp;that&nbsp;are&nbsp;made&nbsp;far&nbsp;away&nbsp;from&nbsp;your&nbsp;usual&nbsp;location.<br />
                                      <br />
                                      While&nbsp;fraud&nbsp;detection&nbsp;can&nbsp;help&nbsp;to&nbsp;catch&nbsp;and&nbsp;stop&nbsp;instances&nbsp;of&nbsp;illegal&nbsp;card&nbsp;activity,&nbsp;it&nbsp;can&nbsp;also&nbsp;sometimes&nbsp;lead&nbsp;to&nbsp;credit&nbsp;card&nbsp;companies&nbsp;flagging&nbsp;legitimate&nbsp;charges.<br />
                                      <br />
                                      If&nbsp;your&nbsp;card&nbsp;has&nbsp;been&nbsp;frozen&nbsp;due&nbsp;to&nbsp;a&nbsp;case&nbsp;of&nbsp;mistaken&nbsp;fraud&nbsp;detection,&nbsp;you&nbsp;can&nbsp;call&nbsp;the&nbsp;credit&nbsp;card&nbsp;company&nbsp;to&nbsp;verify&nbsp;that&nbsp;you&#39;ve&nbsp;authorized&nbsp;the&nbsp;online&nbsp;transaction&nbsp;in&nbsp;question.</p>
  
                                      <p>&nbsp;</p>
  
                                      <p>Thank you,&nbsp;</p>
  
                                      <p>{agent_name},</p>
  
                                      <p>The&nbsp;{brand_name}&nbsp;Customer&nbsp;Satisfaction&nbsp;Team</p>`,
                                  attachments: [
                                    {
                                      type: "image",
                                      src: "https://test-je5-oms.cnzlerp.com/uploads/oms/ticket/2024/09/02/3/8/66d583bf59b83.jpg",
                                      name: "",
                                    },
                                    {
                                      type: "video",
                                      src: "https://test-je5-oms.cnzlerp.com/uploads/oms/ticket/2024/09/02/1/f/66d583b58a7f1.mp4",
                                      name: "",
                                    },
                                    {
                                      type: "image",
                                      src: "https://test-je5-oms.cnzlerp.com/uploads/oms/ticket/2024/09/02/3/8/66d583bf59b83.jpg",
                                      name: "",
                                    },
                                    {
                                      type: "file",
                                      src: "https://test-je5-oms.cnzlerp.com/uploads/oms/ticket/2024/09/02/9/0/66d583d753509.pdf",
                                      name: "_Retail行业_GA4迁移Playbook _1_.pdf",
                                    },
                                    {
                                      type: "file",
                                      src: "https://test-je5-oms.cnzlerp.com/uploads/oms/ticket/2024/09/02/9/0/66d583d753509.pdf",
                                      name: "_Retail行业_GA4迁移Playbook _1_.pdf",
                                    },
                                  ],
                                },
                                props: {},
                              },
                            ],
                          },
                        ],
                      },
                      {
                        component: "Row",
                        children: [
                          {
                            component: "Col",
                            props: { span: 20 },
                            children: [
                              {
                                component: "EmailCard",
                                content: {
                                  comment_id: 2323,
                                  external_id: 182728,
                                  profile_image: "https://picsum.photos/300/300",
                                  emoji: "https://test-je5-oms.cnzlerp.com/emoji/beaming-face-with-smiling-eyes.png",
                                  requester: "John Doe",
                                  datetime: "2024-09-03 18:19:30",
                                  subject: "[Re: Order Priority Confirmation from Jeulia!]",
                                  content: `<p>Hi&nbsp;{customer_name},</p>
  
                                      <p>&nbsp;</p>
  
                                      <p>The reason your payment was refused was due to &quot;Suspected&nbsp;fraud&quot;.</p>
  
                                      <p>&nbsp;</p>
  
                                      <p>Suspected&nbsp;fraud&nbsp;is&nbsp;one&nbsp;of&nbsp;the&nbsp;most&nbsp;common&nbsp;reasons&nbsp;for&nbsp;your&nbsp;card&nbsp;to&nbsp;be&nbsp;declined.&nbsp;Credit&nbsp;card&nbsp;companies&nbsp;are&nbsp;usually&nbsp;hypervigilant&nbsp;when&nbsp;it&nbsp;comes&nbsp;to&nbsp;detecting&nbsp;suspicious&nbsp;activity,&nbsp;including&nbsp;unusually&nbsp;large&nbsp;purchases&nbsp;that&nbsp;are&nbsp;inconsistent&nbsp;with&nbsp;your&nbsp;spending&nbsp;habits&nbsp;and&nbsp;transactions&nbsp;that&nbsp;are&nbsp;made&nbsp;far&nbsp;away&nbsp;from&nbsp;your&nbsp;usual&nbsp;location.<br />
                                      <br />
                                      While&nbsp;fraud&nbsp;detection&nbsp;can&nbsp;help&nbsp;to&nbsp;catch&nbsp;and&nbsp;stop&nbsp;instances&nbsp;of&nbsp;illegal&nbsp;card&nbsp;activity,&nbsp;it&nbsp;can&nbsp;also&nbsp;sometimes&nbsp;lead&nbsp;to&nbsp;credit&nbsp;card&nbsp;companies&nbsp;flagging&nbsp;legitimate&nbsp;charges.<br />
                                      <br />
                                      If&nbsp;your&nbsp;card&nbsp;has&nbsp;been&nbsp;frozen&nbsp;due&nbsp;to&nbsp;a&nbsp;case&nbsp;of&nbsp;mistaken&nbsp;fraud&nbsp;detection,&nbsp;you&nbsp;can&nbsp;call&nbsp;the&nbsp;credit&nbsp;card&nbsp;company&nbsp;to&nbsp;verify&nbsp;that&nbsp;you&#39;ve&nbsp;authorized&nbsp;the&nbsp;online&nbsp;transaction&nbsp;in&nbsp;question.</p>
  
                                      <p>&nbsp;</p>
  
                                      <p>Thank you,&nbsp;</p>
  
                                      <p>{agent_name},</p>
  
                                      <p>The&nbsp;{brand_name}&nbsp;Customer&nbsp;Satisfaction&nbsp;Team</p>`,
                                },
                              },
                            ],
                          },
                          {
                            component: "Col",
                            props: { span: 4 },
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    },
    command: {},
  });
};
