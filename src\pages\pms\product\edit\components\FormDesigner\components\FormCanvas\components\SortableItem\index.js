import styles from "./index.module.scss";
import classNames from "classnames";

import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

import FormFieldItem from "../FormFieldItem";
import GroupContainer from "../GroupContainer";
import Enums from "../../../../common/enums";

function SortableItem({ widget, index, onSelect, selectedKey, parentKey, ...restProps }) {
  const isSelected = selectedKey === widget.key;
  const isGroup = widget.isGroup ?? widget.component_config?.isGroup ?? false;

  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: widget.key,
    data: {
      id: widget.key,
      parentKey: parentKey ?? Enums.DroppableIds.FormCanvas,
      index,
      component: widget,
      isGroup,
      isGhost: widget?.type === "ghost",
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  function handleSelectWidget(e) {
    e.stopPropagation();
    onSelect(widget);
  }

  return (
    <div ref={setNodeRef} className={classNames(styles.sortableItem)} style={style} onClick={handleSelectWidget}>
      {isGroup ? (
        <GroupContainer
          widget={widget}
          index={index}
          isSelected={isSelected}
          handleProps={{
            ...attributes,
            ...listeners,
          }}
          isDragging={isDragging}
          onSelect={onSelect}
          selectedKey={selectedKey}
          {...restProps}
        />
      ) : (
        <FormFieldItem
          widget={widget}
          index={index}
          isSelected={isSelected}
          handleProps={{
            ...attributes,
            ...listeners,
          }}
          isDragging={isDragging}
          {...restProps}
        />
      )}
    </div>
  );
}

export default SortableItem;
