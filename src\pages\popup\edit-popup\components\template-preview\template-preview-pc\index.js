import styles from "./index.module.scss";
import { lazy, Suspense, useEffect, useRef } from "react";
import Utils from "utils";
import Helper from "helpers";
import Enums from "enums";
import { countries } from "@/pages/popup/edit-popup/vars";

const SubscribePhonePopup = lazy(() => import("./content/subscribe-phone"));
const LuckyDrawSubscribePopup = lazy(() => import("./content/lucky-draw-subscribe-popup"));

const Components = {
  SubscribePhonePopup,
  LuckyDrawSubscribePopup,
};

function TemplatePreviewPc(props) {
  const { type, device } = props;
  const contentRef = useRef();
  const paramsRef = useRef({ props, type, device });
  const componentName = `${Enums.PopupTypes[type]}${Utils.textCapitalize(device)}`;

  paramsRef.current = {
    ...paramsRef.current,
    componentName,
  };

  // function renderComponent() {
  //   const { type } = props;
  //   const Component = Components[type];
  //   return (
  //     <Suspense fallback={<div>Loading...</div>}>
  //       <Component {...props} countries={countries} preview />
  //     </Suspense>
  //   );
  // }

  function renderRemoteComponent(props) {
    const { ReactDOM, Component } = paramsRef.current;
    if (ReactDOM && Component) {
      ReactDOM?.render(<Component {...props} preview />, contentRef.current);
    }
  }

  // 加载远程组件
  useEffect(() => {
    (async () => {
      const { props, componentName } = paramsRef.current;
      const { dependencies, popups } = await Helper.getRemoteComponentDependencies();
      const { ReactDOM } = dependencies;
      const Component = popups?.[componentName];

      if (!Component) {
        throw new Error(`Component ${componentName} not found`);
      }

      paramsRef.current.ReactDOM = ReactDOM;
      paramsRef.current.Component = Component;

      renderRemoteComponent(props);
    })();
  }, []);

  useEffect(() => {
    renderRemoteComponent(props);
  }, [props]);

  return (
    <div className={styles.container}>
      <div ref={contentRef}></div>
      {/* {renderComponent()} */}
    </div>
  );
}

export default TemplatePreviewPc;
