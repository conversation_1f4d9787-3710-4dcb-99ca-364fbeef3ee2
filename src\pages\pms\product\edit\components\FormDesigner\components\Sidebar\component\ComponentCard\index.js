import { createElement } from "react";
import classNames from "classnames";
import styles from "./index.module.scss";
import {
  EditOutlined,
  CopyOutlined,
  DownOutlined,
  UnorderedListOutlined,
  PictureOutlined,
  GroupOutlined,
  UpSquareOutlined,
  ApartmentOutlined,
  SnippetsOutlined,
  DiffOutlined,
  FileAddOutlined,
  FontSizeOutlined,
  HeatMapOutlined,
  FileTextOutlined,
  InsertRowAboveOutlined,
  EyeOutlined,
  FileDoneOutlined,
  FileImageOutlined,
  GlobalOutlined,
  RadiusSettingOutlined,
  AreaChartOutlined,
  CheckSquareOutlined,
} from "@ant-design/icons";

const IconMap = {
  EditOutlined,
  CopyOutlined,
  DownOutlined,
  UnorderedListOutlined,
  PictureOutlined,
  GroupOutlined,
  UpSquareOutlined,
  ApartmentOutlined,
  SnippetsOutlined,
  DiffOutlined,
  FileAddOutlined,
  FontSizeOutlined,
  HeatMapOutlined,
  FileTextOutlined,
  InsertRowAboveOutlined,
  EyeOutlined,
  <PERSON>DoneOutlined,
  <PERSON><PERSON>mageOutlined,
  <PERSON>Outlined,
  RadiusSettingOutlined,
  AreaChartOutlined,
  CheckSquareOutlined,
};

function ComponentCard({ className, component }) {
  function getIcon(componentConfig) {
    const iconName = componentConfig?.icon;
    try {
      const IconComponent = IconMap[iconName];
      return IconComponent ? createElement(IconComponent) : <EditOutlined />;
    } catch (error) {
      return <EditOutlined />;
    }
  }

  return (
    <div className={classNames(styles.componentCard, className)}>
      <div className={styles.componentCardContent}>
        {getIcon(component.component_config)}
        {component?.widget_name && <span>{component.widget_name}</span>}
      </div>
    </div>
  );
}

export default ComponentCard;
