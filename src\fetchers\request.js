import Axios from "axios";
import Utils from "utils";
import Api from "./api";
import Helper from "helpers";
import EnvHelper from "helpers/env-helper";

const axios = Axios.create({
  timeout: 30000,
});
const noTokenApis = [Api.login];

export const getRequestHeaders = (config) => {
  const { token, headers } = Helper.getBaseHeaders();
  const apiPath = Utils.createURL(config.url).pathname;

  // 处理认证token
  if (!config.headers.Authorization && token && !noTokenApis.includes(apiPath)) {
    headers.Authorization = `Bearer ${token}`;
  }

  return headers;
};

axios.interceptors.request.use(
  (config) => {
    config.headers = { ...config.headers, ...getRequestHeaders(config) };

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

axios.interceptors.response.use(
  (response) => {
    if (response.data?.success) {
      if (response.data?.message) {
        Helper.openMessage({ type: "success", content: response.data?.message });
      }
    } else {
      if (response.data?.message) {
        Helper.openMessage({ type: "error", content: response.data?.message });
      }
    }
    if (response.data?.command) {
      Helper.commandHandler({ command: response.data?.command });
    }
    return response;
  },
  (error) => {
    if (error.code === "ECONNABORTED" && error.message.includes("timeout")) {
      const originalRequest = error.config;
      new Promise((resolve) => {
        Helper.modal.confirm({
          title: "操作超时，请重试",
          onOk: () => {
            axios.request(originalRequest);
            resolve();
          },
          afterClose: () => resolve(),
        });
      });
    }

    const response = error?.response;

    if (response) {
      const { status, data } = response;
      const { command, message } = data ?? {};

      if (status === 401 && window.location.pathname !== "/login") {
        window.location.href = `${EnvHelper.RedirectOrigin}/login?redirect=${window.location.href}`;
        return;
      }

      if (command) {
        Helper.commandHandler({ command });
        return;
      }

      if (status >= 400 && status < 500) {
        const messageText = message || `${status} ${response?.statusText}` || "";
        Helper.openMessage({ type: "error", content: messageText });
      } else if (status >= 500 && status < 600) {
        Helper.openMessage({ type: "error", content: "服务端错误，请联系管理员" });
      }
    }

    return Promise.reject(error);
  }
);

export default axios;
