import { useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Flex, Button } from "antd";
import JSONForm from "@/components/common/JSONForm";
import { clearFields } from "@/components/common/JSONForm/vars";
import Utils from "utils";
import Enums from "@/enums";
import Search from "@/components/business/Search";

function QueryFilter({ data, onFinish }) {
  const { filterKey = null, showButtons = false, ...restData } = data;
  const formRef = useRef(null);
  const location = useLocation();
  const navigate = useNavigate();
  const filterParams = getFilterParams(filterKey);
  const defaultValues = { ...data?.props?.initialValues, ...filterParams };

  const extendComponents = {
    [Enums.Components.Search]: ({ item }) => {
      const { isPressEnterSubmit, isAutoFocus, isSelected, ...itemOtherProps } = item.props || {};
      return (
        <Search
          {...itemOtherProps}
          autoFocus={isAutoFocus}
          isSelected={isSelected}
          onPressEnter={isPressEnterSubmit ? onHandleSearch : undefined}
          onSearch={onHandleSearch}
        />
      );
    },
  };

  function onHandleFinish(values, { submit }) {
    if (onFinish) {
      onFinish?.(values, { submit });
      return;
    }

    const searchParams = new URLSearchParams(location.search);
    const formFields = formRef.current?.getFieldsValue() || {};

    Object.keys(formFields).forEach((field) => {
      const value = values[field];
      const filterKey = getFilterKey(field);

      if (value === null || value === undefined || value === "" || (Array.isArray(value) && value.length === 0)) {
        searchParams.delete(filterKey);
      } else {
        searchParams.set(filterKey, value);
      }
    });

    searchParams.delete("page");
    updateSearchParams({ searchParams });
    clearFields({ form: formRef.current, fields: submit?.fieldsToClearOnSubmit });
  }

  function onHandleReset() {
    clearFilters();
    formRef.current.resetFields();
  }

  function onHandleSearch() {
    formRef.current.submit();
  }

  function updateSearchParams({ searchParams }) {
    navigate({ pathname: location.pathname, search: decodeURIComponent(searchParams.toString()) });
  }

  function getFilterKey(dataIndex) {
    return filterKey ? `${filterKey}[${dataIndex}]` : dataIndex;
  }

  function clearFilters() {
    if (!filterKey) {
      formRef.current?.resetFields();
      updateSearchParams({ searchParams: "" });
      return;
    }

    const searchParams = new URLSearchParams(decodeURIComponent(location.search));
    [...searchParams.keys()].forEach((key) => {
      if (key.startsWith(`${filterKey}[`)) {
        searchParams.delete(key);
      }
    });
    searchParams.delete("page");
    updateSearchParams({ searchParams });
    formRef.current?.resetFields();
  }

  // 转义正则表达式中的特殊字符
  function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  }

  function getFilterParams(filterKey) {
    try {
      const queryParams = Utils.getQueryParams(decodeURIComponent(location.search));
      if (!filterKey) return queryParams;

      const filterParams = {};
      const regex = new RegExp(`^${escapeRegExp(filterKey)}\\[(\\w+)\\]`);

      for (const key in queryParams) {
        if (Object.prototype.hasOwnProperty.call(queryParams, key)) {
          const dataIndex = regex.exec(key)?.[1];
          if (dataIndex) {
            filterParams[dataIndex] = queryParams[key];
          }
        }
      }

      return filterParams;
    } catch (error) {
      return {};
    }
  }

  return (
    <Flex gap={10}>
      <JSONForm
        ref={formRef}
        data={restData}
        initialValues={defaultValues}
        onFinish={onHandleFinish}
        extendComponents={extendComponents}
        onKeyPress={(e) => {
          if (e.key === "Enter") {
            e.preventDefault();
          }
        }}
      />
      {showButtons && (
        <Flex gap={16}>
          <Button onClick={onHandleReset}>重置</Button>
          <Button type="primary" onClick={onHandleSearch}>
            搜索
          </Button>
        </Flex>
      )}
    </Flex>
  );
}

export default QueryFilter;
