import { Button } from "antd";
import { useEffect, useRef, useCallback } from "react";

function PodEdit(props) {
  const { value, onChange, formRef } = props;
  const openerRef = useRef(null);

  function handleClick() {
    const host = window._POD_PAGE_HOST_ || "https://test-pms-erp6.cnzlerp.com";
    const url = new URL(`${host}/pod/iframe-edit-option-set`);
    if (value) {
      url.searchParams.set("optionSetId", value.id);
    }
    const width = 1000;
    const left = (window.screen.width - width - 17) / 2;
    const features = `width=${width},height=1000,top=0,left=${left},popup=true`;
    openerRef.current = window.open(url.toString(), ``, features);
  }

  const handlePodPageMessage = useCallback(
    (event) => {
      const { action, payload } = event.data || {};
      if (action === "submit") {
        formRef.current.setFieldValue("template", payload.template);
        onChange?.(payload.option_set);
        if (openerRef.current) {
          openerRef.current.close();
          openerRef.current = null;
        }
      }
    },
    [formRef, onChange]
  );

  useEffect(() => {
    window.addEventListener("message", handlePodPageMessage);

    return () => window.removeEventListener("message", handlePodPageMessage);
  }, [handlePodPageMessage]);

  return <Button onClick={handleClick}>{value?.id ? "编辑选项" : "绑定模板"}</Button>;
}

export default PodEdit;
