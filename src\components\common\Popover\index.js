import styles from "./index.module.scss";
import { Popover as AntPop<PERSON> } from "antd";
import { useImperativeHandle, useState, forwardRef } from "react";

function Popover(props, ref) {
  const { children, content, ...otherProps } = props;
  const [popoverOpen, setPopoverOpen] = useState(false);

  function hidden() {
    setPopoverOpen(false);
  }

  useImperativeHandle(ref, () => {
    return {
      hidden,
    };
  });

  return (
    <AntPopover
      open={popoverOpen}
      onOpenChange={(open) => {
        setPopoverOpen(open);
      }}
      content={content}
      trigger="click"
      overlayClassName={styles.editablePopover}
      destroyTooltipOnHide
      placement="right"
      {...otherProps}
    >
      {children}
    </AntPopover>
  );
}

Popover = forwardRef(Popover);

export default Popover;
